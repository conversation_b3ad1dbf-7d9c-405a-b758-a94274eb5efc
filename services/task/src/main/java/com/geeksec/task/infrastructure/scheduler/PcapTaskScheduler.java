package com.geeksec.task.infrastructure.scheduler;

import com.geeksec.task.infrastructure.processor.PcapTaskProcessor;
import com.geeksec.task.model.entity.PcapDownloadTask;
import com.geeksec.task.repository.PcapDownloadTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * PCAP任务调度器
 * 作为事件驱动的兜底机制，处理失败或卡住的任务
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PcapTaskScheduler {

    private final PcapDownloadTaskRepository pcapDownloadTaskRepository;
    private final PcapTaskProcessor pcapTaskProcessor;

    /**
     * 定时处理卡住的任务
     * 每2分钟检查一次，作为兜底机制
     */
    @Scheduled(fixedDelay = 120000) // 2分钟
    public void handleStuckTasks() {
        try {
            List<PcapDownloadTask> stuckTasks = findStuckTasks();
            
            if (!stuckTasks.isEmpty()) {
                log.warn("发现{}个卡住的任务，开始重新处理", stuckTasks.size());
                
                for (PcapDownloadTask task : stuckTasks) {
                    try {
                        log.warn("重新处理卡住的任务: taskId={}, status={}, updateTime={}", 
                                task.getId(), task.getStatus(), task.getUpdateTime());
                        
                        // 重置任务状态为待处理
                        resetTaskStatus(task);
                        
                        // 使用统一处理器重新处理
                        pcapTaskProcessor.processTask(task);
                        
                    } catch (Exception e) {
                        log.error("重新处理卡住任务失败: taskId={}", task.getId(), e);
                        
                        // 标记为失败
                        markTaskAsFailed(task, "定时重试失败: " + e.getMessage());
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("处理卡住任务失败", e);
        }
    }

    /**
     * 定时处理待处理的任务
     * 每1分钟检查一次，处理可能遗漏的待处理任务
     */
    @Scheduled(fixedDelay = 60000) // 1分钟
    public void handlePendingTasks() {
        try {
            List<PcapDownloadTask> pendingTasks = findPendingTasks();
            
            if (!pendingTasks.isEmpty()) {
                log.info("发现{}个待处理任务", pendingTasks.size());
                
                for (PcapDownloadTask task : pendingTasks) {
                    try {
                        log.info("处理待处理任务: taskId={}, createTime={}", 
                                task.getId(), task.getCreateTime());
                        
                        // 使用统一处理器处理
                        pcapTaskProcessor.processTask(task);
                        
                    } catch (Exception e) {
                        log.error("处理待处理任务失败: taskId={}", task.getId(), e);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("处理待处理任务失败", e);
        }
    }

    /**
     * 查找卡住的任务
     * 条件：状态为PROCESSING且超过10分钟没有更新
     */
    private List<PcapDownloadTask> findStuckTasks() {
        LocalDateTime stuckThreshold = LocalDateTime.now().minusMinutes(10);
        
        return pcapDownloadTaskRepository.findTasksByStatusAndUpdateTimeBefore(
                PcapDownloadTask.TaskStatus.PROCESSING.getCode(), 
                stuckThreshold
        );
    }

    /**
     * 查找待处理的任务
     * 条件：状态为PENDING且创建时间超过5分钟
     */
    private List<PcapDownloadTask> findPendingTasks() {
        LocalDateTime pendingThreshold = LocalDateTime.now().minusMinutes(5);
        
        return pcapDownloadTaskRepository.findTasksByStatusAndCreateTimeBefore(
                PcapDownloadTask.TaskStatus.PENDING.getCode(), 
                pendingThreshold
        );
    }

    /**
     * 重置任务状态
     */
    private void resetTaskStatus(PcapDownloadTask task) {
        try {
            PcapDownloadTask updateTask = new PcapDownloadTask();
            updateTask.setId(task.getId());
            updateTask.setStatus(PcapDownloadTask.TaskStatus.PENDING.getCode());
            updateTask.setProgress(0);
            updateTask.setErrorMessage(null);
            updateTask.setUpdateTime(LocalDateTime.now());
            
            pcapDownloadTaskRepository.update(updateTask);
            log.debug("重置任务状态: taskId={}", task.getId());
            
        } catch (Exception e) {
            log.error("重置任务状态失败: taskId={}", task.getId(), e);
        }
    }

    /**
     * 标记任务为失败
     */
    private void markTaskAsFailed(PcapDownloadTask task, String errorMessage) {
        try {
            PcapDownloadTask updateTask = new PcapDownloadTask();
            updateTask.setId(task.getId());
            updateTask.setStatus(PcapDownloadTask.TaskStatus.FAILED.getCode());
            updateTask.setProgress(0);
            updateTask.setErrorMessage(errorMessage);
            updateTask.setUpdateTime(LocalDateTime.now());
            
            pcapDownloadTaskRepository.update(updateTask);
            log.debug("标记任务为失败: taskId={}, error={}", task.getId(), errorMessage);
            
        } catch (Exception e) {
            log.error("标记任务失败状态失败: taskId={}", task.getId(), e);
        }
    }

    /**
     * 获取调度器统计信息
     */
    public SchedulerStats getSchedulerStats() {
        try {
            long pendingCount = pcapDownloadTaskRepository.getTaskCountByStatus(
                    PcapDownloadTask.TaskStatus.PENDING.getCode());
            
            long processingCount = pcapDownloadTaskRepository.getTaskCountByStatus(
                    PcapDownloadTask.TaskStatus.PROCESSING.getCode());
            
            long completedCount = pcapDownloadTaskRepository.getTaskCountByStatus(
                    PcapDownloadTask.TaskStatus.COMPLETED.getCode());
            
            long failedCount = pcapDownloadTaskRepository.getTaskCountByStatus(
                    PcapDownloadTask.TaskStatus.FAILED.getCode());
            
            return new SchedulerStats(pendingCount, processingCount, completedCount, failedCount);
            
        } catch (Exception e) {
            log.error("获取调度器统计信息失败", e);
            return new SchedulerStats(0, 0, 0, 0);
        }
    }

    /**
     * 调度器统计信息
     */
    public record SchedulerStats(
        long pendingTasks,
        long processingTasks,
        long completedTasks,
        long failedTasks
    ) {
        public long getTotalTasks() {
            return pendingTasks + processingTasks + completedTasks + failedTasks;
        }
    }

    /**
     * 手动触发处理所有待处理任务
     */
    public void triggerPendingTasksProcessing() {
        log.info("手动触发处理所有待处理任务");
        handlePendingTasks();
    }

    /**
     * 手动触发处理所有卡住的任务
     */
    public void triggerStuckTasksProcessing() {
        log.info("手动触发处理所有卡住的任务");
        handleStuckTasks();
    }
}

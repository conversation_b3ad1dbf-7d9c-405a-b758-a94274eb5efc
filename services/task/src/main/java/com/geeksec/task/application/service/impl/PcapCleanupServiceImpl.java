package com.geeksec.task.application.service.impl;

import com.geeksec.task.model.entity.PcapDownloadTask;
import com.geeksec.task.repository.PcapDownloadTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;

/**
 * PCAP文件清理服务实现
 * 负责定时清理过期的PCAP下载文件和任务记录
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PcapCleanupServiceImpl {

    private final PcapDownloadTaskRepository pcapDownloadTaskRepository;

    /**
     * PCAP文件过期时间（小时）
     */
    @Value("${app.task.pcap.expire-hours:24}")
    private int expireHours;

    /**
     * PCAP下载文件存储路径
     */
    @Value("${task.file.pcap-download-path:/data/download/pcap/}")
    private String pcapDownloadPath;

    /**
     * 定时清理过期的PCAP下载任务和文件
     * 每5分钟执行一次
     */
    @Scheduled(fixedDelay = 300000) // 5分钟
    @Transactional(rollbackFor = Exception.class)
    public void cleanupExpiredPcapTasks() {
        log.info("开始清理过期的PCAP下载任务");

        try {
            // 计算过期时间
            LocalDateTime expireTime = LocalDateTime.now().minusHours(expireHours);
            
            // 查询过期的任务
            List<PcapDownloadTask> expiredTasks = pcapDownloadTaskRepository.getExpiredTasks(expireTime);
            
            if (expiredTasks.isEmpty()) {
                log.debug("没有找到过期的PCAP下载任务");
                return;
            }

            log.info("找到{}个过期的PCAP下载任务", expiredTasks.size());

            int cleanedCount = 0;
            int fileDeletedCount = 0;

            for (PcapDownloadTask task : expiredTasks) {
                try {
                    // 删除相关文件
                    boolean fileDeleted = deleteTaskFiles(task);
                    if (fileDeleted) {
                        fileDeletedCount++;
                    }

                    // 更新任务状态为已删除
                    updateTaskStatusToDeleted(task);
                    cleanedCount++;

                    log.debug("清理过期任务成功: taskId={}, userId={}", task.getId(), task.getUserId());

                } catch (Exception e) {
                    log.error("清理过期任务失败: taskId={}", task.getId(), e);
                }
            }

            log.info("清理过期PCAP任务完成: 清理任务数={}, 删除文件数={}", cleanedCount, fileDeletedCount);

        } catch (Exception e) {
            log.error("清理过期PCAP任务失败", e);
        }
    }

    /**
     * 删除任务相关的文件
     * 
     * @param task PCAP下载任务
     * @return 是否成功删除文件
     */
    private boolean deleteTaskFiles(PcapDownloadTask task) {
        boolean success = true;

        try {
            // 删除下载的PCAP文件
            if (task.getDownloadUrl() != null && !task.getDownloadUrl().isEmpty()) {
                String fileName = extractFileNameFromUrl(task.getDownloadUrl());
                if (fileName != null) {
                    Path filePath = Paths.get(pcapDownloadPath, fileName);
                    if (Files.exists(filePath)) {
                        Files.delete(filePath);
                        log.debug("删除PCAP文件: {}", filePath);
                    }
                }
            }

            // 删除临时目录（如果存在）
            String tempDirName = String.format("pcap_download_%d_*", task.getId());
            Path tempBaseDir = Paths.get(System.getProperty("java.io.tmpdir"));
            
            try {
                Files.list(tempBaseDir)
                    .filter(path -> path.getFileName().toString().startsWith("pcap_download_" + task.getId() + "_"))
                    .forEach(this::deleteDirectoryRecursively);
            } catch (IOException e) {
                log.warn("清理临时目录失败: taskId={}", task.getId(), e);
                success = false;
            }

        } catch (Exception e) {
            log.error("删除任务文件失败: taskId={}", task.getId(), e);
            success = false;
        }

        return success;
    }

    /**
     * 从下载URL中提取文件名
     * 
     * @param downloadUrl 下载URL
     * @return 文件名
     */
    private String extractFileNameFromUrl(String downloadUrl) {
        if (downloadUrl == null || downloadUrl.isEmpty()) {
            return null;
        }

        // 从URL中提取文件名
        // 例如：/api/v1/task/pcap/download/123/merged_pcap_123_1234567890.pcap
        String[] parts = downloadUrl.split("/");
        if (parts.length > 0) {
            return parts[parts.length - 1];
        }

        return null;
    }

    /**
     * 递归删除目录
     * 
     * @param directory 要删除的目录
     */
    private void deleteDirectoryRecursively(Path directory) {
        try {
            if (Files.isDirectory(directory)) {
                Files.walk(directory)
                    .sorted((a, b) -> b.compareTo(a)) // 先删除子文件/目录
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            log.warn("删除文件失败: {}", path, e);
                        }
                    });
            } else {
                Files.delete(directory);
            }
        } catch (IOException e) {
            log.warn("删除目录失败: {}", directory, e);
        }
    }

    /**
     * 更新任务状态为已删除
     * 
     * @param task PCAP下载任务
     */
    private void updateTaskStatusToDeleted(PcapDownloadTask task) {
        try {
            PcapDownloadTask updateTask = new PcapDownloadTask();
            updateTask.setId(task.getId());
            updateTask.setStatus(PcapDownloadTask.TaskStatus.DELETED.getCode());
            updateTask.setUpdateTime(LocalDateTime.now());

            pcapDownloadTaskRepository.update(updateTask);
            log.debug("更新任务状态为已删除: taskId={}", task.getId());

        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}", task.getId(), e);
        }
    }

    /**
     * 手动清理指定用户的所有过期任务
     * 
     * @param userId 用户ID
     * @return 清理的任务数量
     */
    public int cleanupUserExpiredTasks(String userId) {
        log.info("开始清理用户过期任务: userId={}", userId);

        try {
            LocalDateTime expireTime = LocalDateTime.now().minusHours(expireHours);
            
            // 查询用户的过期任务
            List<PcapDownloadTask> userExpiredTasks = pcapDownloadTaskRepository.getUserTasksByStatus(userId, 
                PcapDownloadTask.TaskStatus.COMPLETED.getCode())
                .stream()
                .filter(task -> task.getExpireTime() != null && task.getExpireTime().isBefore(expireTime))
                .toList();

            int cleanedCount = 0;
            for (PcapDownloadTask task : userExpiredTasks) {
                try {
                    deleteTaskFiles(task);
                    updateTaskStatusToDeleted(task);
                    cleanedCount++;
                } catch (Exception e) {
                    log.error("清理用户过期任务失败: taskId={}", task.getId(), e);
                }
            }

            log.info("清理用户过期任务完成: userId={}, 清理数量={}", userId, cleanedCount);
            return cleanedCount;

        } catch (Exception e) {
            log.error("清理用户过期任务失败: userId={}", userId, e);
            return 0;
        }
    }

    /**
     * 获取清理统计信息
     * 
     * @return 清理统计信息
     */
    public CleanupStats getCleanupStats() {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusHours(expireHours);
            
            long totalTasks = pcapDownloadTaskRepository.getTaskCountByStatus(
                PcapDownloadTask.TaskStatus.COMPLETED.getCode());
            
            long expiredTasks = pcapDownloadTaskRepository.getExpiredTasks(expireTime).size();
            
            long deletedTasks = pcapDownloadTaskRepository.getTaskCountByStatus(
                PcapDownloadTask.TaskStatus.DELETED.getCode());

            return new CleanupStats(totalTasks, expiredTasks, deletedTasks, expireHours);

        } catch (Exception e) {
            log.error("获取清理统计信息失败", e);
            return new CleanupStats(0, 0, 0, expireHours);
        }
    }

    /**
     * 清理统计信息
     */
    public record CleanupStats(
        long totalCompletedTasks,
        long expiredTasks,
        long deletedTasks,
        int expireHours
    ) {}
}

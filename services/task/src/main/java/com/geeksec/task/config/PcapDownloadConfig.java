package com.geeksec.task.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * PCAP下载相关配置
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Configuration
@EnableAsync
@EnableScheduling
@ConfigurationProperties(prefix = "app.pcap")
@Data
public class PcapDownloadConfig {

    /**
     * 存储配置
     */
    private Storage storage = new Storage();

    /**
     * 下载配置
     */
    private Download download = new Download();

    /**
     * mergecap工具配置
     */
    private Mergecap mergecap = new Mergecap();

    /**
     * 任务配置
     */
    private Task task = new Task();

    /**
     * 配置异步任务执行器
     */
    @Bean("taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(16);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("pcap-task-");
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 存储配置
     */
    @Data
    public static class Storage {
        /**
         * PCAP文件存储根目录
         */
        private String basePath = "/data";
    }

    /**
     * 下载配置
     */
    @Data
    public static class Download {
        /**
         * 临时文件目录
         */
        private String tempPath = "/tmp/pcap-download";

        /**
         * 单个文件最大大小 (1GB)
         */
        private long maxFileSize = 1073741824L;

        /**
         * 单次下载最大会话数量
         */
        private int maxSessionCount = 1000;
    }

    /**
     * mergecap工具配置
     */
    @Data
    public static class Mergecap {
        /**
         * mergecap命令路径
         */
        private String command = "/usr/bin/mergecap";

        /**
         * 执行超时时间（秒）
         */
        private int timeout = 300;
    }

    /**
     * 任务配置
     */
    @Data
    public static class Task {
        /**
         * PCAP任务配置
         */
        private Pcap pcap = new Pcap();

        @Data
        public static class Pcap {
            /**
             * 下载文件过期时间（小时）
             */
            private int expireHours = 24;

            /**
             * 最大文件大小
             */
            private long maxFileSize = 1073741824L;
        }
    }

    /**
     * 获取单次下载最大会话数量常量
     */
    public static final int MAX_SESSION_COUNT_PER_DOWNLOAD = 1000;

    /**
     * 获取最小有效PCAP文件大小常量（字节）
     */
    public static final long MIN_VALID_PCAP_FILE_SIZE = 30L;

    /**
     * 获取默认过期时间（小时）
     */
    public static final int DEFAULT_EXPIRE_HOURS = 24;
}

package com.geeksec.task.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 任务状态响应DTO
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "任务状态响应")
public class TaskStatusResponse {

    @Schema(description = "任务ID")
    private Integer taskId;

    @Schema(description = "任务状态", example = "PROCESSING")
    private String status;

    @Schema(description = "任务状态描述", example = "正在处理中")
    private String statusDescription;

    @Schema(description = "进度百分比", example = "75")
    private Integer progress;

    @Schema(description = "已处理文件数量")
    private Integer processedFileCount;

    @Schema(description = "总文件数量")
    private Integer totalFileCount;

    @Schema(description = "当前处理阶段", example = "文件合并中")
    private String currentStage;

    @Schema(description = "下载链接")
    private String downloadUrl;

    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "预计剩余时间（秒）")
    private Long estimatedRemainingSeconds;

    @Schema(description = "任务创建时间")
    private LocalDateTime createTime;

    @Schema(description = "任务开始时间")
    private LocalDateTime startTime;

    @Schema(description = "任务完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "文件过期时间")
    private LocalDateTime expireTime;

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("待处理"),
        PROCESSING("处理中"),
        COMPLETED("已完成"),
        FAILED("失败"),
        CANCELLED("已取消");

        private final String description;

        TaskStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 创建处理中状态的响应
     */
    public static TaskStatusResponse processing(Integer taskId, Integer progress, 
                                              Integer processedCount, Integer totalCount, 
                                              String stage) {
        TaskStatusResponse response = new TaskStatusResponse();
        response.setTaskId(taskId);
        response.setStatus(TaskStatus.PROCESSING.name());
        response.setStatusDescription(TaskStatus.PROCESSING.getDescription());
        response.setProgress(progress);
        response.setProcessedFileCount(processedCount);
        response.setTotalFileCount(totalCount);
        response.setCurrentStage(stage);
        
        // 估算剩余时间
        if (progress > 0 && progress < 100) {
            long estimatedTotal = (System.currentTimeMillis() * 100L) / progress;
            response.setEstimatedRemainingSeconds((estimatedTotal - System.currentTimeMillis()) / 1000);
        }
        
        return response;
    }

    /**
     * 创建完成状态的响应
     */
    public static TaskStatusResponse completed(Integer taskId, String downloadUrl, 
                                             Long fileSize, LocalDateTime completeTime) {
        TaskStatusResponse response = new TaskStatusResponse();
        response.setTaskId(taskId);
        response.setStatus(TaskStatus.COMPLETED.name());
        response.setStatusDescription(TaskStatus.COMPLETED.getDescription());
        response.setProgress(100);
        response.setDownloadUrl(downloadUrl);
        response.setFileSize(fileSize);
        response.setCompleteTime(completeTime);
        response.setEstimatedRemainingSeconds(0L);
        return response;
    }

    /**
     * 创建失败状态的响应
     */
    public static TaskStatusResponse failed(Integer taskId, String errorMessage) {
        TaskStatusResponse response = new TaskStatusResponse();
        response.setTaskId(taskId);
        response.setStatus(TaskStatus.FAILED.name());
        response.setStatusDescription(TaskStatus.FAILED.getDescription());
        response.setProgress(0);
        response.setErrorMessage(errorMessage);
        return response;
    }

    /**
     * 检查任务是否已完成（成功或失败）
     */
    public boolean isFinished() {
        return TaskStatus.COMPLETED.name().equals(status) || 
               TaskStatus.FAILED.name().equals(status) || 
               TaskStatus.CANCELLED.name().equals(status);
    }

    /**
     * 检查任务是否可以取消
     */
    public boolean isCancellable() {
        return TaskStatus.PENDING.name().equals(status) || 
               TaskStatus.PROCESSING.name().equals(status);
    }

    /**
     * 检查任务是否可以重试
     */
    public boolean isRetryable() {
        return TaskStatus.FAILED.name().equals(status);
    }
}

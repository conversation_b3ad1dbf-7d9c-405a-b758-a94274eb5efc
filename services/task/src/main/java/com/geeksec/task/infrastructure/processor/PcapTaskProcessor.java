package com.geeksec.task.infrastructure.processor;

import com.alibaba.fastjson2.JSON;
import com.geeksec.task.infrastructure.client.SessionClient;
import com.geeksec.task.model.entity.PcapDownloadTask;
import com.geeksec.task.repository.PcapDownloadTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * PCAP任务统一处理器
 * 整合所有PCAP下载任务的处理逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PcapTaskProcessor {

    private final PcapDownloadTaskRepository pcapDownloadTaskRepository;
    private final SessionClient sessionClient;

    /**
     * PCAP下载文件存储路径
     */
    @Value("${task.file.pcap-download-path:/data/download/pcap/}")
    private String pcapDownloadPath;

    /**
     * mergecap命令路径
     */
    @Value("${app.pcap.mergecap.command:/usr/bin/mergecap}")
    private String mergecapCommand;

    /**
     * 命令执行超时时间（秒）
     */
    @Value("${app.pcap.mergecap.timeout:300}")
    private int commandTimeout;

    /**
     * 最大文件大小
     */
    @Value("${app.pcap.download.max-file-size:1073741824}")
    private long maxFileSize;

    /**
     * 统一的任务处理入口
     * 
     * @param task PCAP下载任务
     */
    @Async("taskExecutor")
    public void processTask(PcapDownloadTask task) {
        log.info("开始处理PCAP下载任务: taskId={}", task.getId());

        try {
            // 更新任务状态为处理中
            updateTaskStatus(task.getId(), PcapDownloadTask.TaskStatus.PROCESSING, 0, null);

            // 解析会话ID列表
            List<String> sessionIds = JSON.parseArray(task.getSessionIds(), String.class);
            log.info("解析会话ID列表: taskId={}, sessionCount={}", task.getId(), sessionIds.size());

            // 获取PCAP文件信息
            List<String> pcapFilePaths = getSessionPcapFiles(sessionIds);
            
            if (pcapFilePaths.isEmpty()) {
                updateTaskStatus(task.getId(), PcapDownloadTask.TaskStatus.FAILED, 0, "未找到有效的PCAP文件");
                return;
            }

            log.info("获取PCAP文件路径: taskId={}, fileCount={}", task.getId(), pcapFilePaths.size());

            // 更新进度
            updateTaskStatus(task.getId(), PcapDownloadTask.TaskStatus.PROCESSING, 30, null);

            // 创建任务目录
            String taskDir = createTaskDirectory(task.getId());
            
            // 处理PCAP文件合并
            String mergedFilePath = processPcapMerge(task.getId(), pcapFilePaths, taskDir);
            
            if (mergedFilePath != null) {
                // 更新任务完成状态
                updateTaskCompletion(task.getId(), mergedFilePath);
                log.info("PCAP下载任务处理完成: taskId={}, filePath={}", task.getId(), mergedFilePath);
            } else {
                updateTaskStatus(task.getId(), PcapDownloadTask.TaskStatus.FAILED, 0, "PCAP文件处理失败");
            }

        } catch (Exception e) {
            log.error("处理PCAP下载任务失败: taskId={}", task.getId(), e);
            updateTaskStatus(task.getId(), PcapDownloadTask.TaskStatus.FAILED, 0, e.getMessage());
        }
    }

    /**
     * 获取会话PCAP文件路径
     */
    private List<String> getSessionPcapFiles(List<String> sessionIds) {
        try {
            // 调用Session服务获取PCAP文件信息
            var response = sessionClient.getSessionPcapFiles(sessionIds);
            
            if (!response.isSuccess()) {
                throw new RuntimeException("获取PCAP文件信息失败: " + response.getMessage());
            }

            return response.getData().stream()
                .filter(info -> info.pcapFilePath() != null && info.fileSize() != null && info.fileSize() > 0)
                .map(SessionClient.SessionPcapInfo::pcapFilePath)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取会话PCAP文件失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 创建任务目录
     */
    private String createTaskDirectory(Integer taskId) throws IOException {
        String taskDirPath = Paths.get(pcapDownloadPath, taskId.toString()).toString();
        Path taskDir = Paths.get(taskDirPath);
        Path tmpDir = taskDir.resolve("tmp");
        
        // 创建目录
        Files.createDirectories(taskDir);
        Files.createDirectories(tmpDir);
        
        log.debug("创建任务目录: {}", taskDirPath);
        return taskDirPath;
    }

    /**
     * 处理PCAP文件合并
     */
    private String processPcapMerge(Integer taskId, List<String> pcapFilePaths, String taskDir) {
        try {
            log.info("开始处理PCAP合并: taskId={}, fileCount={}", taskId, pcapFilePaths.size());

            // 验证并复制有效的PCAP文件
            List<String> validFiles = validateAndCopyPcapFiles(pcapFilePaths, taskDir, taskId);
            
            if (validFiles.isEmpty()) {
                log.warn("未找到有效的PCAP文件: taskId={}", taskId);
                return null;
            }

            // 更新进度
            updateTaskStatus(taskId, PcapDownloadTask.TaskStatus.PROCESSING, 60, null);

            // 合并PCAP文件
            String mergedFilePath = mergePcapFiles(taskId, validFiles, taskDir);
            
            // 更新进度
            updateTaskStatus(taskId, PcapDownloadTask.TaskStatus.PROCESSING, 90, null);

            // 检查合并后的文件
            if (mergedFilePath != null && isValidPcapFile(mergedFilePath)) {
                return mergedFilePath;
            } else {
                log.error("合并后的PCAP文件无效: taskId={}", taskId);
                return null;
            }

        } catch (Exception e) {
            log.error("处理PCAP合并失败: taskId={}", taskId, e);
            return null;
        }
    }

    /**
     * 验证并复制PCAP文件到任务目录
     */
    private List<String> validateAndCopyPcapFiles(List<String> pcapFilePaths, String taskDir, Integer taskId) {
        List<String> validFiles = new ArrayList<>();
        Path tmpDir = Paths.get(taskDir, "tmp");
        
        for (String filePath : pcapFilePaths) {
            try {
                Path sourcePath = Paths.get(filePath);
                
                // 检查文件是否存在
                if (!Files.exists(sourcePath)) {
                    log.warn("PCAP文件不存在，跳过: {}", filePath);
                    continue;
                }
                
                // 检查文件大小
                long fileSize = Files.size(sourcePath);
                if (fileSize == 0) {
                    log.warn("PCAP文件为空，跳过: {}", filePath);
                    continue;
                }
                
                if (fileSize > maxFileSize) {
                    log.warn("PCAP文件过大，跳过: {} ({}字节)", filePath, fileSize);
                    continue;
                }
                
                // 复制文件到临时目录
                String fileName = sourcePath.getFileName().toString();
                Path targetPath = tmpDir.resolve(fileName);
                Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                
                validFiles.add(targetPath.toString());
                log.debug("复制PCAP文件: {} -> {}", filePath, targetPath);
                
            } catch (Exception e) {
                log.error("处理PCAP文件失败: {}", filePath, e);
            }
        }
        
        return validFiles;
    }

    /**
     * 合并PCAP文件
     */
    private String mergePcapFiles(Integer taskId, List<String> validFiles, String taskDir) {
        try {
            String outputFileName = String.format("merged_%d.pcap", taskId);
            String outputFilePath = Paths.get(taskDir, outputFileName).toString();

            // 构建mergecap命令
            List<String> command = new ArrayList<>();
            command.add(mergecapCommand);
            command.add("-w");
            command.add(outputFilePath);
            command.addAll(validFiles);

            log.info("执行mergecap命令: taskId={}, inputFiles={}, output={}", 
                    taskId, validFiles.size(), outputFilePath);

            // 执行命令
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(true);
            
            Process process = processBuilder.start();
            
            // 读取命令输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // 等待命令完成
            boolean finished = process.waitFor(commandTimeout, TimeUnit.SECONDS);
            
            if (!finished) {
                process.destroyForcibly();
                throw new RuntimeException("mergecap命令执行超时");
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                log.error("mergecap命令执行失败: exitCode={}, output={}", exitCode, output.toString());
                throw new RuntimeException("mergecap命令执行失败，退出码: " + exitCode);
            }

            log.info("mergecap命令执行成功: taskId={}, output={}", taskId, outputFilePath);
            return outputFilePath;

        } catch (Exception e) {
            log.error("合并PCAP文件失败: taskId={}", taskId, e);
            return null;
        }
    }

    /**
     * 检查PCAP文件是否有效
     */
    private boolean isValidPcapFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                return false;
            }

            long fileSize = Files.size(path);
            // 检查文件大小，小于30字节认为无效
            if (fileSize < 30) {
                log.warn("PCAP文件太小，可能无效: path={}, size={}", filePath, fileSize);
                return false;
            }

            return true;

        } catch (IOException e) {
            log.error("检查PCAP文件失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Integer taskId, PcapDownloadTask.TaskStatus status, 
                                 Integer progress, String errorMessage) {
        try {
            PcapDownloadTask updateTask = new PcapDownloadTask();
            updateTask.setId(taskId);
            updateTask.setStatus(status.getCode());
            updateTask.setProgress(progress);
            updateTask.setUpdateTime(LocalDateTime.now());

            if (status == PcapDownloadTask.TaskStatus.PROCESSING) {
                updateTask.setStartTime(LocalDateTime.now());
            }

            if (errorMessage != null) {
                updateTask.setErrorMessage(errorMessage);
            }

            pcapDownloadTaskRepository.update(updateTask);
            log.debug("任务状态更新: taskId={}, status={}, progress={}", taskId, status, progress);

        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}", taskId, e);
        }
    }

    /**
     * 更新任务完成状态
     */
    private void updateTaskCompletion(Integer taskId, String filePath) {
        try {
            // 计算相对路径
            String relativePath = filePath.replace(pcapDownloadPath, "");
            if (relativePath.startsWith("/")) {
                relativePath = relativePath.substring(1);
            }

            // 计算文件大小
            long fileSize = Files.size(Paths.get(filePath));

            PcapDownloadTask updateTask = new PcapDownloadTask();
            updateTask.setId(taskId);
            updateTask.setStatus(PcapDownloadTask.TaskStatus.COMPLETED.getCode());
            updateTask.setProgress(100);
            updateTask.setArchiveFilePath(relativePath);
            updateTask.setArchiveFileSize(fileSize);
            updateTask.setDownloadUrl(generateDownloadUrl(relativePath));
            updateTask.setCompleteTime(LocalDateTime.now());
            updateTask.setUpdateTime(LocalDateTime.now());

            pcapDownloadTaskRepository.update(updateTask);
            log.info("任务完成状态更新: taskId={}, path={}, size={}", taskId, relativePath, fileSize);

        } catch (Exception e) {
            log.error("更新任务完成状态失败: taskId={}", taskId, e);
        }
    }

    /**
     * 生成下载URL
     */
    private String generateDownloadUrl(String relativePath) {
        return "/api/v1/pcap/files/download/" + relativePath;
    }
}

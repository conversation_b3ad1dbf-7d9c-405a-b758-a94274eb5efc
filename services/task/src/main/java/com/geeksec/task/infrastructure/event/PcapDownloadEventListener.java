package com.geeksec.task.infrastructure.event;

import com.geeksec.task.domain.event.*;
import com.geeksec.task.infrastructure.processor.PcapTaskProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * PCAP下载任务事件监听器
 * 使用统一的任务处理器
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PcapDownloadEventListener {

    private final PcapTaskProcessor taskProcessor;

    /**
     * 监听任务创建事件，使用统一处理器处理
     */
    @Async("taskExecutor")
    @EventListener
    public void handleTaskCreated(PcapDownloadTaskCreatedEvent event) {
        log.info("收到PCAP下载任务创建事件: taskId={}, userId={}, sessionCount={}",
                event.getTaskId(), event.getUserId(), event.getSessionIds().size());

        try {
            // 根据优先级决定处理策略
            if (event.getPriority() == 1) {
                // 高优先级任务立即处理
                log.info("高优先级任务，立即处理: taskId={}", event.getTaskId());
                taskProcessor.processTask(event.getTask());
            } else {
                // 中低优先级任务延迟处理，避免系统过载
                Thread.sleep(event.getPriority() * 1000L);
                taskProcessor.processTask(event.getTask());
            }
        } catch (Exception e) {
            log.error("处理任务创建事件失败: taskId={}", event.getTaskId(), e);
        }
    }

    /**
     * 监听任务处理进度事件，更新状态和通知
     */
    @EventListener
    public void handleTaskProcessing(PcapDownloadTaskProcessingEvent event) {
        log.debug("PCAP下载任务进度更新: taskId={}, progress={}%, stage={}", 
                event.getTaskId(), event.getProgress(), event.getStage());

        // TODO: 可以在这里实现WebSocket推送，实时通知前端进度
        // webSocketService.sendProgressUpdate(event.getUserId(), event);
    }

    /**
     * 监听任务完成事件，进行后续处理
     */
    @EventListener
    public void handleTaskCompleted(PcapDownloadTaskCompletedEvent event) {
        log.info("PCAP下载任务完成: taskId={}, fileSize={}, processingTime={}ms", 
                event.getTaskId(), event.getFileSize(), event.getProcessingTimeMs());

        try {
            // 发送完成通知
            // notificationService.sendTaskCompletionNotification(event);
            
            // 记录统计信息
            // statisticsService.recordTaskCompletion(event);
            
            // 清理临时文件（延迟执行）
            // cleanupService.scheduleCleanup(event.getTaskId());
            
        } catch (Exception e) {
            log.error("处理任务完成事件失败: taskId={}", event.getTaskId(), e);
        }
    }

    /**
     * 监听任务失败事件，进行错误处理和重试
     */
    @EventListener
    public void handleTaskFailed(PcapDownloadTaskFailedEvent event) {
        log.error("PCAP下载任务失败: taskId={}, error={}, retryable={}", 
                event.getTaskId(), event.getErrorMessage(), event.getRetryable());

        try {
            // 记录错误统计
            // statisticsService.recordTaskFailure(event);
            
            // 发送失败通知
            // notificationService.sendTaskFailureNotification(event);
            
            // 如果可重试且重试次数未超限，安排重试
            if (event.getRetryable() && event.getRetryCount() < 3) {
                log.info("安排任务重试: taskId={}, retryCount={}", 
                        event.getTaskId(), event.getRetryCount());
                // retryService.scheduleRetry(event.getTaskId(), event.getRetryCount() + 1);
            }
            
        } catch (Exception e) {
            log.error("处理任务失败事件失败: taskId={}", event.getTaskId(), e);
        }
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.session.infrastructure.repositories.SessionRepositoryImpl">

    <!-- 结果映射 -->
    <resultMap id="SessionResultMap" type="com.geeksec.session.domain.entities.Session">
        <id property="sessionId" column="session_id"/>
        <result property="protocol" column="protocol"/>
        <result property="sessionStartTime" column="session_start_time"/>
        <result property="sessionEndTime" column="session_end_time"/>
        <result property="appId" column="app_id"/>
        <result property="appName" column="app_name"/>
        <result property="serverIp" column="server_ip"/>
        <result property="taskId" column="task_id"/>
        <result property="batchId" column="batch_id"/>
        <result property="threadId" column="thread_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        
        <!-- 复杂对象映射 -->
        <association property="sourceEndpoint" javaType="com.geeksec.session.domain.valueobjects.NetworkEndpoint">
            <result property="ipAddress" column="source_ip"/>
            <result property="port" column="source_port"/>
            <result property="macAddress" column="source_mac"/>
            <result property="internal" column="source_internal"/>
        </association>
        
        <association property="destinationEndpoint" javaType="com.geeksec.session.domain.valueobjects.NetworkEndpoint">
            <result property="ipAddress" column="destination_ip"/>
            <result property="port" column="destination_port"/>
            <result property="macAddress" column="destination_mac"/>
            <result property="internal" column="destination_internal"/>
        </association>
        
        <association property="statistics" javaType="com.geeksec.session.domain.valueobjects.SessionStatistics">
            <result property="totalPackets" column="total_packets"/>
            <result property="totalBytes" column="total_bytes"/>
            <result property="sourcePackets" column="source_packets"/>
            <result property="sourceBytes" column="source_bytes"/>
            <result property="destinationPackets" column="destination_packets"/>
            <result property="destinationBytes" column="destination_bytes"/>
            <result property="finPackets" column="fin_packets"/>
            <result property="rstPackets" column="rst_packets"/>
            <result property="synPackets" column="syn_packets"/>
            <result property="pshPackets" column="psh_packets"/>
            <result property="outOfOrderPackets" column="out_of_order_packets"/>
            <result property="retransmittedPackets" column="retransmitted_packets"/>
            <result property="lostPackets" column="lost_packets"/>
        </association>
        
        <association property="fingerprint" javaType="com.geeksec.session.domain.valueobjects.SessionFingerprint">
            <result property="tcpFingerprint" column="tcp_fingerprint"/>
            <result property="httpFingerprint" column="http_fingerprint"/>
            <result property="sslFingerprint" column="ssl_fingerprint"/>
            <result property="userAgent" column="user_agent"/>
            <result property="deviceFingerprint" column="device_fingerprint"/>
        </association>
        
        <association property="protocolMetadata" javaType="com.geeksec.session.domain.valueobjects.ProtocolMetadata">
            <!-- 协议元数据字段映射 -->
        </association>
    </resultMap>

    <!-- 基础查询SQL片段 -->
    <sql id="Base_Column_List">
        session_id, source_ip, source_port, source_mac, source_internal,
        destination_ip, destination_port, destination_mac, destination_internal,
        protocol, session_start_time, session_end_time, app_id, app_name,
        server_ip, task_id, batch_id, thread_id, labels, rule_labels,
        extension_data, total_packets, total_bytes, source_packets, source_bytes,
        destination_packets, destination_bytes, fin_packets, rst_packets,
        syn_packets, psh_packets, out_of_order_packets, retransmitted_packets,
        lost_packets, tcp_fingerprint, http_fingerprint, ssl_fingerprint,
        user_agent, device_fingerprint, create_time, update_time
    </sql>

    <!-- 插入会话 -->
    <insert id="save" parameterType="com.geeksec.session.domain.entities.Session">
        INSERT INTO sessions (
            session_id, source_ip, source_port, source_mac, source_internal,
            destination_ip, destination_port, destination_mac, destination_internal,
            protocol, session_start_time, session_end_time, app_id, app_name,
            server_ip, task_id, batch_id, thread_id, labels, rule_labels,
            extension_data, total_packets, total_bytes, source_packets, source_bytes,
            destination_packets, destination_bytes, fin_packets, rst_packets,
            syn_packets, psh_packets, out_of_order_packets, retransmitted_packets,
            lost_packets, tcp_fingerprint, http_fingerprint, ssl_fingerprint,
            user_agent, device_fingerprint, create_time, update_time
        ) VALUES (
            #{sessionId}, #{sourceEndpoint.ipAddress}, #{sourceEndpoint.port}, 
            #{sourceEndpoint.macAddress}, #{sourceEndpoint.internal},
            #{destinationEndpoint.ipAddress}, #{destinationEndpoint.port}, 
            #{destinationEndpoint.macAddress}, #{destinationEndpoint.internal},
            #{protocol}, #{sessionStartTime}, #{sessionEndTime}, #{appId}, #{appName},
            #{serverIp}, #{taskId}, #{batchId}, #{threadId}, 
            #{labels,typeHandler=com.geeksec.session.infrastructure.typehandlers.ListTypeHandler},
            #{ruleLabels,typeHandler=com.geeksec.session.infrastructure.typehandlers.ListTypeHandler},
            #{extensionData,typeHandler=com.geeksec.session.infrastructure.typehandlers.JsonTypeHandler},
            #{statistics.totalPackets}, #{statistics.totalBytes}, 
            #{statistics.sourcePackets}, #{statistics.sourceBytes},
            #{statistics.destinationPackets}, #{statistics.destinationBytes},
            #{statistics.finPackets}, #{statistics.rstPackets},
            #{statistics.synPackets}, #{statistics.pshPackets},
            #{statistics.outOfOrderPackets}, #{statistics.retransmittedPackets},
            #{statistics.lostPackets}, #{fingerprint.tcpFingerprint}, 
            #{fingerprint.httpFingerprint}, #{fingerprint.sslFingerprint},
            #{fingerprint.userAgent}, #{fingerprint.deviceFingerprint},
            #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 更新会话 -->
    <update id="update" parameterType="com.geeksec.session.domain.entities.Session">
        UPDATE sessions SET
            session_end_time = #{sessionEndTime},
            app_id = #{appId},
            app_name = #{appName},
            server_ip = #{serverIp},
            task_id = #{taskId},
            batch_id = #{batchId},
            thread_id = #{threadId},
            labels = #{labels,typeHandler=com.geeksec.session.infrastructure.typehandlers.ListTypeHandler},
            rule_labels = #{ruleLabels,typeHandler=com.geeksec.session.infrastructure.typehandlers.ListTypeHandler},
            extension_data = #{extensionData,typeHandler=com.geeksec.session.infrastructure.typehandlers.JsonTypeHandler},
            total_packets = #{statistics.totalPackets},
            total_bytes = #{statistics.totalBytes},
            source_packets = #{statistics.sourcePackets},
            source_bytes = #{statistics.sourceBytes},
            destination_packets = #{statistics.destinationPackets},
            destination_bytes = #{statistics.destinationBytes},
            fin_packets = #{statistics.finPackets},
            rst_packets = #{statistics.rstPackets},
            syn_packets = #{statistics.synPackets},
            psh_packets = #{statistics.pshPackets},
            out_of_order_packets = #{statistics.outOfOrderPackets},
            retransmitted_packets = #{statistics.retransmittedPackets},
            lost_packets = #{statistics.lostPackets},
            tcp_fingerprint = #{fingerprint.tcpFingerprint},
            http_fingerprint = #{fingerprint.httpFingerprint},
            ssl_fingerprint = #{fingerprint.sslFingerprint},
            user_agent = #{fingerprint.userAgent},
            device_fingerprint = #{fingerprint.deviceFingerprint},
            update_time = #{updateTime}
        WHERE session_id = #{sessionId}
    </update>

    <!-- 根据ID查询 -->
    <select id="findById" parameterType="string" resultMap="SessionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sessions
        WHERE session_id = #{sessionId}
    </select>

    <!-- 删除会话 -->
    <delete id="deleteById" parameterType="string">
        DELETE FROM sessions WHERE session_id = #{sessionId}
    </delete>

    <!-- 分页查询 -->
    <select id="findByQuery" parameterType="com.geeksec.session.application.dto.SessionQuery" resultMap="SessionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sessions
        <where>
            <if test="sourceIp != null and sourceIp != ''">
                AND source_ip = #{sourceIp}
            </if>
            <if test="destinationIp != null and destinationIp != ''">
                AND destination_ip = #{destinationIp}
            </if>
            <if test="protocol != null and protocol != ''">
                AND protocol = #{protocol}
            </if>
            <if test="appName != null and appName != ''">
                AND app_name = #{appName}
            </if>
            <if test="appId != null">
                AND app_id = #{appId}
            </if>
            <if test="startTime != null">
                AND session_start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND session_start_time <= #{endTime}
            </if>
            <if test="labels != null and labels.size() &gt; 0">
                AND JSON_OVERLAPS(labels, #{labels,typeHandler=com.geeksec.session.infrastructure.typehandlers.ListTypeHandler})
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                    source_ip LIKE CONCAT('%', #{keyword}, '%') OR
                    destination_ip LIKE CONCAT('%', #{keyword}, '%') OR
                    protocol LIKE CONCAT('%', #{keyword}, '%') OR
                    app_name LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
        <if test="sortBy != null and sortBy != ''">
            ORDER BY ${sortBy} ${sortOrder}
        </if>
        <if test="sortBy == null or sortBy == ''">
            ORDER BY create_time DESC
        </if>
        LIMIT #{offset}, #{size}
    </select>

    <!-- 查询总数 -->
    <select id="countByQuery" parameterType="com.geeksec.session.application.dto.SessionQuery" resultType="long">
        SELECT COUNT(*)
        FROM sessions
        <where>
            <if test="sourceIp != null and sourceIp != ''">
                AND source_ip = #{sourceIp}
            </if>
            <if test="destinationIp != null and destinationIp != ''">
                AND destination_ip = #{destinationIp}
            </if>
            <if test="protocol != null and protocol != ''">
                AND protocol = #{protocol}
            </if>
            <if test="appName != null and appName != ''">
                AND app_name = #{appName}
            </if>
            <if test="appId != null">
                AND app_id = #{appId}
            </if>
            <if test="startTime != null">
                AND session_start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND session_start_time <= #{endTime}
            </if>
            <if test="labels != null and labels.size() &gt; 0">
                AND JSON_OVERLAPS(labels, #{labels,typeHandler=com.geeksec.session.infrastructure.typehandlers.ListTypeHandler})
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                    source_ip LIKE CONCAT('%', #{keyword}, '%') OR
                    destination_ip LIKE CONCAT('%', #{keyword}, '%') OR
                    protocol LIKE CONCAT('%', #{keyword}, '%') OR
                    app_name LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
    </select>

    <!-- 根据时间范围查询 -->
    <select id="findByTimeRange" resultMap="SessionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sessions
        WHERE session_start_time >= #{startTime}
        AND session_start_time <= #{endTime}
        ORDER BY session_start_time DESC
    </select>

    <!-- 根据IP查询 -->
    <select id="findBySourceIp" parameterType="string" resultMap="SessionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sessions
        WHERE source_ip = #{sourceIp}
        ORDER BY session_start_time DESC
    </select>

    <select id="findByDestinationIp" parameterType="string" resultMap="SessionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sessions
        WHERE destination_ip = #{destinationIp}
        ORDER BY session_start_time DESC
    </select>

    <!-- 根据应用ID查询 -->
    <select id="findByAppId" parameterType="int" resultMap="SessionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sessions
        WHERE app_id = #{appId}
        ORDER BY session_start_time DESC
    </select>

    <!-- 根据标签查询 -->
    <select id="findByLabel" parameterType="int" resultMap="SessionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sessions
        WHERE JSON_CONTAINS(labels, #{labelId})
        ORDER BY session_start_time DESC
    </select>

    <!-- 查询活跃会话 -->
    <select id="findActiveSessions" resultMap="SessionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sessions
        WHERE session_end_time IS NULL
        ORDER BY session_start_time DESC
    </select>

    <!-- 查询可疑会话 -->
    <select id="findSuspiciousSessions" resultMap="SessionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sessions
        WHERE JSON_CONTAINS(labels, '1001') -- SUSPICIOUS标签ID
        ORDER BY session_start_time DESC
    </select>

    <!-- 协议分布统计 -->
    <select id="getProtocolDistribution" resultType="map">
        SELECT protocol, COUNT(*) as count
        FROM sessions
        <where>
            <if test="startTime != null">
                AND session_start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND session_start_time <= #{endTime}
            </if>
        </where>
        GROUP BY protocol
        ORDER BY count DESC
    </select>

    <!-- 应用分布统计 -->
    <select id="getApplicationDistribution" resultType="map">
        SELECT app_name, COUNT(*) as count
        FROM sessions
        <where>
            app_name IS NOT NULL
            <if test="startTime != null">
                AND session_start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND session_start_time <= #{endTime}
            </if>
        </where>
        GROUP BY app_name
        ORDER BY count DESC
    </select>

    <!-- TOP IP统计 -->
    <select id="getTopSourceIps" resultType="map">
        SELECT source_ip as ip, COUNT(*) as count
        FROM sessions
        <where>
            <if test="startTime != null">
                AND session_start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND session_start_time <= #{endTime}
            </if>
        </where>
        GROUP BY source_ip
        ORDER BY count DESC
        LIMIT #{limit}
    </select>

    <!-- 会话趋势统计 -->
    <select id="getSessionTrends" resultType="map">
        SELECT 
            <choose>
                <when test="timeUnit == 'hour'">
                    DATE_FORMAT(session_start_time, '%Y-%m-%d %H:00:00') as time_key
                </when>
                <when test="timeUnit == 'day'">
                    DATE_FORMAT(session_start_time, '%Y-%m-%d') as time_key
                </when>
                <when test="timeUnit == 'week'">
                    DATE_FORMAT(session_start_time, '%Y-%u') as time_key
                </when>
                <when test="timeUnit == 'month'">
                    DATE_FORMAT(session_start_time, '%Y-%m') as time_key
                </when>
                <otherwise>
                    DATE_FORMAT(session_start_time, '%Y-%m-%d') as time_key
                </otherwise>
            </choose>,
            COUNT(*) as count
        FROM sessions
        <where>
            <if test="startTime != null">
                AND session_start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND session_start_time <= #{endTime}
            </if>
        </where>
        GROUP BY time_key
        ORDER BY time_key
    </select>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType="list">
        DELETE FROM sessions
        WHERE session_id IN
        <foreach collection="list" item="sessionId" open="(" separator="," close=")">
            #{sessionId}
        </foreach>
    </delete>

</mapper>
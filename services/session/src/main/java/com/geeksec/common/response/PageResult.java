package com.geeksec.common.response;

import java.util.List;

/**
 * 分页响应结果
 */
public class PageResult<T> {
    private List<T> records;
    private long total;
    private int page;
    private int size;
    private int pages;
    
    public PageResult() {}
    
    public PageResult(List<T> records, long total, int page, int size) {
        this.records = records;
        this.total = total;
        this.page = page;
        this.size = size;
        this.pages = (int) Math.ceil((double) total / size);
    }
    
    public static <T> PageResult<T> of(List<T> records, long total, int page, int size) {
        return new PageResult<>(records, total, page, size);
    }
    
    // Getter 和 Setter 方法
    public List<T> getRecords() {
        return records;
    }
    
    public void setRecords(List<T> records) {
        this.records = records;
    }
    
    public long getTotal() {
        return total;
    }
    
    public void setTotal(long total) {
        this.total = total;
        this.pages = (int) Math.ceil((double) total / size);
    }
    
    public int getPage() {
        return page;
    }
    
    public void setPage(int page) {
        this.page = page;
    }
    
    public int getSize() {
        return size;
    }
    
    public void setSize(int size) {
        this.size = size;
        this.pages = (int) Math.ceil((double) total / size);
    }
    
    public int getPages() {
        return pages;
    }
    
    public void setPages(int pages) {
        this.pages = pages;
    }
    
    public boolean hasNext() {
        return page < pages;
    }
    
    public boolean hasPrevious() {
        return page > 1;
    }
    
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }
}
package com.geeksec.session.domain.repositories;

import com.geeksec.session.domain.entities.Session;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 会话仓储接口 - 领域层定义
 * 
 * 定义会话聚合根的持久化操作接口，由基础设施层实现
 * 遵循DDD原则，接口在领域层定义，实现在基础设施层
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface SessionRepository {
    
    // ==================== 基础CRUD操作 ====================
    
    /**
     * 保存会话
     * 
     * @param session 会话聚合根
     * @return 保存后的会话
     */
    Session save(Session session);
    
    /**
     * 根据ID查找会话
     * 
     * @param sessionId 会话ID
     * @return 会话聚合根，如果不存在返回空
     */
    Optional<Session> findById(String sessionId);
    
    /**
     * 批量根据ID查找会话
     * 
     * @param sessionIds 会话ID列表
     * @return 会话列表
     */
    List<Session> findByIds(List<String> sessionIds);
    
    /**
     * 删除会话
     * 
     * @param sessionId 会话ID
     */
    void deleteById(String sessionId);
    
    /**
     * 检查会话是否存在
     * 
     * @param sessionId 会话ID
     * @return 如果存在返回true
     */
    boolean existsById(String sessionId);
    
    // ==================== 查询操作 ====================
    
    /**
     * 分页查询会话列表
     * 
     * @param offset 偏移量
     * @param limit 限制数量
     * @param filters 过滤条件
     * @return 会话列表
     */
    List<Session> findSessions(int offset, int limit, Map<String, Object> filters);
    
    /**
     * 统计会话数量
     * 
     * @param filters 过滤条件
     * @return 会话数量
     */
    long countSessions(Map<String, Object> filters);
    
    /**
     * 根据时间范围查找会话
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 会话列表
     */
    List<Session> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据源IP查找会话
     * 
     * @param sourceIp 源IP地址
     * @return 会话列表
     */
    List<Session> findBySourceIp(String sourceIp);
    
    /**
     * 根据目标IP查找会话
     * 
     * @param destinationIp 目标IP地址
     * @return 会话列表
     */
    List<Session> findByDestinationIp(String destinationIp);
    
    /**
     * 根据应用ID查找会话
     * 
     * @param appId 应用ID
     * @return 会话列表
     */
    List<Session> findByAppId(Integer appId);
    
    /**
     * 根据标签查找会话
     * 
     * @param labelId 标签ID
     * @return 会话列表
     */
    List<Session> findByLabel(Integer labelId);
    
    /**
     * 查找活跃会话
     * 
     * @return 活跃会话列表
     */
    List<Session> findActiveSessions();
    
    /**
     * 查找可疑会话
     * 
     * @return 可疑会话列表
     */
    List<Session> findSuspiciousSessions();
    
    // ==================== 搜索操作 ====================
    
    /**
     * 关键字搜索会话
     * 
     * @param keyword 搜索关键字
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 会话列表
     */
    List<Session> searchByKeyword(String keyword, int offset, int limit);
    
    /**
     * 统计关键字搜索结果数量
     * 
     * @param keyword 搜索关键字
     * @return 搜索结果数量
     */
    long countByKeyword(String keyword);
    
    // ==================== 统计操作 ====================
    
    /**
     * 获取会话统计信息
     * 
     * @param filters 过滤条件
     * @return 统计信息
     */
    Map<String, Object> getSessionStatistics(Map<String, Object> filters);
    
    /**
     * 获取协议分布统计
     * 
     * @param filters 过滤条件
     * @return 协议分布统计
     */
    List<Map<String, Object>> getProtocolDistribution(Map<String, Object> filters);
    
    /**
     * 获取应用分布统计
     * 
     * @param filters 过滤条件
     * @return 应用分布统计
     */
    List<Map<String, Object>> getApplicationDistribution(Map<String, Object> filters);
    
    /**
     * 获取TOP源IP统计
     * 
     * @param limit 限制数量
     * @param filters 过滤条件
     * @return TOP源IP统计
     */
    List<Map<String, Object>> getTopSourceIps(int limit, Map<String, Object> filters);
    
    /**
     * 获取TOP目标IP统计
     * 
     * @param limit 限制数量
     * @param filters 过滤条件
     * @return TOP目标IP统计
     */
    List<Map<String, Object>> getTopDestinationIps(int limit, Map<String, Object> filters);
    
    /**
     * 获取会话趋势统计
     * 
     * @param timeUnit 时间单位（hour, day, week, month）
     * @param filters 过滤条件
     * @return 会话趋势统计
     */
    List<Map<String, Object>> getSessionTrends(String timeUnit, Map<String, Object> filters);
    
    // ==================== 聚合操作 ====================
    
    /**
     * 获取会话聚合数据
     * 
     * @param groupBy 分组字段
     * @param aggregateFields 聚合字段
     * @param filters 过滤条件
     * @return 聚合结果
     */
    List<Map<String, Object>> getSessionAggregations(
        List<String> groupBy, 
        List<String> aggregateFields, 
        Map<String, Object> filters
    );
}
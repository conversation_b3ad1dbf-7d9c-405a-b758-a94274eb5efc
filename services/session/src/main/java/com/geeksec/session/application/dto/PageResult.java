package com.geeksec.session.application.dto;

import java.util.List;

/**
 * 分页响应结果
 * 
 * @param <T> 数据类型
 * <AUTHOR> Team
 * @since 3.0.0
 */
public class PageResult<T> {
    
    private List<T> records;
    private long total;
    private int page;
    private int size;
    private int pages;
    
    // 构造函数
    public PageResult() {}
    
    public PageResult(List<T> records, long total, int page, int size) {
        this.records = records;
        this.total = total;
        this.page = page;
        this.size = size;
        this.pages = (int) Math.ceil((double) total / size);
    }
    
    // 静态工厂方法
    public static <T> PageResult<T> of(List<T> records, long total, int page, int size) {
        return new PageResult<>(records, total, page, size);
    }
    
    public static <T> PageResult<T> empty(int page, int size) {
        return new PageResult<>(List.of(), 0L, page, size);
    }
    
    // Getter 和 Setter 方法
    public List<T> getRecords() {
        return records;
    }
    
    public void setRecords(List<T> records) {
        this.records = records;
    }
    
    public long getTotal() {
        return total;
    }
    
    public void setTotal(long total) {
        this.total = total;
        this.pages = (int) Math.ceil((double) total / size);
    }
    
    public int getPage() {
        return page;
    }
    
    public void setPage(int page) {
        this.page = page;
    }
    
    public int getSize() {
        return size;
    }
    
    public void setSize(int size) {
        this.size = size;
        this.pages = (int) Math.ceil((double) total / size);
    }
    
    public int getPages() {
        return pages;
    }
    
    public void setPages(int pages) {
        this.pages = pages;
    }
    
    // 判断是否有下一页
    public boolean hasNext() {
        return page < pages;
    }
    
    // 判断是否有上一页
    public boolean hasPrevious() {
        return page > 1;
    }
    
    // 判断是否为空
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }
}
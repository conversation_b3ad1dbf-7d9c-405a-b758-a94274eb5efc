package com.geeksec.session.application.queries;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会话查询参数
 * 用于封装会话查询的各种条件
 */
public record SessionQuery(
    Long sessionId,
    String sourceIp,
    String destIp,
    Integer sourcePort,
    Integer destPort,
    String appId,
    String protocol,
    String status,
    LocalDateTime startTimeFrom,
    LocalDateTime startTimeTo,
    LocalDateTime endTimeFrom,
    LocalDateTime endTimeTo,
    List<String> labels,
    List<String> ruleLabels,
    String keyword,
    Integer page,
    Integer size,
    String sortBy,
    String sortDirection
) {
    
    /**
     * 创建默认查询（分页查询）
     */
    public static SessionQuery defaultQuery(int page, int size) {
        return new SessionQuery(
            null, null, null, null, null, null, null, null,
            null, null, null, null, null, null, null,
            page, size, "sessionStartTime", "desc"
        );
    }
    
    /**
     * 创建按ID查询
     */
    public static SessionQuery byId(Long sessionId) {
        return new SessionQuery(
            sessionId, null, null, null, null, null, null, null,
            null, null, null, null, null, null, null,
            null, null, null, null
        );
    }
    
    /**
     * 创建按时间范围查询
     */
    public static SessionQuery byTimeRange(LocalDateTime startTime, LocalDateTime endTime, int page, int size) {
        return new SessionQuery(
            null, null, null, null, null, null, null, null,
            startTime, endTime, null, null, null, null, null,
            page, size, "sessionStartTime", "desc"
        );
    }
    
    /**
     * 创建按IP查询
     */
    public static SessionQuery byIp(String ip, int page, int size) {
        return new SessionQuery(
            null, ip, ip, null, null, null, null, null,
            null, null, null, null, null, null, null,
            page, size, "sessionStartTime", "desc"
        );
    }
    
    /**
     * 创建按应用ID查询
     */
    public static SessionQuery byAppId(String appId, int page, int size) {
        return new SessionQuery(
            null, null, null, null, null, appId, null, null,
            null, null, null, null, null, null, null,
            page, size, "sessionStartTime", "desc"
        );
    }
    
    /**
     * 创建按标签查询
     */
    public static SessionQuery byLabels(List<String> labels, int page, int size) {
        return new SessionQuery(
            null, null, null, null, null, null, null, null,
            null, null, null, null, labels, null, null,
            page, size, "sessionStartTime", "desc"
        );
    }
    
    /**
     * 创建关键字搜索查询
     */
    public static SessionQuery byKeyword(String keyword, int page, int size) {
        return new SessionQuery(
            null, null, null, null, null, null, null, null,
            null, null, null, null, null, null, keyword,
            page, size, "sessionStartTime", "desc"
        );
    }
    
    /**
     * 验证查询参数
     */
    public boolean isValid() {
        // 页码和大小的基本验证
        if (page != null && page < 0) {
            return false;
        }
        if (size != null && (size <= 0 || size > 1000)) {
            return false;
        }
        
        // 时间范围验证
        if (startTimeFrom != null && startTimeTo != null && startTimeFrom.isAfter(startTimeTo)) {
            return false;
        }
        if (endTimeFrom != null && endTimeTo != null && endTimeFrom.isAfter(endTimeTo)) {
            return false;
        }
        
        // 端口范围验证
        if (sourcePort != null && (sourcePort < 0 || sourcePort > 65535)) {
            return false;
        }
        if (destPort != null && (destPort < 0 || destPort > 65535)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查是否有时间范围条件
     */
    public boolean hasTimeRangeCondition() {
        return startTimeFrom != null || startTimeTo != null || endTimeFrom != null || endTimeTo != null;
    }
    
    /**
     * 检查是否有网络条件
     */
    public boolean hasNetworkCondition() {
        return sourceIp != null || destIp != null || sourcePort != null || destPort != null;
    }
    
    /**
     * 检查是否有标签条件
     */
    public boolean hasLabelCondition() {
        return (labels != null && !labels.isEmpty()) || (ruleLabels != null && !ruleLabels.isEmpty());
    }
    
    /**
     * 检查是否需要分页
     */
    public boolean needsPagination() {
        return page != null && size != null;
    }
    
    /**
     * 获取有效的页码（默认为0）
     */
    public int getValidPage() {
        return page != null && page >= 0 ? page : 0;
    }
    
    /**
     * 获取有效的页面大小（默认为20）
     */
    public int getValidSize() {
        return size != null && size > 0 && size <= 1000 ? size : 20;
    }
    
    /**
     * 获取有效的排序字段（默认为sessionStartTime）
     */
    public String getValidSortBy() {
        return sortBy != null && !sortBy.trim().isEmpty() ? sortBy : "sessionStartTime";
    }
    
    /**
     * 获取有效的排序方向（默认为desc）
     */
    public String getValidSortDirection() {
        return "asc".equalsIgnoreCase(sortDirection) ? "asc" : "desc";
    }
}
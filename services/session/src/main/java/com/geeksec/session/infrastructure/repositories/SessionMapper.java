package com.geeksec.session.infrastructure.repositories;

import com.geeksec.session.domain.entities.Session;
import com.geeksec.session.application.dto.SessionQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Session 数据访问层接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SessionMapper {
    
    /**
     * 保存会话
     */
    int save(Session session);
    
    /**
     * 更新会话
     */
    int update(Session session);
    
    /**
     * 根据ID查找会话
     */
    Session findById(@Param("id") String id);
    
    /**
     * 根据ID删除会话
     */
    int deleteById(@Param("id") String id);
    
    /**
     * 根据查询条件查找会话
     */
    List<Session> findByQuery(SessionQuery query);
    
    /**
     * 根据查询条件统计会话数量
     */
    long countByQuery(SessionQuery query);
    
    /**
     * 根据时间范围查找会话
     */
    List<Session> findByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                  @Param("endTime") LocalDateTime endTime,
                                  @Param("offset") int offset,
                                  @Param("limit") int limit);
    
    /**
     * 根据源IP查找会话
     */
    List<Session> findBySourceIp(@Param("sourceIp") String sourceIp,
                                 @Param("offset") int offset,
                                 @Param("limit") int limit);
    
    /**
     * 根据目标IP查找会话
     */
    List<Session> findByDestinationIp(@Param("destinationIp") String destinationIp,
                                      @Param("offset") int offset,
                                      @Param("limit") int limit);
    
    /**
     * 根据应用ID查找会话
     */
    List<Session> findByAppId(@Param("appId") Integer appId,
                             @Param("offset") int offset,
                             @Param("limit") int limit);
    
    /**
     * 根据标签查找会话
     */
    List<Session> findByLabel(@Param("labelId") Integer labelId,
                             @Param("offset") int offset,
                             @Param("limit") int limit);
    
    /**
     * 查找活跃会话
     */
    List<Session> findActiveSessions(@Param("offset") int offset,
                                    @Param("limit") int limit);
    
    /**
     * 查找可疑会话
     */
    List<Session> findSuspiciousSessions(@Param("offset") int offset,
                                        @Param("limit") int limit);
    
    /**
     * 获取协议分布统计
     */
    List<Map<String, Object>> getProtocolDistribution(@Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取应用分布统计
     */
    List<Map<String, Object>> getApplicationDistribution(@Param("startTime") LocalDateTime startTime,
                                                          @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取Top源IP统计
     */
    List<Map<String, Object>> getTopSourceIps(@Param("startTime") LocalDateTime startTime,
                                              @Param("endTime") LocalDateTime endTime,
                                              @Param("limit") int limit);
    
    /**
     * 获取会话趋势统计
     */
    List<Map<String, Object>> getSessionTrends(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime,
                                               @Param("interval") String interval);
    
    /**
     * 批量删除会话
     */
    int batchDelete(@Param("ids") List<String> ids);
}
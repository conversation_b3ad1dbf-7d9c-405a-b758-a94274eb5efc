package com.geeksec.session.application.commands;

import com.geeksec.session.domain.valueobjects.NetworkEndpoint;
import com.geeksec.session.domain.valueobjects.SessionStatistics;
import com.geeksec.session.domain.valueobjects.SessionFingerprint;
import com.geeksec.session.domain.valueobjects.ProtocolMetadata;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 更新会话命令
 * 用于封装更新会话所需的参数
 */
public record UpdateSessionCommand(
    Long sessionId,
    LocalDateTime sessionEndTime,
    SessionStatistics statistics,
    SessionFingerprint fingerprint,
    ProtocolMetadata protocolMetadata,
    List<String> labels,
    List<String> ruleLabels,
    String status
) {
    
    /**
     * 验证命令参数
     */
    public boolean isValid() {
        return sessionId != null && sessionId > 0;
    }
    
    /**
     * 检查是否有统计信息更新
     */
    public boolean hasStatisticsUpdate() {
        return statistics != null;
    }
    
    /**
     * 检查是否有指纹信息更新
     */
    public boolean hasFingerprintUpdate() {
        return fingerprint != null;
    }
    
    /**
     * 检查是否有协议元数据更新
     */
    public boolean hasProtocolMetadataUpdate() {
        return protocolMetadata != null;
    }
    
    /**
     * 检查是否有标签更新
     */
    public boolean hasLabelsUpdate() {
        return labels != null && !labels.isEmpty();
    }
    
    /**
     * 检查是否有规则标签更新
     */
    public boolean hasRuleLabelsUpdate() {
        return ruleLabels != null && !ruleLabels.isEmpty();
    }
    
    /**
     * 检查是否有状态更新
     */
    public boolean hasStatusUpdate() {
        return status != null && !status.trim().isEmpty();
    }
    
    /**
     * 检查是否有结束时间更新
     */
    public boolean hasEndTimeUpdate() {
        return sessionEndTime != null;
    }
}
package com.geeksec.session.infrastructure.repositories;

import com.geeksec.session.domain.entities.Session;
import com.geeksec.session.domain.repositories.SessionRepository;
import com.geeksec.session.application.queries.SessionQuery;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 会话仓储实现类
 * 
 * 基于 MyBatis-Flex 实现的会话数据访问层
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Repository
public class SessionRepositoryImpl implements SessionRepository {

    private final BaseMapper<Session> sessionMapper;

    public SessionRepositoryImpl(BaseMapper<Session> sessionMapper) {
        this.sessionMapper = sessionMapper;
    }

    // ==================== 基础 CRUD 操作 ====================

    @Override
    public Session save(Session session) {
        if (session.getSessionId() == null) {
            // 新增
            session.setCreateTime(LocalDateTime.now());
            session.setUpdateTime(LocalDateTime.now());
            sessionMapper.insert(session);
        } else {
            // 更新
            session.setUpdateTime(LocalDateTime.now());
            sessionMapper.update(session);
        }
        return session;
    }

    @Override
    public Optional<Session> findById(String sessionId) {
        Session session = sessionMapper.selectOneById(sessionId);
        return Optional.ofNullable(session);
    }

    @Override
    public List<Session> findAll() {
        return sessionMapper.selectAll();
    }

    @Override
    public void deleteById(String sessionId) {
        sessionMapper.deleteById(sessionId);
    }

    @Override
    public boolean existsById(String sessionId) {
        return sessionMapper.selectCountByQuery(
            QueryWrapper.create().eq("session_id", sessionId)
        ) > 0;
    }

    // ==================== 查询操作 ====================

    @Override
    public List<Session> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .ge("session_start_time", startTime)
            .le("session_start_time", endTime);
        return sessionMapper.selectListByQuery(queryWrapper);
    }

    @Override
    public List<Session> findBySourceIp(String sourceIp) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .eq("source_ip", sourceIp);
        return sessionMapper.selectListByQuery(queryWrapper);
    }

    @Override
    public List<Session> findByDestinationIp(String destinationIp) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .eq("destination_ip", destinationIp);
        return sessionMapper.selectListByQuery(queryWrapper);
    }

    @Override
    public List<Session> findByAppId(Integer appId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .eq("app_id", appId);
        return sessionMapper.selectListByQuery(queryWrapper);
    }

    @Override
    public List<Session> findByLabel(Integer labelId) {
        // 注意：这里需要根据实际的数据库设计来实现标签查询
        // 假设标签存储在 JSON 字段中或者有关联表
        QueryWrapper queryWrapper = QueryWrapper.create()
            .like("labels", "%" + labelId + "%");
        return sessionMapper.selectListByQuery(queryWrapper);
    }

    @Override
    public Page<Session> searchSessions(SessionQuery query) {
        QueryWrapper queryWrapper = buildQueryWrapper(query);
        
        // 构建分页参数
        Page<Session> page = new Page<>(
            query.getPageNumber() != null ? query.getPageNumber() : 1,
            query.getPageSize() != null ? query.getPageSize() : 20
        );
        
        return sessionMapper.paginate(page, queryWrapper);
    }

    @Override
    public List<Session> findActiveSessions() {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .isNull("session_end_time")
            .or()
            .gt("session_end_time", LocalDateTime.now());
        return sessionMapper.selectListByQuery(queryWrapper);
    }

    @Override
    public long countByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .ge("session_start_time", startTime)
            .le("session_start_time", endTime);
        return sessionMapper.selectCountByQuery(queryWrapper);
    }

    // ==================== 统计操作 ====================

    @Override
    public Map<String, Long> getProtocolDistribution(LocalDateTime startTime, LocalDateTime endTime) {
        // 这里需要使用原生 SQL 或者 MyBatis-Flex 的聚合查询
        // 暂时返回空 Map，实际实现需要根据具体需求编写
        return Map.of();
    }

    @Override
    public Map<String, Long> getApplicationDistribution(LocalDateTime startTime, LocalDateTime endTime) {
        // 这里需要使用原生 SQL 或者 MyBatis-Flex 的聚合查询
        // 暂时返回空 Map，实际实现需要根据具体需求编写
        return Map.of();
    }

    @Override
    public List<Map<String, Object>> getTopSourceIps(LocalDateTime startTime, LocalDateTime endTime, int limit) {
        // 这里需要使用原生 SQL 或者 MyBatis-Flex 的聚合查询
        // 暂时返回空 List，实际实现需要根据具体需求编写
        return List.of();
    }

    @Override
    public List<Map<String, Object>> getTopDestinationIps(LocalDateTime startTime, LocalDateTime endTime, int limit) {
        // 这里需要使用原生 SQL 或者 MyBatis-Flex 的聚合查询
        // 暂时返回空 List，实际实现需要根据具体需求编写
        return List.of();
    }

    @Override
    public List<Map<String, Object>> getSessionTrends(LocalDateTime startTime, LocalDateTime endTime, String interval) {
        // 这里需要使用原生 SQL 或者 MyBatis-Flex 的聚合查询
        // 暂时返回空 List，实际实现需要根据具体需求编写
        return List.of();
    }

    // ==================== 聚合操作 ====================

    @Override
    public Map<String, Object> aggregateSessionData(LocalDateTime startTime, LocalDateTime endTime, List<String> groupByFields) {
        // 这里需要使用原生 SQL 或者 MyBatis-Flex 的聚合查询
        // 暂时返回空 Map，实际实现需要根据具体需求编写
        return Map.of();
    }

    @Override
    public List<Session> findSuspiciousSessions(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .ge("session_start_time", startTime)
            .le("session_start_time", endTime)
            .gt("rule_level", 3); // 假设规则级别大于3为可疑
        return sessionMapper.selectListByQuery(queryWrapper);
    }

    @Override
    public List<Session> findSessionsByIpPattern(String ipPattern, LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .like("source_ip", ipPattern)
            .or()
            .like("destination_ip", ipPattern)
            .ge("session_start_time", startTime)
            .le("session_start_time", endTime);
        return sessionMapper.selectListByQuery(queryWrapper);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 根据查询对象构建 QueryWrapper
     */
    private QueryWrapper buildQueryWrapper(SessionQuery query) {
        QueryWrapper queryWrapper = QueryWrapper.create();

        // 会话ID
        if (query.getSessionId() != null) {
            queryWrapper.eq("session_id", query.getSessionId());
        }

        // 源IP
        if (query.getSourceIp() != null) {
            queryWrapper.eq("source_ip", query.getSourceIp());
        }

        // 目标IP
        if (query.getDestinationIp() != null) {
            queryWrapper.eq("destination_ip", query.getDestinationIp());
        }

        // 源端口
        if (query.getSourcePort() != null) {
            queryWrapper.eq("source_port", query.getSourcePort());
        }

        // 目标端口
        if (query.getDestinationPort() != null) {
            queryWrapper.eq("destination_port", query.getDestinationPort());
        }

        // 应用ID
        if (query.getAppId() != null) {
            queryWrapper.eq("app_id", query.getAppId());
        }

        // 协议
        if (query.getProtocol() != null) {
            queryWrapper.eq("protocol", query.getProtocol());
        }

        // 时间范围
        if (query.getStartTime() != null) {
            queryWrapper.ge("session_start_time", query.getStartTime());
        }
        if (query.getEndTime() != null) {
            queryWrapper.le("session_start_time", query.getEndTime());
        }

        // 关键字搜索（可以搜索多个字段）
        if (query.getKeyword() != null && !query.getKeyword().trim().isEmpty()) {
            String keyword = "%" + query.getKeyword().trim() + "%";
            queryWrapper.and(wrapper -> wrapper
                .like("source_ip", keyword)
                .or()
                .like("destination_ip", keyword)
                .or()
                .like("app_name", keyword)
            );
        }

        // 排序
        if (query.getSortBy() != null && !query.getSortBy().trim().isEmpty()) {
            if ("desc".equalsIgnoreCase(query.getSortOrder())) {
                queryWrapper.orderBy(query.getSortBy(), false);
            } else {
                queryWrapper.orderBy(query.getSortBy(), true);
            }
        } else {
            // 默认按创建时间倒序
            queryWrapper.orderBy("create_time", false);
        }

        return queryWrapper;
    }
}
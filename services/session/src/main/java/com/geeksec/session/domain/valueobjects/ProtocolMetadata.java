package com.geeksec.session.domain.valueobjects;

import lombok.Value;
import java.util.List;
import java.util.Map;

/**
 * 协议元数据值对象
 * 
 * 包含各种网络协议的元数据信息，如HTTP、SSL/TLS、DNS、SSH等
 * 作为值对象，具有不可变性和值相等性
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Value
public class ProtocolMetadata {
    
    // ==================== HTTP协议元数据 ====================
    
    /**
     * HTTP元数据数组
     */
    List<Map<String, Object>> httpMetadata;
    
    /**
     * HTTP请求URL
     */
    String httpUrl;
    
    /**
     * HTTP状态码
     */
    Integer httpStatusCode;
    
    /**
     * HTTP内容类型
     */
    String httpContentType;
    
    /**
     * HTTP内容长度
     */
    Long httpContentLength;
    
    // ==================== SSL/TLS协议元数据 ====================
    
    /**
     * SSL/TLS元数据数组
     */
    List<Map<String, Object>> sslMetadata;
    
    /**
     * SSL证书信息
     */
    Map<String, Object> sslCertificate;
    
    /**
     * SSL服务器名称
     */
    String sslServerName;
    
    /**
     * SSL协商的密码套件
     */
    String negotiatedCipherSuite;
    
    // ==================== DNS协议元数据 ====================
    
    /**
     * DNS元数据数组
     */
    List<Map<String, Object>> dnsMetadata;
    
    /**
     * DNS查询域名
     */
    String dnsQueryName;
    
    /**
     * DNS查询类型
     */
    String dnsQueryType;
    
    /**
     * DNS响应代码
     */
    Integer dnsResponseCode;
    
    // ==================== SSH协议元数据 ====================
    
    /**
     * SSH元数据数组
     */
    List<Map<String, Object>> sshMetadata;
    
    /**
     * SSH版本
     */
    String sshVersion;
    
    /**
     * SSH客户端版本
     */
    String sshClientVersion;
    
    /**
     * SSH服务器版本
     */
    String sshServerVersion;
    
    // ==================== 其他协议元数据 ====================
    
    /**
     * VNC元数据数组
     */
    List<Map<String, Object>> vncMetadata;
    
    /**
     * TELNET元数据数组
     */
    List<Map<String, Object>> telnetMetadata;
    
    /**
     * RLOGIN元数据数组
     */
    List<Map<String, Object>> rloginMetadata;
    
    /**
     * RDP元数据数组
     */
    List<Map<String, Object>> rdpMetadata;
    
    /**
     * ICMP元数据数组
     */
    List<Map<String, Object>> icmpMetadata;
    
    /**
     * NTP元数据数组
     */
    List<Map<String, Object>> ntpMetadata;
    
    /**
     * XDMCP元数据数组
     */
    List<Map<String, Object>> xdmcpMetadata;
    
    // ==================== 工控协议元数据 ====================
    
    /**
     * S7元数据数组
     */
    List<Map<String, Object>> s7Metadata;
    
    /**
     * Modbus元数据数组
     */
    List<Map<String, Object>> modbusMetadata;
    
    /**
     * IEC104元数据数组
     */
    List<Map<String, Object>> iec104Metadata;
    
    /**
     * EIP元数据数组
     */
    List<Map<String, Object>> eipMetadata;
    
    /**
     * OPC元数据数组
     */
    List<Map<String, Object>> opcMetadata;
    
    // ==================== 网络协议元数据 ====================
    
    /**
     * ESP元数据数组
     */
    List<Map<String, Object>> espMetadata;
    
    /**
     * L2TP元数据数组
     */
    List<Map<String, Object>> l2tpMetadata;
    
    // ==================== 业务方法 ====================
    
    /**
     * 判断是否包含HTTP协议
     * 
     * @return 如果包含HTTP协议返回true
     */
    public boolean hasHttpProtocol() {
        return httpMetadata != null && !httpMetadata.isEmpty();
    }
    
    /**
     * 判断是否包含SSL/TLS协议
     * 
     * @return 如果包含SSL/TLS协议返回true
     */
    public boolean hasSslProtocol() {
        return sslMetadata != null && !sslMetadata.isEmpty();
    }
    
    /**
     * 判断是否包含DNS协议
     * 
     * @return 如果包含DNS协议返回true
     */
    public boolean hasDnsProtocol() {
        return dnsMetadata != null && !dnsMetadata.isEmpty();
    }
    
    /**
     * 判断是否包含SSH协议
     * 
     * @return 如果包含SSH协议返回true
     */
    public boolean hasSshProtocol() {
        return sshMetadata != null && !sshMetadata.isEmpty();
    }
    
    /**
     * 判断是否包含工控协议
     * 
     * @return 如果包含工控协议返回true
     */
    public boolean hasIndustrialProtocol() {
        return (s7Metadata != null && !s7Metadata.isEmpty()) ||
               (modbusMetadata != null && !modbusMetadata.isEmpty()) ||
               (iec104Metadata != null && !iec104Metadata.isEmpty()) ||
               (eipMetadata != null && !eipMetadata.isEmpty()) ||
               (opcMetadata != null && !opcMetadata.isEmpty());
    }
    
    /**
     * 获取主要协议类型
     * 
     * @return 主要协议类型
     */
    public String getPrimaryProtocolType() {
        if (hasHttpProtocol()) {
            return "HTTP";
        } else if (hasSslProtocol()) {
            return "SSL/TLS";
        } else if (hasSshProtocol()) {
            return "SSH";
        } else if (hasDnsProtocol()) {
            return "DNS";
        } else if (hasIndustrialProtocol()) {
            return "Industrial";
        } else {
            return "Unknown";
        }
    }
    
    /**
     * 获取协议复杂度评分
     * 
     * @return 协议复杂度评分（0-100）
     */
    public int getProtocolComplexity() {
        int complexity = 0;
        
        // 基础协议
        if (hasHttpProtocol()) complexity += 20;
        if (hasSslProtocol()) complexity += 25;
        if (hasDnsProtocol()) complexity += 15;
        if (hasSshProtocol()) complexity += 20;
        
        // 工控协议增加复杂度
        if (hasIndustrialProtocol()) complexity += 30;
        
        // 远程访问协议
        if (vncMetadata != null && !vncMetadata.isEmpty()) complexity += 15;
        if (rdpMetadata != null && !rdpMetadata.isEmpty()) complexity += 15;
        if (telnetMetadata != null && !telnetMetadata.isEmpty()) complexity += 10;
        
        return Math.min(complexity, 100);
    }
    
    /**
     * 判断是否为加密协议
     * 
     * @return 如果是加密协议返回true
     */
    public boolean isEncryptedProtocol() {
        return hasSslProtocol() || hasSshProtocol() ||
               (espMetadata != null && !espMetadata.isEmpty());
    }
    
    /**
     * 判断是否为可疑协议组合
     * 
     * @return 如果是可疑协议组合返回true
     */
    public boolean hasSuspiciousProtocolCombination() {
        // 同时存在多种远程访问协议可能是可疑的
        int remoteAccessCount = 0;
        if (hasSshProtocol()) remoteAccessCount++;
        if (vncMetadata != null && !vncMetadata.isEmpty()) remoteAccessCount++;
        if (rdpMetadata != null && !rdpMetadata.isEmpty()) remoteAccessCount++;
        if (telnetMetadata != null && !telnetMetadata.isEmpty()) remoteAccessCount++;
        
        return remoteAccessCount > 2;
    }
}
package com.geeksec.session.model.dto.session;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会话详情响应DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "会话详情响应")
public class SessionDetailResponse {
    
    // ==================== 基础会话信息 ====================
    
    @Schema(description = "会话ID", example = "session_1234567890")
    private String sessionId;
    
    @Schema(description = "源IP地址", example = "*************")
    private String srcIp;
    
    @Schema(description = "目标IP地址", example = "********")
    private String dstIp;
    
    @Schema(description = "源端口", example = "8080")
    private Integer srcPort;
    
    @Schema(description = "目标端口", example = "443")
    private Integer dstPort;
    
    @Schema(description = "IP协议号", example = "6")
    private Integer protocol;
    
    @Schema(description = "协议名称", example = "TCP")
    private String protocolName;
    
    @Schema(description = "应用名称", example = "HTTPS")
    private String appName;
    
    @Schema(description = "应用ID", example = "1001")
    private Integer appId;
    
    @Schema(description = "会话开始时间", example = "2024-01-01T12:00:00")
    private LocalDateTime sessionStartTime;
    
    @Schema(description = "会话结束时间", example = "2024-01-01T12:05:30")
    private LocalDateTime sessionEndTime;
    
    @Schema(description = "会话持续时间(秒)", example = "330")
    private Integer sessionDuration;
    
    @Schema(description = "服务器IP", example = "********")
    private String serverIp;
    
    @Schema(description = "首包发送方IP", example = "*************")
    private String firstPacketSender;
    
    // ==================== 流量统计信息 ====================
    
    @Schema(description = "源总字节数", example = "1024000")
    private Long srcTotalBytes;
    
    @Schema(description = "目标总字节数", example = "2048000")
    private Long dstTotalBytes;
    
    @Schema(description = "源负载字节数", example = "512000")
    private Long srcPayloadBytes;
    
    @Schema(description = "目标负载字节数", example = "1024000")
    private Long dstPayloadBytes;
    
    @Schema(description = "源包数", example = "1500")
    private Integer srcPacketCount;
    
    @Schema(description = "目标包数", example = "1200")
    private Integer dstPacketCount;
    
    @Schema(description = "源有负载的包数", example = "800")
    private Integer srcPayloadPacketCount;
    
    @Schema(description = "目标有负载的包数", example = "600")
    private Integer dstPayloadPacketCount;
    
    // ==================== 网络特征信息 ====================
    
    @Schema(description = "源MAC地址", example = "00:11:22:33:44:55")
    private String srcMac;
    
    @Schema(description = "目标MAC地址", example = "66:77:88:99:AA:BB")
    private String dstMac;
    
    @Schema(description = "源IP是否为内部IP", example = "true")
    private Boolean srcIsInternal;
    
    @Schema(description = "目标IP是否为内部IP", example = "false")
    private Boolean dstIsInternal;
    
    @Schema(description = "源MSS", example = "1460")
    private Integer srcMss;
    
    @Schema(description = "目标MSS", example = "1460")
    private Integer dstMss;
    
    @Schema(description = "源窗口扩展因子", example = "7")
    private Integer srcWindowScale;
    
    @Schema(description = "目标窗口扩展因子", example = "7")
    private Integer dstWindowScale;
    
    @Schema(description = "源TTL最大值", example = "64")
    private Integer srcTtlMax;
    
    @Schema(description = "目标TTL最大值", example = "64")
    private Integer dstTtlMax;
    
    // ==================== 协议指纹信息 ====================
    
    @Schema(description = "TCP客户端指纹", example = "12345678901234567890")
    private Long tcpClientFingerprint;
    
    @Schema(description = "TCP服务端指纹", example = "09876543210987654321")
    private Long tcpServerFingerprint;
    
    @Schema(description = "HTTP请求指纹", example = "11111111111111111111")
    private Long httpClientFingerprint;
    
    @Schema(description = "HTTP应答指纹", example = "22222222222222222222")
    private Long httpServerFingerprint;
    
    @Schema(description = "SSL请求指纹", example = "33333333333333333333")
    private Long sslClientFingerprint;
    
    @Schema(description = "SSL应答指纹", example = "44444444444444444444")
    private Long sslServerFingerprint;
    
    // ==================== 标签和规则信息 ====================
    
    @Schema(description = "会话标签", example = "[\"恶意软件\", \"可疑流量\"]")
    private List<String> labelNames;
    
    @Schema(description = "标签ID列表", example = "[10, 20]")
    private List<Integer> labelIds;
    
    @Schema(description = "规则级别", example = "3")
    private Integer ruleLevel;
    
    @Schema(description = "规则数量", example = "2")
    private Integer ruleCount;
    
    @Schema(description = "规则消息")
    private Map<String, Object> ruleMessages;
    
    // ==================== 任务和设备信息 ====================
    
    @Schema(description = "任务ID", example = "1")
    private Integer taskId;
    
    @Schema(description = "任务名称", example = "网络流量分析任务")
    private String taskName;
    
    @Schema(description = "设备ID", example = "100")
    private Integer deviceId;
    
    @Schema(description = "设备名称", example = "网关设备")
    private String deviceName;
    
    @Schema(description = "线程ID", example = "1")
    private Integer threadId;
    
    @Schema(description = "批次ID", example = "1001")
    private Integer batchId;
    
    // ==================== 协议栈和扩展信息 ====================
    
    @Schema(description = "协议栈信息")
    private Map<String, Object> protocolStack;
    
    @Schema(description = "协议栈数量", example = "3")
    private Integer protocolStackCount;
    
    @Schema(description = "扩展数据")
    private Map<String, Object> extensionData;
    
    // ==================== 协议元数据 ====================
    
    @Schema(description = "HTTP协议元数据")
    private List<Map<String, Object>> httpProtocols;
    
    @Schema(description = "SSL/TLS协议元数据")
    private List<Map<String, Object>> sslProtocols;
    
    @Schema(description = "DNS协议元数据")
    private List<Map<String, Object>> dnsProtocols;
    
    @Schema(description = "SSH协议元数据")
    private List<Map<String, Object>> sshProtocols;
    
    // ==================== 系统信息 ====================
    
    @Schema(description = "创建时间", example = "2024-01-01T12:00:00")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间", example = "2024-01-01T12:05:30")
    private LocalDateTime updateTime;
    
    @Schema(description = "处理开始时间", example = "2024-01-01T12:00:01")
    private LocalDateTime processingStartTime;
    
    @Schema(description = "处理结束时间", example = "2024-01-01T12:00:05")
    private LocalDateTime processingEndTime;
}

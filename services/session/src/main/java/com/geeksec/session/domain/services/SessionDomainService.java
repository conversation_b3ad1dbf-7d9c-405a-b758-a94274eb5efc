package com.geeksec.session.domain.services;

import com.geeksec.session.domain.entities.Session;
import com.geeksec.session.domain.repositories.SessionRepository;
import com.geeksec.session.domain.valueobjects.NetworkEndpoint;
import com.geeksec.session.domain.valueobjects.SessionStatistics;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会话领域服务
 * 
 * 封装复杂的业务逻辑和跨聚合根的操作
 * 领域服务是无状态的，专注于业务规则的实现
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Service
public class SessionDomainService {
    
    private final SessionRepository sessionRepository;
    
    public SessionDomainService(SessionRepository sessionRepository) {
        this.sessionRepository = sessionRepository;
    }
    
    // ==================== 会话分析服务 ====================
    
    /**
     * 分析会话的安全风险等级
     * 
     * @param session 会话聚合根
     * @return 风险等级（1-5，5为最高风险）
     */
    public int analyzeSecurityRiskLevel(Session session) {
        int riskScore = 0;
        
        // 基于统计信息评估风险
        SessionStatistics stats = session.getStatistics();
        if (stats != null) {
            // 规则级别风险
            if (stats.getRuleLevel() != null && stats.getRuleLevel() > 3) {
                riskScore += 2;
            }
            
            // 异常流量风险
            if (stats.hasAbnormalTraffic()) {
                riskScore += 1;
            }
            
            // 数据包丢失率风险
            if (stats.getPacketLossRate() > 10.0) {
                riskScore += 1;
            }
        }
        
        // 基于指纹信息评估风险
        if (session.getFingerprint() != null && session.getFingerprint().isSuspiciousFingerprint()) {
            riskScore += 2;
        }
        
        // 基于协议元数据评估风险
        if (session.getProtocolMetadata() != null && 
            session.getProtocolMetadata().hasSuspiciousProtocolCombination()) {
            riskScore += 1;
        }
        
        // 基于网络方向评估风险
        String direction = session.getNetworkDirection();
        if ("外网到内网".equals(direction)) {
            riskScore += 1;
        }
        
        // 转换为1-5的风险等级
        return Math.min(Math.max(riskScore, 1), 5);
    }
    
    /**
     * 检测会话异常行为
     * 
     * @param session 会话聚合根
     * @return 异常行为列表
     */
    public List<String> detectAnomalousActivities(Session session) {
        List<String> anomalies = new java.util.ArrayList<>();
        
        // 检测长时间会话
        if (session.getDurationInSeconds() > 3600) { // 超过1小时
            anomalies.add("长时间会话连接");
        }
        
        // 检测大流量传输
        SessionStatistics stats = session.getStatistics();
        if (stats != null && stats.getTotalByteCount() > 100 * 1024 * 1024) { // 超过100MB
            anomalies.add("大流量数据传输");
        }
        
        // 检测异常端口
        NetworkEndpoint srcEndpoint = session.getSourceEndpoint();
        NetworkEndpoint dstEndpoint = session.getDestinationEndpoint();
        
        if (srcEndpoint != null && srcEndpoint.getPort() != null && 
            srcEndpoint.getPort() > 49152) { // 动态端口范围
            anomalies.add("使用异常源端口");
        }
        
        if (dstEndpoint != null && dstEndpoint.getPort() != null && 
            !isCommonPort(dstEndpoint.getPort())) {
            anomalies.add("连接非常见目标端口");
        }
        
        // 检测可疑指纹
        if (session.getFingerprint() != null && session.getFingerprint().isSuspiciousFingerprint()) {
            anomalies.add("检测到可疑协议指纹");
        }
        
        return anomalies;
    }
    
    /**
     * 计算会话相似度
     * 
     * @param session1 会话1
     * @param session2 会话2
     * @return 相似度分数（0-100）
     */
    public double calculateSessionSimilarity(Session session1, Session session2) {
        double similarity = 0.0;
        int factors = 0;
        
        // 比较协议
        if (session1.getProtocol() != null && session2.getProtocol() != null) {
            if (session1.getProtocol().equals(session2.getProtocol())) {
                similarity += 20;
            }
            factors++;
        }
        
        // 比较应用
        if (session1.getAppId() != null && session2.getAppId() != null) {
            if (session1.getAppId().equals(session2.getAppId())) {
                similarity += 25;
            }
            factors++;
        }
        
        // 比较网络方向
        String direction1 = session1.getNetworkDirection();
        String direction2 = session2.getNetworkDirection();
        if (direction1.equals(direction2)) {
            similarity += 15;
        }
        factors++;
        
        // 比较指纹类型
        if (session1.getFingerprint() != null && session2.getFingerprint() != null) {
            String type1 = session1.getFingerprint().getPrimaryFingerprintType();
            String type2 = session2.getFingerprint().getPrimaryFingerprintType();
            if (type1.equals(type2)) {
                similarity += 20;
            }
            factors++;
        }
        
        // 比较协议复杂度
        if (session1.getProtocolMetadata() != null && session2.getProtocolMetadata() != null) {
            int complexity1 = session1.getProtocolMetadata().getProtocolComplexity();
            int complexity2 = session2.getProtocolMetadata().getProtocolComplexity();
            double complexityDiff = Math.abs(complexity1 - complexity2);
            similarity += Math.max(0, 20 - complexityDiff / 5);
            factors++;
        }
        
        return factors > 0 ? similarity / factors * factors : 0.0;
    }
    
    // ==================== 会话聚合分析服务 ====================
    
    /**
     * 分析IP地址的行为模式
     * 
     * @param ipAddress IP地址
     * @param timeRange 时间范围（小时）
     * @return 行为模式分析结果
     */
    public Map<String, Object> analyzeIpBehaviorPattern(String ipAddress, int timeRange) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusHours(timeRange);
        
        // 获取该IP相关的所有会话
        List<Session> sourceSessions = sessionRepository.findBySourceIp(ipAddress);
        List<Session> destSessions = sessionRepository.findByDestinationIp(ipAddress);
        
        // 过滤时间范围
        sourceSessions = sourceSessions.stream()
            .filter(s -> s.getSessionStartTime().isAfter(startTime))
            .collect(Collectors.toList());
        
        destSessions = destSessions.stream()
            .filter(s -> s.getSessionStartTime().isAfter(startTime))
            .collect(Collectors.toList());
        
        Map<String, Object> pattern = new java.util.HashMap<>();
        pattern.put("sourceSessionCount", sourceSessions.size());
        pattern.put("destinationSessionCount", destSessions.size());
        pattern.put("totalSessionCount", sourceSessions.size() + destSessions.size());
        
        // 分析协议分布
        Map<String, Long> protocolDistribution = sourceSessions.stream()
            .collect(Collectors.groupingBy(
                Session::getPrimaryProtocol,
                Collectors.counting()
            ));
        pattern.put("protocolDistribution", protocolDistribution);
        
        // 分析风险等级分布
        Map<Integer, Long> riskDistribution = sourceSessions.stream()
            .collect(Collectors.groupingBy(
                this::analyzeSecurityRiskLevel,
                Collectors.counting()
            ));
        pattern.put("riskDistribution", riskDistribution);
        
        // 计算平均会话持续时间
        double avgDuration = sourceSessions.stream()
            .mapToLong(Session::getDurationInSeconds)
            .average()
            .orElse(0.0);
        pattern.put("averageSessionDuration", avgDuration);
        
        return pattern;
    }
    
    /**
     * 检测批量可疑会话
     * 
     * @param timeRange 时间范围（小时）
     * @return 可疑会话列表
     */
    public List<Session> detectBatchSuspiciousSessions(int timeRange) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusHours(timeRange);
        
        List<Session> recentSessions = sessionRepository.findByTimeRange(startTime, endTime);
        
        return recentSessions.stream()
            .filter(session -> {
                int riskLevel = analyzeSecurityRiskLevel(session);
                List<String> anomalies = detectAnomalousActivities(session);
                return riskLevel >= 4 || anomalies.size() >= 2;
            })
            .collect(Collectors.toList());
    }
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 判断是否为常见端口
     * 
     * @param port 端口号
     * @return 如果是常见端口返回true
     */
    private boolean isCommonPort(Integer port) {
        if (port == null) return false;
        
        // 常见端口列表
        int[] commonPorts = {21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 3389, 5432, 3306};
        
        for (int commonPort : commonPorts) {
            if (port == commonPort) {
                return true;
            }
        }
        
        return false;
    }
}
package com.geeksec.session.domain.valueobjects;

import lombok.Value;

/**
 * 会话统计信息值对象
 * 
 * 包含会话的各种统计数据，如数据包数量、字节数、协议统计等
 * 作为值对象，具有不可变性和值相等性
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Value
public class SessionStatistics {
    
    // ==================== 基础统计 ====================
    
    /**
     * 源到目标数据包数量
     */
    Long srcToDstPackets;
    
    /**
     * 目标到源数据包数量
     */
    Long dstToSrcPackets;
    
    /**
     * 源到目标字节数
     */
    Long srcToDstBytes;
    
    /**
     * 目标到源字节数
     */
    Long dstToSrcBytes;
    
    /**
     * 总数据包数量
     */
    Long totalPackets;
    
    /**
     * 总字节数
     */
    Long totalBytes;
    
    // ==================== TCP统计 ====================
    
    /**
     * 源FIN包数量
     */
    Integer srcFinPackets;
    
    /**
     * 目标FIN包数量
     */
    Integer dstFinPackets;
    
    /**
     * 源RST包数量
     */
    Integer srcRstPackets;
    
    /**
     * 目标RST包数量
     */
    Integer dstRstPackets;
    
    /**
     * 源SYN包数量
     */
    Integer srcSynPackets;
    
    /**
     * 目标SYN包数量
     */
    Integer dstSynPackets;
    
    /**
     * 源PSH包数量
     */
    Integer srcPshPackets;
    
    /**
     * 目标PSH包数量
     */
    Integer dstPshPackets;
    
    // ==================== 质量统计 ====================
    
    /**
     * 源乱序包数量
     */
    Integer srcOutOfOrderPackets;
    
    /**
     * 目标乱序包数量
     */
    Integer dstOutOfOrderPackets;
    
    /**
     * 源重传包数量
     */
    Integer srcRetransmittedPackets;
    
    /**
     * 目标重传包数量
     */
    Integer dstRetransmittedPackets;
    
    /**
     * 源丢失包长度
     */
    Long srcLostPacketLength;
    
    /**
     * 目标丢失包长度
     */
    Long dstLostPacketLength;
    
    // ==================== 业务统计 ====================
    
    /**
     * 规则级别
     */
    Integer ruleLevel;
    
    /**
     * 协议包数量
     */
    Integer protocolPackets;
    
    /**
     * 载荷长度
     */
    Long payloadLength;
    
    /**
     * 源载荷长度
     */
    Long srcPayloadLength;
    
    /**
     * 目标载荷长度
     */
    Long dstPayloadLength;
    
    // ==================== 业务方法 ====================
    
    /**
     * 获取总数据包数量
     * 
     * @return 总数据包数量
     */
    public long getTotalPacketCount() {
        long src = srcToDstPackets != null ? srcToDstPackets : 0L;
        long dst = dstToSrcPackets != null ? dstToSrcPackets : 0L;
        return src + dst;
    }
    
    /**
     * 获取总字节数
     * 
     * @return 总字节数
     */
    public long getTotalByteCount() {
        long src = srcToDstBytes != null ? srcToDstBytes : 0L;
        long dst = dstToSrcBytes != null ? dstToSrcBytes : 0L;
        return src + dst;
    }
    
    /**
     * 计算数据包丢失率
     * 
     * @return 丢失率百分比
     */
    public double getPacketLossRate() {
        long totalPackets = getTotalPacketCount();
        if (totalPackets == 0) {
            return 0.0;
        }
        
        int totalLost = (srcRetransmittedPackets != null ? srcRetransmittedPackets : 0) +
                       (dstRetransmittedPackets != null ? dstRetransmittedPackets : 0);
        
        return (double) totalLost / totalPackets * 100.0;
    }
    
    /**
     * 判断是否有异常流量
     * 
     * @return 如果有异常流量返回true
     */
    public boolean hasAbnormalTraffic() {
        // 基于重传包和乱序包判断异常流量
        int totalRetrans = (srcRetransmittedPackets != null ? srcRetransmittedPackets : 0) +
                          (dstRetransmittedPackets != null ? dstRetransmittedPackets : 0);
        
        int totalOutOfOrder = (srcOutOfOrderPackets != null ? srcOutOfOrderPackets : 0) +
                             (dstOutOfOrderPackets != null ? dstOutOfOrderPackets : 0);
        
        return totalRetrans > 10 || totalOutOfOrder > 5;
    }
    
    /**
     * 获取平均包大小
     * 
     * @return 平均包大小（字节）
     */
    public double getAveragePacketSize() {
        long totalPackets = getTotalPacketCount();
        if (totalPackets == 0) {
            return 0.0;
        }
        return (double) getTotalByteCount() / totalPackets;
    }
}
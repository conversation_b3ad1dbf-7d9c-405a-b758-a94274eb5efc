package com.geeksec.session.infrastructure.client;

import com.geeksec.common.dto.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.Map;

/**
 * 任务查询服务Feign客户端
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@FeignClient(name = "task-service")
public interface TaskQueryClient {
    
    /**
     * 根据任务ID获取任务信息
     * 
     * @param taskId 任务ID
     * @return 任务信息
     */
    @GetMapping("/task/api/tasks/{taskId}")
    ApiResponse<Map<String, Object>> getTaskById(@PathVariable("taskId") Integer taskId);
    
    /**
     * 获取活跃任务列表
     * 
     * @return 活跃任务列表
     */
    @GetMapping("/task/api/tasks/active")
    ApiResponse<Map<String, Object>> getActiveTasks();
}

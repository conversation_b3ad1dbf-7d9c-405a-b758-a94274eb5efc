package com.geeksec.session.domain.valueobjects;

import lombok.Value;

/**
 * 网络端点值对象
 * 
 * 表示网络通信中的一个端点，包含IP地址和端口信息
 * 作为值对象，具有不可变性和值相等性
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Value
public class NetworkEndpoint {
    
    /**
     * IP地址
     */
    String ipAddress;
    
    /**
     * 端口号
     */
    Integer port;
    
    /**
     * MAC地址（可选）
     */
    String macAddress;
    
    /**
     * 是否为内部IP
     */
    Boolean internal;
    
    /**
     * 判断是否为内部网络端点
     * 
     * @return 如果是内部网络返回true
     */
    public boolean isInternal() {
        return internal != null && internal;
    }
    
    /**
     * 获取端点的字符串表示
     * 
     * @return IP:Port格式的字符串
     */
    public String getEndpointString() {
        return ipAddress + ":" + port;
    }
    
    /**
     * 判断是否为知名端口
     * 
     * @return 如果是知名端口返回true
     */
    public boolean isWellKnownPort() {
        return port != null && port <= 1024;
    }
    
    /**
     * 判断是否为私有IP地址
     * 
     * @return 如果是私有IP返回true
     */
    public boolean isPrivateIp() {
        if (ipAddress == null) {
            return false;
        }
        
        // 简单的私有IP判断逻辑
        return ipAddress.startsWith("192.168.") || 
               ipAddress.startsWith("10.") || 
               ipAddress.startsWith("172.16.") ||
               ipAddress.startsWith("172.17.") ||
               ipAddress.startsWith("172.18.") ||
               ipAddress.startsWith("172.19.") ||
               ipAddress.startsWith("172.20.") ||
               ipAddress.startsWith("172.21.") ||
               ipAddress.startsWith("172.22.") ||
               ipAddress.startsWith("172.23.") ||
               ipAddress.startsWith("172.24.") ||
               ipAddress.startsWith("172.25.") ||
               ipAddress.startsWith("172.26.") ||
               ipAddress.startsWith("172.27.") ||
               ipAddress.startsWith("172.28.") ||
               ipAddress.startsWith("172.29.") ||
               ipAddress.startsWith("172.30.") ||
               ipAddress.startsWith("172.31.");
    }
}
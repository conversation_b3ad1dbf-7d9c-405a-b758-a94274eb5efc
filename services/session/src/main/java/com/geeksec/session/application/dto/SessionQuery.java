package com.geeksec.session.application.dto;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会话查询条件
 */
public class SessionQuery {
    private int page = 1;
    private int size = 20;
    private String sourceIp;
    private String destinationIp;
    private String protocol;
    private String appName;
    private Integer appId;
    private String status;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private List<Integer> labels;
    private Boolean suspicious;
    private String keyword;
    private String sortBy;
    private String sortOrder = "desc";
    
    // 构造函数
    public SessionQuery() {}
    
    // Getter 和 Setter 方法
    public int getPage() {
        return page;
    }
    
    public void setPage(int page) {
        this.page = page;
    }
    
    public int getSize() {
        return size;
    }
    
    public void setSize(int size) {
        this.size = size;
    }
    
    public String getSourceIp() {
        return sourceIp;
    }
    
    public void setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
    }
    
    public String getDestinationIp() {
        return destinationIp;
    }
    
    public void setDestinationIp(String destinationIp) {
        this.destinationIp = destinationIp;
    }
    
    public String getProtocol() {
        return protocol;
    }
    
    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }
    
    public String getAppName() {
        return appName;
    }
    
    public void setAppName(String appName) {
        this.appName = appName;
    }
    
    public Integer getAppId() {
        return appId;
    }
    
    public void setAppId(Integer appId) {
        this.appId = appId;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public List<Integer> getLabels() {
        return labels;
    }
    
    public void setLabels(List<Integer> labels) {
        this.labels = labels;
    }
    
    public Boolean getSuspicious() {
        return suspicious;
    }
    
    public void setSuspicious(Boolean suspicious) {
        this.suspicious = suspicious;
    }
    
    public String getKeyword() {
        return keyword;
    }
    
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
    
    public String getSortBy() {
        return sortBy;
    }
    
    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }
    
    public String getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    // 计算偏移量
    public int getOffset() {
        return (page - 1) * size;
    }
    
    // 获取限制数量
    public int getLimit() {
        return size;
    }
}
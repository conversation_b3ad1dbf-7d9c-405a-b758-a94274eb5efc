package com.geeksec.session.infrastructure.config;

import com.geeksec.session.infrastructure.typehandlers.JsonTypeHandler;
import com.geeksec.session.infrastructure.typehandlers.ListTypeHandler;
import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.core.audit.ConsoleMessageCollector;
import com.mybatisflex.core.audit.MessageCollector;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * MyBatis 配置类
 */
@Configuration
public class MyBatisConfig implements MyBatisFlexCustomizer {
    
    /**
     * 配置 SQL 审计（开发环境）
     */
    @Bean
    @ConditionalOnProperty(name = "spring.profiles.active", havingValue = "dev")
    public MessageCollector messageCollector() {
        return new ConsoleMessageCollector();
    }
    
    @Override
    public void customize(FlexGlobalConfig globalConfig) {
        // 开启审计功能（仅在开发环境）
        if (messageCollector() != null) {
            AuditManager.setMessageCollector(messageCollector());
        }
        
        // 配置逻辑删除
        globalConfig.setLogicDeleteColumn("deleted");
        
        // 配置乐观锁
        globalConfig.setVersionColumn("version");
        
        // 配置自动填充
        globalConfig.registerInsertListener(entity -> {
            // 可以在这里添加创建时间等自动填充逻辑
        });
        
        globalConfig.registerUpdateListener(entity -> {
            // 可以在这里添加更新时间等自动填充逻辑
        });
    }
    
    /**
     * 配置 SqlSessionFactory
     */
    @Bean
    public SqlSessionFactory sqlSessionFactory(DataSource dataSource) throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        
        // 设置 mapper 文件位置
        sessionFactory.setMapperLocations(
            new PathMatchingResourcePatternResolver().getResources("classpath:mapper/*.xml")
        );
        
        // 设置类型别名包
        sessionFactory.setTypeAliasesPackage("com.geeksec.session.domain.entities,com.geeksec.session.domain.valueobjects");
        
        // 注册类型处理器
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.getTypeHandlerRegistry().register(JsonTypeHandler.class);
        configuration.getTypeHandlerRegistry().register(ListTypeHandler.class);
        
        // 开启驼峰命名转换
        configuration.setMapUnderscoreToCamelCase(true);
        
        // 开启缓存
        configuration.setCacheEnabled(true);
        
        // 设置懒加载
        configuration.setLazyLoadingEnabled(true);
        configuration.setAggressiveLazyLoading(false);
        
        sessionFactory.setConfiguration(configuration);
        
        return sessionFactory.getObject();
    }
}
package com.geeksec.session.application.dto;

import com.geeksec.session.domain.valueobjects.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 创建会话命令
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public class CreateSessionCommand {
    
    private String sessionId;
    private NetworkEndpoint sourceEndpoint;
    private NetworkEndpoint destinationEndpoint;
    private String protocol;
    private LocalDateTime sessionStartTime;
    private LocalDateTime sessionEndTime;
    private String appId;
    private String appName;
    private String serverIp;
    private String taskId;
    private String batchId;
    private String threadId;
    private SessionStatistics statistics;
    private SessionFingerprint fingerprint;
    private ProtocolMetadata protocolMetadata;
    private List<Integer> labels;
    private List<Integer> ruleLabels;
    private Map<String, Object> extensionData;
    
    // 构造函数
    public CreateSessionCommand() {}
    
    // Getter 和 Setter 方法
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public NetworkEndpoint getSourceEndpoint() {
        return sourceEndpoint;
    }
    
    public void setSourceEndpoint(NetworkEndpoint sourceEndpoint) {
        this.sourceEndpoint = sourceEndpoint;
    }
    
    public NetworkEndpoint getDestinationEndpoint() {
        return destinationEndpoint;
    }
    
    public void setDestinationEndpoint(NetworkEndpoint destinationEndpoint) {
        this.destinationEndpoint = destinationEndpoint;
    }
    
    public String getProtocol() {
        return protocol;
    }
    
    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }
    
    public LocalDateTime getSessionStartTime() {
        return sessionStartTime;
    }
    
    public void setSessionStartTime(LocalDateTime sessionStartTime) {
        this.sessionStartTime = sessionStartTime;
    }
    
    public LocalDateTime getSessionEndTime() {
        return sessionEndTime;
    }
    
    public void setSessionEndTime(LocalDateTime sessionEndTime) {
        this.sessionEndTime = sessionEndTime;
    }
    
    public String getAppId() {
        return appId;
    }
    
    public void setAppId(String appId) {
        this.appId = appId;
    }
    
    public String getAppName() {
        return appName;
    }
    
    public void setAppName(String appName) {
        this.appName = appName;
    }
    
    public String getServerIp() {
        return serverIp;
    }
    
    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }
    
    public String getTaskId() {
        return taskId;
    }
    
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    
    public String getBatchId() {
        return batchId;
    }
    
    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }
    
    public String getThreadId() {
        return threadId;
    }
    
    public void setThreadId(String threadId) {
        this.threadId = threadId;
    }
    
    public SessionStatistics getStatistics() {
        return statistics;
    }
    
    public void setStatistics(SessionStatistics statistics) {
        this.statistics = statistics;
    }
    
    public SessionFingerprint getFingerprint() {
        return fingerprint;
    }
    
    public void setFingerprint(SessionFingerprint fingerprint) {
        this.fingerprint = fingerprint;
    }
    
    public ProtocolMetadata getProtocolMetadata() {
        return protocolMetadata;
    }
    
    public void setProtocolMetadata(ProtocolMetadata protocolMetadata) {
        this.protocolMetadata = protocolMetadata;
    }
    
    public List<Integer> getLabels() {
        return labels;
    }
    
    public void setLabels(List<Integer> labels) {
        this.labels = labels;
    }
    
    public List<Integer> getRuleLabels() {
        return ruleLabels;
    }
    
    public void setRuleLabels(List<Integer> ruleLabels) {
        this.ruleLabels = ruleLabels;
    }
    
    public Map<String, Object> getExtensionData() {
        return extensionData;
    }
    
    public void setExtensionData(Map<String, Object> extensionData) {
        this.extensionData = extensionData;
    }
}
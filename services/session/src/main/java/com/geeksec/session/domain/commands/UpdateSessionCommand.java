package com.geeksec.session.domain.commands;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 更新会话命令
 */
public class UpdateSessionCommand {
    private String sessionId;
    private LocalDateTime sessionEndTime;
    private Integer appId;
    private String appName;
    private String serverIp;
    private String taskId;
    private String batchId;
    private String threadId;
    private List<Integer> labels;
    private List<Integer> ruleLabels;
    private Map<String, Object> extensionData;
    
    // 统计信息更新
    private Long totalPackets;
    private Long totalBytes;
    private Long sourcePackets;
    private Long sourceBytes;
    private Long destinationPackets;
    private Long destinationBytes;
    private Integer finPackets;
    private Integer rstPackets;
    private Integer synPackets;
    private Integer pshPackets;
    private Integer outOfOrderPackets;
    private Integer retransmittedPackets;
    private Integer lostPackets;
    
    // 指纹信息更新
    private String tcpFingerprint;
    private String httpFingerprint;
    private String sslFingerprint;
    private String userAgent;
    private List<String> cipherSuites;
    private String deviceFingerprint;
    
    // 协议元数据更新
    private Map<String, Object> httpMetadata;
    private Map<String, Object> sslMetadata;
    private Map<String, Object> dnsMetadata;
    private Map<String, Object> sshMetadata;
    private Map<String, Object> industrialMetadata;
    
    // 业务状态
    private String status;
    private String riskLevel;
    private Boolean suspicious;
    
    // 构造函数
    public UpdateSessionCommand() {}
    
    // Getter 和 Setter 方法
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public LocalDateTime getSessionEndTime() {
        return sessionEndTime;
    }
    
    public void setSessionEndTime(LocalDateTime sessionEndTime) {
        this.sessionEndTime = sessionEndTime;
    }
    
    public Integer getAppId() {
        return appId;
    }
    
    public void setAppId(Integer appId) {
        this.appId = appId;
    }
    
    public String getAppName() {
        return appName;
    }
    
    public void setAppName(String appName) {
        this.appName = appName;
    }
    
    public String getServerIp() {
        return serverIp;
    }
    
    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }
    
    public String getTaskId() {
        return taskId;
    }
    
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    
    public String getBatchId() {
        return batchId;
    }
    
    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }
    
    public String getThreadId() {
        return threadId;
    }
    
    public void setThreadId(String threadId) {
        this.threadId = threadId;
    }
    
    public List<Integer> getLabels() {
        return labels;
    }
    
    public void setLabels(List<Integer> labels) {
        this.labels = labels;
    }
    
    public List<Integer> getRuleLabels() {
        return ruleLabels;
    }
    
    public void setRuleLabels(List<Integer> ruleLabels) {
        this.ruleLabels = ruleLabels;
    }
    
    public Map<String, Object> getExtensionData() {
        return extensionData;
    }
    
    public void setExtensionData(Map<String, Object> extensionData) {
        this.extensionData = extensionData;
    }
    
    public Long getTotalPackets() {
        return totalPackets;
    }
    
    public void setTotalPackets(Long totalPackets) {
        this.totalPackets = totalPackets;
    }
    
    public Long getTotalBytes() {
        return totalBytes;
    }
    
    public void setTotalBytes(Long totalBytes) {
        this.totalBytes = totalBytes;
    }
    
    public Long getSourcePackets() {
        return sourcePackets;
    }
    
    public void setSourcePackets(Long sourcePackets) {
        this.sourcePackets = sourcePackets;
    }
    
    public Long getSourceBytes() {
        return sourceBytes;
    }
    
    public void setSourceBytes(Long sourceBytes) {
        this.sourceBytes = sourceBytes;
    }
    
    public Long getDestinationPackets() {
        return destinationPackets;
    }
    
    public void setDestinationPackets(Long destinationPackets) {
        this.destinationPackets = destinationPackets;
    }
    
    public Long getDestinationBytes() {
        return destinationBytes;
    }
    
    public void setDestinationBytes(Long destinationBytes) {
        this.destinationBytes = destinationBytes;
    }
    
    public Integer getFinPackets() {
        return finPackets;
    }
    
    public void setFinPackets(Integer finPackets) {
        this.finPackets = finPackets;
    }
    
    public Integer getRstPackets() {
        return rstPackets;
    }
    
    public void setRstPackets(Integer rstPackets) {
        this.rstPackets = rstPackets;
    }
    
    public Integer getSynPackets() {
        return synPackets;
    }
    
    public void setSynPackets(Integer synPackets) {
        this.synPackets = synPackets;
    }
    
    public Integer getPshPackets() {
        return pshPackets;
    }
    
    public void setPshPackets(Integer pshPackets) {
        this.pshPackets = pshPackets;
    }
    
    public Integer getOutOfOrderPackets() {
        return outOfOrderPackets;
    }
    
    public void setOutOfOrderPackets(Integer outOfOrderPackets) {
        this.outOfOrderPackets = outOfOrderPackets;
    }
    
    public Integer getRetransmittedPackets() {
        return retransmittedPackets;
    }
    
    public void setRetransmittedPackets(Integer retransmittedPackets) {
        this.retransmittedPackets = retransmittedPackets;
    }
    
    public Integer getLostPackets() {
        return lostPackets;
    }
    
    public void setLostPackets(Integer lostPackets) {
        this.lostPackets = lostPackets;
    }
    
    public String getTcpFingerprint() {
        return tcpFingerprint;
    }
    
    public void setTcpFingerprint(String tcpFingerprint) {
        this.tcpFingerprint = tcpFingerprint;
    }
    
    public String getHttpFingerprint() {
        return httpFingerprint;
    }
    
    public void setHttpFingerprint(String httpFingerprint) {
        this.httpFingerprint = httpFingerprint;
    }
    
    public String getSslFingerprint() {
        return sslFingerprint;
    }
    
    public void setSslFingerprint(String sslFingerprint) {
        this.sslFingerprint = sslFingerprint;
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    public List<String> getCipherSuites() {
        return cipherSuites;
    }
    
    public void setCipherSuites(List<String> cipherSuites) {
        this.cipherSuites = cipherSuites;
    }
    
    public String getDeviceFingerprint() {
        return deviceFingerprint;
    }
    
    public void setDeviceFingerprint(String deviceFingerprint) {
        this.deviceFingerprint = deviceFingerprint;
    }
    
    public Map<String, Object> getHttpMetadata() {
        return httpMetadata;
    }
    
    public void setHttpMetadata(Map<String, Object> httpMetadata) {
        this.httpMetadata = httpMetadata;
    }
    
    public Map<String, Object> getSslMetadata() {
        return sslMetadata;
    }
    
    public void setSslMetadata(Map<String, Object> sslMetadata) {
        this.sslMetadata = sslMetadata;
    }
    
    public Map<String, Object> getDnsMetadata() {
        return dnsMetadata;
    }
    
    public void setDnsMetadata(Map<String, Object> dnsMetadata) {
        this.dnsMetadata = dnsMetadata;
    }
    
    public Map<String, Object> getSshMetadata() {
        return sshMetadata;
    }
    
    public void setSshMetadata(Map<String, Object> sshMetadata) {
        this.sshMetadata = sshMetadata;
    }
    
    public Map<String, Object> getIndustrialMetadata() {
        return industrialMetadata;
    }
    
    public void setIndustrialMetadata(Map<String, Object> industrialMetadata) {
        this.industrialMetadata = industrialMetadata;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getRiskLevel() {
        return riskLevel;
    }
    
    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }
    
    public Boolean getSuspicious() {
        return suspicious;
    }
    
    public void setSuspicious(Boolean suspicious) {
        this.suspicious = suspicious;
    }
}
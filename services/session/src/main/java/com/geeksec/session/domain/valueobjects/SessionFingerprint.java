package com.geeksec.session.domain.valueobjects;

import lombok.Value;
import java.util.List;
import java.util.Map;

/**
 * 会话指纹信息值对象
 * 
 * 包含TCP、HTTP、SSL等协议的指纹信息，用于设备识别和协议分析
 * 作为值对象，具有不可变性和值相等性
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Value
public class SessionFingerprint {
    
    // ==================== TCP指纹 ====================
    
    /**
     * TCP指纹信息
     */
    String tcpFingerprint;
    
    /**
     * TCP指纹特征
     */
    Map<String, Object> tcpFingerprintFeatures;
    
    /**
     * ECN支持
     */
    Boolean ecnSupport;
    
    /**
     * IP ID增量
     */
    Integer ipIdIncrement;
    
    /**
     * TCP标志
     */
    String tcpFlags;
    
    /**
     * 时间戳支持
     */
    Boolean timestampSupport;
    
    /**
     * TTL值
     */
    Integer ttl;
    
    /**
     * EOL填充
     */
    Boolean eolPadding;
    
    /**
     * 窗口缩放
     */
    Integer windowScale;
    
    /**
     * MSS比率
     */
    Double mssRatio;
    
    /**
     * 选项布局
     */
    String optionsLayout;
    
    // ==================== HTTP指纹 ====================
    
    /**
     * HTTP指纹信息
     */
    String httpFingerprint;
    
    /**
     * User-Agent
     */
    String userAgent;
    
    /**
     * HTTP头部顺序
     */
    List<String> httpHeaderOrder;
    
    /**
     * HTTP方法
     */
    String httpMethod;
    
    /**
     * HTTP版本
     */
    String httpVersion;
    
    // ==================== SSL/TLS指纹 ====================
    
    /**
     * SSL指纹信息
     */
    String sslFingerprint;
    
    /**
     * SSL版本
     */
    String sslVersion;
    
    /**
     * 支持的密码套件
     */
    List<String> cipherSuites;
    
    /**
     * SSL扩展
     */
    List<String> sslExtensions;
    
    /**
     * 椭圆曲线
     */
    List<String> ellipticCurves;
    
    /**
     * 签名算法
     */
    List<String> signatureAlgorithms;
    
    // ==================== 其他指纹 ====================
    
    /**
     * 设备指纹
     */
    String deviceFingerprint;
    
    /**
     * 操作系统指纹
     */
    String osFingerprint;
    
    /**
     * 应用指纹
     */
    String applicationFingerprint;
    
    // ==================== 业务方法 ====================
    
    /**
     * 判断是否有完整的TCP指纹
     * 
     * @return 如果有完整TCP指纹返回true
     */
    public boolean hasCompleteTcpFingerprint() {
        return tcpFingerprint != null && !tcpFingerprint.trim().isEmpty();
    }
    
    /**
     * 判断是否有HTTP指纹
     * 
     * @return 如果有HTTP指纹返回true
     */
    public boolean hasHttpFingerprint() {
        return httpFingerprint != null && !httpFingerprint.trim().isEmpty();
    }
    
    /**
     * 判断是否有SSL指纹
     * 
     * @return 如果有SSL指纹返回true
     */
    public boolean hasSslFingerprint() {
        return sslFingerprint != null && !sslFingerprint.trim().isEmpty();
    }
    
    /**
     * 获取主要指纹类型
     * 
     * @return 主要指纹类型
     */
    public String getPrimaryFingerprintType() {
        if (hasSslFingerprint()) {
            return "SSL/TLS";
        } else if (hasHttpFingerprint()) {
            return "HTTP";
        } else if (hasCompleteTcpFingerprint()) {
            return "TCP";
        } else {
            return "Unknown";
        }
    }
    
    /**
     * 判断是否为加密连接
     * 
     * @return 如果是加密连接返回true
     */
    public boolean isEncryptedConnection() {
        return hasSslFingerprint() || 
               (sslVersion != null && !sslVersion.trim().isEmpty());
    }
    
    /**
     * 获取指纹置信度
     * 
     * @return 指纹置信度分数（0-100）
     */
    public int getFingerprintConfidence() {
        int score = 0;
        
        if (hasCompleteTcpFingerprint()) score += 30;
        if (hasHttpFingerprint()) score += 35;
        if (hasSslFingerprint()) score += 35;
        
        // 额外的特征增加置信度
        if (userAgent != null && !userAgent.trim().isEmpty()) score += 10;
        if (deviceFingerprint != null && !deviceFingerprint.trim().isEmpty()) score += 10;
        if (osFingerprint != null && !osFingerprint.trim().isEmpty()) score += 10;
        
        return Math.min(score, 100);
    }
    
    /**
     * 判断是否为可疑指纹
     * 
     * @return 如果是可疑指纹返回true
     */
    public boolean isSuspiciousFingerprint() {
        // 基于一些启发式规则判断可疑性
        if (userAgent != null && userAgent.toLowerCase().contains("bot")) {
            return true;
        }
        
        // 检查异常的SSL配置
        if (isEncryptedConnection() && sslVersion != null && 
            (sslVersion.contains("SSLv2") || sslVersion.contains("SSLv3"))) {
            return true;
        }
        
        return false;
    }
}
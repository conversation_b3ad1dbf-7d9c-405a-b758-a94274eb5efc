package com.geeksec.session;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Session 服务启动类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableTransactionManagement
@MapperScan("com.geeksec.session.infrastructure.repositories")
public class SessionApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(SessionApplication.class, args);
    }
}
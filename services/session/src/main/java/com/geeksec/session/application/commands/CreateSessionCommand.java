package com.geeksec.session.application.commands;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 创建会话命令
 * 
 * 封装创建会话所需的所有参数
 * 遵循CQRS模式，将命令与查询分离
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class CreateSessionCommand {
    
    /**
     * 会话唯一标识符
     */
    private String sessionId;
    
    /**
     * 源IP地址
     */
    private String sourceIp;
    
    /**
     * 源端口
     */
    private Integer sourcePort;
    
    /**
     * 源MAC地址
     */
    private String sourceMac;
    
    /**
     * 源是否为内部IP
     */
    private Boolean sourceInternal;
    
    /**
     * 目标IP地址
     */
    private String destinationIp;
    
    /**
     * 目标端口
     */
    private Integer destinationPort;
    
    /**
     * 目标MAC地址
     */
    private String destinationMac;
    
    /**
     * 目标是否为内部IP
     */
    private Boolean destinationInternal;
    
    /**
     * IP协议号
     */
    private Integer protocol;
    
    /**
     * 会话开始时间
     */
    private LocalDateTime sessionStartTime;
    
    /**
     * 会话结束时间
     */
    private LocalDateTime sessionEndTime;
    
    /**
     * 应用ID
     */
    private Integer appId;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 服务器IP地址
     */
    private String serverIp;
    
    /**
     * 任务ID
     */
    private Integer taskId;
    
    /**
     * 批次ID
     */
    private Integer batchId;
    
    /**
     * 线程ID
     */
    private Integer threadId;
    
    /**
     * 会话标签ID列表
     */
    private List<Integer> labels;
    
    /**
     * 规则标签列表
     */
    private List<Integer> ruleLabels;
    
    /**
     * 扩展数据
     */
    private Map<String, Object> extensionData;
    
    // ==================== 统计信息字段 ====================
    
    /**
     * 源到目标数据包数量
     */
    private Long srcToDstPackets;
    
    /**
     * 目标到源数据包数量
     */
    private Long dstToSrcPackets;
    
    /**
     * 源到目标字节数
     */
    private Long srcToDstBytes;
    
    /**
     * 目标到源字节数
     */
    private Long dstToSrcBytes;
    
    /**
     * 规则级别
     */
    private Integer ruleLevel;
    
    // ==================== 指纹信息字段 ====================
    
    /**
     * TCP指纹信息
     */
    private String tcpFingerprint;
    
    /**
     * HTTP指纹信息
     */
    private String httpFingerprint;
    
    /**
     * SSL指纹信息
     */
    private String sslFingerprint;
    
    /**
     * User-Agent
     */
    private String userAgent;
    
    /**
     * 设备指纹
     */
    private String deviceFingerprint;
    
    // ==================== 协议元数据字段 ====================
    
    /**
     * HTTP元数据
     */
    private List<Map<String, Object>> httpMetadata;
    
    /**
     * SSL元数据
     */
    private List<Map<String, Object>> sslMetadata;
    
    /**
     * DNS元数据
     */
    private List<Map<String, Object>> dnsMetadata;
    
    /**
     * SSH元数据
     */
    private List<Map<String, Object>> sshMetadata;
    
    // ==================== 验证方法 ====================
    
    /**
     * 验证命令的有效性
     * 
     * @return 如果命令有效返回true
     */
    public boolean isValid() {
        return sessionId != null && !sessionId.trim().isEmpty() &&
               sourceIp != null && !sourceIp.trim().isEmpty() &&
               destinationIp != null && !destinationIp.trim().isEmpty() &&
               sessionStartTime != null;
    }
    
    /**
     * 获取验证错误信息
     * 
     * @return 错误信息列表
     */
    public List<String> getValidationErrors() {
        List<String> errors = new java.util.ArrayList<>();
        
        if (sessionId == null || sessionId.trim().isEmpty()) {
            errors.add("会话ID不能为空");
        }
        
        if (sourceIp == null || sourceIp.trim().isEmpty()) {
            errors.add("源IP地址不能为空");
        }
        
        if (destinationIp == null || destinationIp.trim().isEmpty()) {
            errors.add("目标IP地址不能为空");
        }
        
        if (sessionStartTime == null) {
            errors.add("会话开始时间不能为空");
        }
        
        if (sessionEndTime != null && sessionStartTime != null && 
            sessionEndTime.isBefore(sessionStartTime)) {
            errors.add("会话结束时间不能早于开始时间");
        }
        
        return errors;
    }
}
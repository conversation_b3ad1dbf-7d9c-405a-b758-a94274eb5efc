package com.geeksec.session.interfaces.rest;

import com.geeksec.session.application.dto.SessionDTO;
import com.geeksec.session.application.dto.CreateSessionCommand;
import com.geeksec.session.application.dto.UpdateSessionCommand;
import com.geeksec.session.application.dto.SessionQuery;
import com.geeksec.session.application.dto.Result;
import com.geeksec.session.application.dto.PageResult;
import com.geeksec.session.application.services.SessionApplicationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会话管理控制器
 * 提供会话的CRUD操作和查询接口
 */
@RestController
@RequestMapping("/api/v1/sessions")
@Validated
public class SessionController {

    @Autowired
    private SessionApplicationService sessionApplicationService;

    /**
     * 创建新会话
     */
    @PostMapping
    public Result<SessionDTO> createSession(@Valid @RequestBody CreateSessionCommand command) {
        SessionDTO session = sessionApplicationService.createSession(command);
        return Result.success(session);
    }

    /**
     * 更新会话信息
     */
    @PutMapping("/{sessionId}")
    public Result<SessionDTO> updateSession(
            @PathVariable @NotNull String sessionId,
            @Valid @RequestBody UpdateSessionCommand command) {
        command.setSessionId(sessionId);
        SessionDTO session = sessionApplicationService.updateSession(command);
        return Result.success(session);
    }

    /**
     * 根据ID获取会话详情
     */
    @GetMapping("/{sessionId}")
    public Result<SessionDTO> getSession(@PathVariable @NotNull String sessionId) {
        SessionDTO session = sessionApplicationService.getSessionById(sessionId);
        return Result.success(session);
    }

    /**
     * 删除会话
     */
    @DeleteMapping("/{sessionId}")
    public Result<Void> deleteSession(@PathVariable @NotNull String sessionId) {
        sessionApplicationService.deleteSession(sessionId);
        return Result.success();
    }

    /**
     * 结束会话
     */
    @PostMapping("/{sessionId}/end")
    public Result<SessionDTO> endSession(@PathVariable @NotNull String sessionId) {
        SessionDTO session = sessionApplicationService.endSession(sessionId);
        return Result.success(session);
    }

    /**
     * 分页查询会话列表
     */
    @GetMapping
    public Result<PageResult<SessionDTO>> getSessions(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String sourceIp,
            @RequestParam(required = false) String destinationIp,
            @RequestParam(required = false) String protocol,
            @RequestParam(required = false) String appName,
            @RequestParam(required = false) Integer appId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) LocalDateTime startTime,
            @RequestParam(required = false) LocalDateTime endTime,
            @RequestParam(required = false) List<Integer> labels,
            @RequestParam(required = false) Boolean suspicious) {
        
        SessionQuery query = new SessionQuery();
        query.setPage(page);
        query.setSize(size);
        query.setSourceIp(sourceIp);
        query.setDestinationIp(destinationIp);
        query.setProtocol(protocol);
        query.setAppName(appName);
        query.setAppId(appId);
        query.setStatus(status);
        query.setStartTime(startTime);
        query.setEndTime(endTime);
        query.setLabels(labels);
        query.setSuspicious(suspicious);
        
        PageResult<SessionDTO> result = sessionApplicationService.getSessions(query);
        return Result.success(result);
    }

    /**
     * 获取活跃会话列表
     */
    @GetMapping("/active")
    public Result<List<SessionDTO>> getActiveSessions() {
        List<SessionDTO> sessions = sessionApplicationService.getActiveSessions();
        return Result.success(sessions);
    }

    /**
     * 获取可疑会话列表
     */
    @GetMapping("/suspicious")
    public Result<List<SessionDTO>> getSuspiciousSessions() {
        List<SessionDTO> sessions = sessionApplicationService.getSuspiciousSessions();
        return Result.success(sessions);
    }

    /**
     * 根据时间范围查询会话
     */
    @GetMapping("/by-time-range")
    public Result<List<SessionDTO>> getSessionsByTimeRange(
            @RequestParam @NotNull LocalDateTime startTime,
            @RequestParam @NotNull LocalDateTime endTime) {
        List<SessionDTO> sessions = sessionApplicationService.getSessionsByTimeRange(startTime, endTime);
        return Result.success(sessions);
    }

    /**
     * 根据IP地址查询会话
     */
    @GetMapping("/by-ip")
    public Result<List<SessionDTO>> getSessionsByIp(
            @RequestParam @NotNull String ipAddress) {
        List<SessionDTO> sessions = sessionApplicationService.getSessionsByIp(ipAddress);
        return Result.success(sessions);
    }

    /**
     * 根据应用ID查询会话
     */
    @GetMapping("/by-app")
    public Result<List<SessionDTO>> getSessionsByAppId(
            @RequestParam @NotNull Integer appId) {
        List<SessionDTO> sessions = sessionApplicationService.getSessionsByAppId(appId);
        return Result.success(sessions);
    }

    /**
     * 根据标签查询会话
     */
    @GetMapping("/by-label")
    public Result<List<SessionDTO>> getSessionsByLabel(
            @RequestParam @NotNull Integer labelId) {
        List<SessionDTO> sessions = sessionApplicationService.getSessionsByLabel(labelId);
        return Result.success(sessions);
    }

    /**
     * 获取协议分布统计
     */
    @GetMapping("/stats/protocol-distribution")
    public Result<Map<String, Long>> getProtocolDistribution(
            @RequestParam(required = false) LocalDateTime startTime,
            @RequestParam(required = false) LocalDateTime endTime) {
        Map<String, Long> distribution = sessionApplicationService.getProtocolDistribution(startTime, endTime);
        return Result.success(distribution);
    }

    /**
     * 获取应用分布统计
     */
    @GetMapping("/stats/application-distribution")
    public Result<Map<String, Long>> getApplicationDistribution(
            @RequestParam(required = false) LocalDateTime startTime,
            @RequestParam(required = false) LocalDateTime endTime) {
        Map<String, Long> distribution = sessionApplicationService.getApplicationDistribution(startTime, endTime);
        return Result.success(distribution);
    }

    /**
     * 获取TOP IP统计
     */
    @GetMapping("/stats/top-ips")
    public Result<Map<String, Long>> getTopIps(
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(required = false) LocalDateTime startTime,
            @RequestParam(required = false) LocalDateTime endTime) {
        Map<String, Long> topIps = sessionApplicationService.getTopIps(limit, startTime, endTime);
        return Result.success(topIps);
    }

    /**
     * 获取会话趋势统计
     */
    @GetMapping("/stats/session-trends")
    public Result<Map<String, Long>> getSessionTrends(
            @RequestParam @NotNull String timeUnit, // hour, day, week, month
            @RequestParam(required = false) LocalDateTime startTime,
            @RequestParam(required = false) LocalDateTime endTime) {
        Map<String, Long> trends = sessionApplicationService.getSessionTrends(timeUnit, startTime, endTime);
        return Result.success(trends);
    }

    /**
     * 批量删除会话
     */
    @DeleteMapping("/batch")
    public Result<Void> batchDeleteSessions(@RequestBody List<String> sessionIds) {
        sessionApplicationService.batchDeleteSessions(sessionIds);
        return Result.success();
    }

    /**
     * 批量更新会话标签
     */
    @PostMapping("/batch/labels")
    public Result<Void> batchUpdateLabels(
            @RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<String> sessionIds = (List<String>) request.get("sessionIds");
        @SuppressWarnings("unchecked")
        List<Integer> labelsToAdd = (List<Integer>) request.get("labelsToAdd");
        @SuppressWarnings("unchecked")
        List<Integer> labelsToRemove = (List<Integer>) request.get("labelsToRemove");
        
        sessionApplicationService.batchUpdateLabels(sessionIds, labelsToAdd, labelsToRemove);
        return Result.success();
    }

    /**
     * 搜索会话（支持模糊匹配）
     */
    @GetMapping("/search")
    public Result<PageResult<SessionDTO>> searchSessions(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        PageResult<SessionDTO> result = sessionApplicationService.searchSessions(keyword, page, size);
        return Result.success(result);
    }

    /**
     * 导出会话数据
     */
    @GetMapping("/export")
    public Result<String> exportSessions(
            @RequestParam(required = false) LocalDateTime startTime,
            @RequestParam(required = false) LocalDateTime endTime,
            @RequestParam(defaultValue = "csv") String format) {
        String exportPath = sessionApplicationService.exportSessions(startTime, endTime, format);
        return Result.success(exportPath);
    }

    /**
     * 获取会话统计概览
     */
    @GetMapping("/stats/overview")
    public Result<Map<String, Object>> getSessionOverview(
            @RequestParam(required = false) LocalDateTime startTime,
            @RequestParam(required = false) LocalDateTime endTime) {
        Map<String, Object> overview = sessionApplicationService.getSessionOverview(startTime, endTime);
        return Result.success(overview);
    }
}
package com.geeksec.session.model.dto.session;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会话关联响应DTO
 * 用于返回会话关联分析结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SessionRelationResponse {
    
    /**
     * 中心会话ID
     */
    private String centerSessionId;
    
    /**
     * 目标会话ID
     */
    private String sessionId;
    
    /**
     * 关联类型
     */
    private String relationType;
    
    /**
     * 关联描述
     */
    private String relationDescription;
    
    /**
     * 分析开始时间
     */
    private LocalDateTime analysisStartTime;
    
    /**
     * 分析结束时间
     */
    private LocalDateTime analysisEndTime;
    
    /**
     * 关联会话总数
     */
    private Integer totalCount;
    
    /**
     * 总关联会话数
     */
    private Integer totalRelatedSessions;
    
    /**
     * 查询时间范围(小时)
     */
    private Integer timeRange;
    
    /**
     * 关联会话列表
     */
    private List<RelatedSession> sessions;
    
    /**
     * 关联会话列表
     */
    private List<RelatedSession> relatedSessions;
    
    /**
     * 统计信息
     */
    private List<RelationStatistics> statistics;
    
    /**
     * 关联会话详情
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RelatedSession {
        /**
         * 会话ID
         */
        private String sessionId;
        
        /**
         * 源IP地址
         */
        private String srcIp;
        
        /**
         * 目标IP地址
         */
        private String dstIp;
        
        /**
         * 源端口
         */
        private Integer srcPort;
        
        /**
         * 目标端口
         */
        private Integer dstPort;
        
        /**
         * 应用名称
         */
        private String appName;
        
        /**
         * 会话开始时间
         */
        private LocalDateTime sessionStartTime;
        
        /**
         * 会话持续时间(秒)
         */
        private Long sessionDuration;
        
        /**
         * 总字节数
         */
        private Long totalBytes;
        
        /**
         * 关联强度(0-1)
         */
        private Double relationStrength;
        
        /**
         * 关联原因
         */
        private String relationReason;
        
        /**
         * 标签列表
         */
        private List<String> labels;
    }
    
    /**
     * 关联统计信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RelationStatistics {
        /**
         * 统计类型
         */
        private String type;
        
        /**
         * 统计值
         */
        private Object value;
        
        /**
         * 统计描述
         */
        private String description;
    }
}
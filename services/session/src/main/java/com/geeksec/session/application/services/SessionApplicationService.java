package com.geeksec.session.application.services;

import com.geeksec.session.application.dto.CreateSessionCommand;
import com.geeksec.session.application.dto.UpdateSessionCommand;
import com.geeksec.session.application.dto.SessionQuery;
import java.time.LocalDateTime;
import com.geeksec.session.domain.entities.Session;
import com.geeksec.session.domain.repositories.SessionRepository;
import com.geeksec.session.domain.services.SessionDomainService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 会话应用服务
 * 
 * 应用层的核心服务，负责协调领域服务、仓储和外部服务
 * 处理用例的编排和事务管理
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Service
@Transactional
public class SessionApplicationService {
    
    private final SessionRepository sessionRepository;
    private final SessionDomainService sessionDomainService;
    
    public SessionApplicationService(
            SessionRepository sessionRepository,
            SessionDomainService sessionDomainService) {
        this.sessionRepository = sessionRepository;
        this.sessionDomainService = sessionDomainService;
    }
    
    // ==================== 命令处理 ====================
    
    /**
     * 创建会话
     * 
     * @param command 创建会话命令
     * @return 创建的会话
     */
    public Session createSession(CreateSessionCommand command) {
        // 验证命令
        validateCreateSessionCommand(command);
        
        // 构建会话聚合根
        Session session = buildSessionFromCommand(command);
        
        // 验证业务规则
        if (!session.isDataComplete()) {
            throw new IllegalArgumentException("会话数据不完整");
        }
        
        // 保存会话
        return sessionRepository.save(session);
    }
    
    /**
     * 更新会话
     * 
     * @param command 更新会话命令
     * @return 更新后的会话
     */
    public Session updateSession(UpdateSessionCommand command) {
        // 查找现有会话
        Optional<Session> existingSession = sessionRepository.findById(command.getSessionId());
        if (existingSession.isEmpty()) {
            throw new IllegalArgumentException("会话不存在: " + command.getSessionId());
        }
        
        Session session = existingSession.get();
        
        // 应用更新
        applyUpdateToSession(session, command);
        
        // 保存更新
        return sessionRepository.save(session);
    }
    
    /**
     * 删除会话
     * 
     * @param sessionId 会话ID
     */
    public void deleteSession(String sessionId) {
        if (!sessionRepository.existsById(sessionId)) {
            throw new IllegalArgumentException("会话不存在: " + sessionId);
        }
        
        sessionRepository.deleteById(sessionId);
    }
    
    /**
     * 批量添加会话标签
     * 
     * @param sessionIds 会话ID列表
     * @param labelId 标签ID
     */
    public void addLabelToSessions(List<String> sessionIds, Integer labelId) {
        List<Session> sessions = sessionRepository.findByIds(sessionIds);
        
        for (Session session : sessions) {
            session.addLabel(labelId);
        }
        
        // 批量保存
        sessions.forEach(sessionRepository::save);
    }
    
    /**
     * 结束活跃会话
     * 
     * @param sessionId 会话ID
     */
    public void endSession(String sessionId) {
        Optional<Session> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isEmpty()) {
            throw new IllegalArgumentException("会话不存在: " + sessionId);
        }
        
        Session session = sessionOpt.get();
        if (!session.isActive()) {
            throw new IllegalStateException("会话已经结束");
        }
        
        session.endSession(java.time.LocalDateTime.now());
        sessionRepository.save(session);
    }
    
    // ==================== 查询处理 ====================
    
    /**
     * 根据ID获取会话详情
     * 
     * @param sessionId 会话ID
     * @return 会话详情
     */
    @Transactional(readOnly = true)
    public Optional<Session> getSessionById(String sessionId) {
        return sessionRepository.findById(sessionId);
    }
    
    /**
     * 批量获取会话
     * 
     * @param sessionIds 会话ID列表
     * @return 会话列表
     */
    @Transactional(readOnly = true)
    public List<Session> getSessionsByIds(List<String> sessionIds) {
        return sessionRepository.findByIds(sessionIds);
    }
    
    /**
     * 分页查询会话列表
     * 
     * @param query 查询条件
     * @return 会话列表
     */
    @Transactional(readOnly = true)
    public List<Session> getSessionList(SessionQuery query) {
        Map<String, Object> filters = buildFiltersFromQuery(query);
        return sessionRepository.findSessions(query.getOffset(), query.getLimit(), filters);
    }
    
    /**
     * 统计会话数量
     * 
     * @param query 查询条件
     * @return 会话数量
     */
    @Transactional(readOnly = true)
    public long countSessions(SessionQuery query) {
        Map<String, Object> filters = buildFiltersFromQuery(query);
        return sessionRepository.countSessions(filters);
    }
    
    /**
     * 搜索会话
     * 
     * @param keyword 搜索关键字
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 搜索结果
     */
    @Transactional(readOnly = true)
    public List<Session> searchSessions(String keyword, int offset, int limit) {
        return sessionRepository.searchByKeyword(keyword, offset, limit);
    }
    
    // ==================== 统计分析 ====================
    
    /**
     * 获取会话统计信息
     * 
     * @param query 查询条件
     * @return 统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getSessionStatistics(SessionQuery query) {
        Map<String, Object> filters = buildFiltersFromQuery(query);
        return sessionRepository.getSessionStatistics(filters);
    }
    
    /**
     * 获取协议分布统计
     * 
     * @param query 查询条件
     * @return 协议分布统计
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getProtocolDistribution(SessionQuery query) {
        Map<String, Object> filters = buildFiltersFromQuery(query);
        return sessionRepository.getProtocolDistribution(filters);
    }
    
    /**
     * 获取应用分布统计
     * 
     * @param query 查询条件
     * @return 应用分布统计
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getApplicationDistribution(SessionQuery query) {
        Map<String, Object> filters = buildFiltersFromQuery(query);
        return sessionRepository.getApplicationDistribution(filters);
    }
    
    /**
     * 获取TOP源IP统计
     * 
     * @param limit 限制数量
     * @param query 查询条件
     * @return TOP源IP统计
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getTopSourceIps(int limit, SessionQuery query) {
        Map<String, Object> filters = buildFiltersFromQuery(query);
        return sessionRepository.getTopSourceIps(limit, filters);
    }
    
    /**
     * 获取TOP目标IP统计
     * 
     * @param limit 限制数量
     * @param query 查询条件
     * @return TOP目标IP统计
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getTopDestinationIps(int limit, SessionQuery query) {
        Map<String, Object> filters = buildFiltersFromQuery(query);
        return sessionRepository.getTopDestinationIps(limit, filters);
    }
    
    /**
     * 获取会话趋势统计
     * 
     * @param timeUnit 时间单位
     * @param query 查询条件
     * @return 会话趋势统计
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getSessionTrends(String timeUnit, SessionQuery query) {
        Map<String, Object> filters = buildFiltersFromQuery(query);
        return sessionRepository.getSessionTrends(timeUnit, filters);
    }
    
    // ==================== 业务分析 ====================
    
    /**
     * 分析会话安全风险
     * 
     * @param sessionId 会话ID
     * @return 风险分析结果
     */
    @Transactional(readOnly = true)
    public Map<String, Object> analyzeSessionSecurity(String sessionId) {
        Optional<Session> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isEmpty()) {
            throw new IllegalArgumentException("会话不存在: " + sessionId);
        }
        
        Session session = sessionOpt.get();
        
        Map<String, Object> analysis = new java.util.HashMap<>();
        analysis.put("sessionId", sessionId);
        analysis.put("riskLevel", sessionDomainService.analyzeSecurityRiskLevel(session));
        analysis.put("anomalousActivities", sessionDomainService.detectAnomalousActivities(session));
        analysis.put("isSuspicious", session.isSuspicious());
        analysis.put("networkDirection", session.getNetworkDirection());
        
        return analysis;
    }
    
    /**
     * 分析IP行为模式
     * 
     * @param ipAddress IP地址
     * @param timeRange 时间范围（小时）
     * @return 行为模式分析
     */
    @Transactional(readOnly = true)
    public Map<String, Object> analyzeIpBehavior(String ipAddress, int timeRange) {
        return sessionDomainService.analyzeIpBehaviorPattern(ipAddress, timeRange);
    }
    
    /**
     * 检测可疑会话
     * 
     * @param timeRange 时间范围（小时）
     * @return 可疑会话列表
     */
    @Transactional(readOnly = true)
    public List<Session> detectSuspiciousSessions(int timeRange) {
        return sessionDomainService.detectBatchSuspiciousSessions(timeRange);
    }
    
    // ==================== 私有辅助方法 ====================
    
    private void validateCreateSessionCommand(CreateSessionCommand command) {
        if (command.getSessionId() == null || command.getSessionId().trim().isEmpty()) {
            throw new IllegalArgumentException("会话ID不能为空");
        }
        
        if (sessionRepository.existsById(command.getSessionId())) {
            throw new IllegalArgumentException("会话ID已存在: " + command.getSessionId());
        }
    }
    
    private Session buildSessionFromCommand(CreateSessionCommand command) {
        // 暂时返回一个简单的 Session 实例，只设置基本字段
        Session session = new Session();
        // 暂时跳过所有字段设置，直到 Lombok 问题解决
        return session;
    }
    
    private void applyUpdateToSession(Session session, UpdateSessionCommand command) {
        if (command.getSourceEndpoint() != null) {
            session.setSourceEndpoint(command.getSourceEndpoint());
        }
        if (command.getDestinationEndpoint() != null) {
            session.setDestinationEndpoint(command.getDestinationEndpoint());
        }
        if (command.getProtocol() != null) {
            session.setProtocol(command.getProtocol());
        }
        if (command.getSessionStartTime() != null) {
            session.setSessionStartTime(command.getSessionStartTime());
        }
        if (command.getSessionEndTime() != null) {
            session.setSessionEndTime(command.getSessionEndTime());
        }
        if (command.getAppId() != null) {
            session.setAppId(command.getAppId());
        }
        if (command.getAppName() != null) {
            session.setAppName(command.getAppName());
        }
        if (command.getServerIp() != null) {
            session.setServerIp(command.getServerIp());
        }
        if (command.getTaskId() != null) {
            session.setTaskId(command.getTaskId());
        }
        if (command.getBatchId() != null) {
            session.setBatchId(command.getBatchId());
        }
        if (command.getThreadId() != null) {
            session.setThreadId(command.getThreadId());
        }
        if (command.getStatistics() != null) {
            session.setStatistics(command.getStatistics());
        }
        // 注意：fingerprint, protocolMetadata 是值对象，暂时跳过设置
        // labels, ruleLabels, extensionData 字段暂时跳过设置
    }
    
    private Map<String, Object> buildFiltersFromQuery(SessionQuery query) {
        Map<String, Object> filters = new java.util.HashMap<>();
        
        if (query.getStartTime() != null) {
            filters.put("startTime", query.getStartTime());
        }
        if (query.getEndTime() != null) {
            filters.put("endTime", query.getEndTime());
        }
        if (query.getSourceIp() != null) {
            filters.put("sourceIp", query.getSourceIp());
        }
        if (query.getDestinationIp() != null) {
            filters.put("destinationIp", query.getDestinationIp());
        }
        if (query.getProtocol() != null) {
            filters.put("protocol", query.getProtocol());
        }
        if (query.getAppId() != null) {
            filters.put("appId", query.getAppId());
        }
        
        return filters;
    }
}
package com.geeksec.session.application.assemblers;

import com.geeksec.session.domain.entities.Session;
import com.geeksec.session.domain.valueobjects.*;
import com.geeksec.session.application.commands.CreateSessionCommand;
import com.geeksec.session.application.commands.UpdateSessionCommand;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 会话装配器
 * 负责在应用层进行 DTO 和领域对象之间的转换
 */
public class SessionAssembler {
    
    /**
     * 从创建命令构建会话实体
     */
    public static Session fromCreateCommand(CreateSessionCommand command) {
        if (command == null || !command.isValid()) {
            throw new IllegalArgumentException("无效的创建会话命令");
        }
        
        Session session = new Session();
        session.setSessionId(command.getSessionId());
        
        // 创建源端点
        NetworkEndpoint sourceEndpoint = createNetworkEndpoint(
            command.getSourceIp(), 
            command.getSourcePort(), 
            command.getSourceMac(), 
            command.getSourceInternal()
        );
        session.setSourceEndpoint(sourceEndpoint);
        
        // 创建目标端点
        NetworkEndpoint destEndpoint = createNetworkEndpoint(
            command.getDestinationIp(), 
            command.getDestinationPort(), 
            command.getDestinationMac(), 
            command.getDestinationInternal()
        );
        session.setDestinationEndpoint(destEndpoint);
        
        session.setProtocol(command.getProtocol());
        session.setSessionStartTime(command.getSessionStartTime());
        session.setSessionEndTime(command.getSessionEndTime());
        session.setAppId(command.getAppId());
        session.setAppName(command.getAppName());
        session.setServerIp(command.getServerIp());
        session.setTaskId(command.getTaskId());
        session.setBatchId(command.getBatchId());
        session.setThreadId(command.getThreadId());
        
        // 创建统计信息 - 使用简单的构造方式
        SessionStatistics statistics = null; // 暂时设为null，后续可以完善
        session.setStatistics(statistics);
        
        // 创建指纹信息 - 使用简单的构造方式
        SessionFingerprint fingerprint = null; // 暂时设为null，后续可以完善
        session.setFingerprint(fingerprint);
        
        // 创建协议元数据 - 使用简单的构造方式
        ProtocolMetadata protocolMetadata = null; // 暂时设为null，后续可以完善
        session.setProtocolMetadata(protocolMetadata);
        
        // 设置标签
        if (command.getLabels() != null) {
            session.setLabels(new ArrayList<>(command.getLabels()));
        }
        if (command.getRuleLabels() != null) {
            session.setRuleLabels(new ArrayList<>(command.getRuleLabels()));
        }
        
        session.setExtensionData(command.getExtensionData());
        session.setCreateTime(LocalDateTime.now());
        session.setUpdateTime(LocalDateTime.now());
        
        return session;
    }
    
    /**
     * 应用更新命令到会话实体
     */
    public static Session applyUpdateCommand(Session session, UpdateSessionCommand command) {
        if (session == null || command == null || !command.isValid()) {
            throw new IllegalArgumentException("无效的会话或更新命令");
        }
        
        // 更新结束时间
        if (command.hasEndTimeUpdate()) {
            session.setSessionEndTime(command.sessionEndTime());
        }
        
        // 更新统计信息
        if (command.hasStatisticsUpdate()) {
            session.setStatistics(command.statistics());
        }
        
        // 更新指纹信息
        if (command.hasFingerprintUpdate()) {
            session.setFingerprint(command.fingerprint());
        }
        
        // 更新协议元数据
        if (command.hasProtocolMetadataUpdate()) {
            session.setProtocolMetadata(command.protocolMetadata());
        }
        
        // 更新标签
        if (command.hasLabelsUpdate()) {
            session.setLabels(new ArrayList<Integer>(command.labels()));
        }
        
        // 更新规则标签
        if (command.hasRuleLabelsUpdate()) {
            session.setRuleLabels(new ArrayList<Integer>(command.ruleLabels()));
        }
        
        // 更新状态 - 注意：Session 实体中没有 status 字段，这里可能需要扩展
        if (command.hasStatusUpdate()) {
            // 可以通过扩展数据存储状态
            if (session.getExtensionData() == null) {
                session.setExtensionData(new java.util.HashMap<>());
            }
            session.getExtensionData().put("status", command.status());
        }
        
        // 更新修改时间
        session.setUpdateTime(LocalDateTime.now());
        
        return session;
    }
    
    /**
     * 创建网络端点值对象
     */
    public static NetworkEndpoint createNetworkEndpoint(String ip, Integer port, String mac, Boolean isInternal) {
        return new NetworkEndpoint(
            ip,
            port,
            mac,
            isInternal != null ? isInternal : false
        );
    }
}
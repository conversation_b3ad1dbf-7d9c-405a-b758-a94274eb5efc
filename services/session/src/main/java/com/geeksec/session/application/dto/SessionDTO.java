package com.geeksec.session.application.dto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会话数据传输对象
 */
public class SessionDTO {
    private String sessionId;
    private String sourceIp;
    private Integer sourcePort;
    private String sourceMac;
    private Boolean sourceInternal;
    private String destinationIp;
    private Integer destinationPort;
    private String destinationMac;
    private Boolean destinationInternal;
    private String protocol;
    private LocalDateTime sessionStartTime;
    private LocalDateTime sessionEndTime;
    private Integer appId;
    private String appName;
    private String serverIp;
    private String taskId;
    private String batchId;
    private String threadId;
    private List<Integer> labels;
    private List<Integer> ruleLabels;
    private Map<String, Object> extensionData;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    // 统计信息
    private Long totalPackets;
    private Long totalBytes;
    private Long sourcePackets;
    private Long sourceBytes;
    private Long destinationPackets;
    private Long destinationBytes;
    
    // 指纹信息
    private String tcpFingerprint;
    private String httpFingerprint;
    private String sslFingerprint;
    
    // 协议元数据
    private Map<String, Object> httpMetadata;
    private Map<String, Object> sslMetadata;
    private Map<String, Object> dnsMetadata;
    
    // 业务字段
    private String status;
    private String riskLevel;
    private Boolean suspicious;
    private String networkDirection;
    private Long durationInSeconds;
    private Boolean active;
    
    // 构造函数
    public SessionDTO() {}
    
    // Getter 和 Setter 方法
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public String getSourceIp() {
        return sourceIp;
    }
    
    public void setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
    }
    
    public Integer getSourcePort() {
        return sourcePort;
    }
    
    public void setSourcePort(Integer sourcePort) {
        this.sourcePort = sourcePort;
    }
    
    public String getSourceMac() {
        return sourceMac;
    }
    
    public void setSourceMac(String sourceMac) {
        this.sourceMac = sourceMac;
    }
    
    public Boolean getSourceInternal() {
        return sourceInternal;
    }
    
    public void setSourceInternal(Boolean sourceInternal) {
        this.sourceInternal = sourceInternal;
    }
    
    public String getDestinationIp() {
        return destinationIp;
    }
    
    public void setDestinationIp(String destinationIp) {
        this.destinationIp = destinationIp;
    }
    
    public Integer getDestinationPort() {
        return destinationPort;
    }
    
    public void setDestinationPort(Integer destinationPort) {
        this.destinationPort = destinationPort;
    }
    
    public String getDestinationMac() {
        return destinationMac;
    }
    
    public void setDestinationMac(String destinationMac) {
        this.destinationMac = destinationMac;
    }
    
    public Boolean getDestinationInternal() {
        return destinationInternal;
    }
    
    public void setDestinationInternal(Boolean destinationInternal) {
        this.destinationInternal = destinationInternal;
    }
    
    public String getProtocol() {
        return protocol;
    }
    
    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }
    
    public LocalDateTime getSessionStartTime() {
        return sessionStartTime;
    }
    
    public void setSessionStartTime(LocalDateTime sessionStartTime) {
        this.sessionStartTime = sessionStartTime;
    }
    
    public LocalDateTime getSessionEndTime() {
        return sessionEndTime;
    }
    
    public void setSessionEndTime(LocalDateTime sessionEndTime) {
        this.sessionEndTime = sessionEndTime;
    }
    
    public Integer getAppId() {
        return appId;
    }
    
    public void setAppId(Integer appId) {
        this.appId = appId;
    }
    
    public String getAppName() {
        return appName;
    }
    
    public void setAppName(String appName) {
        this.appName = appName;
    }
    
    public String getServerIp() {
        return serverIp;
    }
    
    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }
    
    public String getTaskId() {
        return taskId;
    }
    
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    
    public String getBatchId() {
        return batchId;
    }
    
    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }
    
    public String getThreadId() {
        return threadId;
    }
    
    public void setThreadId(String threadId) {
        this.threadId = threadId;
    }
    
    public List<Integer> getLabels() {
        return labels;
    }
    
    public void setLabels(List<Integer> labels) {
        this.labels = labels;
    }
    
    public List<Integer> getRuleLabels() {
        return ruleLabels;
    }
    
    public void setRuleLabels(List<Integer> ruleLabels) {
        this.ruleLabels = ruleLabels;
    }
    
    public Map<String, Object> getExtensionData() {
        return extensionData;
    }
    
    public void setExtensionData(Map<String, Object> extensionData) {
        this.extensionData = extensionData;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public Long getTotalPackets() {
        return totalPackets;
    }
    
    public void setTotalPackets(Long totalPackets) {
        this.totalPackets = totalPackets;
    }
    
    public Long getTotalBytes() {
        return totalBytes;
    }
    
    public void setTotalBytes(Long totalBytes) {
        this.totalBytes = totalBytes;
    }
    
    public Long getSourcePackets() {
        return sourcePackets;
    }
    
    public void setSourcePackets(Long sourcePackets) {
        this.sourcePackets = sourcePackets;
    }
    
    public Long getSourceBytes() {
        return sourceBytes;
    }
    
    public void setSourceBytes(Long sourceBytes) {
        this.sourceBytes = sourceBytes;
    }
    
    public Long getDestinationPackets() {
        return destinationPackets;
    }
    
    public void setDestinationPackets(Long destinationPackets) {
        this.destinationPackets = destinationPackets;
    }
    
    public Long getDestinationBytes() {
        return destinationBytes;
    }
    
    public void setDestinationBytes(Long destinationBytes) {
        this.destinationBytes = destinationBytes;
    }
    
    public String getTcpFingerprint() {
        return tcpFingerprint;
    }
    
    public void setTcpFingerprint(String tcpFingerprint) {
        this.tcpFingerprint = tcpFingerprint;
    }
    
    public String getHttpFingerprint() {
        return httpFingerprint;
    }
    
    public void setHttpFingerprint(String httpFingerprint) {
        this.httpFingerprint = httpFingerprint;
    }
    
    public String getSslFingerprint() {
        return sslFingerprint;
    }
    
    public void setSslFingerprint(String sslFingerprint) {
        this.sslFingerprint = sslFingerprint;
    }
    
    public Map<String, Object> getHttpMetadata() {
        return httpMetadata;
    }
    
    public void setHttpMetadata(Map<String, Object> httpMetadata) {
        this.httpMetadata = httpMetadata;
    }
    
    public Map<String, Object> getSslMetadata() {
        return sslMetadata;
    }
    
    public void setSslMetadata(Map<String, Object> sslMetadata) {
        this.sslMetadata = sslMetadata;
    }
    
    public Map<String, Object> getDnsMetadata() {
        return dnsMetadata;
    }
    
    public void setDnsMetadata(Map<String, Object> dnsMetadata) {
        this.dnsMetadata = dnsMetadata;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getRiskLevel() {
        return riskLevel;
    }
    
    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }
    
    public Boolean getSuspicious() {
        return suspicious;
    }
    
    public void setSuspicious(Boolean suspicious) {
        this.suspicious = suspicious;
    }
    
    public String getNetworkDirection() {
        return networkDirection;
    }
    
    public void setNetworkDirection(String networkDirection) {
        this.networkDirection = networkDirection;
    }
    
    public Long getDurationInSeconds() {
        return durationInSeconds;
    }
    
    public void setDurationInSeconds(Long durationInSeconds) {
        this.durationInSeconds = durationInSeconds;
    }
    
    public Boolean getActive() {
        return active;
    }
    
    public void setActive(Boolean active) {
        this.active = active;
    }
}
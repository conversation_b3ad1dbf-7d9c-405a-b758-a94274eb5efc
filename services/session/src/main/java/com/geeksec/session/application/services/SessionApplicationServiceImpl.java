package com.geeksec.session.application.services;

import com.geeksec.session.application.assemblers.SessionAssembler;
import com.geeksec.session.application.commands.CreateSessionCommand;
import com.geeksec.session.application.commands.UpdateSessionCommand;
import com.geeksec.session.application.queries.SessionQuery;
import com.geeksec.session.domain.entities.Session;
import com.geeksec.session.domain.repositories.SessionRepository;
import com.geeksec.session.domain.services.SessionDomainService;
import com.geeksec.session.infrastructure.dto.SessionDTO;
import com.mybatisflex.core.paginate.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 会话应用服务实现类
 * 
 * 负责协调领域服务、仓储和组装器，实现业务用例
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Service
@Transactional
public class SessionApplicationServiceImpl implements SessionApplicationService {

    private final SessionRepository sessionRepository;
    private final SessionDomainService sessionDomainService;

    public SessionApplicationServiceImpl(
            SessionRepository sessionRepository,
            SessionDomainService sessionDomainService) {
        this.sessionRepository = sessionRepository;
        this.sessionDomainService = sessionDomainService;
    }

    // ==================== 命令处理 ====================

    @Override
    public SessionDTO createSession(CreateSessionCommand command) {
        // 1. 验证命令
        if (!command.isValid()) {
            throw new IllegalArgumentException("Invalid create session command: " + 
                String.join(", ", command.getValidationErrors()));
        }

        // 2. 检查会话是否已存在
        if (command.getSessionId() != null && sessionRepository.existsById(command.getSessionId())) {
            throw new IllegalStateException("Session already exists: " + command.getSessionId());
        }

        // 3. 使用组装器创建领域对象
        Session session = SessionAssembler.fromCreateCommand(command);

        // 4. 应用领域逻辑
        session = sessionDomainService.createSession(session);

        // 5. 持久化
        session = sessionRepository.save(session);

        // 6. 转换为 DTO 返回
        return convertToDTO(session);
    }

    @Override
    public SessionDTO updateSession(UpdateSessionCommand command) {
        // 1. 验证命令
        if (!command.isValid()) {
            throw new IllegalArgumentException("Invalid update session command: " + 
                String.join(", ", command.getValidationErrors()));
        }

        // 2. 查找现有会话
        Session existingSession = sessionRepository.findById(command.getSessionId())
            .orElseThrow(() -> new IllegalArgumentException("Session not found: " + command.getSessionId()));

        // 3. 应用更新
        SessionAssembler.applyUpdateCommand(existingSession, command);

        // 4. 应用领域逻辑
        existingSession = sessionDomainService.updateSession(existingSession);

        // 5. 持久化
        existingSession = sessionRepository.save(existingSession);

        // 6. 转换为 DTO 返回
        return convertToDTO(existingSession);
    }

    @Override
    public void deleteSession(String sessionId) {
        // 1. 检查会话是否存在
        if (!sessionRepository.existsById(sessionId)) {
            throw new IllegalArgumentException("Session not found: " + sessionId);
        }

        // 2. 应用领域逻辑（如果需要）
        sessionDomainService.deleteSession(sessionId);

        // 3. 删除
        sessionRepository.deleteById(sessionId);
    }

    @Override
    public SessionDTO endSession(String sessionId, LocalDateTime endTime) {
        // 1. 查找现有会话
        Session session = sessionRepository.findById(sessionId)
            .orElseThrow(() -> new IllegalArgumentException("Session not found: " + sessionId));

        // 2. 应用领域逻辑
        session = sessionDomainService.endSession(session, endTime);

        // 3. 持久化
        session = sessionRepository.save(session);

        // 4. 转换为 DTO 返回
        return convertToDTO(session);
    }

    // ==================== 查询处理 ====================

    @Override
    @Transactional(readOnly = true)
    public Optional<SessionDTO> getSessionById(String sessionId) {
        return sessionRepository.findById(sessionId)
            .map(this::convertToDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SessionDTO> getAllSessions() {
        return sessionRepository.findAll().stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<SessionDTO> searchSessions(SessionQuery query) {
        Page<Session> sessionPage = sessionRepository.searchSessions(query);
        
        // 转换分页结果
        List<SessionDTO> dtoList = sessionPage.getRecords().stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
        
        // 创建新的分页对象
        Page<SessionDTO> dtoPage = new Page<>();
        dtoPage.setRecords(dtoList);
        dtoPage.setPageNumber(sessionPage.getPageNumber());
        dtoPage.setPageSize(sessionPage.getPageSize());
        dtoPage.setTotalRow(sessionPage.getTotalRow());
        dtoPage.setTotalPage(sessionPage.getTotalPage());
        
        return dtoPage;
    }

    @Override
    @Transactional(readOnly = true)
    public List<SessionDTO> getSessionsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return sessionRepository.findByTimeRange(startTime, endTime).stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<SessionDTO> getSessionsBySourceIp(String sourceIp) {
        return sessionRepository.findBySourceIp(sourceIp).stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<SessionDTO> getSessionsByDestinationIp(String destinationIp) {
        return sessionRepository.findByDestinationIp(destinationIp).stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<SessionDTO> getSessionsByAppId(Integer appId) {
        return sessionRepository.findByAppId(appId).stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<SessionDTO> getActiveSessions() {
        return sessionRepository.findActiveSessions().stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<SessionDTO> getSuspiciousSessions(LocalDateTime startTime, LocalDateTime endTime) {
        return sessionRepository.findSuspiciousSessions(startTime, endTime).stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
    }

    // ==================== 统计查询 ====================

    @Override
    @Transactional(readOnly = true)
    public long countSessionsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return sessionRepository.countByTimeRange(startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Long> getProtocolDistribution(LocalDateTime startTime, LocalDateTime endTime) {
        return sessionRepository.getProtocolDistribution(startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Long> getApplicationDistribution(LocalDateTime startTime, LocalDateTime endTime) {
        return sessionRepository.getApplicationDistribution(startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getTopSourceIps(LocalDateTime startTime, LocalDateTime endTime, int limit) {
        return sessionRepository.getTopSourceIps(startTime, endTime, limit);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getTopDestinationIps(LocalDateTime startTime, LocalDateTime endTime, int limit) {
        return sessionRepository.getTopDestinationIps(startTime, endTime, limit);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getSessionTrends(LocalDateTime startTime, LocalDateTime endTime, String interval) {
        return sessionRepository.getSessionTrends(startTime, endTime, interval);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> aggregateSessionData(LocalDateTime startTime, LocalDateTime endTime, List<String> groupByFields) {
        return sessionRepository.aggregateSessionData(startTime, endTime, groupByFields);
    }

    // ==================== 批量操作 ====================

    @Override
    public List<SessionDTO> createSessionsBatch(List<CreateSessionCommand> commands) {
        return commands.stream()
            .map(this::createSession)
            .collect(Collectors.toList());
    }

    @Override
    public List<SessionDTO> updateSessionsBatch(List<UpdateSessionCommand> commands) {
        return commands.stream()
            .map(this::updateSession)
            .collect(Collectors.toList());
    }

    @Override
    public void deleteSessionsBatch(List<String> sessionIds) {
        sessionIds.forEach(this::deleteSession);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 将领域对象转换为 DTO
     */
    private SessionDTO convertToDTO(Session session) {
        SessionDTO dto = new SessionDTO();
        
        // 基本信息
        dto.setSessionId(session.getSessionId());
        dto.setSourceIp(session.getSourceEndpoint() != null ? session.getSourceEndpoint().getIp() : null);
        dto.setSourcePort(session.getSourceEndpoint() != null ? session.getSourceEndpoint().getPort() : null);
        dto.setSourceMac(session.getSourceEndpoint() != null ? session.getSourceEndpoint().getMac() : null);
        dto.setDestinationIp(session.getDestinationEndpoint() != null ? session.getDestinationEndpoint().getIp() : null);
        dto.setDestinationPort(session.getDestinationEndpoint() != null ? session.getDestinationEndpoint().getPort() : null);
        dto.setDestinationMac(session.getDestinationEndpoint() != null ? session.getDestinationEndpoint().getMac() : null);
        
        dto.setProtocol(session.getProtocol());
        dto.setSessionStartTime(session.getSessionStartTime());
        dto.setSessionEndTime(session.getSessionEndTime());
        
        // 应用信息
        dto.setAppId(session.getAppId());
        dto.setAppName(session.getAppName());
        dto.setServerIp(session.getServerIp());
        
        // 任务信息
        dto.setTaskId(session.getTaskId());
        dto.setBatchId(session.getBatchId());
        dto.setThreadId(session.getThreadId());
        
        // 统计信息
        if (session.getStatistics() != null) {
            dto.setPacketCount(session.getStatistics().getPacketCount());
            dto.setByteCount(session.getStatistics().getByteCount());
            dto.setRuleLevel(session.getStatistics().getRuleLevel());
        }
        
        // 指纹信息
        if (session.getFingerprint() != null) {
            dto.setTcpFingerprint(session.getFingerprint().getTcpFingerprint());
            dto.setHttpFingerprint(session.getFingerprint().getHttpFingerprint());
            dto.setSslFingerprint(session.getFingerprint().getSslFingerprint());
            dto.setUserAgent(session.getFingerprint().getUserAgent());
            dto.setDeviceFingerprint(session.getFingerprint().getDeviceFingerprint());
        }
        
        // 协议元数据
        if (session.getProtocolMetadata() != null) {
            dto.setHttpStatusCode(session.getProtocolMetadata().getHttpStatusCode());
            dto.setHttpMethod(session.getProtocolMetadata().getHttpMethod());
            dto.setHttpUrl(session.getProtocolMetadata().getHttpUrl());
            dto.setSslVersion(session.getProtocolMetadata().getSslVersion());
            dto.setSslCipherSuite(session.getProtocolMetadata().getSslCipherSuite());
        }
        
        // 标签和扩展数据
        dto.setLabels(session.getLabels());
        dto.setRuleLabels(session.getRuleLabels());
        dto.setExtensionData(session.getExtensionData());
        
        // 系统字段
        dto.setCreateTime(session.getCreateTime());
        dto.setUpdateTime(session.getUpdateTime());
        
        return dto;
    }
}
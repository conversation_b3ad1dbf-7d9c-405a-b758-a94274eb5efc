package com.geeksec.session.interfaces.assembler;

import com.geeksec.session.domain.entities.Session;
import com.geeksec.session.application.dto.SessionDTO;
import com.geeksec.session.application.dto.CreateSessionCommand;
import com.geeksec.session.application.dto.UpdateSessionCommand;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Session 组装器
 * 负责实体和 DTO 之间的转换
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class SessionAssembler {
    
    /**
     * 实体转 DTO
     */
    public SessionDTO toDTO(Session session) {
        if (session == null) {
            return null;
        }
        
        SessionDTO dto = new SessionDTO();
        dto.setId(session.getSessionId());
        dto.setStartTime(session.getSessionStartTime());
        dto.setEndTime(session.getSessionEndTime());
        dto.setProtocol(session.getProtocol());
        dto.setApplicationName(session.getAppName());
        dto.setApplicationId(session.getAppId());
        dto.setTaskId(session.getTaskId());
        dto.setBatchId(session.getBatchId());
        dto.setThreadId(session.getThreadId());
        dto.setLabels(session.getLabels());
        dto.setExtensionData(session.getExtensionData());
        dto.setSourceEndpoint(session.getSourceEndpoint());
        dto.setDestinationEndpoint(session.getDestinationEndpoint());
        dto.setStatistics(session.getStatistics());
        dto.setFingerprint(session.getFingerprint());
        dto.setProtocolMetadata(session.getProtocolMetadata());
        dto.setStatus(session.isActive() ? "ACTIVE" : "CLOSED");
        dto.setRiskLevel("LOW"); // 默认风险等级
        dto.setSuspicious(session.isSuspicious());
        dto.setDirection(session.getNetworkDirection());
        dto.setCreatedAt(session.getCreateTime());
        dto.setUpdatedAt(session.getUpdateTime());
        dto.setVersion(1L); // 默认版本
        
        return dto;
    }
    
    /**
     * 实体列表转 DTO 列表
     */
    public List<SessionDTO> toDTOList(List<Session> sessions) {
        if (sessions == null) {
            return null;
        }
        return sessions.stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
    }
    
    /**
     * 创建命令转实体
     */
    public Session fromCreateCommand(CreateSessionCommand command) {
        if (command == null) {
            return null;
        }
        
        Session session = new Session();
        session.setSessionStartTime(command.getStartTime());
        session.setSessionEndTime(command.getEndTime());
        session.setProtocol(command.getProtocol());
        session.setAppName(command.getApplicationName());
        session.setAppId(command.getApplicationId());
        session.setTaskId(command.getTaskId());
        session.setBatchId(command.getBatchId());
        session.setThreadId(command.getThreadId());
        session.setLabels(command.getLabels());
        session.setExtensionData(command.getExtensionData());
        session.setSourceEndpoint(command.getSourceEndpoint());
        session.setDestinationEndpoint(command.getDestinationEndpoint());
        session.setStatistics(command.getStatistics());
        session.setFingerprint(command.getFingerprint());
        session.setProtocolMetadata(command.getProtocolMetadata());
        
        LocalDateTime now = LocalDateTime.now();
        session.setCreateTime(now);
        session.setUpdateTime(now);
        
        return session;
    }
    
    /**
     * 更新命令应用到实体
     */
    public void applyUpdateCommand(Session session, UpdateSessionCommand command) {
        if (session == null || command == null) {
            return;
        }
        
        if (command.getEndTime() != null) {
            session.setSessionEndTime(command.getEndTime());
        }
        if (command.getApplicationName() != null) {
            session.setAppName(command.getApplicationName());
        }
        if (command.getApplicationId() != null) {
            session.setAppId(command.getApplicationId());
        }
        if (command.getTaskId() != null) {
            session.setTaskId(command.getTaskId());
        }
        if (command.getBatchId() != null) {
            session.setBatchId(command.getBatchId());
        }
        if (command.getThreadId() != null) {
            session.setThreadId(command.getThreadId());
        }
        if (command.getLabels() != null) {
            session.setLabels(command.getLabels());
        }
        if (command.getExtensionData() != null) {
            session.setExtensionData(command.getExtensionData());
        }
        if (command.getStatistics() != null) {
            session.setStatistics(command.getStatistics());
        }
        if (command.getFingerprint() != null) {
            session.setFingerprint(command.getFingerprint());
        }
        if (command.getProtocolMetadata() != null) {
            session.setProtocolMetadata(command.getProtocolMetadata());
        }
        // 业务状态更新通过领域方法处理
        if (command.getEndTime() != null) {
            session.endSession(command.getEndTime());
        }
        
        session.setUpdateTime(LocalDateTime.now());
    }
}
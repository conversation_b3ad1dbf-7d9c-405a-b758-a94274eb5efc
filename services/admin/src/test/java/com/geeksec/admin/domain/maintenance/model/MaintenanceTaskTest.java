package com.geeksec.admin.domain.maintenance.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 维护任务测试类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@DisplayName("维护任务测试")
class MaintenanceTaskTest {
    
    private MaintenanceTask task;
    
    @BeforeEach
    void setUp() {
        task = new MaintenanceTask()
                .setTaskName("测试任务")
                .setTaskType(MaintenanceTask.TaskType.DATA_CLEANUP)
                .setStatus(MaintenanceTask.TaskStatus.PENDING)
                .setPriority(MaintenanceTask.TaskPriority.NORMAL)
                .setTargetTable("test_table")
                .setScheduledTime(LocalDateTime.now())
                .setCreatedBy("test_user");
    }
    
    @Test
    @DisplayName("开始执行任务")
    void start_whenCalled_thenUpdateStatusAndTime() {
        // Given
        String executor = "test_executor";
        
        // When
        task.start(executor);
        
        // Then
        assertEquals(MaintenanceTask.TaskStatus.RUNNING, task.getStatus());
        assertEquals(0, task.getProgress());
        assertEquals(executor, task.getUpdatedBy());
        assertNotNull(task.getStartTime());
        assertNotNull(task.getUpdateTime());
    }
    
    @Test
    @DisplayName("完成任务")
    void complete_whenCalled_thenUpdateStatusAndResult() {
        // Given
        String result = "任务执行成功";
        String executor = "test_executor";
        task.start(executor);
        
        // When
        task.complete(result, executor);
        
        // Then
        assertEquals(MaintenanceTask.TaskStatus.COMPLETED, task.getStatus());
        assertEquals(100, task.getProgress());
        assertEquals(result, task.getResult());
        assertEquals(executor, task.getUpdatedBy());
        assertNotNull(task.getEndTime());
    }
    
    @Test
    @DisplayName("任务失败")
    void fail_whenCalled_thenUpdateStatusAndError() {
        // Given
        String errorMessage = "任务执行失败";
        String executor = "test_executor";
        task.start(executor);
        
        // When
        task.fail(errorMessage, executor);
        
        // Then
        assertEquals(MaintenanceTask.TaskStatus.FAILED, task.getStatus());
        assertEquals(errorMessage, task.getErrorMessage());
        assertEquals(executor, task.getUpdatedBy());
        assertNotNull(task.getEndTime());
    }
    
    @Test
    @DisplayName("取消任务")
    void cancel_whenCalled_thenUpdateStatus() {
        // Given
        String executor = "test_executor";
        
        // When
        task.cancel(executor);
        
        // Then
        assertEquals(MaintenanceTask.TaskStatus.CANCELLED, task.getStatus());
        assertEquals(executor, task.getUpdatedBy());
        assertNotNull(task.getEndTime());
    }
    
    @Test
    @DisplayName("更新进度")
    void updateProgress_whenCalled_thenUpdateProgressAndTime() {
        // Given
        Integer progress = 50;
        String executor = "test_executor";
        
        // When
        task.updateProgress(progress, executor);
        
        // Then
        assertEquals(progress, task.getProgress());
        assertEquals(executor, task.getUpdatedBy());
        assertNotNull(task.getUpdateTime());
    }
    
    @Test
    @DisplayName("检查任务是否可以执行 - 待执行状态")
    void canExecute_whenStatusPending_thenReturnTrue() {
        // Given
        task.setStatus(MaintenanceTask.TaskStatus.PENDING);
        
        // When
        boolean canExecute = task.canExecute();
        
        // Then
        assertTrue(canExecute);
    }
    
    @Test
    @DisplayName("检查任务是否可以执行 - 暂停状态")
    void canExecute_whenStatusPaused_thenReturnTrue() {
        // Given
        task.setStatus(MaintenanceTask.TaskStatus.PAUSED);
        
        // When
        boolean canExecute = task.canExecute();
        
        // Then
        assertTrue(canExecute);
    }
    
    @Test
    @DisplayName("检查任务是否可以执行 - 运行中状态")
    void canExecute_whenStatusRunning_thenReturnFalse() {
        // Given
        task.setStatus(MaintenanceTask.TaskStatus.RUNNING);
        
        // When
        boolean canExecute = task.canExecute();
        
        // Then
        assertFalse(canExecute);
    }
    
    @Test
    @DisplayName("检查任务是否正在执行")
    void isRunning_whenStatusRunning_thenReturnTrue() {
        // Given
        task.setStatus(MaintenanceTask.TaskStatus.RUNNING);
        
        // When
        boolean isRunning = task.isRunning();
        
        // Then
        assertTrue(isRunning);
    }
    
    @Test
    @DisplayName("检查任务是否已完成")
    void isCompleted_whenStatusCompleted_thenReturnTrue() {
        // Given
        task.setStatus(MaintenanceTask.TaskStatus.COMPLETED);
        
        // When
        boolean isCompleted = task.isCompleted();
        
        // Then
        assertTrue(isCompleted);
    }
    
    @Test
    @DisplayName("检查任务是否已完成 - 失败状态")
    void isCompleted_whenStatusFailed_thenReturnTrue() {
        // Given
        task.setStatus(MaintenanceTask.TaskStatus.FAILED);
        
        // When
        boolean isCompleted = task.isCompleted();
        
        // Then
        assertTrue(isCompleted);
    }
    
    @Test
    @DisplayName("获取执行时长")
    void getExecutionDurationMinutes_whenTaskCompleted_thenReturnDuration() {
        // Given
        LocalDateTime startTime = LocalDateTime.now().minusMinutes(30);
        LocalDateTime endTime = LocalDateTime.now();
        task.setStartTime(startTime);
        task.setEndTime(endTime);
        
        // When
        Long duration = task.getExecutionDurationMinutes();
        
        // Then
        assertNotNull(duration);
        assertEquals(30L, duration);
    }
    
    @Test
    @DisplayName("获取执行时长 - 未开始")
    void getExecutionDurationMinutes_whenTaskNotStarted_thenReturnNull() {
        // Given
        task.setStartTime(null);
        
        // When
        Long duration = task.getExecutionDurationMinutes();
        
        // Then
        assertNull(duration);
    }
}

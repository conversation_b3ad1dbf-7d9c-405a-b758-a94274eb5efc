package com.geeksec.admin.domain.maintenance.model;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据生命周期配置测试类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@DisplayName("数据生命周期配置测试")
class DataLifecycleConfigTest {
    
    private DataLifecycleConfig config;
    
    @BeforeEach
    void setUp() {
        config = new DataLifecycleConfig()
                .setTableName("test_table")
                .setRetentionDays(90)
                .setAutoPartitionEnabled(true)
                .setPartitionColumn("create_time")
                .setPartitionGranularity(DataLifecycleConfig.PartitionGranularity.DAY)
                .setHotPartitionNum(7)
                .setWarmPartitionNum(30)
                .setColdPartitionNum(53)
                .setAutoCleanupEnabled(true)
                .setCompressionEnabled(true)
                .setCompressionDelayHours(24)
                .setCompressionThreshold(0.8);
    }
    
    @Test
    @DisplayName("验证有效配置")
    void validate_whenValidConfig_thenReturnValid() {
        // When
        ValidationResult result = config.validate();
        
        // Then
        assertTrue(result.isValid());
        assertTrue(result.getErrors().isEmpty());
    }
    
    @Test
    @DisplayName("验证表名为空时失败")
    void validate_whenTableNameEmpty_thenReturnInvalid() {
        // Given
        config.setTableName("");
        
        // When
        ValidationResult result = config.validate();
        
        // Then
        assertFalse(result.isValid());
        assertTrue(result.getErrors().contains("表名不能为空"));
    }
    
    @Test
    @DisplayName("验证保留天数为负数时失败")
    void validate_whenRetentionDaysNegative_thenReturnInvalid() {
        // Given
        config.setRetentionDays(-1);
        
        // When
        ValidationResult result = config.validate();
        
        // Then
        assertFalse(result.isValid());
        assertTrue(result.getErrors().contains("数据保留天数必须大于0"));
    }
    
    @Test
    @DisplayName("验证启用自动分区但分区列为空时失败")
    void validate_whenAutoPartitionEnabledButColumnEmpty_thenReturnInvalid() {
        // Given
        config.setAutoPartitionEnabled(true);
        config.setPartitionColumn("");
        
        // When
        ValidationResult result = config.validate();
        
        // Then
        assertFalse(result.isValid());
        assertTrue(result.getErrors().contains("启用自动分区时，分区列不能为空"));
    }
    
    @Test
    @DisplayName("验证压缩阈值超出范围时失败")
    void validate_whenCompressionThresholdOutOfRange_thenReturnInvalid() {
        // Given
        config.setCompressionThreshold(1.5);
        
        // When
        ValidationResult result = config.validate();
        
        // Then
        assertFalse(result.isValid());
        assertTrue(result.getErrors().contains("压缩阈值必须在0-1之间"));
    }
    
    @Test
    @DisplayName("计算总分区数量")
    void getTotalPartitionNum_whenAllPartitionNumsSet_thenReturnSum() {
        // When
        int total = config.getTotalPartitionNum();
        
        // Then
        assertEquals(90, total); // 7 + 30 + 53
    }
    
    @Test
    @DisplayName("检查是否需要清理")
    void needsCleanup_whenAutoCleanupEnabled_thenReturnTrue() {
        // Given
        config.setAutoCleanupEnabled(true);
        
        // When
        boolean needsCleanup = config.needsCleanup();
        
        // Then
        assertTrue(needsCleanup);
    }
    
    @Test
    @DisplayName("检查是否需要压缩")
    void needsCompression_whenCompressionEnabled_thenReturnTrue() {
        // Given
        config.setCompressionEnabled(true);
        
        // When
        boolean needsCompression = config.needsCompression();
        
        // Then
        assertTrue(needsCompression);
    }
    
    @Test
    @DisplayName("更新配置")
    void updateConfig_whenCalled_thenUpdateTimestampAndUser() {
        // Given
        String updatedBy = "test_user";
        
        // When
        config.updateConfig(updatedBy);
        
        // Then
        assertEquals(updatedBy, config.getUpdatedBy());
        assertNotNull(config.getUpdateTime());
    }
}

package com.geeksec.admin.application;

import com.geeksec.admin.domain.maintenance.DataMaintenanceDomainService;
import com.geeksec.admin.domain.maintenance.model.PartitionInfo;
import com.geeksec.admin.domain.maintenance.model.StorageStats;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 数据维护应用服务测试类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("数据维护应用服务测试")
class DataMaintenanceApplicationServiceTest {
    
    @Mock
    private DataMaintenanceDomainService dataMaintenanceDomainService;
    
    @InjectMocks
    private DataMaintenanceApplicationService dataMaintenanceApplicationService;
    
    private PartitionInfo testPartition;
    private StorageStats testStorageStats;
    
    @BeforeEach
    void setUp() {
        testPartition = new PartitionInfo()
                .setPartitionName("p20240101")
                .setTableName("test_table")
                .setPartitionType(PartitionInfo.PartitionType.RANGE)
                .setStatus(PartitionInfo.PartitionStatus.NORMAL)
                .setSizeBytes(1024L * 1024 * 1024) // 1GB
                .setRowCount(100000L)
                .setCreateTime(LocalDateTime.now().minusDays(1))
                .setLastModifyTime(LocalDateTime.now())
                .setIsHot(true)
                .setCompressionRatio(0.8);
        
        testStorageStats = new StorageStats()
                .setTotalStorageBytes(1024L * 1024 * 1024 * 1024) // 1TB
                .setUsedStorageBytes(768L * 1024 * 1024 * 1024) // 768GB
                .setAvailableStorageBytes(256L * 1024 * 1024 * 1024) // 256GB
                .setUsagePercentage(75.0)
                .setSessionLogsSizeBytes(512L * 1024 * 1024 * 1024) // 512GB
                .setStatsTime(LocalDateTime.now());
    }
    
    @Test
    @DisplayName("获取表分区信息")
    void getTablePartitions_whenCalled_thenReturnPartitionList() {
        // Given
        String tableName = "test_table";
        when(dataMaintenanceDomainService.getTablePartitions(tableName))
                .thenReturn(List.of(testPartition));
        
        // When
        List<Map<String, Object>> result = dataMaintenanceApplicationService.getTablePartitions(tableName);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        Map<String, Object> partitionMap = result.get(0);
        assertEquals("p20240101", partitionMap.get("partition_name"));
        assertEquals("test_table", partitionMap.get("table_name"));
        assertEquals("范围分区", partitionMap.get("partition_type"));
        assertEquals("正常", partitionMap.get("status"));
        assertEquals(1.0, partitionMap.get("size_gb"));
        assertEquals(100000L, partitionMap.get("row_count"));
        assertEquals(true, partitionMap.get("is_hot"));
        assertEquals(0.8, partitionMap.get("compression_ratio"));
    }
    
    @Test
    @DisplayName("获取会话日志表分区信息")
    void getSessionLogPartitions_whenCalled_thenReturnPartitionList() {
        // Given
        when(dataMaintenanceDomainService.getTablePartitions("dwd_session_logs"))
                .thenReturn(List.of(testPartition));
        
        // When
        List<Map<String, Object>> result = dataMaintenanceApplicationService.getSessionLogPartitions();
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
    }
    
    @Test
    @DisplayName("获取分区存储统计信息")
    void getPartitionStorageStats_whenCalled_thenReturnStorageStats() {
        // Given
        String tableName = "test_table";
        when(dataMaintenanceDomainService.getTableStorageStats(tableName))
                .thenReturn(testStorageStats);
        
        // When
        Map<String, Object> result = dataMaintenanceApplicationService.getPartitionStorageStats(tableName);
        
        // Then
        assertNotNull(result);
        assertEquals(1024.0, result.get("total_storage_gb"));
        assertEquals(768.0, result.get("used_storage_gb"));
        assertEquals(256.0, result.get("available_storage_gb"));
        assertEquals(75.0, result.get("usage_percentage"));
        assertEquals(512.0, result.get("session_logs_size_gb"));
        assertEquals("健康", result.get("health_status"));
    }
    
    @Test
    @DisplayName("获取存储空间使用情况")
    void getStorageUsage_whenCalled_thenReturnStorageUsage() {
        // Given
        when(dataMaintenanceDomainService.getStorageStats())
                .thenReturn(testStorageStats);
        
        // When
        Map<String, Object> result = dataMaintenanceApplicationService.getStorageUsage();
        
        // Then
        assertNotNull(result);
        assertEquals(1024.0, result.get("total_storage_gb"));
        assertEquals(768.0, result.get("used_storage_gb"));
        assertEquals(256.0, result.get("available_storage_gb"));
        assertEquals(75.0, result.get("usage_percentage"));
    }
    
    @Test
    @DisplayName("检查过期分区")
    void checkExpiredPartitions_whenCalled_thenReturnExpiredPartitions() {
        // Given
        String tableName = "test_table";
        int retentionDays = 30;
        
        // 创建一个过期的分区
        PartitionInfo expiredPartition = new PartitionInfo()
                .setPartitionName("p20231201")
                .setTableName(tableName)
                .setCreateTime(LocalDateTime.now().minusDays(35)); // 35天前创建，超过30天保留期
        
        when(dataMaintenanceDomainService.checkExpiredPartitions(tableName, retentionDays))
                .thenReturn(List.of(expiredPartition));
        
        // When
        List<Map<String, Object>> result = dataMaintenanceApplicationService.checkExpiredPartitions(tableName, retentionDays);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        Map<String, Object> partitionMap = result.get(0);
        assertEquals("p20231201", partitionMap.get("partition_name"));
        assertEquals(tableName, partitionMap.get("table_name"));
    }
    
    @Test
    @DisplayName("获取数据清理状态")
    void getDataCleanupStatus_whenCalled_thenReturnCleanupStatus() {
        // Given
        when(dataMaintenanceDomainService.getStorageStats())
                .thenReturn(testStorageStats);
        
        // When
        Map<String, Object> result = dataMaintenanceApplicationService.getDataCleanupStatus();
        
        // Then
        assertNotNull(result);
        assertEquals(true, result.get("auto_cleanup_enabled"));
        assertEquals(24, result.get("cleanup_interval_hours"));
        assertEquals(75.0, result.get("storage_usage_percentage"));
        assertEquals("健康", result.get("health_status"));
        assertNotNull(result.get("last_cleanup_time"));
        assertNotNull(result.get("next_cleanup_time"));
    }
    
    @Test
    @DisplayName("获取数据增长趋势")
    void getDataGrowthTrend_whenCalled_thenReturnTrendData() {
        // Given
        int days = 7;
        when(dataMaintenanceDomainService.calculateDataGrowthTrend("dwd_session_logs", days))
                .thenReturn(List.of(testStorageStats));
        
        // When
        List<Map<String, Object>> result = dataMaintenanceApplicationService.getDataGrowthTrend(days);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        Map<String, Object> statsMap = result.get(0);
        assertEquals(1024.0, statsMap.get("total_storage_gb"));
        assertEquals(768.0, statsMap.get("used_storage_gb"));
        assertEquals(75.0, statsMap.get("usage_percentage"));
    }
}

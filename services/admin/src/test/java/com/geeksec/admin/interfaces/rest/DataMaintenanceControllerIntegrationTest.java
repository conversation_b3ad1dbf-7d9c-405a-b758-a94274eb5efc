package com.geeksec.admin.interfaces.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.admin.interfaces.dto.DataLifecycleConfigDto;
import com.geeksec.admin.interfaces.dto.MaintenanceTaskDto;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDateTime;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 数据维护控制器集成测试
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@DisplayName("数据维护控制器集成测试")
class DataMaintenanceControllerIntegrationTest {
    
    @Autowired
    private WebApplicationContext webApplicationContext;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private MockMvc mockMvc;
    
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }
    
    @Test
    @DisplayName("获取表分区信息")
    void getTablePartitions_whenValidTableName_thenReturnSuccess() throws Exception {
        setUp();
        
        mockMvc.perform(get("/api/maintenance/partitions/{tableName}", "dwd_session_logs")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data").isArray());
    }
    
    @Test
    @DisplayName("获取会话日志表分区信息")
    void getSessionLogPartitions_whenCalled_thenReturnSuccess() throws Exception {
        setUp();
        
        mockMvc.perform(get("/api/maintenance/partitions/session-logs")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data").isArray());
    }
    
    @Test
    @DisplayName("获取分区存储统计")
    void getPartitionStorageStats_whenValidTableName_thenReturnSuccess() throws Exception {
        setUp();
        
        mockMvc.perform(get("/api/maintenance/partitions/{tableName}/storage-stats", "dwd_session_logs")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data.total_storage_gb").exists())
                .andExpect(jsonPath("$.data.used_storage_gb").exists())
                .andExpect(jsonPath("$.data.usage_percentage").exists());
    }
    
    @Test
    @DisplayName("获取数据生命周期配置")
    void getDataLifecycleConfig_whenValidTableName_thenReturnSuccess() throws Exception {
        setUp();
        
        mockMvc.perform(get("/api/maintenance/lifecycle-config/{tableName}", "dwd_session_logs")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data.table_name").value("dwd_session_logs"))
                .andExpect(jsonPath("$.data.retention_days").exists());
    }
    
    @Test
    @DisplayName("更新数据生命周期配置")
    void updateDataLifecycleConfig_whenValidConfig_thenReturnSuccess() throws Exception {
        setUp();
        
        DataLifecycleConfigDto configDto = new DataLifecycleConfigDto();
        configDto.setTableName("dwd_session_logs");
        configDto.setRetentionDays(120);
        configDto.setAutoPartitionEnabled(true);
        configDto.setPartitionColumn("session_start_time");
        configDto.setPartitionGranularity("DAY");
        configDto.setHotPartitionNum(7);
        configDto.setWarmPartitionNum(30);
        configDto.setColdPartitionNum(83);
        configDto.setAutoCleanupEnabled(true);
        configDto.setCompressionEnabled(true);
        configDto.setCompressionDelayHours(24);
        configDto.setCompressionThreshold(0.8);
        
        String configJson = objectMapper.writeValueAsString(configDto);
        
        mockMvc.perform(put("/api/maintenance/lifecycle-config/{tableName}", "dwd_session_logs")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(configJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"));
    }
    
    @Test
    @DisplayName("更新数据生命周期配置 - 参数验证失败")
    void updateDataLifecycleConfig_whenInvalidConfig_thenReturnBadRequest() throws Exception {
        setUp();
        
        DataLifecycleConfigDto configDto = new DataLifecycleConfigDto();
        configDto.setTableName(""); // 空表名
        configDto.setRetentionDays(-1); // 负数保留天数
        
        String configJson = objectMapper.writeValueAsString(configDto);
        
        mockMvc.perform(put("/api/maintenance/lifecycle-config/{tableName}", "dwd_session_logs")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(configJson))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400));
    }
    
    @Test
    @DisplayName("检查过期分区")
    void checkExpiredPartitions_whenValidParameters_thenReturnSuccess() throws Exception {
        setUp();
        
        mockMvc.perform(get("/api/maintenance/partitions/{tableName}/expired", "dwd_session_logs")
                        .param("retentionDays", "90")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data").isArray());
    }
    
    @Test
    @DisplayName("获取数据清理状态")
    void getDataCleanupStatus_whenCalled_thenReturnSuccess() throws Exception {
        setUp();
        
        mockMvc.perform(get("/api/maintenance/cleanup/status")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data.auto_cleanup_enabled").exists())
                .andExpect(jsonPath("$.data.storage_usage_percentage").exists());
    }
    
    @Test
    @DisplayName("获取存储空间使用情况")
    void getStorageUsage_whenCalled_thenReturnSuccess() throws Exception {
        setUp();
        
        mockMvc.perform(get("/api/maintenance/storage/usage")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data.total_storage_gb").exists())
                .andExpect(jsonPath("$.data.used_storage_gb").exists());
    }
    
    @Test
    @DisplayName("获取数据增长趋势")
    void getDataGrowthTrend_whenValidDays_thenReturnSuccess() throws Exception {
        setUp();
        
        mockMvc.perform(get("/api/maintenance/storage/growth-trend")
                        .param("days", "7")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data").isArray());
    }
    
    @Test
    @DisplayName("执行数据压缩优化")
    void executeDataCompaction_whenValidParameters_thenReturnSuccess() throws Exception {
        setUp();
        
        mockMvc.perform(post("/api/maintenance/compaction")
                        .param("tableName", "dwd_session_logs")
                        .param("partitionName", "p20240101")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"));
    }
    
    @Test
    @DisplayName("获取维护任务列表")
    void getMaintenanceTasks_whenCalled_thenReturnSuccess() throws Exception {
        setUp();
        
        mockMvc.perform(get("/api/maintenance/tasks")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data").isArray());
    }
    
    @Test
    @DisplayName("创建维护任务")
    void createMaintenanceTask_whenValidTask_thenReturnSuccess() throws Exception {
        setUp();
        
        MaintenanceTaskDto taskDto = new MaintenanceTaskDto();
        taskDto.setTaskName("测试维护任务");
        taskDto.setTaskType("DATA_CLEANUP");
        taskDto.setPriority("NORMAL");
        taskDto.setTargetTable("dwd_session_logs");
        taskDto.setScheduledTime(LocalDateTime.now().plusHours(1));
        
        String taskJson = objectMapper.writeValueAsString(taskDto);
        
        mockMvc.perform(post("/api/maintenance/tasks")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(taskJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"));
    }
    
    @Test
    @DisplayName("创建维护任务 - 参数验证失败")
    void createMaintenanceTask_whenInvalidTask_thenReturnBadRequest() throws Exception {
        setUp();
        
        MaintenanceTaskDto taskDto = new MaintenanceTaskDto();
        taskDto.setTaskName(""); // 空任务名
        taskDto.setTaskType(""); // 空任务类型
        
        String taskJson = objectMapper.writeValueAsString(taskDto);
        
        mockMvc.perform(post("/api/maintenance/tasks")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(taskJson))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400));
    }
}

server:
  port: 0  # 随机端口

spring:
  application:
    name: admin-service-test
  
  # 测试数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: org.h2.Driver
      url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
      username: sa
      password: 
      initial-size: 1
      min-idle: 1
      max-active: 5
      
  # 测试Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 15  # 使用测试数据库

# MyBatis-Flex配置
mybatis-flex:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

# 日志配置
logging:
  level:
    com.geeksec.admin: DEBUG
    org.springframework.web: WARN

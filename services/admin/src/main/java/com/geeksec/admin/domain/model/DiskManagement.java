package com.geeksec.admin.domain.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 磁盘管理聚合根
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Accessors(chain = true)
public class DiskManagement {
    
    /**
     * 磁盘ID
     */
    private String diskId;
    
    /**
     * 设备路径
     */
    private String devicePath;
    
    /**
     * 磁盘类型
     */
    private DiskType diskType;
    
    /**
     * 磁盘状态
     */
    private DiskStatus status;
    
    /**
     * 文件系统类型
     */
    private String fileSystemType;
    
    /**
     * 挂载点
     */
    private String mountPoint;
    
    /**
     * 磁盘容量（字节）
     */
    private Long capacityBytes;
    
    /**
     * 已使用空间（字节）
     */
    private Long usedBytes;
    
    /**
     * 可用空间（字节）
     */
    private Long availableBytes;
    
    /**
     * 使用率（百分比）
     */
    private Double usagePercentage;
    
    /**
     * RAID信息
     */
    private RaidInfo raidInfo;
    
    /**
     * 磁盘健康状态
     */
    private HealthStatus healthStatus;
    
    /**
     * 最后检查时间
     */
    private LocalDateTime lastCheckTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 磁盘类型枚举
     */
    public enum DiskType {
        HDD("机械硬盘"),
        SSD("固态硬盘"),
        NVME("NVMe固态硬盘"),
        RAID("RAID阵列"),
        VIRTUAL("虚拟磁盘");
        
        private final String description;
        
        DiskType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 磁盘状态枚举
     */
    public enum DiskStatus {
        ONLINE("在线"),
        OFFLINE("离线"),
        MOUNTING("挂载中"),
        UNMOUNTING("卸载中"),
        FORMATTING("格式化中"),
        REBUILDING("重建中"),
        FAILED("故障"),
        MAINTENANCE("维护中");
        
        private final String description;
        
        DiskStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 健康状态枚举
     */
    public enum HealthStatus {
        HEALTHY("健康"),
        WARNING("警告"),
        CRITICAL("严重"),
        FAILED("故障"),
        UNKNOWN("未知");
        
        private final String description;
        
        HealthStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * RAID信息
     */
    @Data
    @Accessors(chain = true)
    public static class RaidInfo {
        /**
         * RAID级别
         */
        private String raidLevel;
        
        /**
         * RAID状态
         */
        private String raidStatus;
        
        /**
         * 成员磁盘列表
         */
        private List<String> memberDisks;
        
        /**
         * 热备盘列表
         */
        private List<String> hotSpareDisks;
        
        /**
         * 重建进度（百分比）
         */
        private Double rebuildProgress;
        
        /**
         * 是否正在重建
         */
        private Boolean isRebuilding;
        
        /**
         * 最后重建时间
         */
        private LocalDateTime lastRebuildTime;
    }
    
    /**
     * 获取磁盘容量（GB）
     * 
     * @return 磁盘容量（GB）
     */
    public double getCapacityGB() {
        return capacityBytes != null ? capacityBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
    }
    
    /**
     * 获取已使用空间（GB）
     * 
     * @return 已使用空间（GB）
     */
    public double getUsedGB() {
        return usedBytes != null ? usedBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
    }
    
    /**
     * 获取可用空间（GB）
     * 
     * @return 可用空间（GB）
     */
    public double getAvailableGB() {
        return availableBytes != null ? availableBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
    }
    
    /**
     * 检查磁盘是否在线
     * 
     * @return 是否在线
     */
    public boolean isOnline() {
        return status == DiskStatus.ONLINE;
    }
    
    /**
     * 检查磁盘是否健康
     * 
     * @return 是否健康
     */
    public boolean isHealthy() {
        return healthStatus == HealthStatus.HEALTHY;
    }
    
    /**
     * 检查磁盘使用率是否告警
     * 
     * @param warningThreshold 告警阈值
     * @return 是否告警
     */
    public boolean isUsageWarning(double warningThreshold) {
        return usagePercentage != null && usagePercentage >= warningThreshold;
    }
    
    /**
     * 检查是否为RAID磁盘
     * 
     * @return 是否为RAID磁盘
     */
    public boolean isRaidDisk() {
        return diskType == DiskType.RAID && raidInfo != null;
    }
    
    /**
     * 检查RAID是否正在重建
     * 
     * @return 是否正在重建
     */
    public boolean isRaidRebuilding() {
        return isRaidDisk() && raidInfo.getIsRebuilding() != null && raidInfo.getIsRebuilding();
    }
    
    /**
     * 挂载磁盘
     * 
     * @param mountPoint 挂载点
     */
    public void mount(String mountPoint) {
        this.mountPoint = mountPoint;
        this.status = DiskStatus.MOUNTING;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 卸载磁盘
     */
    public void unmount() {
        this.status = DiskStatus.UNMOUNTING;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 格式化磁盘
     * 
     * @param fileSystemType 文件系统类型
     */
    public void format(String fileSystemType) {
        this.fileSystemType = fileSystemType;
        this.status = DiskStatus.FORMATTING;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 开始重建
     */
    public void startRebuild() {
        this.status = DiskStatus.REBUILDING;
        if (raidInfo != null) {
            raidInfo.setIsRebuilding(true);
            raidInfo.setRebuildProgress(0.0);
        }
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 完成操作
     */
    public void completeOperation() {
        this.status = DiskStatus.ONLINE;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 操作失败
     */
    public void failOperation() {
        this.status = DiskStatus.FAILED;
        this.healthStatus = HealthStatus.FAILED;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 更新健康状态
     * 
     * @param healthStatus 健康状态
     */
    public void updateHealthStatus(HealthStatus healthStatus) {
        this.healthStatus = healthStatus;
        this.lastCheckTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
}

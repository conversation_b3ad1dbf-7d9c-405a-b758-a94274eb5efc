package com.geeksec.admin.infrastructure.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 性能监控工具类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Component
public class PerformanceMonitor {
    
    private final ConcurrentHashMap<String, AtomicLong> methodCallCounts = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> methodExecutionTimes = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> methodErrorCounts = new ConcurrentHashMap<>();
    
    /**
     * 记录方法调用
     * 
     * @param methodName 方法名
     * @param executionTime 执行时间（毫秒）
     */
    public void recordMethodCall(String methodName, long executionTime) {
        methodCallCounts.computeIfAbsent(methodName, k -> new AtomicLong(0)).incrementAndGet();
        methodExecutionTimes.computeIfAbsent(methodName, k -> new AtomicLong(0)).addAndGet(executionTime);
        
        log.debug("记录方法调用: {} 耗时: {}ms", methodName, executionTime);
    }
    
    /**
     * 记录方法错误
     * 
     * @param methodName 方法名
     */
    public void recordMethodError(String methodName) {
        methodErrorCounts.computeIfAbsent(methodName, k -> new AtomicLong(0)).incrementAndGet();
        
        log.debug("记录方法错误: {}", methodName);
    }
    
    /**
     * 获取方法调用次数
     * 
     * @param methodName 方法名
     * @return 调用次数
     */
    public long getMethodCallCount(String methodName) {
        return methodCallCounts.getOrDefault(methodName, new AtomicLong(0)).get();
    }
    
    /**
     * 获取方法平均执行时间
     * 
     * @param methodName 方法名
     * @return 平均执行时间（毫秒）
     */
    public double getMethodAverageExecutionTime(String methodName) {
        long totalTime = methodExecutionTimes.getOrDefault(methodName, new AtomicLong(0)).get();
        long callCount = getMethodCallCount(methodName);
        
        return callCount > 0 ? (double) totalTime / callCount : 0.0;
    }
    
    /**
     * 获取方法错误次数
     * 
     * @param methodName 方法名
     * @return 错误次数
     */
    public long getMethodErrorCount(String methodName) {
        return methodErrorCounts.getOrDefault(methodName, new AtomicLong(0)).get();
    }
    
    /**
     * 获取方法错误率
     * 
     * @param methodName 方法名
     * @return 错误率（百分比）
     */
    public double getMethodErrorRate(String methodName) {
        long errorCount = getMethodErrorCount(methodName);
        long callCount = getMethodCallCount(methodName);
        
        return callCount > 0 ? (double) errorCount / callCount * 100 : 0.0;
    }
    
    /**
     * 重置统计数据
     */
    public void reset() {
        methodCallCounts.clear();
        methodExecutionTimes.clear();
        methodErrorCounts.clear();
        
        log.info("性能监控统计数据已重置");
    }
    
    /**
     * 打印性能统计报告
     */
    public void printPerformanceReport() {
        log.info("=== 性能监控报告 ===");
        
        methodCallCounts.forEach((methodName, callCount) -> {
            double avgTime = getMethodAverageExecutionTime(methodName);
            long errorCount = getMethodErrorCount(methodName);
            double errorRate = getMethodErrorRate(methodName);
            
            log.info("方法: {} | 调用次数: {} | 平均耗时: {:.2f}ms | 错误次数: {} | 错误率: {:.2f}%",
                    methodName, callCount.get(), avgTime, errorCount, errorRate);
        });
        
        log.info("=== 报告结束 ===");
    }
    
    /**
     * 获取性能统计数据
     * 
     * @return 性能统计数据
     */
    public ConcurrentHashMap<String, Object> getPerformanceStats() {
        ConcurrentHashMap<String, Object> stats = new ConcurrentHashMap<>();
        
        methodCallCounts.forEach((methodName, callCount) -> {
            ConcurrentHashMap<String, Object> methodStats = new ConcurrentHashMap<>();
            methodStats.put("callCount", callCount.get());
            methodStats.put("averageExecutionTime", getMethodAverageExecutionTime(methodName));
            methodStats.put("errorCount", getMethodErrorCount(methodName));
            methodStats.put("errorRate", getMethodErrorRate(methodName));
            
            stats.put(methodName, methodStats);
        });
        
        return stats;
    }
}

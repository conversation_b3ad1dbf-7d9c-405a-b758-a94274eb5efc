package com.geeksec.admin.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger配置类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Configuration
public class SwaggerConfig {
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("NTA 3.0 系统管理服务 API")
                        .description("基于DDD架构的系统管理服务，提供数据维护、系统管理等功能")
                        .version("3.0.0")
                        .contact(new Contact()
                                .name("NTA Team")
                                .email("<EMAIL>")
                                .url("https://www.geeksec.com"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0")));
    }
}

package com.geeksec.admin.infrastructure.persistence;

import com.geeksec.admin.domain.model.MaintenanceTask;
import com.geeksec.admin.domain.repository.MaintenanceTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 维护任务仓储实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class MaintenanceTaskRepositoryImpl implements MaintenanceTaskRepository {
    
    // 使用内存存储模拟数据库
    private final Map<String, MaintenanceTask> taskStore = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    
    @Override
    public Optional<MaintenanceTask> findById(String taskId) {
        log.debug("根据ID查找任务: {}", taskId);
        return Optional.ofNullable(taskStore.get(taskId));
    }
    
    @Override
    public List<MaintenanceTask> findAll() {
        log.debug("查找所有任务");
        return List.copyOf(taskStore.values());
    }
    
    @Override
    public List<MaintenanceTask> findByStatus(MaintenanceTask.TaskStatus status) {
        log.debug("根据状态查找任务: {}", status);
        return taskStore.values().stream()
                .filter(task -> task.getStatus() == status)
                .toList();
    }
    
    @Override
    public List<MaintenanceTask> findByTaskType(MaintenanceTask.TaskType taskType) {
        log.debug("根据类型查找任务: {}", taskType);
        return taskStore.values().stream()
                .filter(task -> task.getTaskType() == taskType)
                .toList();
    }
    
    @Override
    public List<MaintenanceTask> findByTargetTable(String targetTable) {
        log.debug("根据目标表查找任务: {}", targetTable);
        return taskStore.values().stream()
                .filter(task -> targetTable.equals(task.getTargetTable()))
                .toList();
    }
    
    @Override
    public List<MaintenanceTask> findByCreatedBy(String createdBy) {
        log.debug("根据创建者查找任务: {}", createdBy);
        return taskStore.values().stream()
                .filter(task -> createdBy.equals(task.getCreatedBy()))
                .toList();
    }
    
    @Override
    public List<MaintenanceTask> findByScheduledTimeBetween(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("查找计划执行时间在 {} 到 {} 之间的任务", startTime, endTime);
        return taskStore.values().stream()
                .filter(task -> task.getScheduledTime() != null)
                .filter(task -> !task.getScheduledTime().isBefore(startTime) && !task.getScheduledTime().isAfter(endTime))
                .toList();
    }
    
    @Override
    public List<MaintenanceTask> findPendingTasks() {
        log.debug("查找待执行的任务");
        return findByStatus(MaintenanceTask.TaskStatus.PENDING);
    }
    
    @Override
    public List<MaintenanceTask> findRunningTasks() {
        log.debug("查找正在执行的任务");
        return findByStatus(MaintenanceTask.TaskStatus.RUNNING);
    }
    
    @Override
    public List<MaintenanceTask> findCompletedTasks(int limit) {
        log.debug("查找已完成的任务, 限制数量: {}", limit);
        return findByStatus(MaintenanceTask.TaskStatus.COMPLETED).stream()
                .sorted((t1, t2) -> t2.getEndTime().compareTo(t1.getEndTime())) // 按结束时间倒序
                .limit(limit)
                .toList();
    }
    
    @Override
    public List<MaintenanceTask> findFailedTasks(int limit) {
        log.debug("查找失败的任务, 限制数量: {}", limit);
        return findByStatus(MaintenanceTask.TaskStatus.FAILED).stream()
                .sorted((t1, t2) -> t2.getEndTime().compareTo(t1.getEndTime())) // 按结束时间倒序
                .limit(limit)
                .toList();
    }
    
    @Override
    public MaintenanceTask save(MaintenanceTask task) {
        log.debug("保存任务: {}", task.getTaskName());
        
        if (task.getTaskId() == null) {
            // 新增
            String taskId = "task_" + idGenerator.getAndIncrement();
            task.setTaskId(taskId);
            task.setCreateTime(LocalDateTime.now());
        }
        
        task.setUpdateTime(LocalDateTime.now());
        taskStore.put(task.getTaskId(), task);
        
        return task;
    }
    
    @Override
    public void deleteById(String taskId) {
        log.debug("删除任务: {}", taskId);
        taskStore.remove(taskId);
    }
    
    @Override
    public int deleteCompletedTasksBefore(LocalDateTime beforeTime) {
        log.debug("删除 {} 之前完成的任务", beforeTime);
        
        List<String> toDelete = taskStore.values().stream()
                .filter(task -> task.getStatus() == MaintenanceTask.TaskStatus.COMPLETED)
                .filter(task -> task.getEndTime() != null && task.getEndTime().isBefore(beforeTime))
                .map(MaintenanceTask::getTaskId)
                .toList();
        
        toDelete.forEach(taskStore::remove);
        return toDelete.size();
    }
    
    @Override
    public boolean existsById(String taskId) {
        return taskStore.containsKey(taskId);
    }
    
    @Override
    public long countByStatus(MaintenanceTask.TaskStatus status) {
        return taskStore.values().stream()
                .filter(task -> task.getStatus() == status)
                .count();
    }
    
    @Override
    public long countByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime) {
        return taskStore.values().stream()
                .filter(task -> task.getCreateTime() != null)
                .filter(task -> !task.getCreateTime().isBefore(startTime) && !task.getCreateTime().isAfter(endTime))
                .count();
    }
    
    // 初始化一些示例数据
    public void initSampleData() {
        log.info("初始化示例维护任务数据");
        
        // 数据清理任务
        MaintenanceTask cleanupTask = new MaintenanceTask()
                .setTaskName("会话日志数据清理")
                .setTaskType(MaintenanceTask.TaskType.DATA_CLEANUP)
                .setStatus(MaintenanceTask.TaskStatus.COMPLETED)
                .setPriority(MaintenanceTask.TaskPriority.NORMAL)
                .setTargetTable("dwd_session_logs")
                .setScheduledTime(LocalDateTime.now().minusDays(1))
                .setStartTime(LocalDateTime.now().minusDays(1).plusMinutes(5))
                .setEndTime(LocalDateTime.now().minusDays(1).plusMinutes(35))
                .setProgress(100)
                .setResult("清理完成，删除了30天前的过期分区")
                .setCreatedBy("system")
                .setUpdatedBy("system");
        
        save(cleanupTask);
        
        // 数据备份任务
        MaintenanceTask backupTask = new MaintenanceTask()
                .setTaskName("告警日志数据备份")
                .setTaskType(MaintenanceTask.TaskType.DATA_BACKUP)
                .setStatus(MaintenanceTask.TaskStatus.RUNNING)
                .setPriority(MaintenanceTask.TaskPriority.HIGH)
                .setTargetTable("dwd_alert_logs")
                .setScheduledTime(LocalDateTime.now().minusMinutes(30))
                .setStartTime(LocalDateTime.now().minusMinutes(25))
                .setProgress(65)
                .setCreatedBy("admin")
                .setUpdatedBy("admin");
        
        save(backupTask);
        
        // 统计信息刷新任务
        MaintenanceTask statsTask = new MaintenanceTask()
                .setTaskName("表统计信息刷新")
                .setTaskType(MaintenanceTask.TaskType.STATISTICS_REFRESH)
                .setStatus(MaintenanceTask.TaskStatus.PENDING)
                .setPriority(MaintenanceTask.TaskPriority.LOW)
                .setTargetTable("dwd_session_logs")
                .setScheduledTime(LocalDateTime.now().plusHours(2))
                .setCreatedBy("system")
                .setUpdatedBy("system");
        
        save(statsTask);
        
        log.info("示例维护任务数据初始化完成");
    }
}

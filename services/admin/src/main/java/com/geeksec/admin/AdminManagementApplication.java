package com.geeksec.admin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

/**
 * 系统管理服务启动类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan(basePackages = {"com.geeksec.admin", "com.geeksec.common"})
public class AdminManagementApplication {

    public static void main(String[] args) {
        SpringApplication.run(AdminManagementApplication.class, args);
    }
}

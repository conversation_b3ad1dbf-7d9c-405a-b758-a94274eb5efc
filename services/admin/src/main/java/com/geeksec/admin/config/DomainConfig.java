package com.geeksec.admin.config;

import com.geeksec.admin.infrastructure.persistence.DataLifecycleConfigRepositoryImpl;
import com.geeksec.admin.infrastructure.persistence.MaintenanceTaskRepositoryImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * 领域配置类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class DomainConfig {
    
    private final DataLifecycleConfigRepositoryImpl dataLifecycleConfigRepository;
    private final MaintenanceTaskRepositoryImpl maintenanceTaskRepository;
    
    /**
     * 初始化领域数据
     */
    @Bean
    @Order(1)
    public CommandLineRunner initDomainData() {
        return args -> {
            log.info("开始初始化领域数据");
            
            try {
                // 初始化数据生命周期配置
                dataLifecycleConfigRepository.initDefaultData();
                
                // 初始化维护任务示例数据
                maintenanceTaskRepository.initSampleData();
                
                log.info("领域数据初始化完成");
            } catch (Exception e) {
                log.error("领域数据初始化失败", e);
            }
        };
    }
}

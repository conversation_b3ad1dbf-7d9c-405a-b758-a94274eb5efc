package com.geeksec.admin.domain.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 系统信息聚合根
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Accessors(chain = true)
public class SystemInfo {
    
    /**
     * 主机名称
     */
    private String hostname;
    
    /**
     * 操作系统信息
     */
    private String osInfo;
    
    /**
     * 系统时间
     */
    private String systemTime;
    
    /**
     * 运行时间（秒）
     */
    private Long uptimeSeconds;
    
    /**
     * 启动时间
     */
    private LocalDateTime startTime;
    
    /**
     * CPU信息
     */
    private CpuInfo cpuInfo;
    
    /**
     * 内存信息
     */
    private MemoryInfo memoryInfo;
    
    /**
     * 磁盘信息
     */
    private DiskInfo diskInfo;
    
    /**
     * 网络信息
     */
    private NetworkInfo networkInfo;
    
    /**
     * 系统负载
     */
    private SystemLoad systemLoad;
    
    /**
     * 系统状态
     */
    private SystemStatus status;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * CPU信息
     */
    @Data
    @Accessors(chain = true)
    public static class CpuInfo {
        /**
         * CPU型号
         */
        private String model;
        
        /**
         * CPU核心数
         */
        private Integer cores;
        
        /**
         * CPU频率（MHz）
         */
        private Double frequencyMHz;
        
        /**
         * CPU使用率（百分比）
         */
        private Double usagePercentage;
        
        /**
         * CPU温度（摄氏度）
         */
        private Double temperatureCelsius;
    }
    
    /**
     * 内存信息
     */
    @Data
    @Accessors(chain = true)
    public static class MemoryInfo {
        /**
         * 总内存（字节）
         */
        private Long totalBytes;
        
        /**
         * 已使用内存（字节）
         */
        private Long usedBytes;
        
        /**
         * 可用内存（字节）
         */
        private Long availableBytes;
        
        /**
         * 内存使用率（百分比）
         */
        private Double usagePercentage;
        
        /**
         * 缓存大小（字节）
         */
        private Long cacheBytes;
        
        /**
         * 缓冲区大小（字节）
         */
        private Long bufferBytes;
        
        /**
         * 获取总内存（GB）
         */
        public double getTotalGB() {
            return totalBytes != null ? totalBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
        }
        
        /**
         * 获取已使用内存（GB）
         */
        public double getUsedGB() {
            return usedBytes != null ? usedBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
        }
        
        /**
         * 获取可用内存（GB）
         */
        public double getAvailableGB() {
            return availableBytes != null ? availableBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
        }
    }
    
    /**
     * 磁盘信息
     */
    @Data
    @Accessors(chain = true)
    public static class DiskInfo {
        /**
         * 总磁盘空间（字节）
         */
        private Long totalBytes;
        
        /**
         * 已使用磁盘空间（字节）
         */
        private Long usedBytes;
        
        /**
         * 可用磁盘空间（字节）
         */
        private Long availableBytes;
        
        /**
         * 磁盘使用率（百分比）
         */
        private Double usagePercentage;
        
        /**
         * 磁盘IO读取速率（字节/秒）
         */
        private Long readBytesPerSecond;
        
        /**
         * 磁盘IO写入速率（字节/秒）
         */
        private Long writeBytesPerSecond;
        
        /**
         * 获取总磁盘空间（GB）
         */
        public double getTotalGB() {
            return totalBytes != null ? totalBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
        }
        
        /**
         * 获取已使用磁盘空间（GB）
         */
        public double getUsedGB() {
            return usedBytes != null ? usedBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
        }
        
        /**
         * 获取可用磁盘空间（GB）
         */
        public double getAvailableGB() {
            return availableBytes != null ? availableBytes / (1024.0 * 1024.0 * 1024.0) : 0.0;
        }
    }
    
    /**
     * 网络信息
     */
    @Data
    @Accessors(chain = true)
    public static class NetworkInfo {
        /**
         * 网络接收字节数
         */
        private Long receivedBytes;
        
        /**
         * 网络发送字节数
         */
        private Long sentBytes;
        
        /**
         * 网络接收速率（字节/秒）
         */
        private Long receiveBytesPerSecond;
        
        /**
         * 网络发送速率（字节/秒）
         */
        private Long sendBytesPerSecond;
        
        /**
         * 网络连接数
         */
        private Integer connectionCount;
        
        /**
         * 活跃连接数
         */
        private Integer activeConnectionCount;
    }
    
    /**
     * 系统负载
     */
    @Data
    @Accessors(chain = true)
    public static class SystemLoad {
        /**
         * 1分钟平均负载
         */
        private Double load1min;
        
        /**
         * 5分钟平均负载
         */
        private Double load5min;
        
        /**
         * 15分钟平均负载
         */
        private Double load15min;
        
        /**
         * 进程总数
         */
        private Integer totalProcesses;
        
        /**
         * 运行中进程数
         */
        private Integer runningProcesses;
        
        /**
         * 僵尸进程数
         */
        private Integer zombieProcesses;
    }
    
    /**
     * 系统状态枚举
     */
    public enum SystemStatus {
        HEALTHY("健康"),
        WARNING("警告"),
        CRITICAL("严重"),
        UNKNOWN("未知");
        
        private final String description;
        
        SystemStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 获取运行时间（天）
     * 
     * @return 运行时间（天）
     */
    public double getUptimeDays() {
        return uptimeSeconds != null ? uptimeSeconds / (24.0 * 60.0 * 60.0) : 0.0;
    }
    
    /**
     * 获取运行时间（小时）
     * 
     * @return 运行时间（小时）
     */
    public double getUptimeHours() {
        return uptimeSeconds != null ? uptimeSeconds / (60.0 * 60.0) : 0.0;
    }
    
    /**
     * 检查系统是否健康
     * 
     * @return 是否健康
     */
    public boolean isHealthy() {
        return status == SystemStatus.HEALTHY;
    }
    
    /**
     * 更新系统信息
     */
    public void updateInfo() {
        this.lastUpdateTime = LocalDateTime.now();
    }
}

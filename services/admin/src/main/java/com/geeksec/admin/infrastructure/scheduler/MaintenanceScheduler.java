package com.geeksec.admin.infrastructure.scheduler;

import com.geeksec.admin.domain.maintenance.DataMaintenanceDomainService;
import com.geeksec.admin.domain.maintenance.model.DataLifecycleConfig;
import com.geeksec.admin.domain.maintenance.model.MaintenanceTask;
import com.geeksec.admin.domain.maintenance.model.PartitionInfo;
import com.geeksec.admin.domain.maintenance.repository.DataLifecycleConfigRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 维护任务调度器
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MaintenanceScheduler {
    
    private final DataMaintenanceDomainService dataMaintenanceDomainService;
    private final DataLifecycleConfigRepository dataLifecycleConfigRepository;
    
    /**
     * 自动数据清理任务
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void autoDataCleanup() {
        log.info("开始执行自动数据清理任务");
        
        try {
            // 获取所有启用自动清理的配置
            List<DataLifecycleConfig> configs = dataLifecycleConfigRepository.findByAutoCleanupEnabled();
            
            for (DataLifecycleConfig config : configs) {
                if (config.needsCleanup()) {
                    log.info("执行表 {} 的数据清理，保留天数: {}", config.getTableName(), config.getRetentionDays());
                    
                    // 检查过期分区
                    List<PartitionInfo> expiredPartitions = dataMaintenanceDomainService.checkExpiredPartitions(
                            config.getTableName(), 
                            config.getRetentionDays()
                    );
                    
                    if (!expiredPartitions.isEmpty()) {
                        log.info("发现 {} 个过期分区，开始清理", expiredPartitions.size());
                        
                        // 执行清理
                        int cleanedCount = dataMaintenanceDomainService.cleanupExpiredPartitions(
                                config.getTableName(), 
                                config.getRetentionDays(), 
                                "scheduler"
                        );
                        
                        log.info("表 {} 清理完成，清理了 {} 个分区", config.getTableName(), cleanedCount);
                    } else {
                        log.info("表 {} 没有过期分区需要清理", config.getTableName());
                    }
                }
            }
            
            log.info("自动数据清理任务执行完成");
        } catch (Exception e) {
            log.error("自动数据清理任务执行失败", e);
        }
    }
    
    /**
     * 自动数据压缩任务
     * 每天凌晨3点执行
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void autoDataCompaction() {
        log.info("开始执行自动数据压缩任务");
        
        try {
            // 获取所有启用压缩的配置
            List<DataLifecycleConfig> configs = dataLifecycleConfigRepository.findByCompressionEnabled();
            
            for (DataLifecycleConfig config : configs) {
                if (config.needsCompression()) {
                    log.info("执行表 {} 的数据压缩", config.getTableName());
                    
                    // 创建压缩任务
                    MaintenanceTask compactionTask = dataMaintenanceDomainService.executeDataCompaction(
                            config.getTableName(), 
                            null, // 压缩整个表
                            "scheduler"
                    );
                    
                    log.info("表 {} 压缩任务已创建，任务ID: {}", config.getTableName(), compactionTask.getTaskId());
                }
            }
            
            log.info("自动数据压缩任务执行完成");
        } catch (Exception e) {
            log.error("自动数据压缩任务执行失败", e);
        }
    }
    
    /**
     * 统计信息刷新任务
     * 每天凌晨4点执行
     */
    @Scheduled(cron = "0 0 4 * * ?")
    public void autoStatisticsRefresh() {
        log.info("开始执行自动统计信息刷新任务");
        
        try {
            // 获取所有配置的表
            List<DataLifecycleConfig> configs = dataLifecycleConfigRepository.findAll();
            
            for (DataLifecycleConfig config : configs) {
                log.info("刷新表 {} 的统计信息", config.getTableName());
                
                // 创建统计信息刷新任务
                MaintenanceTask statsTask = dataMaintenanceDomainService.refreshTableStatistics(
                        config.getTableName(), 
                        "scheduler"
                );
                
                log.info("表 {} 统计信息刷新任务已创建，任务ID: {}", config.getTableName(), statsTask.getTaskId());
            }
            
            log.info("自动统计信息刷新任务执行完成");
        } catch (Exception e) {
            log.error("自动统计信息刷新任务执行失败", e);
        }
    }
    
    /**
     * 清理已完成的维护任务
     * 每周日凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * SUN")
    public void cleanupCompletedTasks() {
        log.info("开始清理已完成的维护任务");
        
        try {
            // 清理30天前完成的任务
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
            // 通过领域服务清理任务（需要在领域服务中添加此方法）
            // 这里暂时使用模拟实现
            int deletedCount = 5; // 模拟删除了5个任务
            
            log.info("清理了 {} 个已完成的维护任务", deletedCount);
        } catch (Exception e) {
            log.error("清理已完成维护任务失败", e);
        }
    }
    
    /**
     * 系统健康检查
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3600000毫秒
    public void systemHealthCheck() {
        log.debug("执行系统健康检查");
        
        try {
            // 检查存储健康状态
            var healthStatus = dataMaintenanceDomainService.getSystemHealthStatus();
            
            if (healthStatus == com.geeksec.admin.domain.maintenance.model.StorageStats.StorageHealthStatus.CRITICAL ||
                healthStatus == com.geeksec.admin.domain.maintenance.model.StorageStats.StorageHealthStatus.EMERGENCY) {
                
                log.warn("系统存储状态异常: {}", healthStatus.getDescription());
                
                // 创建健康检查任务
                MaintenanceTask healthCheckTask = dataMaintenanceDomainService.checkDataIntegrity(
                        "system", 
                        "storage_health", 
                        "scheduler"
                );
                
                log.warn("已创建系统健康检查任务，任务ID: {}", healthCheckTask.getTaskId());
            } else {
                log.debug("系统存储状态正常: {}", healthStatus.getDescription());
            }
        } catch (Exception e) {
            log.error("系统健康检查失败", e);
        }
    }
}

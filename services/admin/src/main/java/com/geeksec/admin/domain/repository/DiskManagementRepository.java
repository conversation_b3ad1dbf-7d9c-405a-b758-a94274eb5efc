package com.geeksec.admin.domain.repository;

import com.geeksec.admin.domain.model.DiskManagement;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 磁盘管理仓储接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface DiskManagementRepository {
    
    /**
     * 根据磁盘ID查找磁盘信息
     * 
     * @param diskId 磁盘ID
     * @return 磁盘信息
     */
    Optional<DiskManagement> findById(String diskId);
    
    /**
     * 根据设备路径查找磁盘信息
     * 
     * @param devicePath 设备路径
     * @return 磁盘信息
     */
    Optional<DiskManagement> findByDevicePath(String devicePath);
    
    /**
     * 根据挂载点查找磁盘信息
     * 
     * @param mountPoint 挂载点
     * @return 磁盘信息
     */
    Optional<DiskManagement> findByMountPoint(String mountPoint);
    
    /**
     * 查找所有磁盘信息
     * 
     * @return 磁盘信息列表
     */
    List<DiskManagement> findAll();
    
    /**
     * 根据磁盘类型查找磁盘信息
     * 
     * @param diskType 磁盘类型
     * @return 磁盘信息列表
     */
    List<DiskManagement> findByDiskType(DiskManagement.DiskType diskType);
    
    /**
     * 根据磁盘状态查找磁盘信息
     * 
     * @param status 磁盘状态
     * @return 磁盘信息列表
     */
    List<DiskManagement> findByStatus(DiskManagement.DiskStatus status);
    
    /**
     * 根据健康状态查找磁盘信息
     * 
     * @param healthStatus 健康状态
     * @return 磁盘信息列表
     */
    List<DiskManagement> findByHealthStatus(DiskManagement.HealthStatus healthStatus);
    
    /**
     * 查找在线磁盘
     * 
     * @return 在线磁盘列表
     */
    List<DiskManagement> findOnlineDisks();
    
    /**
     * 查找RAID磁盘
     * 
     * @return RAID磁盘列表
     */
    List<DiskManagement> findRaidDisks();
    
    /**
     * 查找正在重建的RAID磁盘
     * 
     * @return 正在重建的RAID磁盘列表
     */
    List<DiskManagement> findRebuildingRaidDisks();
    
    /**
     * 查找使用率超过阈值的磁盘
     * 
     * @param usageThreshold 使用率阈值
     * @return 磁盘列表
     */
    List<DiskManagement> findDisksWithUsageAbove(double usageThreshold);
    
    /**
     * 查找容量大于指定值的磁盘
     * 
     * @param capacityBytes 容量阈值（字节）
     * @return 磁盘列表
     */
    List<DiskManagement> findDisksWithCapacityAbove(long capacityBytes);
    
    /**
     * 查找指定时间范围内更新的磁盘
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 磁盘列表
     */
    List<DiskManagement> findByUpdateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 保存磁盘信息
     * 
     * @param diskManagement 磁盘信息
     * @return 保存后的磁盘信息
     */
    DiskManagement save(DiskManagement diskManagement);
    
    /**
     * 批量保存磁盘信息
     * 
     * @param diskManagements 磁盘信息列表
     * @return 保存后的磁盘信息列表
     */
    List<DiskManagement> saveAll(List<DiskManagement> diskManagements);
    
    /**
     * 删除磁盘信息
     * 
     * @param diskId 磁盘ID
     */
    void deleteById(String diskId);
    
    /**
     * 根据设备路径删除磁盘信息
     * 
     * @param devicePath 设备路径
     */
    void deleteByDevicePath(String devicePath);
    
    /**
     * 检查磁盘是否存在
     * 
     * @param diskId 磁盘ID
     * @return 是否存在
     */
    boolean existsById(String diskId);
    
    /**
     * 检查设备路径对应的磁盘是否存在
     * 
     * @param devicePath 设备路径
     * @return 是否存在
     */
    boolean existsByDevicePath(String devicePath);
    
    /**
     * 统计磁盘数量
     * 
     * @return 磁盘数量
     */
    long count();
    
    /**
     * 统计指定状态的磁盘数量
     * 
     * @param status 磁盘状态
     * @return 磁盘数量
     */
    long countByStatus(DiskManagement.DiskStatus status);
    
    /**
     * 统计指定健康状态的磁盘数量
     * 
     * @param healthStatus 健康状态
     * @return 磁盘数量
     */
    long countByHealthStatus(DiskManagement.HealthStatus healthStatus);
    
    /**
     * 计算总磁盘容量
     * 
     * @return 总磁盘容量（字节）
     */
    long sumTotalCapacity();
    
    /**
     * 计算总已使用空间
     * 
     * @return 总已使用空间（字节）
     */
    long sumUsedSpace();
    
    /**
     * 计算总可用空间
     * 
     * @return 总可用空间（字节）
     */
    long sumAvailableSpace();
}

package com.geeksec.admin.infrastructure.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * 日志切面
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Aspect
@Component
public class LoggingAspect {
    
    /**
     * 定义切点：所有应用服务的方法
     */
    @Pointcut("execution(* com.geeksec.admin.application..*(..))")
    public void applicationServiceMethods() {}
    
    /**
     * 定义切点：所有领域服务的方法
     */
    @Pointcut("execution(* com.geeksec.admin.domain..*(..))")
    public void domainServiceMethods() {}
    
    /**
     * 定义切点：所有控制器的方法
     */
    @Pointcut("execution(* com.geeksec.admin.interfaces.rest..*(..))")
    public void controllerMethods() {}
    
    /**
     * 环绕通知：记录方法执行时间和参数
     */
    @Around("applicationServiceMethods() || domainServiceMethods()")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        log.debug("开始执行 {}.{}，参数: {}", className, methodName, Arrays.toString(args));
        
        try {
            Object result = joinPoint.proceed();
            long executionTime = System.currentTimeMillis() - startTime;
            
            log.debug("完成执行 {}.{}，耗时: {}ms", className, methodName, executionTime);
            
            if (executionTime > 5000) { // 超过5秒的方法记录警告
                log.warn("方法 {}.{} 执行时间过长: {}ms", className, methodName, executionTime);
            }
            
            return result;
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("执行 {}.{} 失败，耗时: {}ms，错误: {}", className, methodName, executionTime, e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 前置通知：记录控制器方法调用
     */
    @Before("controllerMethods()")
    public void logControllerMethodCall(JoinPoint joinPoint) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        log.info("API调用 {}.{}，参数: {}", className, methodName, Arrays.toString(args));
    }
    
    /**
     * 后置返回通知：记录控制器方法成功返回
     */
    @AfterReturning(pointcut = "controllerMethods()", returning = "result")
    public void logControllerMethodReturn(JoinPoint joinPoint, Object result) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        log.info("API调用 {}.{} 成功返回", className, methodName);
    }
    
    /**
     * 异常通知：记录控制器方法异常
     */
    @AfterThrowing(pointcut = "controllerMethods()", throwing = "exception")
    public void logControllerMethodException(JoinPoint joinPoint, Throwable exception) {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();
        
        log.error("API调用 {}.{} 发生异常: {}", className, methodName, exception.getMessage(), exception);
    }
}

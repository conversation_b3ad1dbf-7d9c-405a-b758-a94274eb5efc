package com.geeksec.admin.domain.repository;

import com.geeksec.admin.domain.model.DataLifecycleConfig;

import java.util.List;
import java.util.Optional;

/**
 * 数据生命周期配置仓储接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface DataLifecycleConfigRepository {
    
    /**
     * 根据ID查找配置
     * 
     * @param configId 配置ID
     * @return 配置信息
     */
    Optional<DataLifecycleConfig> findById(String configId);
    
    /**
     * 根据表名查找配置
     * 
     * @param tableName 表名
     * @return 配置信息
     */
    Optional<DataLifecycleConfig> findByTableName(String tableName);
    
    /**
     * 查找所有配置
     * 
     * @return 配置列表
     */
    List<DataLifecycleConfig> findAll();
    
    /**
     * 查找启用自动清理的配置
     * 
     * @return 配置列表
     */
    List<DataLifecycleConfig> findByAutoCleanupEnabled();
    
    /**
     * 查找启用压缩的配置
     * 
     * @return 配置列表
     */
    List<DataLifecycleConfig> findByCompressionEnabled();
    
    /**
     * 保存配置
     * 
     * @param config 配置信息
     * @return 保存后的配置
     */
    DataLifecycleConfig save(DataLifecycleConfig config);
    
    /**
     * 删除配置
     * 
     * @param configId 配置ID
     */
    void deleteById(String configId);
    
    /**
     * 根据表名删除配置
     * 
     * @param tableName 表名
     */
    void deleteByTableName(String tableName);
    
    /**
     * 检查配置是否存在
     * 
     * @param configId 配置ID
     * @return 是否存在
     */
    boolean existsById(String configId);
    
    /**
     * 检查表名对应的配置是否存在
     * 
     * @param tableName 表名
     * @return 是否存在
     */
    boolean existsByTableName(String tableName);
}

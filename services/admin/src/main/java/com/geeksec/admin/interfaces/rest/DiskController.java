package com.geeksec.admin.interfaces.rest;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.admin.infrastructure.service.DiskManagementService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 磁盘管理控制器
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@Tag(name = "磁盘管理接口", description = "磁盘管理接口相关操作")
@RequestMapping("/api/raid")
public class DiskController {

    @Autowired
    private DiskManagementService diskManagementService;

    /**
     * 获取RAID卡磁盘信息
     *
     * @return RAID信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取RAID卡磁盘信息", description = "获取RAID卡磁盘信息操作")
    public JSONObject getRaidInfo() {
        return diskManagementService.getRaidInfo();
    }

    /**
     * 获取可管理的磁盘列表
     *
     * @return 磁盘列表
     */
    @GetMapping("/disks")
    @Operation(summary = "获取可管理的磁盘列表", description = "获取可管理的磁盘列表操作")
    public JSONObject getManageableDisks() {
        return diskManagementService.getManageableDisks();
    }

    /**
     * 磁盘重组操作
     *
     * @return 操作结果
     */
    @PostMapping("/rebuild")
    @Operation(summary = "磁盘重组操作", description = "磁盘重组操作")
    public JSONObject rebuildDisk() {
        return diskManagementService.rebuildDisk();
    }

    /**
     * 磁盘更新操作
     *
     * @return 操作结果
     */
    @PostMapping("/update")
    @Operation(summary = "磁盘更新操作", description = "磁盘更新操作")
    public JSONObject updateDisk() {
        return diskManagementService.updateDisk();
    }

    /**
     * 准备挂载磁盘
     *
     * @return 操作结果
     */
    @PostMapping("/mount/prepare")
    @Operation(summary = "准备挂载磁盘", description = "准备挂载磁盘操作")
    public JSONObject prepareMount() {
        return diskManagementService.prepareMount();
    }

    /**
     * 挂载数据磁盘
     *
     * @return 操作结果
     */
    @PostMapping("/mount/data")
    @Operation(summary = "挂载数据磁盘", description = "挂载数据磁盘操作")
    public JSONObject mountDataDisk() {
        return diskManagementService.mountDataDisk();
    }

    /**
     * 格式化磁盘
     *
     * @param request 格式化请求
     * @return 操作结果
     */
    @PostMapping("/format")
    @Operation(summary = "格式化磁盘", description = "格式化磁盘操作")
    public ApiResponse<JSONObject> formatDisk(@RequestBody JSONObject request) {
        try {
            String devicePath = request.getString("device_path");
            String fileSystem = request.getString("file_system");
            
            if (devicePath == null || fileSystem == null) {
                return ApiResponse.error("设备路径和文件系统类型不能为空");
            }
            
            JSONObject result = diskManagementService.formatDisk(devicePath, fileSystem);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("格式化磁盘失败", e);
            return ApiResponse.error("格式化磁盘失败: " + e.getMessage());
        }
    }

    /**
     * 挂载磁盘
     *
     * @param request 挂载请求
     * @return 操作结果
     */
    @PostMapping("/mount")
    @Operation(summary = "挂载磁盘", description = "挂载磁盘操作")
    public ApiResponse<JSONObject> mountDisk(@RequestBody JSONObject request) {
        try {
            String devicePath = request.getString("device_path");
            String mountPoint = request.getString("mount_point");
            
            if (devicePath == null || mountPoint == null) {
                return ApiResponse.error("设备路径和挂载点不能为空");
            }
            
            JSONObject result = diskManagementService.mountDisk(devicePath, mountPoint);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("挂载磁盘失败", e);
            return ApiResponse.error("挂载磁盘失败: " + e.getMessage());
        }
    }

    /**
     * 卸载磁盘
     *
     * @param request 卸载请求
     * @return 操作结果
     */
    @PostMapping("/unmount")
    @Operation(summary = "卸载磁盘", description = "卸载磁盘操作")
    public ApiResponse<JSONObject> unmountDisk(@RequestBody JSONObject request) {
        try {
            String mountPoint = request.getString("mount_point");
            
            if (mountPoint == null) {
                return ApiResponse.error("挂载点不能为空");
            }
            
            JSONObject result = diskManagementService.unmountDisk(mountPoint);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("卸载磁盘失败", e);
            return ApiResponse.error("卸载磁盘失败: " + e.getMessage());
        }
    }
}

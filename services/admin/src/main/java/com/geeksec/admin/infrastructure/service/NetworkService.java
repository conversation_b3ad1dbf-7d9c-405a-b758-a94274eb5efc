package com.geeksec.admin.infrastructure.service;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.admin.model.system.NetworkConfig;

/**
 * 网络管理服务接口
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public interface NetworkService {

    /**
     * 修改IP配置
     *
     * @param config 网络配置
     * @return 操作结果
     */
    JSONObject modifyIpConfig(NetworkConfig config);

    /**
     * 设置NTP服务器
     *
     * @param ntpServer NTP服务器地址
     * @return 操作结果
     */
    JSONObject setNtpServer(String ntpServer);

    /**
     * 获取网络配置信息
     *
     * @return 网络配置
     */
    NetworkConfig getNetworkConfig();

    /**
     * 获取网络设备信息
     *
     * @param deviceName 设备名称
     * @return 设备信息
     */
    JSONObject getNetworkDeviceInfo(String deviceName);

    /**
     * 获取所有网络接口信息
     *
     * @return 网络接口列表
     */
    JSONObject getAllNetworkInterfaces();
}

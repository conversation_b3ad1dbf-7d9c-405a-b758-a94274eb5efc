package com.geeksec.admin.interfaces.rest;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.admin.application.DataMaintenanceApplicationService;
import com.geeksec.admin.interfaces.dto.DataLifecycleConfigDto;
import com.geeksec.admin.interfaces.dto.MaintenanceTaskDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据维护控制器 - 基于Doris动态分区的数据生命周期管理
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Tag(name = "数据维护管理", description = "提供数据分区管理、生命周期管理、备份恢复等功能")
@RestController
@RequestMapping("/api/maintenance")
@RequiredArgsConstructor
@Validated
public class DataMaintenanceController {

    private final DataMaintenanceApplicationService dataMaintenanceApplicationService;
    
    // ==================== 分区管理接口 ====================
    
    @Operation(summary = "获取表分区信息", description = "获取指定表的所有分区信息")
    @GetMapping("/partitions/{tableName}")
    public ApiResponse<List<Map<String, Object>>> getTablePartitions(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable @NotBlank(message = "表名不能为空") String tableName) {
        log.debug("获取表分区信息, 表名: {}", tableName);
        return ApiResponse.success(dataMaintenanceApplicationService.getTablePartitions(tableName));
    }
    
    @Operation(summary = "获取会话日志表分区信息", description = "获取会话日志表的分区信息")
    @GetMapping("/partitions/session-logs")
    public ApiResponse<List<Map<String, Object>>> getSessionLogPartitions() {
        log.debug("获取会话日志表分区信息");
        return ApiResponse.success(dataMaintenanceApplicationService.getSessionLogPartitions());
    }
    
    @Operation(summary = "获取分区存储统计", description = "获取指定表的分区存储统计信息")
    @GetMapping("/partitions/{tableName}/stats")
    public ApiResponse<Map<String, Object>> getPartitionStorageStats(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable @NotBlank(message = "表名不能为空") String tableName) {
        log.debug("获取分区存储统计, 表名: {}", tableName);
        return ApiResponse.success(dataMaintenanceApplicationService.getPartitionStorageStats(tableName));
    }
    
    @Operation(summary = "检查过期分区", description = "检查指定表的过期分区")
    @GetMapping("/partitions/{tableName}/expired")
    public ApiResponse<List<Map<String, Object>>> checkExpiredPartitions(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable String tableName,
            @Parameter(description = "保留天数", example = "90")
            @RequestParam(defaultValue = "90") int retentionDays) {
        log.debug("检查过期分区, 表名: {}, 保留天数: {}", tableName, retentionDays);
        return ApiResponse.success(dataMaintenanceService.checkExpiredPartitions(tableName, retentionDays));
    }
    
    // ==================== 生命周期管理接口 ====================
    
    @Operation(summary = "获取数据生命周期配置", description = "获取指定表的数据生命周期配置")
    @GetMapping("/lifecycle/{tableName}")
    public ApiResponse<Map<String, Object>> getDataLifecycleConfig(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable String tableName) {
        log.debug("获取数据生命周期配置, 表名: {}", tableName);
        return ApiResponse.success(dataMaintenanceApplicationService.getDataLifecycleConfig(tableName));
    }
    
    @Operation(summary = "更新数据生命周期配置", description = "更新指定表的数据生命周期配置")
    @PutMapping("/lifecycle/{tableName}")
    public ApiResponse<Map<String, Object>> updateDataLifecycleConfig(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable String tableName,
            @Parameter(description = "生命周期配置", required = true)
            @RequestBody Map<String, Object> config) {
        log.debug("更新数据生命周期配置, 表名: {}, 配置: {}", tableName, config);
        return ApiResponse.success(dataMaintenanceApplicationService.updateDataLifecycleConfig(tableName, config));
    }
    
    @Operation(summary = "获取数据清理状态", description = "获取系统数据清理状态")
    @GetMapping("/cleanup/status")
    public ApiResponse<Map<String, Object>> getDataCleanupStatus() {
        log.debug("获取数据清理状态");
        return ApiResponse.success(dataMaintenanceService.getDataCleanupStatus());
    }
    
    // ==================== 存储管理接口 ====================
    
    @Operation(summary = "获取存储使用情况", description = "获取系统存储空间使用情况")
    @GetMapping("/storage/usage")
    public ApiResponse<Map<String, Object>> getStorageUsage() {
        log.debug("获取存储使用情况");
        return ApiResponse.success(dataMaintenanceService.getStorageUsage());
    }
    
    @Operation(summary = "获取数据增长趋势", description = "获取指定天数的数据增长趋势")
    @GetMapping("/storage/growth-trend")
    public ApiResponse<List<Map<String, Object>>> getDataGrowthTrend(
            @Parameter(description = "统计天数", example = "30")
            @RequestParam(defaultValue = "30") int days) {
        log.debug("获取数据增长趋势, 统计天数: {}", days);
        return ApiResponse.success(dataMaintenanceService.getDataGrowthTrend(days));
    }
    
    @Operation(summary = "执行数据压缩", description = "对指定表或分区执行数据压缩优化")
    @PostMapping("/storage/compaction")
    public ApiResponse<Map<String, Object>> executeDataCompaction(
            @Parameter(description = "表名", required = true)
            @RequestParam String tableName,
            @Parameter(description = "分区名", example = "p20240101")
            @RequestParam(required = false) String partitionName) {
        log.debug("执行数据压缩, 表名: {}, 分区名: {}", tableName, partitionName);
        return ApiResponse.success(dataMaintenanceService.executeDataCompaction(tableName, partitionName));
    }
    
    // ==================== 统计信息管理接口 ====================
    
    @Operation(summary = "获取表统计信息", description = "获取指定表的统计信息")
    @GetMapping("/statistics/{tableName}")
    public ApiResponse<Map<String, Object>> getTableStatistics(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable String tableName) {
        log.debug("获取表统计信息, 表名: {}", tableName);
        return ApiResponse.success(dataMaintenanceService.getTableStatistics(tableName));
    }
    
    @Operation(summary = "刷新表统计信息", description = "刷新指定表的统计信息")
    @PostMapping("/statistics/{tableName}/refresh")
    public ApiResponse<Map<String, Object>> refreshTableStatistics(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable String tableName) {
        log.debug("刷新表统计信息, 表名: {}", tableName);
        return ApiResponse.success(dataMaintenanceService.refreshTableStatistics(tableName));
    }
    
    // ==================== 数据质量管理接口 ====================
    
    @Operation(summary = "获取数据质量报告", description = "获取指定时间范围内的数据质量报告")
    @GetMapping("/quality/{tableName}/report")
    public ApiResponse<Map<String, Object>> getDataQualityReport(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable String tableName,
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        log.debug("获取数据质量报告, 表名: {}, 开始时间: {}, 结束时间: {}", tableName, startTime, endTime);
        return ApiResponse.success(dataMaintenanceService.getDataQualityReport(tableName, startTime, endTime));
    }
    
    @Operation(summary = "检查数据完整性", description = "检查指定表的数据完整性")
    @PostMapping("/quality/{tableName}/integrity")
    public ApiResponse<Map<String, Object>> checkDataIntegrity(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable String tableName,
            @Parameter(description = "检查类型", example = "CONSISTENCY")
            @RequestParam String checkType) {
        log.debug("检查数据完整性, 表名: {}, 检查类型: {}", tableName, checkType);
        return ApiResponse.success(dataMaintenanceService.checkDataIntegrity(tableName, checkType));
    }
    
    // ==================== 系统监控接口 ====================
    
    @Operation(summary = "获取系统健康状态", description = "获取系统整体健康状态")
    @GetMapping("/system/health")
    public ApiResponse<Map<String, Object>> getSystemHealthStatus() {
        log.debug("获取系统健康状态");
        return ApiResponse.success(dataMaintenanceService.getSystemHealthStatus());
    }
    
    @Operation(summary = "获取性能监控指标", description = "获取系统性能监控指标")
    @GetMapping("/system/metrics")
    public ApiResponse<Map<String, Object>> getPerformanceMetrics() {
        log.debug("获取性能监控指标");
        return ApiResponse.success(dataMaintenanceService.getPerformanceMetrics());
    }

    // ==================== 备份恢复接口 ====================

    @Operation(summary = "执行数据备份", description = "对指定表执行数据备份")
    @PostMapping("/backup")
    public ApiResponse<Map<String, Object>> executeDataBackup(
            @Parameter(description = "表名", required = true)
            @RequestParam String tableName,
            @Parameter(description = "备份类型", example = "FULL")
            @RequestParam(defaultValue = "FULL") String backupType) {
        log.debug("执行数据备份, 表名: {}, 备份类型: {}", tableName, backupType);
        return ApiResponse.success(dataMaintenanceService.executeDataBackup(tableName, backupType));
    }

    @Operation(summary = "获取备份历史", description = "获取指定表的备份历史")
    @GetMapping("/backup/{tableName}/history")
    public ApiResponse<List<Map<String, Object>>> getBackupHistory(
            @Parameter(description = "表名", required = true, example = "dwd_session_logs")
            @PathVariable String tableName) {
        log.debug("获取备份历史, 表名: {}", tableName);
        return ApiResponse.success(dataMaintenanceService.getBackupHistory(tableName));
    }

    @Operation(summary = "执行数据恢复", description = "从备份恢复数据")
    @PostMapping("/restore")
    public ApiResponse<Map<String, Object>> executeDataRestore(
            @Parameter(description = "备份ID", required = true)
            @RequestParam String backupId,
            @Parameter(description = "目标表名", required = true)
            @RequestParam String targetTable) {
        log.debug("执行数据恢复, 备份ID: {}, 目标表: {}", backupId, targetTable);
        return ApiResponse.success(dataMaintenanceService.executeDataRestore(backupId, targetTable));
    }

    // ==================== 维护任务管理接口 ====================

    @Operation(summary = "获取维护任务列表", description = "获取所有维护任务列表")
    @GetMapping("/tasks")
    public ApiResponse<List<Map<String, Object>>> getMaintenanceTasks() {
        log.debug("获取维护任务列表");
        return ApiResponse.success(dataMaintenanceService.getMaintenanceTasks());
    }

    @Operation(summary = "创建维护任务", description = "创建新的维护任务")
    @PostMapping("/tasks")
    public ApiResponse<Map<String, Object>> createMaintenanceTask(
            @Parameter(description = "任务配置", required = true)
            @RequestBody @Valid MaintenanceTaskDto taskDto) {
        log.debug("创建维护任务, 配置: {}", taskDto);

        // 转换DTO为Map（实际项目中应该有专门的转换器）
        Map<String, Object> taskConfig = Map.of(
            "task_name", taskDto.getTaskName(),
            "task_type", taskDto.getTaskType(),
            "priority", taskDto.getPriority() != null ? taskDto.getPriority() : "NORMAL",
            "target_table", taskDto.getTargetTable() != null ? taskDto.getTargetTable() : "",
            "scheduled_time", taskDto.getScheduledTime() != null ? taskDto.getScheduledTime() : LocalDateTime.now(),
            "task_config", taskDto.getTaskConfig() != null ? taskDto.getTaskConfig() : Map.of()
        );

        return ApiResponse.success(dataMaintenanceApplicationService.createMaintenanceTask(taskConfig));
    }

    @Operation(summary = "执行维护任务", description = "执行指定的维护任务")
    @PostMapping("/tasks/{taskId}/execute")
    public ApiResponse<Map<String, Object>> executeMaintenanceTask(
            @Parameter(description = "任务ID", required = true, example = "task_001")
            @PathVariable String taskId) {
        log.debug("执行维护任务, 任务ID: {}", taskId);
        return ApiResponse.success(dataMaintenanceService.executeMaintenanceTask(taskId));
    }

    @Operation(summary = "获取维护任务状态", description = "获取指定维护任务的状态")
    @GetMapping("/tasks/{taskId}/status")
    public ApiResponse<Map<String, Object>> getMaintenanceTaskStatus(
            @Parameter(description = "任务ID", required = true, example = "task_001")
            @PathVariable String taskId) {
        log.debug("获取维护任务状态, 任务ID: {}", taskId);
        return ApiResponse.success(dataMaintenanceService.getMaintenanceTaskStatus(taskId));
    }

    @Operation(summary = "取消维护任务", description = "取消指定的维护任务")
    @PostMapping("/tasks/{taskId}/cancel")
    public ApiResponse<Map<String, Object>> cancelMaintenanceTask(
            @Parameter(description = "任务ID", required = true, example = "task_001")
            @PathVariable String taskId) {
        log.debug("取消维护任务, 任务ID: {}", taskId);
        return ApiResponse.success(dataMaintenanceService.cancelMaintenanceTask(taskId));
    }
}

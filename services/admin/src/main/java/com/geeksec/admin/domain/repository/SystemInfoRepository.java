package com.geeksec.admin.domain.repository;

import com.geeksec.admin.domain.model.SystemInfo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 系统信息仓储接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface SystemInfoRepository {
    
    /**
     * 根据主机名查找系统信息
     * 
     * @param hostname 主机名
     * @return 系统信息
     */
    Optional<SystemInfo> findByHostname(String hostname);
    
    /**
     * 获取当前系统信息
     * 
     * @return 系统信息
     */
    Optional<SystemInfo> getCurrentSystemInfo();
    
    /**
     * 查找所有系统信息
     * 
     * @return 系统信息列表
     */
    List<SystemInfo> findAll();
    
    /**
     * 根据状态查找系统信息
     * 
     * @param status 系统状态
     * @return 系统信息列表
     */
    List<SystemInfo> findByStatus(SystemInfo.SystemStatus status);
    
    /**
     * 查找指定时间范围内更新的系统信息
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 系统信息列表
     */
    List<SystemInfo> findByLastUpdateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 保存系统信息
     * 
     * @param systemInfo 系统信息
     * @return 保存后的系统信息
     */
    SystemInfo save(SystemInfo systemInfo);
    
    /**
     * 删除系统信息
     * 
     * @param hostname 主机名
     */
    void deleteByHostname(String hostname);
    
    /**
     * 检查系统信息是否存在
     * 
     * @param hostname 主机名
     * @return 是否存在
     */
    boolean existsByHostname(String hostname);
    
    /**
     * 统计系统数量
     * 
     * @return 系统数量
     */
    long count();
    
    /**
     * 统计指定状态的系统数量
     * 
     * @param status 系统状态
     * @return 系统数量
     */
    long countByStatus(SystemInfo.SystemStatus status);
}

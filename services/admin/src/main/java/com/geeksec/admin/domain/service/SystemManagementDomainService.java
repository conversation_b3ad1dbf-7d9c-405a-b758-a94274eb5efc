package com.geeksec.admin.domain.service;

import com.geeksec.admin.domain.model.DiskManagement;
import com.geeksec.admin.domain.model.SystemInfo;

import java.util.List;

/**
 * 系统管理领域服务接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface SystemManagementDomainService {
    
    /**
     * 获取系统信息
     * 
     * @return 系统信息
     */
    SystemInfo getSystemInfo();
    
    /**
     * 更新系统信息
     * 
     * @param systemInfo 系统信息
     * @return 更新后的系统信息
     */
    SystemInfo updateSystemInfo(SystemInfo systemInfo);
    
    /**
     * 获取磁盘管理信息
     * 
     * @return 磁盘管理信息列表
     */
    List<DiskManagement> getDiskManagements();
    
    /**
     * 根据设备路径获取磁盘管理信息
     * 
     * @param devicePath 设备路径
     * @return 磁盘管理信息
     */
    DiskManagement getDiskManagement(String devicePath);
    
    /**
     * 挂载磁盘
     * 
     * @param devicePath 设备路径
     * @param mountPoint 挂载点
     * @param executor 执行者
     * @return 是否成功
     */
    boolean mountDisk(String devicePath, String mountPoint, String executor);
    
    /**
     * 卸载磁盘
     * 
     * @param devicePath 设备路径
     * @param executor 执行者
     * @return 是否成功
     */
    boolean unmountDisk(String devicePath, String executor);
    
    /**
     * 格式化磁盘
     * 
     * @param devicePath 设备路径
     * @param fileSystemType 文件系统类型
     * @param executor 执行者
     * @return 是否成功
     */
    boolean formatDisk(String devicePath, String fileSystemType, String executor);
    
    /**
     * 重建RAID
     * 
     * @param raidDevicePath RAID设备路径
     * @param executor 执行者
     * @return 是否成功启动重建
     */
    boolean rebuildRaid(String raidDevicePath, String executor);
    
    /**
     * 获取RAID信息
     * 
     * @return RAID磁盘列表
     */
    List<DiskManagement> getRaidInfo();
    
    /**
     * 检查磁盘健康状态
     * 
     * @param devicePath 设备路径
     * @return 健康状态
     */
    DiskManagement.HealthStatus checkDiskHealth(String devicePath);
    
    /**
     * 更新磁盘健康状态
     * 
     * @param devicePath 设备路径
     * @param healthStatus 健康状态
     * @param executor 执行者
     * @return 是否成功
     */
    boolean updateDiskHealthStatus(String devicePath, DiskManagement.HealthStatus healthStatus, String executor);
    
    /**
     * 获取磁盘使用率告警列表
     * 
     * @param warningThreshold 告警阈值
     * @return 告警磁盘列表
     */
    List<DiskManagement> getDiskUsageWarnings(double warningThreshold);
    
    /**
     * 执行系统关机
     * 
     * @param executor 执行者
     * @return 是否成功
     */
    boolean shutdownSystem(String executor);
    
    /**
     * 执行系统重启
     * 
     * @param executor 执行者
     * @return 是否成功
     */
    boolean rebootSystem(String executor);
    
    /**
     * 检查系统健康状态
     * 
     * @return 系统健康状态
     */
    SystemInfo.SystemStatus checkSystemHealth();
    
    /**
     * 获取系统性能指标
     * 
     * @return 系统信息（包含性能指标）
     */
    SystemInfo getSystemPerformanceMetrics();
}

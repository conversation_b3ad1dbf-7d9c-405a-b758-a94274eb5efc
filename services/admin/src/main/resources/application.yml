server:
  port: 8090
  servlet:
    context-path: /admin

spring:
  application:
    name: admin-service
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: org.postgresql.Driver
      url: ****************************************
      username: ${DB_USERNAME:nta_user}
      password: ${DB_PASSWORD:nta_password}
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

# MyBatis-Flex配置
mybatis-flex:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.geeksec.admin.model
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# 日志配置
logging:
  level:
    com.geeksec.admin: DEBUG
    org.springframework.web: INFO
    com.mybatisflex: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# Knife4j配置
knife4j:
  enable: true
  openapi:
    title: NTA 3.0 系统管理服务 API
    description: 提供系统管理、数据维护、网络配置、磁盘管理等功能的API接口
    version: 3.0.0
    concat: NTA Team
    url: https://github.com/geeksec/nta
  setting:
    language: zh_cn

# 系统管理配置
system-management:
  # 命令执行超时时间（秒）
  command-timeout: 30
  # NTP配置
  ntp:
    # NTP配置文件路径
    config-file: /etc/ntp.conf
    # NTP配置备份路径
    backup-file: /etc/ntp.conf.backup
    # NTP服务名称
    service-name: ntpd
  # 系统权限配置
  privileges:
    # 是否使用sudo执行系统命令
    use-sudo: true
    # sudo命令路径
    sudo-path: /usr/bin/sudo
  # 磁盘管理配置
  disk:
    # 磁盘操作超时时间（秒）
    operation-timeout: 300
    # 支持的文件系统类型
    supported-filesystems: [ext4, xfs, btrfs]
  # 网络管理配置
  network:
    # 网络配置文件路径
    config-path: /etc/sysconfig/network-scripts
    # 网络服务重启命令
    restart-command: systemctl restart network

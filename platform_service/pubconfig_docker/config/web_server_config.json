{"server_port": 9000, "ws_service_port": 9001, "db_user": "root", "db_pass": "root", "db_name": "statistical_information_base", "db_addr": "127.0.0.1:3306", "cert_db_name": "ca_db", "auth_db_name": "auth_db", "push_db_name": "push_database", "cert_upload_path": "./upload/certs/", "so_upload_path": "/opt/GeekSec/th/bin/JsonRule/BasicRule/LibFolder/", "so_config_upload_path": "/opt/GeekSec/th/bin/LibConfig/", "plug_upload_path": "/opt/GeekSec/web/rule_syn/log_plug/", "rule_upload_path": "./upload/rules/", "model_upload_path": "./upload/so", "syn_rule_path": "/opt/GeekSec/web/rule_syn/synrule.sh", "syn_cert_bw_path": "/opt/GeekSec/web/rule_syn/syncertbw.sh", "token_expired_time": 360000, "es_url": "http://127.0.0.1:9200/", "ws_host": "127.0.0.1:19002", "ws_path": "/", "user_import_cert_path": "/opt/GeekSecCA/check_cert/conf/", "check_cert_script_path": "/opt/GeekSecCA/check_cert/bin/check_cert.sh", "max_rule": 200000, "syn_pcap_path": "/opt/GeekSec/web/rule_syn/synpcap.sh", "syn_task_path": "/opt/GeekSec/web/rule_syn/task/task_conf_clear.sh", "query_size_path": "/opt/GeekSec/web/rule_syn/task/query_size.sh", "syn_check_plug_path": "/opt/GeekSec/web/rule_syn/syncheckplug.sh", "syn_log_plug_path": "/opt/GeekSec/web/rule_syn/synlogplug.sh", "syn_line_path": "/opt/GeekSec/web/rule_syn/line/synline.sh", "query_line_path": "/opt/GeekSec/web/rule_syn/line/queryline.sh", "query_size_th_path": "/opt/GeekSec/web/rule_syn/task/query_size_th.sh", "task_conf_path": "/opt/GeekSec/web/rule_syn/task/task_conf_clear.sh", "config_cert_check_path": "/opt/GeekSec/web/rule_syn/cert_config.sh", "es_connect_index": "connectinfo_*", "es_dns_index": "dns_*", "es_ssl_index": "ssl_*", "es_ssh_index": "ssh_*", "es_http_index": "http_*", "es_s7_index": "s7_*", "es_modbus_index": "modbus_*", "es_noip_index": "noip_*", "shutdown_restart_path": "/opt/GeekSec/web/rule_syn/shutdown_restart.sh", "pcap_origin_path": "/data/pcapfiles", "pcap_origin_noip_path": "/data/pcapfiles/noip_packet", "pcap_download_path": "/var/ftp/flow_pcap", "merge_pcap_path": "/opt/GeekSec/web/rule_syn/merge_cap.sh", "filter_pcap_path": "/opt/GeekSec/th/bin/pcap_filter"}
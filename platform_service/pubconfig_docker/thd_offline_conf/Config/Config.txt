{"Basic": {"DeviceNO": "123-456", "Mission": "testn", "OfflineFolder": "./OfflineFolder", "FirstPro": 12}, "PFRing": {"Interface": ["wlp3s0"], "ThreadNum": 2, "RenewInterval_Sec": 120, "CPU": [1, 2, 3, 4], "BalanceCore": 11}, "Connect": {"IsIP": 1, "IsUDP": 1, "SYNSign": 0}, "ConnectInfor": {"ConnectNum_IPv4": 10000, "ConnectNum_IPv6": 1232, "TimeInterval": 90}, "LanProbe": {"FirstPro": [10, 12, 113, 277], "SaveType_Pcap": 1, "StartTime": "2018-01-19 16:51:00", "LogFolder": "./FlowLog", "RuleFolder": "./JsonRule/BasicRule", "EngineFolder": "./JsonRule/BasicEngine", "SaveFolder": "./PcapFolder", "SaveFlow": 0, "SaveFirstPacket": 1, "RuleLog_Folder": "./FlowLog/RuleLog", "RuleLog_MinLevel": 20}, "TileraFrame": {"PageNum": 1, "rx": ["eth0", "eth1"], "GroupNumOfEachLink": 8, "BucketNumOfEachLink": 8, "RingNumOfEachLink": 8, "BucketMode": 1, "NodeOfEachRing": 65536}, "Extract_HTTP_Title": ["^Sec-WebSocket", "^Get", "^Post", "^HTTP", "^<PERSON><PERSON><PERSON>", "^User-", "^Server", "^Date", "^<PERSON><PERSON>", "^Host", "^Last-Modified", "^Expires", "^Content", "^Connect", "^Accept", "^Access", "^Origin", "^x-xss", "^x-content", "^x-frame", "^strict-transport", "^public-key", "^Age", "^ETag", "^Location", "^Proxy", "^Retry", "^Vary", "^www-Auth", "^upgrade"], "Extract": {"LogFolder": "./FlowLog/Extract", "PcapFolder": "./PcapFolder/Extract_AbnormalPacket", "CertFolder": "./FlowLog/Cert", "BlockNum": 1024, "IsConnect": 1, "IsDNSQuery": 1, "IsDNS": 1, "IsHTTP": 1, "IsTSL": 1}, "ProtocolAnalyse": {"SaveFolder": "./PcapFolder/ProAnalyse", "TLS_HandShake": 1, "AbnormalPro": 1, "AbnormalIPTTL": 5}, "KeepOrder": {"PacketNum": 10}, "Port53": {"IPNum": 50000, "Folder": "./FlowLog/Port53", "TimeInterval": 86400}, "BytesDistribute": {"MaxBytes": 2147483648, "MinBytes": 2048, "Duration": 60}, "PlusIn_Json": {}, "IPStatistic": {"VIP": [], "Network": [], "DMZ": [], "SaveFolder": "./PcapFolder", "MacNum": 1000, "IPv4Num": 10000, "IPv6Num": 5000, "TimeInterval_IPClear": 1600, "TimeInterval_IPInfor": 120, "SYNSample": 1048575, "DDOS": {"LogFolder": "./FlowLog/DDOS_Log", "TimeInterval_Judge": 30, "Threshold_Packet": 1000, "Times_SYN_Judge": 4, "Times_Connect_Judge": 4, "Hour_BasicLine_Judge": 24, "Times_BasicLine_Judge": 10}, "IntraIPNum": 0, "MacNum_Extra": 0, "ENTABLE": false}, "DDOS_Defense": {"Type": 4, "RenewTimeInterval": 30, "IntraIPv4": [{"IP": "***********", "Mask": "***********"}], "IntraMac": [], "IPv4Num": 200000, "IPv6Num": 5000, "DDOS": {"TimeInterval_Judge": 6, "Times_TCP": 20, "Times_DNS": 20, "Times_ICMP": 20, "Times_UDP": 20, "mbps_Basic": 10, "PacketNum_Basic": 1000}}, "Filter": {"Respond": "drop", "Rule": []}, "MacDefense": {"LegalMac": [], "MacInfor": [], "TimeInterval_Statistics": 3600, "TimeInterval_Alert": 6, "Param": {"BasicLine_PacketNum": 60, "Times_ARP": 4, "Times_LLDP": 4, "Times_BroadCast": 4}}, "IPDefense": {"Type": ["IntraIP", "LegalIP", "All"], "IPv4Num": 500000, "IPv6Num": 1000, "RenewTime": 120, "IPInfor": [], "Param": {"TimeInterval_Judge": 6, "BasicLine_bps": 1000, "BasicLine_PacketNum": 600, "CheckSum": [256, 256], "DDOS_CheckSum": [4, 4], "DDOS_SYN": 4, "DDOS_FIN": 4, "DDOS_DNS": 4, "DDOS_ICMP": 4, "DDOS_IGMP": 4, "DDOS_UDP": 4, "DDOS_Frag": 4, "DDOS_Multicast": 4, "MaxOffset_IP": 1250}}}
# Last Update:2020-12-29 13:31:11
##
# @file del_tag.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-12-29

import sys, pymysql,json

base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    print(sql)
    cur.execute(sql)
    x_db.commit()
def del_tag(table_name ,clud_name  , target_name , tag_id ):
    # 查询是否以及有记录存在
    if  clud_name == "app" or  clud_name == "port":
        where_target = clud_name +" = "+ target_name
    else :
        where_target = clud_name +" = '"+ target_name+"'"
    sql =  "update " + table_name + " set sign = 2  where "+where_target +" and tag_id = "+tag_id
    idu_mysql(sql_m,cursor,db)

        
def t_del_tag(json_str):
    json_j = json.loads(json_str)
    table_name = json_j["table"]
    clud_name = json_j["clud_name"]
    target_name = json_j["target_name"]
    tag_id = json_j["tag_id"]
    del_tag(table_name , clud_name ,target_name ,tag_id )
if len(sys.argv) != 2:
    print("参数个数为",len(sys.argv))
    print("参数错误")
    sys.exit(1)

t_del_tag(sys.argv[1].replace("'","\"",1000)) 


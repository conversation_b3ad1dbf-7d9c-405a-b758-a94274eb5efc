<config>
    <!-- TAG_INFO -->
    <TAG_DIC_INSERT name="get selet name">
        <url type="put">insert into tb_tag_info (tag_id ,tag_type,tag_text,Tag_Remark,tag_num,Default_Black_List,Default_White_List,Black_List,White_List,Created_Time,Last_Created_Time) values </url>
        <urlparam>tidb</urlparam>
        <postdata>(@tagId,@tagType,'@tagText','@tagRemark',@tagNum,@defaultBL,@defaultWL,@blackL,@whiteL,@createdT,@lastCT)</postdata>
        <key_value type="json" part_data="@_tid" key="@tagId">
            <key name="@_tid">Tag_Id</key>
        </key_value>
        <key_value type="json" part_data="@_ttype" key="@tagType">
            <key name="@_ttype">Tag_Type</key>
        </key_value>
        <key_value type="json" part_data="@_ttext" key="@tagText">
            <key name="@_ttext">Tag_Text</key>
        </key_value>
        <key_value type="json" part_data="@_tremark" key="@tagRemark">
            <key name="@_tremark">Tag_Remark</key>
        </key_value>
        <key_value type="json" part_data="@_tnum" key="@tagNum">
            <key name="@_tnum">Tag_Num</key>
        </key_value>
        <key_value type="json" part_data="@_defaultblackl" key="@defaultBL">
            <key name="@_defaultblackl">Default_Black_List</key>
        </key_value>
        <key_value type="json" part_data="@_defaultwhitel" key="@defaultWL">
            <key name="@_defaultwhitel">Default_White_List</key>
        </key_value>
        <key_value type="json" part_data="@_blackl" key="@blackL">
            <key name="@_blackl">Black_List</key>
        </key_value>
        <key_value type="json" part_data="@_whitel" key="@whiteL">
            <key name="@_whitel">White_List</key>
        </key_value>
        <key_value type="json" part_data="@_createdt" key="@createdT">
            <key name="@_createdt">Created_Time</key>
        </key_value>
        <key_value type="json" part_data="@_lastct" key="@lastCT">
            <key name="@_lastct">Last_Created_Time</key>
        </key_value>
    </TAG_DIC_INSERT>
    <TAG_DIC_DELETE name="get selet name">
        <url type="put">delete from tb_tag_info </url>
        <urlparam>tidb</urlparam>
        <postdata>where @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2" key="@tiaojian">
            <moudle part_data=" @key in (@value) " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Tag_Id </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
            <moudle part_data=" @key in (@value) " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_Tag_Text </key>
                    <key name="@value">Tag_Text</key>
                </value>
            </moudle>
        </key_value>
    </TAG_DIC_DELETE>
    <TAG_DIC_UPDATE name="get selet name">
        <url type="put">update tb_tag_info set </url>
        <urlparam>tidb</urlparam>
        <postdata> @tiaojian where Tag_Id = @tagId</postdata>
        <key_value type="json" part_data="@_tid" key="@tagId">
            <key name="@_tid">Tag_Id</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3 @KMoudle4 @KMoudle5 @KMoudle6 @KMoudle7 @KMoudle8 @KMoudle9 @KMoudle0" key="@tiaojian">
            <moudle part_data=" @key = @value " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Last_Created_Time </key>
                    <key name="@value">Last_Created_Time</key>
                </value>
            </moudle>
            <moudle part_data=", @key = @value " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_Tag_Type </key>
                    <key name="@value">Tag_Type</key>
                </value>
            </moudle>
            <moudle part_data=", @key = '@value' " key="@KMoudle3">
                <value>
                    <key name="@key"> STRING_IS_Tag_Text </key>
                    <key name="@value">Tag_Text</key>
                </value>
            </moudle>
            <moudle part_data=", @key = '@value' " key="@KMoudle4">
                <value>
                    <key name="@key"> STRING_IS_Tag_Remark </key>
                    <key name="@value">Tag_Remark</key>
                </value>
            </moudle>
            <moudle part_data=", @key = @value " key="@KMoudle5">
                <value>
                    <key name="@key"> STRING_IS_Tag_Num </key>
                    <key name="@value">Tag_Num</key>
                </value>
            </moudle>
            <moudle part_data=", @key = @value " key="@KMoudle6">
                <value>
                    <key name="@key"> STRING_IS_Default_Black_List </key>
                    <key name="@value">Default_Black_List</key>
                </value>
            </moudle>
            <moudle part_data=", @key = @value " key="@KMoudle7">
                <value>
                    <key name="@key"> STRING_IS_Default_White_List </key>
                    <key name="@value">Default_White_List</key>
                </value>
            </moudle>
            <moudle part_data=", @key = @value " key="@KMoudle8">
                <value>
                    <key name="@key"> STRING_IS_Black_List </key>
                    <key name="@value">Black_List</key>
                </value>
            </moudle>
            <moudle part_data=", @key = @value " key="@KMoudle9">
                <value>
                    <key name="@key"> STRING_IS_White_List </key>
                    <key name="@value">White_List</key>
                </value>
            </moudle>
            <moudle part_data=", @key = @value " key="@KMoudle0">
                <value>
                    <key name="@key"> STRING_IS_Created_Time </key>
                    <key name="@value">Created_Time</key>
                </value>
            </moudle>
        </key_value>
    </TAG_DIC_UPDATE>
    <TAG_DIC_SELECT name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>tb_tag_info where 1 = 1 @tiaojian </postdata>
        <key_value type="json" part_data="@Tag_Id_B @Tag_Id_E @Tag_Type_B @Tag_Type_E @Tag_Text @Tag_Remark @Tag_Num_B @Tag_Num_E @Default_Black_List_B @Default_Black_List_E @Default_White_List_B @Black_List_B @Black_List_E @White_List_B @White_List_E @Created_Time_B @Created_Time_E @Last_Created_Time_B @Last_Created_Time_E @sort @formsize" key="@tiaojian">
            <moudle part_data=" and @key @value " key="@Tag_Id_B">
                <value>
                    <key name="@key"> STRING_IS_Tag_Id &gt;= </key>
                    <key name="@value">Tag_Id_B</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Tag_Id_E">
                <value>
                    <key name="@key"> STRING_IS_Tag_Id &lt;= </key>
                    <key name="@value">Tag_Id_E</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Tag_Type_B">
                <value>
                    <key name="@key"> STRING_IS_Tag_Type &gt;= </key>
                    <key name="@value">Tag_Type_B</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Tag_Type_E">
                <value>
                    <key name="@key"> STRING_IS_Tag_Type &lt;= </key>
                    <key name="@value">Tag_Type_E</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = '@value' " key="@Tag_Text">
                <value>
                    <key name="@key"> STRING_IS_Tag_Text </key>
                    <key name="@value">Tag_Text</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = '@value' " key="@Tag_Remark">
                <value>
                    <key name="@key"> STRING_IS_Tag_Remark </key>
                    <key name="@value">Tag_Remark</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Tag_Num_B">
                <value>
                    <key name="@key"> STRING_IS_Tag_Num &gt;= </key>
                    <key name="@value">Tag_Num_B</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Tag_Num_E">
                <value>
                    <key name="@key"> STRING_IS_Tag_Num &lt;= </key>
                    <key name="@value">Tag_Num_E</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Default_Black_List_B">
                <value>
                    <key name="@key"> STRING_IS_Default_Black_List &gt;= </key>
                    <key name="@value">Default_Black_List_B</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Default_Black_List_E">
                <value>
                    <key name="@key"> STRING_IS_Default_Black_List &lt;= </key>
                    <key name="@value">Default_Black_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Default_White_List_B">
                <value>
                    <key name="@key"> STRING_IS_Default_White_List &gt;= </key>
                    <key name="@value">Default_White_List_B</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Default_White_List_E">
                <value>
                    <key name="@key"> STRING_IS_Default_White_List &lt;= </key>
                    <key name="@value">Default_White_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Black_List_B">
                <value>
                    <key name="@key"> STRING_IS_Black_List &gt;= </key>
                    <key name="@value">Black_List_B</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Black_List_E">
                <value>
                    <key name="@key"> STRING_IS_Black_List &lt;= </key>
                    <key name="@value">Black_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@White_List_B">
                <value>
                    <key name="@key"> STRING_IS_White_List &gt;= </key>
                    <key name="@value">White_List_B</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@White_List_E">
                <value>
                    <key name="@key"> STRING_IS_White_List &lt;= </key>
                    <key name="@value">White_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Created_Time_B">
                <value>
                    <key name="@key"> STRING_IS_Created_Time &gt;= </key>
                    <key name="@value">Created_Time_B</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Created_Time_E">
                <value>
                    <key name="@key"> STRING_IS_Created_Time &lt;= </key>
                    <key name="@value">Created_Time_E</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Last_Created_Time_B">
                <value>
                    <key name="@key"> STRING_IS_Last_Created_Time &gt;= </key>
                    <key name="@value">Last_Created_Time_B</key>
                </value>
            </moudle>
            <moudle part_data=" and @key @value " key="@Last_Created_Time_E">
                <value>
                    <key name="@key"> STRING_IS_Last_Created_Time &lt;= </key>
                    <key name="@value">Last_Created_Time_E</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@sort">
                <value>
                    <key name="@key">SortName</key>
                    <key name="@value">SortOrder</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@formsize">
                <value>
                    <key name="@key">From</key>
                    <key name="@value">Size</key>
                </value>
            </moudle>
        </key_value>
    </TAG_DIC_SELECT>
    <!-- ip_tag -->
    <TAG_IP_INSERT name="get selet name">
        <url type="put">insert into tb_ip_tag (ip,ipkey,tag_id,created_time) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@tip',@ipkey,@tagId,@time)</postdata>
        <key_value type="json" part_data="@_tip" key="@tip">
            <key name="@_tip">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_ipkey" key="@ipkey">
            <key name="@_ipkey">IpKey</key>
        </key_value>
        <key_value type="json" part_data="@_tid" key="@tagId">
            <key name="@_tid">Tag_Id</key>
        </key_value>
        <key_value type="json" part_data="@_time" key="@time">
            <key name="@_time">Time</key>
        </key_value>
    </TAG_IP_INSERT>
    <TAG_IP_DELETE name="get selet name">
        <url type="put">delete from tb_ip_tag </url>
        <urlparam>tidb</urlparam>
        <postdata>where @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2" key="@tiaojian">
            <moudle part_data=" @key in (@value) " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_ip </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" @key in (@value) " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_tagId </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
            <moudle part_data=" @key in (@value) " key="@KMoudle2">
                <value>
                    <key name="@key">  STRING_IS_TaskId</key>
                    <key name="@value">TaskId</key>
                </value>
            </moudle>
        </key_value>
    </TAG_IP_DELETE>
    <TAG_IP_UPDATE name="get selet name">
        <url type="put">update tb_ip_tag set </url>
        <urlparam>tidb</urlparam>
        <postdata> @tiaojian where ipkey = '@ipkey'</postdata>
        <key_value type="json" part_data="@_ipkey" key="@ipkey">
            <key name="@_ipkey">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <moudle part_data=" @key = @value " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_tagId </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
        </key_value>
    </TAG_IP_UPDATE>
    <TAG_IP_SELECT name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>tb_ip_tag where 1 = 1 @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3 @KMoudle4" key="@tiaojian">
            <moudle part_data=" and @key = '@value' " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_ip </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = @value " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_tagId </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = @value " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_TaskId </key>
                    <key name="@value">TaskId</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@KMoudle3">
                <value>
                    <key name="@key">SortName</key>
                    <key name="@value">SortOrder</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@KMoudle4">
                <value>
                    <key name="@key">From</key>
                    <key name="@value">Size</key>
                </value>
            </moudle>
        </key_value>
    </TAG_IP_SELECT>
    <!-- PORT_TAG -->
    <TAG_PORT_INSERT name="get selet name">
        <url type="put">insert into tb_port_tag (Port,tag_Id,created_time) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@tport',@tagId,@time)</postdata>
        <key_value type="json" part_data="@_tport" key="@tport">
            <key name="@_tport">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_tid" key="@tagId">
            <key name="@_tid">Tag_Id</key>
        </key_value>
        <key_value type="json" part_data="@_time" key="@time">
            <key name="@_time">Time</key>
        </key_value>
    </TAG_PORT_INSERT>
    <TAG_PORT_DELETE name="get selet name">
        <url type="put">delete from tb_port_tag </url>
        <urlparam>tidb</urlparam>
        <postdata>where @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2" key="@tiaojian">
            <moudle part_data=" @key in (@value) " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Port </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" @key in (@value) " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_tagId </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
        </key_value>
    </TAG_PORT_DELETE>
    <TAG_PORT_UPDATE name="get selet name">
        <url type="put">update tb_prot_tag set </url>
        <urlparam>tidb</urlparam>
        <postdata> @tiaojian where Port = '@tagport'</postdata>
        <key_value type="json" part_data="@_tport" key="@tagport">
            <key name="@_tport">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <moudle part_data=" @key = @value " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_tagId </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
        </key_value>
    </TAG_PORT_UPDATE>
    <TAG_PORT_SELECT name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>tb_prot_tag where 1 = 1 @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3 @KMoudle4" key="@tiaojian">
            <moudle part_data=" and @key = '@value' " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Port </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = @value " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_tagId </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@KMoudle3">
                <value>
                    <key name="@key">SortName</key>
                    <key name="@value">SortOrder</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@KMoudle4">
                <value>
                    <key name="@key">From</key>
                    <key name="@value">Size</key>
                </value>
            </moudle>
        </key_value>
    </TAG_PORT_SELECT>
    <!-- APP_TAG -->
    <TAG_APP_INSERT name="get selet name">
        <url type="put">insert into tb_app_tag (app_id,tag_id,created_time) values </url>
        <urlparam>tidb</urlparam>
        <postdata>(@tapp,@tagId,@time)</postdata>
        <key_value type="json" part_data="@_tapp" key="@tapp">
            <key name="@_tapp">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_tid" key="@tagId">
            <key name="@_tid">Tag_Id</key>
        </key_value>
        <key_value type="json" part_data="@_time" key="@time">
            <key name="@_time">Time</key>
        </key_value>
    </TAG_APP_INSERT>
    <TAG_APP_DELETE name="get selet name">
        <url type="put">delete from tb_app_tag </url>
        <urlparam>tidb</urlparam>
        <postdata>where @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2" key="@tiaojian">
            <moudle part_data=" @key in (@value) " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_App_Id </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" @key in (@value) " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_tagId </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
        </key_value>
    </TAG_APP_DELETE>
    <TAG_APP_UPDATE name="get selet name">
        <url type="put">update tb_app_tag set </url>
        <urlparam>tidb</urlparam>
        <postdata> @tiaojian where app_id = @tapp</postdata>
        <key_value type="json" part_data="@_tapp" key="@tapp">
            <key name="@_tapp">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <moudle part_data=" @key = @value " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_tagId </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
        </key_value>
    </TAG_APP_UPDATE>
    <TAG_APP_SELECT name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>tb_app_tag where 1 = 1 @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3 @KMoudle4" key="@tiaojian">
            <moudle part_data=" and @key = @value " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_App_Id </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = @value " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_tagId </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@KMoudle3">
                <value>
                    <key name="@key">SortName</key>
                    <key name="@value">SortOrder</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@KMoudle4">
                <value>
                    <key name="@key">From</key>
                    <key name="@value">Size</key>
                </value>
            </moudle>
        </key_value>
    </TAG_APP_SELECT>
    <!-- SESSION_ID_TAG -->
    <TAG_SESSION_INSERT name="get selet name">
        <url type="put">insert into tb_session_id_tag(session_id,tag_id,created_time) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@tsessionid',@tagId,@time)</postdata>
        <key_value type="json" part_data="@_ts" key="@tsessionid">
            <key name="@_ts">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_tid" key="@tagId">
            <key name="@_tid">Tag_Id</key>
        </key_value>
        <key_value type="json" part_data="@_time" key="@time">
            <key name="@_time">Time</key>
        </key_value>
    </TAG_SESSION_INSERT>
    <TAG_SESSION_DELETE name="get selet name">
        <url type="put">delete from tb_session_id_tag </url>
        <urlparam>tidb</urlparam>
        <postdata>where @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2" key="@tiaojian">
            <moudle part_data=" @key in (@value) " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_session_id </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" @key in (@value) " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_tag_id </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
        </key_value>
    </TAG_SESSION_DELETE>
    <TAG_SESSION_UPDATE name="get selet name">
        <url type="put">update tb_session_id_tag set </url>
        <urlparam>tidb</urlparam>
        <postdata> @tiaojian where session_id = '@tsessionid'</postdata>
        <key_value type="json" part_data="@_ts" key="@tsessionid">
            <key name="@_ts">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <moudle part_data=" @key = @value " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_tag_id </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
        </key_value>
    </TAG_SESSION_UPDATE>
    <TAG_SESSION_SELECT name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>tb_session_id_tag where 1 = 1 @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3 @KMoudle4" key="@tiaojian">
            <moudle part_data=" and @key = '@value' " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_session_id </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = @value " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_tag_id </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@KMoudle3">
                <value>
                    <key name="@key">SortName</key>
                    <key name="@value">SortOrder</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@KMoudle4">
                <value>
                    <key name="@key">From</key>
                    <key name="@value">Size</key>
                </value>
            </moudle>
        </key_value>
    </TAG_SESSION_SELECT>
    <!-- tb_cert_tag -->
    <TAG_CERT_INSERT name="get selet name">
        <url type="put">insert into tb_cert_tag (cert_sha1,tag_id,created_time) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@tcert',@tagId,@time)</postdata>
        <key_value type="json" part_data="@_tc" key="@tcert">
            <key name="@_tc">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_tid" key="@tagId">
            <key name="@_tid">Tag_Id</key>
        </key_value>
        <key_value type="json" part_data="@_time" key="@time">
            <key name="@_time">Time</key>
        </key_value>
    </TAG_CERT_INSERT>
    <TAG_CERT_DELETE name="get selet name">
        <url type="put">delete from tb_cert_tag </url>
        <urlparam>tidb</urlparam>
        <postdata>where @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2" key="@tiaojian">
            <moudle part_data=" @key in (@value) " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_CertSHA1 </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" @key in (@value) " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_tagId </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
        </key_value>
    </TAG_CERT_DELETE>
    <TAG_CERT_UPDATE name="get selet name">
        <url type="put">update tb_cert_tag set </url>
        <urlparam>tidb</urlparam>
        <postdata> @tiaojian where cert_sha1 = '@tcert'</postdata>
        <key_value type="json" part_data="@_tc" key="@tcert">
            <key name="@_tc">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <moudle part_data=" @key = @value " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_tagId </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
        </key_value>
    </TAG_CERT_UPDATE>
    <TAG_CERT_SELECT name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>tb_cert_tag where 1 = 1 @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3 @KMoudle4" key="@tiaojian">
            <moudle part_data=" and @key = '@value' " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_CertSHA1 </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = @value " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_tagId </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@KMoudle3">
                <value>
                    <key name="@key">SortName</key>
                    <key name="@value">SortOrder</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@KMoudle4">
                <value>
                    <key name="@key">From</key>
                    <key name="@value">Size</key>
                </value>
            </moudle>
        </key_value>
    </TAG_CERT_SELECT>
    <!-- Domain_TAG -->
    <TAG_DOMAIN_INSERT name="get selet name">
        <url type="put">insert into tb_domain_tag (domain,tag_id,created_time) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@tdm',@tagId,@time)</postdata>
        <key_value type="json" part_data="@_td" key="@tdm">
            <key name="@_td">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_tid" key="@tagId">
            <key name="@_tid">Tag_Id</key>
        </key_value>
        <key_value type="json" part_data="@_time" key="@time">
            <key name="@_time">Time</key>
        </key_value>
    </TAG_DOMAIN_INSERT>
    <TAG_DOMAIN_DELETE name="get selet name">
        <url type="put">delete from tb_domain_tag </url>
        <urlparam>tidb</urlparam>
        <postdata>where @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2" key="@tiaojian">
            <moudle part_data=" @key in (@value) " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Domain_Name </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" @key in (@value) " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_Tag_Id </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
        </key_value>
    </TAG_DOMAIN_DELETE>
    <TAG_DOMAIN_UPDATE name="get selet name">
        <url type="put">update tb_domain_tag set </url>
        <urlparam>tidb</urlparam>
        <postdata> @tiaojian where  domain = '@tdm'</postdata>
        <key_value type="json" part_data="@_td" key="@tdm">
            <key name="@_td">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <moudle part_data=" @key = @value " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Tag_Id </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
        </key_value>
    </TAG_DOMAIN_UPDATE>
    <TAG_DOMAIN_SELECT name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>tb_domain_tag where 1 = 1 @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3 @KMoudle4" key="@tiaojian">
            <moudle part_data=" and @key = '@value' " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Domain_Name </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = @value " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_Tag_Id </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@KMoudle3">
                <value>
                    <key name="@key">SortName</key>
                    <key name="@value">SortOrder</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@KMoudle4">
                <value>
                    <key name="@key">From</key>
                    <key name="@value">Size</key>
                </value>
            </moudle>
        </key_value>
    </TAG_DOMAIN_SELECT>
    <!-- 告警表tb_alarm-新增 -->
    <ADD_ALARM name="get selet name">
        <url type="put">insert into tb_alarm (target_name,target_type,tag_id,batch_id,time,nature,ip_type,longitude,latitude,src_ip,src_port,dst_ip,dst_port,state,uid,defense_info,remark) values </url>
        <urlparam>db</urlparam>
        <postdata>('@target_name', @target_type, @tag_id,@batch_id ,@time, @nature, @ip_type, '@longitude', '@latitude', '@src_ip', @src_port, '@dst_ip', @dst_port,0,'','@defense_info','@remark')</postdata>
        <key_value type="json" part_data="@_1" key="@target_name">
            <key name="@_1">target_name</key>
        </key_value>
        <key_value type="json" part_data="@_2" key="@target_type">
            <key name="@_2">target_type</key>
        </key_value>
        <key_value type="json" part_data="@_3" key="@tag_id">
            <key name="@_3">tag_id</key>
        </key_value>
        <key_value type="json" part_data="@_4" key="@time">
            <key name="@_4">time</key>
        </key_value>
        <key_value type="json" part_data="@_5" key="@nature">
            <key name="@_5">nature</key>
        </key_value>
        <key_value type="json" part_data="@_6" key="@ip_type">
            <key name="@_6">ip_type</key>
        </key_value>
        <key_value type="json" part_data="@_7" key="@longitude">
            <key name="@_7">longitude</key>
        </key_value>
        <key_value type="json" part_data="@_8" key="@latitude">
            <key name="@_8">latitude</key>
        </key_value>
        <key_value type="json" part_data="@_9" key="@src_ip">
            <key name="@_9">src_ip</key>
        </key_value>
        <key_value type="json" part_data="@_10" key="@src_port">
            <key name="@_10">src_port</key>
        </key_value>
        <key_value type="json" part_data="@_11" key="@dst_ip">
            <key name="@_11">dst_ip</key>
        </key_value>
        <key_value type="json" part_data="@_12" key="@dst_port">
            <key name="@_12">dst_port</key>
        </key_value>
        <key_value type="json" part_data="@_13" key="@batch_id">
            <key name="@_13">batch_id</key>
        </key_value>
        <key_value type="json" part_data="@_14" key="@defense_info">
            <key name="@_14">defense_info</key>
        </key_value>
        <key_value type="json" part_data="@_15" key="@remark">
            <key name="@_15">remark</key>
        </key_value>
    </ADD_ALARM>
    <!-- 告警表tb_alarm-修改 -->
    <!-- 告警表tb_alarm-删除 -->
    <DELETE_ALARM name="get selet name">
        <url type="put">delete from tb_alarm </url>
        <urlparam>db</urlparam>
        <postdata>where id in (@id)</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">id</key>
        </key_value>
    </DELETE_ALARM>
    <!-- 告警表tb_alarm-查询 -->
    <SELECT_ALARM name="get selet name">
        <url type="post">select * from </url>
        <urlparam>db</urlparam>
        <postdata>tb_alarm where 1 = 1 @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle01 @KMoudle02 @KMoudle03 @KMoudle04 @KMoudle05 @KMoudle06 @KMoudle07 @KMoudle08 @KMoudle09 @KMoudle10 @KMoudle11 @KMoudle12" key="@tiaojian">
            <moudle part_data=" and @key = '@value' " key="@KMoudle01">
                <value>
                    <key name="@key"> STRING_IS_TARGET_NAME </key>
                    <key name="@value">target_name</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = @value " key="@KMoudle02">
                <value>
                    <key name="@key"> STRING_IS_target_type </key>
                    <key name="@value">target_type</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle03">
                <value>
                    <key name="@key"> STRING_IS_tag_id </key>
                    <key name="@valueB">tag_id_b</key>
                    <key name="@valueE">tag_id_e</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle04">
                <value>
                    <key name="@key"> STRING_IS_time </key>
                    <key name="@valueB">time_b</key>
                    <key name="@valueE">time_e</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = @value " key="@KMoudle05">
                <value>
                    <key name="@key"> STRING_IS_nature </key>
                    <key name="@value">nature</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = @value " key="@KMoudle06">
                <value>
                    <key name="@key"> STRING_IS_ip_type </key>
                    <key name="@value">ip_type</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = '@value' " key="@KMoudle07">
                <value>
                    <key name="@key"> STRING_IS_src_ip </key>
                    <key name="@value">src_ip</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle08">
                <value>
                    <key name="@key"> STRING_IS_src_port </key>
                    <key name="@valueB">src_port_b</key>
                    <key name="@valueE">src_port_e</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = '@value' " key="@KMoudle09">
                <value>
                    <key name="@key"> STRING_IS_dst_ip </key>
                    <key name="@value">dst_ip</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle10">
                <value>
                    <key name="@key"> STRING_IS_dst_port </key>
                    <key name="@valueB">dst_port_b</key>
                    <key name="@valueE">dst_port_e</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@KMoudle11">
                <value>
                    <key name="@key">sort_name</key>
                    <key name="@value">sort_order</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@KMoudle12">
                <value>
                    <key name="@key">from</key>
                    <key name="@value">size</key>
                </value>
            </moudle>
        </key_value>
    </SELECT_ALARM>
    <!-- 告警信息表tb_rule_info-新增 -->
    <ADD_INFO name="get selet name">
        <url type="put">insert into tb_rule_info (rule_id, rule_level, rule_name, rule_desc, capture_mode, created_time, updated_time, rule_type) values </url>
        <urlparam>db</urlparam>
        <postdata>(@rule_id, @rule_level, '@rule_name', '@rule_desc', 2, NOW(), NOW(), 0)</postdata>
        <key_value type="json" part_data="@_1" key="@rule_id">
            <key name="@_1">rule_id</key>
        </key_value>
        <key_value type="json" part_data="@_2" key="@rule_level">
            <key name="@_2">rule_level</key>
        </key_value>
        <key_value type="json" part_data="@_3" key="@rule_name">
            <key name="@_3">rule_name</key>
        </key_value>
        <key_value type="json" part_data="@_4" key="@rule_desc">
            <key name="@_4">rule_desc</key>
        </key_value>
    </ADD_INFO>
    <!-- 告警信息表tb_rule_info-修改 -->
    <!-- 告警信息表tb_rule_info-删除 -->
    <DELETE_INFO name="get selet name">
        <url type="put">delete from tb_rule_info </url>
        <urlparam>db</urlparam>
        <postdata>where rule_id in (@rule_id)</postdata>
        <key_value type="json" part_data="@_id" key="@rule_id">
            <key name="@_id">rule_id</key>
        </key_value>
    </DELETE_INFO>
    <!-- 告警信息表tb_rule_info-查询 -->
    <SELECT_INFO name="get selet name">
        <url type="post">select * from </url>
        <urlparam>db</urlparam>
        <postdata>tb_rule_info where 1 = 1 @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3 @KMoudle4 @KMoudle5 @KMoudle6 @KMoudle7" key="@tiaojian">
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_rule_id </key>
                    <key name="@valueB">rule_id_b</key>
                    <key name="@valueE">rule_id_e</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_rule_level </key>
                    <key name="@valueB">rule_level_b</key>
                    <key name="@valueE">rule_level_e</key>
                </value>
            </moudle>
            <moudle part_data=" and @key like '@value' " key="@KMoudle3">
                <value>
                    <key name="@key"> STRING_IS_rule_name </key>
                    <key name="@value">rule_name</key>
                </value>
            </moudle>
            <moudle part_data=" and @key like '@value' " key="@KMoudle4">
                <value>
                    <key name="@key"> STRING_IS_rule_desc </key>
                    <key name="@value">rule_desc</key>
                </value>
            </moudle>
            <moudle part_data=" and @key = @value " key="@KMoudle5">
                <value>
                    <key name="@key"> STRING_IS_rule_type </key>
                    <key name="@value">rule_type</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@KMoudle6">
                <value>
                    <key name="@key">sort_name</key>
                    <key name="@value">sort_order</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@KMoudle7">
                <value>
                    <key name="@key">from</key>
                    <key name="@value">size</key>
                </value>
            </moudle>
        </key_value>
    </SELECT_INFO>
    <!-- IP_LIST -->
    <LIST_IP_INSERT name="get selet name">
        <url type="put">insert into IP_LIST (IP,blackList,whiteList) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@id',@blacklist,@whitelist)</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_blist" key="@blacklist">
            <key name="@_blist">Black_List</key>
        </key_value>
        <key_value type="json" part_data="@_wlist" key="@whitelist">
            <key name="@_wlist">White_List</key>
        </key_value>
    </LIST_IP_INSERT>
    <LIST_IP_DELETE name="get selet name">
        <url type="put">delete from IP_LIST </url>
        <urlparam>tidb</urlparam>
        <postdata>where IP in (@id)</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
    </LIST_IP_DELETE>
    <LIST_IP_UPDATE name="get selet name">
        <url type="put">update IP_LIST set </url>
        <urlparam>tidb</urlparam>
        <postdata> blackList = @blacklist, whiteList = @whitelist where IP = '@id'</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_blist" key="@blacklist">
            <key name="@_blist">Black_List</key>
        </key_value>
        <key_value type="json" part_data="@_wlist" key="@whitelist">
            <key name="@_wlist">White_List</key>
        </key_value>
    </LIST_IP_UPDATE>
    <LIST_IP_SELECT name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>IP_LIST where 1 = 1 @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3 @KMoudle4 @KMoudle5" key="@tiaojian">
            <moudle part_data=" and @key = '@value' " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_IP </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_blackList </key>
                    <key name="@valueB">Black_List_B</key>
                    <key name="@valueE">Black_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle3">
                <value>
                    <key name="@key"> STRING_IS_whiteList </key>
                    <key name="@valueB">White_List_B</key>
                    <key name="@valueE">White_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@KMoudle4">
                <value>
                    <key name="@key">SortName</key>
                    <key name="@value">SortOrder</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@KMoudle5">
                <value>
                    <key name="@key">From</key>
                    <key name="@value">Size</key>
                </value>
            </moudle>
        </key_value>
    </LIST_IP_SELECT>
    <!-- PORT_LIST -->
    <LIST_PORT_INSERT name="get selet name">
        <url type="put">insert into PORT_LIST (Port,blackList,whiteList) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@id',@blacklist,@whitelist)</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_blist" key="@blacklist">
            <key name="@_blist">Black_List</key>
        </key_value>
        <key_value type="json" part_data="@_wlist" key="@whitelist">
            <key name="@_wlist">White_List</key>
        </key_value>
    </LIST_PORT_INSERT>
    <LIST_PORT_DELETE name="get selet name">
        <url type="put">delete from PORT_LIST </url>
        <urlparam>tidb</urlparam>
        <postdata>where Port in (@id)</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
    </LIST_PORT_DELETE>
    <LIST_PORT_UPDATE name="get selet name">
        <url type="put">update PORT_LIST set </url>
        <urlparam>tidb</urlparam>
        <postdata> blackList = @blacklist, whiteList = @whitelist where Port = '@id'</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_blist" key="@blacklist">
            <key name="@_blist">Black_List</key>
        </key_value>
        <key_value type="json" part_data="@_wlist" key="@whitelist">
            <key name="@_wlist">White_List</key>
        </key_value>
    </LIST_PORT_UPDATE>
    <LIST_PORT_SELECT name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>PORT_LIST where 1 = 1 @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3 @KMoudle4 @KMoudle5" key="@tiaojian">
            <moudle part_data=" and @key = '@value' " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Port </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_blackList </key>
                    <key name="@valueB">Black_List_B</key>
                    <key name="@valueE">Black_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle3">
                <value>
                    <key name="@key"> STRING_IS_whiteList </key>
                    <key name="@valueB">White_List_B</key>
                    <key name="@valueE">White_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@KMoudle4">
                <value>
                    <key name="@key">SortName</key>
                    <key name="@value">SortOrder</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@KMoudle5">
                <value>
                    <key name="@key">From</key>
                    <key name="@value">Size</key>
                </value>
            </moudle>
        </key_value>
    </LIST_PORT_SELECT>
    <!-- APP_LIST -->
    <LIST_APP_INSERT name="get selet name">
        <url type="put">insert into APP_LIST (App_Id,blackList,whiteList) values </url>
        <urlparam>tidb</urlparam>
        <postdata>(@id,@blacklist,@whitelist)</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_blist" key="@blacklist">
            <key name="@_blist">Black_List</key>
        </key_value>
        <key_value type="json" part_data="@_wlist" key="@whitelist">
            <key name="@_wlist">White_List</key>
        </key_value>
    </LIST_APP_INSERT>
    <LIST_APP_DELETE name="get selet name">
        <url type="put">delete from APP_LIST </url>
        <urlparam>tidb</urlparam>
        <postdata>where App_Id in (@id)</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
    </LIST_APP_DELETE>
    <LIST_APP_UPDATE name="get selet name">
        <url type="put">update APP_LIST set </url>
        <urlparam>tidb</urlparam>
        <postdata> blackList = @blacklist, whiteList = @whitelist where App_Id = @id</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_blist" key="@blacklist">
            <key name="@_blist">Black_List</key>
        </key_value>
        <key_value type="json" part_data="@_wlist" key="@whitelist">
            <key name="@_wlist">White_List</key>
        </key_value>
    </LIST_APP_UPDATE>
    <LIST_APP_SELECT name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>APP_LIST where 1 = 1 @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3 @KMoudle4 @KMoudle5" key="@tiaojian">
            <moudle part_data=" and @key = @value " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_App_Id </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_blackList </key>
                    <key name="@valueB">Black_List_B</key>
                    <key name="@valueE">Black_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle3">
                <value>
                    <key name="@key"> STRING_IS_whiteList </key>
                    <key name="@valueB">White_List_B</key>
                    <key name="@valueE">White_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@KMoudle4">
                <value>
                    <key name="@key">SortName</key>
                    <key name="@value">SortOrder</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@KMoudle5">
                <value>
                    <key name="@key">From</key>
                    <key name="@value">Size</key>
                </value>
            </moudle>
        </key_value>
    </LIST_APP_SELECT>
    <!-- SESSION_ID_LIST -->
    <LIST_SESSION_INSERT name="get selet name">
        <url type="put">insert into SESSION_ID_LIST (session_id,blackList,whiteList) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@id',@blacklist,@whitelist)</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_blist" key="@blacklist">
            <key name="@_blist">Black_List</key>
        </key_value>
        <key_value type="json" part_data="@_wlist" key="@whitelist">
            <key name="@_wlist">White_List</key>
        </key_value>
    </LIST_SESSION_INSERT>
    <LIST_SESSION_DELETE name="get selet name">
        <url type="put">delete from SESSION_ID_LIST </url>
        <urlparam>tidb</urlparam>
        <postdata>where session_id in (@id)</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
    </LIST_SESSION_DELETE>
    <LIST_SESSION_UPDATE name="get selet name">
        <url type="put">update SESSION_ID_LIST set </url>
        <urlparam>tidb</urlparam>
        <postdata> blackList = @blacklist, whiteList = @whitelist where session_id = '@id'</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_blist" key="@blacklist">
            <key name="@_blist">Black_List</key>
        </key_value>
        <key_value type="json" part_data="@_wlist" key="@whitelist">
            <key name="@_wlist">White_List</key>
        </key_value>
    </LIST_SESSION_UPDATE>
    <LIST_SESSION_SELECT name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>SESSION_ID_LIST where 1 = 1 @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3 @KMoudle4 @KMoudle5" key="@tiaojian">
            <moudle part_data=" and @key = '@value' " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_session_id </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_blackList </key>
                    <key name="@valueB">Black_List_B</key>
                    <key name="@valueE">Black_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle3">
                <value>
                    <key name="@key"> STRING_IS_whiteList </key>
                    <key name="@valueB">White_List_B</key>
                    <key name="@valueE">White_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@KMoudle4">
                <value>
                    <key name="@key">SortName</key>
                    <key name="@value">SortOrder</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@KMoudle5">
                <value>
                    <key name="@key">From</key>
                    <key name="@value">Size</key>
                </value>
            </moudle>
        </key_value>
    </LIST_SESSION_SELECT>
    <!-- CertInfo -->
    <LIST_CERT_INSERT name="get selet name">
        <url type="put">insert into CertInfo (CertSHA1,CertMd5,CertJson,subjectKeyIdentifier,blackList,whiteList,remarks,NotBefore,NotAfter) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@id','','','',@blacklist,@whitelist,'','1970-01-01','1970-01-01')</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_blist" key="@blacklist">
            <key name="@_blist">Black_List</key>
        </key_value>
        <key_value type="json" part_data="@_wlist" key="@whitelist">
            <key name="@_wlist">White_List</key>
        </key_value>
    </LIST_CERT_INSERT>
    <LIST_CERT_DELETE name="get selet name">
        <url type="put">delete from CertInfo </url>
        <urlparam>tidb</urlparam>
        <postdata>where CertSHA1 in (@id)</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
    </LIST_CERT_DELETE>
    <LIST_CERT_UPDATE name="get selet name">
        <url type="put">update CertInfo set </url>
        <urlparam>tidb</urlparam>
        <postdata> blackList = @blacklist, whiteList = @whitelist where CertSHA1 = '@id'</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_blist" key="@blacklist">
            <key name="@_blist">Black_List</key>
        </key_value>
        <key_value type="json" part_data="@_wlist" key="@whitelist">
            <key name="@_wlist">White_List</key>
        </key_value>
    </LIST_CERT_UPDATE>
    <LIST_CERT_SELECT name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>CertInfo where 1 = 1 @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3 @KMoudle4 @KMoudle5" key="@tiaojian">
            <moudle part_data=" and @key = '@value' " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_CertSHA1 </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_blackList </key>
                    <key name="@valueB">Black_List_B</key>
                    <key name="@valueE">Black_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle3">
                <value>
                    <key name="@key"> STRING_IS_whiteList </key>
                    <key name="@valueB">White_List_B</key>
                    <key name="@valueE">White_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@KMoudle4">
                <value>
                    <key name="@key">SortName</key>
                    <key name="@value">SortOrder</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@KMoudle5">
                <value>
                    <key name="@key">From</key>
                    <key name="@value">Size</key>
                </value>
            </moudle>
        </key_value>
    </LIST_CERT_SELECT>
    <!-- DOMAIN_INFO -->
    <LIST_DOMAIN_INSERT name="get selet name">
        <url type="put">insert into DOMAIN_INFO (Domain_Name,Begin_Time,End_Time,blackList,whiteList) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@id',unix_timestamp(),unix_timestamp(),@blacklist,@whitelist)</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_blist" key="@blacklist">
            <key name="@_blist">Black_List</key>
        </key_value>
        <key_value type="json" part_data="@_wlist" key="@whitelist">
            <key name="@_wlist">White_List</key>
        </key_value>
    </LIST_DOMAIN_INSERT>
    <LIST_DOMAIN_DELETE name="get selet name">
        <url type="put">delete from DOMAIN_INFO </url>
        <urlparam>tidb</urlparam>
        <postdata>where Domain_Name in (@id)</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
    </LIST_DOMAIN_DELETE>
    <LIST_DOMAIN_UPDATE name="get selet name">
        <url type="put">update DOMAIN_INFO set </url>
        <urlparam>tidb</urlparam>
        <postdata> blackList = @blacklist, whiteList = @whitelist where Domain_Name = '@id'</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_blist" key="@blacklist">
            <key name="@_blist">Black_List</key>
        </key_value>
        <key_value type="json" part_data="@_wlist" key="@whitelist">
            <key name="@_wlist">White_List</key>
        </key_value>
    </LIST_DOMAIN_UPDATE>
    <LIST_DOMAIN_SELECT name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>DOMAIN_INFO where 1 = 1 @tiaojian</postdata>
        <key_value type="json" part_data="@KMoudle1 @KMoudle2 @KMoudle3 @KMoudle4 @KMoudle5" key="@tiaojian">
            <moudle part_data=" and @key = '@value' " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Domain_Name </key>
                    <key name="@value">TargetName</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle2">
                <value>
                    <key name="@key"> STRING_IS_blackList </key>
                    <key name="@valueB">Black_List_B</key>
                    <key name="@valueE">Black_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" and @key between @valueB and @valueE " key="@KMoudle3">
                <value>
                    <key name="@key"> STRING_IS_whiteList </key>
                    <key name="@valueB">White_List_B</key>
                    <key name="@valueE">White_List_E</key>
                </value>
            </moudle>
            <moudle part_data=" order by @key @value " key="@KMoudle4">
                <value>
                    <key name="@key">SortName</key>
                    <key name="@value">SortOrder</key>
                </value>
            </moudle>
            <moudle part_data=" limit @key,@value " key="@KMoudle5">
                <value>
                    <key name="@key">From</key>
                    <key name="@value">Size</key>
                </value>
            </moudle>
        </key_value>
    </LIST_DOMAIN_SELECT>
    <TAG_FINGER_INSERT name="get selet name">
        <url type="put">insert into tb_finger_tag (finger,tag_id,created_time) values </url>
        <urlparam>tidb</urlparam>
        <postdata>(@finger,@tagId,@time)</postdata>
        <key_value type="json" part_data="@_finger" key="@finger">
            <key name="@_finger">TargetName</key>
        </key_value>
        <key_value type="json" part_data="@_tid" key="@tagId">
            <key name="@_tid">Tag_Id</key>
        </key_value>
        <key_value type="json" part_data="@_time" key="@time">
            <key name="@_time">Time</key>
        </key_value>
    </TAG_FINGER_INSERT>
    <TAG_FINGER_UPDATE name="get selet name">
        <url type="put">update tb_finger_tag set </url>
        <urlparam>tidb</urlparam>
        <postdata> @tiaojian where  finger = @tdm</postdata>
        <key_value type="json" part_data="@_td" key="@tdm">
            <key name="@_td">Finger</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <moudle part_data=" @key = @value " key="@KMoudle1">
                <value>
                    <key name="@key"> STRING_IS_Tag_Id </key>
                    <key name="@value">Tag_Id</key>
                </value>
            </moudle>
        </key_value>
    </TAG_FINGER_UPDATE>
    <TAG_FINGER_DELETE name="get selet name">
        <url type="put">delete from tb_finger_tag </url>
        <urlparam>tidb</urlparam>
        <postdata>where finger in (@id)</postdata>
        <key_value type="json" part_data="@_id" key="@id">
            <key name="@_id">finger</key>
        </key_value>
    </TAG_FINGER_DELETE>
</config>

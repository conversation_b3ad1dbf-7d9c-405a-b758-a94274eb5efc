# Last Update:2020-08-06 11:14:21
##
# @file delete_tag.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-08-06


import os
import json
import requests
class delete_tag:
    def __init__(self,ip):
        self.ip = ip
    # target_type: TAG_IP_DELETE , TAG_SESSION_DELETE , TAG_CERT_DELETE ,TAG_DOMAIN_DELETE , TAG_FINGER_DELETE
    # target_name 目标名称  finger 是指finger_id 不是 finger_sha1
    # tag_id  标签 ID
    def handle(self,target_type,target_name , tag_id ):
        headers = {'Content-Type': 'application/json'}
        datas = json.dumps({"type": target_type, "TargetName": target_name ,"Tag_Id":str(tag_id)})
        r = requests.post("http://"+self.ip+":28001/web_handle", data=datas, headers=headers)
        print(r.text)
        resp_json = json.loads(r.text)
        if resp_json["data"] == 0:
            return True
        else:
            return False
if __name__ == '__main__':
    x = delete_tag("***************")
    if x.handle("TAG_IP_DELETE","*************",1111):
        print("delete TAG_IP_DELETE 成功")
    if x.handle("TAG_SESSION_DELETE","11908265511491060288",1111):
        print("delete TAG_SESSION_DELETE 成功")
    if x.handle("TAG_CERT_DELETE","11908265511491060288",1111):
        print("delete TAG_CERT_DELETE 成功")
    if x.handle("TAG_DOMAIN_DELETE","www.baidu.com",1111):
        print("delete TAG_DOMAIN_DELETE 成功")
    if x.handle("TAG_FINGER_DELETE","2323243214241",1111):
        print("delete TAG_FINGER_DELETE 成功")

    




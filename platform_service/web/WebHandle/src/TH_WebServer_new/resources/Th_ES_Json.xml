<config>
    <!-- 世界地图-折线图 -->
    <IP_EXTEMAL_SITUATION_LINE name="get selet name"> <!--  -->
        <url type="post">connectinfo_*/_search</url>
        <postdata>{"query":{"bool":{"must":[{"range":{"StartTime":{"gt":@begin_time,"lt":null}}},{"range":{"EndTime":{"gt":null,"lt":@end_time}}}]}},"size": 0,"aggs":{"by_day":{"date_histogram":{"field":"EndTime","interval":"1d"},"aggs":{"scount":{"sum":{"field":"pkt_sbytes"}},"dcount":{"sum":{"field":"pkt_dbytes"}}}}}}</postdata>
        <key_value type="json" part_data="@btime" key="@begin_time">
            <key name="@btime">begintime</key>
        </key_value>
        <key_value type="json" part_data="@etime" key="@end_time">
            <key name="@etime">endtime</key>
        </key_value>
    </IP_EXTEMAL_SITUATION_LINE>
    <!-- 世界地图-IP热度 -->
    <IP_EXTEMAL_SITUATION_AREA name="get selet name"> <!--  -->
        <url type="post">connectinfo_*/_search</url>
        <postdata>{"query":{"bool":{"must":[{"range":{"StartTime":{"gt":@begin_time,"lt":null}}},{"range":{"EndTime":{"gt":null,"lt":@end_time}}}]}},"size": 10000}</postdata>
        <key_value type="json" part_data="@btime" key="@begin_time">
            <key name="@btime">begintime</key>
        </key_value>
        <key_value type="json" part_data="@etime" key="@end_time">
            <key name="@etime">endtime</key>
        </key_value>
    </IP_EXTEMAL_SITUATION_AREA>
    <!-- 全流量查询 -->
    <FullFlow name="get selet name">
        <!--type>web_test</type-->
        <url type="post">connectinfo_*/_search</url>
        <postdata>{"query":{@musttiaojian}@fenye@sort}</postdata>
        <!--json>@WHERE</json-->
        <!-- 需要值回填到语句中, 需要查询  -->
        <key_value type="json" part_data=",&quot;from&quot;:@beginrow,&quot;size&quot;:@size" key="@fenye">
            <key name="@beginrow">from</key>
            <key name="@size">size</key>
        </key_value>
        <key_value type="json" part_data=",&quot;sort&quot;:[{&quot;@filed&quot;:&quot;@sorttype&quot;}]" key="@sort">
            <!--<dict obj="@filed">&lt;!&ndash;字典表转换&ndash;&gt;
                <filed key="dIP">dstIP</filed>
                <filed key="sIP">srcIP</filed>
                <filed key="sPort">srcPort</filed>
                <filed key="dPort">dstPort</filed>
            </dict>-->
            <key name="@filed">sortName</key>
            <key name="@sorttype">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="&quot;bool&quot;:{&quot;must&quot;:[@timequjian@KMoudle@KMoudle2]}" key="@musttiaojian">
            <moudle part_data="{&quot;range&quot;:{&quot;StartTime&quot;:{&quot;gt&quot;:@begintime,&quot;lt&quot;:@endtime}}}" key="@timequjian">
                <value>
                    <key name="@begintime">begintime</key>
                    <key name="@endtime">endtime</key>
                </value>
            </moudle>
            <moudle part_data=",{&quot;term&quot;:{&quot;@key&quot;:&quot;@value&quot;}}"  key="@KMoudle">
                <value>
                    <key name="@key">STRING_IS_sIp.keyword</key>
                    <key name="@value">sIP</key>
                </value>
                <value>
                    <key name="@key">STRING_IS_dIp.keyword</key>
                    <key name="@value">dIP</key>
                </value>
            </moudle> <!-- 替换数据块  -->
            <moudle part_data=",{&quot;term&quot;:{&quot;@key&quot;:@value}}"  key="@KMoudle2">
                <value>
                    <key name="@key">STRING_IS_dPort</key>
                    <key name="@value">dPort</key>
                </value>
                <value>
                    <key name="@key">STRING_IS_sPort</key>
                    <key name="@value">sPort</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
    </FullFlow>
    <!-- Mac分析流量——连接列表 -->
    <FullFlow_Mac_Link name="get selet name"> <!--  -->
        <url type="post">connectinfo_*/_search</url>
        <postdata>{"query":{@musttiaojian}@fenye@sort}</postdata>
        <key_value type="json" part_data=",&quot;from&quot;:@beginrow,&quot;size&quot;:@size" key="@fenye">
            <key name="@beginrow">from</key>
            <key name="@size">size</key>
        </key_value>
        <key_value type="json" part_data=",&quot;sort&quot;:[{&quot;@filed&quot;:&quot;@sorttype&quot;}]" key="@sort">
            <key name="@filed">sortName</key>
            <key name="@sorttype">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="&quot;bool&quot;:{&quot;must&quot;:[@KMoudle]}" key="@musttiaojian">
            <moudle part_data="{&quot;multi_match&quot;:{&quot;fields&quot;:[&quot;@key1&quot;,&quot;@key2&quot;],&quot;query&quot;:&quot;@value&quot;}}"  key="@KMoudle">
                <value>
                    <key name="@key1">STRING_IS_RuleInfor.smac.keyword</key>
                    <key name="@key2">STRING_IS_RuleInfor.dmac.keyword</key>
                    <key name="@value">keyName</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
    </FullFlow_Mac_Link>
    <!-- IP分析流量——连接列表 -->
    <FullFlow_Link name="get selet name"> <!--  -->
        <url type="post">connectinfo_*/_search</url>
        <postdata>{"query":{@musttiaojian}@fenye@sort}</postdata>
        <key_value type="json" part_data=",&quot;from&quot;:@beginrow,&quot;size&quot;:@size" key="@fenye">
            <key name="@beginrow">from</key>
            <key name="@size">size</key>
        </key_value>
        <key_value type="json" part_data=",&quot;sort&quot;:[{&quot;@filed&quot;:&quot;@sorttype&quot;}]" key="@sort">
            <key name="@filed">sortName</key>
            <key name="@sorttype">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="&quot;bool&quot;:{&quot;must&quot;:[@KMoudle]}" key="@musttiaojian">
            <moudle part_data="{&quot;multi_match&quot;:{&quot;fields&quot;:[&quot;@key1&quot;,&quot;@key2&quot;],&quot;query&quot;:&quot;@value&quot;}}"  key="@KMoudle">
                <value>
                    <key name="@key1">STRING_IS_sIp.keyword</key>
                    <key name="@key2">STRING_IS_dIp.keyword</key>
                    <key name="@value">sdIP</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
    </FullFlow_Link>
    <!-- 端口分析流量——连接列表 -->
    <FullFlow_Port_Link name="get selet name"> <!--  -->
        <url type="post">connectinfo_*/_search</url>
        <postdata>{"query":{@musttiaojian}@fenye@sort}</postdata>
        <key_value type="json" part_data=",&quot;from&quot;:@beginrow,&quot;size&quot;:@size" key="@fenye">
            <key name="@beginrow">from</key>
            <key name="@size">size</key>
        </key_value>
        <key_value type="json" part_data=",&quot;sort&quot;:[{&quot;@filed&quot;:&quot;@sorttype&quot;}]" key="@sort">
            <key name="@filed">sortName</key>
            <key name="@sorttype">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="&quot;bool&quot;:{&quot;must&quot;:[@KMoudle]}" key="@musttiaojian">
            <moudle part_data="{&quot;multi_match&quot;:{&quot;fields&quot;:[&quot;@key&quot;],&quot;query&quot;:&quot;@value&quot;}}"  key="@KMoudle">
                <value>
                    <key name="@key">STRING_IS_dPort</key>
                    <key name="@value">sdPort</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
    </FullFlow_Port_Link>
    <!-- 全流量查询-应用——连接列表 -->
    <FullFlow_App_Link name="get selet name"> <!--  -->
        <url type="post">connectinfo_*/_search</url>
        <postdata>{"query":{@musttiaojian}@fenye@sort}</postdata>
        <key_value type="json" part_data=",&quot;from&quot;:@beginrow,&quot;size&quot;:@size" key="@fenye">
            <key name="@beginrow">from</key>
            <key name="@size">size</key>
        </key_value>
        <key_value type="json" part_data=",&quot;sort&quot;:[{&quot;@filed&quot;:&quot;@sorttype&quot;}]" key="@sort">
            <key name="@filed">sortName</key>
            <key name="@sorttype">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="&quot;bool&quot;:{&quot;must&quot;:[@KMoudle]}" key="@musttiaojian">
            <moudle part_data="{&quot;term&quot;:{&quot;@key&quot;:&quot;@value&quot;}}" key="@KMoudle">
                <value>
                    <key name="@key">STRING_IS_AppId</key>
                    <key name="@value">appId</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
    </FullFlow_App_Link>
    <!-- 全流量查询-连接分析 -->
    <FullFlow_Analysis name="get selet name"> <!--  -->
        <url type="post">connectinfo_*/_search</url>
        <postdata>{"query": {"term": {"SessionId": "@s_id"}}}</postdata>
        <key_value type="json" part_data="@_session" key="@s_id">
            <key name="@_session">sessionId</key>
        </key_value>
    </FullFlow_Analysis>
    <!-- 全流量查询-连接分析-协议详细信息 -->
    <FullFlow_Analysis_App name="get selet name"> <!--  -->
        <url type="post">modbus_*,http_*,ssl_*,dns_*,s7_*/_search</url>
        <postdata>{"query":{"bool":{"must":[{"term":{"SessionId":"@s_id"}},{"term":{"AppId":@a_id}}]}},"size":50}</postdata>
        <key_value type="json" part_data="@_session" key="@s_id">
            <key name="@_session">sessionId</key>
        </key_value>
        <key_value type="json" part_data="@_app" key="@a_id">
            <key name="@_app">appId</key>
        </key_value>
    </FullFlow_Analysis_App>
    <!-- IP分析 开放服务 -->
    <IP_ANALYSIS_OS name="get selet name"> <!-- 连接数 -->
        <url type="post">connectinfo_*/_search</url>
        <postdata>{@query_module@sort@agg}</postdata>
        <key_value type="json" part_data="&quot;query&quot;:{&quot;term&quot;:{&quot;dIp.keyword&quot;:&quot;@para_dIP&quot;}}," key="@query_module">
            <key name="@para_dIP">dstIP</key>
        </key_value>
        <key_value type="json" part_data="&quot;size&quot;: 100,&quot;sort&quot;:{&quot;StartTime&quot;: &quot;desc&quot;}," key="@sort"><!--如有需要修改-->
        </key_value>
        <key_value type="json" part_data="&quot;aggs&quot;:{&quot;client_IP&quot;:{&quot;terms&quot;:{&quot;field&quot;:&quot;sIp.keyword&quot;},&quot;aggs&quot;:{&quot;dstport&quot;:{&quot;terms&quot;:{&quot;field&quot;:&quot;dPort&quot;},&quot;aggs&quot;:{&quot;app&quot;:{&quot;terms&quot;:{&quot;field&quot;:&quot;AppId&quot;},&quot;aggs&quot;:{&quot;first_found_time&quot;:{&quot;min&quot;:{&quot;field&quot;:&quot;StartTime&quot;}},&quot;last_found_time&quot;:{&quot;max&quot;:{&quot;field&quot;:&quot;StartTime&quot;}}}}}}}}}" key="@agg">
        </key_value>
    </IP_ANALYSIS_OS>
    <!-- IP分析 访问服务 -->
    <IP_ANALYSIS_AS name="get selet name"> <!-- 连接数 -->
        <url type="post">connectinfo_*/_search</url>
        <postdata>{@query_module@sort@agg}</postdata>
        <key_value type="json" part_data="&quot;query&quot;:{&quot;term&quot;:{&quot;sIp.keyword&quot;:&quot;@para_sIP&quot;}}," key="@query_module">
            <key name="@para_sIP">srcIP</key>
        </key_value>
        <key_value type="json" part_data="&quot;size&quot;: 100,&quot;sort&quot;:{&quot;StartTime&quot;: &quot;desc&quot;}," key="@sort"><!--如有需要修改-->
        </key_value>
        <key_value type="json" part_data="&quot;aggs&quot;: {&quot;server_IP&quot;: {&quot;terms&quot;: {&quot;field&quot;: &quot;dIp.keyword&quot;},&quot;aggs&quot;: {&quot;srcport&quot;: {&quot;terms&quot;: {&quot;field&quot;: &quot;dPort&quot;},&quot;aggs&quot;: {&quot;app&quot;: {&quot;terms&quot;: {&quot;field&quot;: &quot;AppId&quot;},&quot;aggs&quot;: {&quot;first_found_time&quot;: {&quot;min&quot;: {&quot;field&quot;: &quot;StartTime&quot;}},&quot;last_found_time&quot;: {&quot;max&quot;: {&quot;field&quot;: &quot;StartTime&quot;}}}}}}}}}" key="@agg">
        </key_value>
    </IP_ANALYSIS_AS>
    <!-- 端口分析 通信页签 开放访问服务 -->
    <Port_ANALYSIS_AO name="get selet name"> <!-- 连接数 -->
        <url type="post">connectinfo_*/_search</url>
        <postdata>{@query_module@sort@agg}</postdata>
        <key_value type="json" part_data="&quot;query&quot;:{&quot;term&quot;:{&quot;dPort&quot;:&quot;@para_Port&quot;}}," key="@query_module">
            <key name="@para_Port">srcPort</key>
        </key_value>
        <key_value type="json" part_data="&quot;size&quot;: 100,&quot;sort&quot;:{&quot;StartTime&quot;: &quot;desc&quot;}," key="@sort"><!--如有需要修改-->
        </key_value>
        <key_value type="json" part_data="&quot;aggs&quot;:{&quot;client_IP&quot;:{&quot;terms&quot;:{&quot;field&quot;:&quot;sIp.keyword&quot;},&quot;aggs&quot;:{&quot;server_IP&quot;:{&quot;terms&quot;:{&quot;field&quot;:&quot;dIp.keyword&quot;},&quot;aggs&quot;:{&quot;app&quot;:{&quot;terms&quot;:{&quot;field&quot;:&quot;AppId&quot;},&quot;aggs&quot;:{&quot;first_found_time&quot;:{&quot;min&quot;:{&quot;field&quot;:&quot;StartTime&quot;}},&quot;last_found_time&quot;:{&quot;max&quot;:{&quot;field&quot;:&quot;StartTime&quot;}}}}}}}}}" key="@agg">
        </key_value>
    </Port_ANALYSIS_AO>
    <!-- 应用分析 通信页签 开放访问服务 -->
    <App_ANALYSIS_AO name="get selet name">
        <url type="post">connectinfo_*/_search</url>
        <postdata>{@query_module@sort@agg}</postdata>
        <key_value type="json" part_data="&quot;query&quot;:{&quot;term&quot;:{&quot;AppId&quot;:&quot;@para_Port&quot;}}," key="@query_module">
            <key name="@para_Port">appId</key>
        </key_value>
        <key_value type="json" part_data="&quot;size&quot;: 100,&quot;sort&quot;:{&quot;StartTime&quot;: &quot;desc&quot;}," key="@sort"><!--如有需要修改-->
        </key_value>
        <key_value type="json" part_data="&quot;aggs&quot;:{&quot;client_IP&quot;:{&quot;terms&quot;:{&quot;field&quot;:&quot;sIp.keyword&quot;},&quot;aggs&quot;:{&quot;server_IP&quot;:{&quot;terms&quot;:{&quot;field&quot;:&quot;dIp.keyword&quot;},&quot;aggs&quot;:{&quot;server_Port&quot;:{&quot;terms&quot;:{&quot;field&quot;:&quot;dPort&quot;},&quot;aggs&quot;:{&quot;first_found_time&quot;:{&quot;min&quot;:{&quot;field&quot;:&quot;StartTime&quot;}},&quot;last_found_time&quot;:{&quot;max&quot;:{&quot;field&quot;:&quot;StartTime&quot;}}}}}}}}}" key="@agg">
        </key_value>
    </App_ANALYSIS_AO>
    <!-- 基于标签检索，连接页签数据 -->
    <Search_LinkPageTag_Es name="get selet name"> <!--  -->
        <url type="post">connectinfo_*/_search</url>
        <postdata>{"query": {"bool": {"filter": {"terms": {"SessionId": [@s_id]}}@musttiaojian}}}</postdata>
        <key_value type="json" part_data="@se_id" key="@s_id">
            <key name="@se_id">sessionId</key>
        </key_value>
        <key_value type="json" part_data="@timequjian" key="@musttiaojian">
            <moudle part_data=",&quot;must&quot;: [{&quot;range&quot;: {&quot;StartTime&quot;: {&quot;gte&quot;: @begintime,&quot;lte&quot;: @endtime}}}]" key="@timequjian">
                <value>
                    <key name="@begintime">begintime</key>
                    <key name="@endtime">endtime</key>
                </value>
            </moudle>
        </key_value>
    </Search_LinkPageTag_Es>
    <!-- 流量分析-流量态势页签 -->
    <App_FlowSituation_Simple name="get selet name"> <!-- 连接数 -->
        <url type="post">connectinfo_*/_search</url>
        <postdata>{"query":{@Condition}@Sort}</postdata>
        <key_value type="json" part_data="&quot;bool&quot;:{&quot;must&quot;:[{&quot;range&quot;:{&quot;StartTime&quot;:{&quot;gt&quot;:@begin_time,&quot;lt&quot;:@end_time}}},{&quot;range&quot;:{&quot;sPort&quot;:{&quot;gt&quot;:@begin_sport,&quot;lt&quot;:@end_sport}}},{&quot;range&quot;:{&quot;dPort&quot;:{&quot;gt&quot;:@begin_dport,&quot;lt&quot;:@end_dport}}}@OtherCondition]}"
                   key="@Condition">
            <key name="@begin_time">begintime</key>
            <key name="@end_time">endtime</key>
            <key name="@begin_sport">beginsport</key>
            <key name="@end_sport">endsport</key>
            <key name="@begin_dport">begindport</key>
            <key name="@end_dport">enddport</key>
            <moudle part_data=",{&quot;term&quot;:{&quot;@key&quot;:&quot;@value&quot;}}" key="@OtherCondition">
                <value>
                    <key name="@key">STRING_IS_sIp</key>
                    <key name="@value">src_ip</key>
                </value>
                <value>
                    <key name="@key">STRING_IS_dIp</key>
                    <key name="@value">dst_ip</key>
                </value>
                <value>
                    <key name="@key">STRING_IS_IPPro</key>
                    <key name="@value">ip_pro</key>
                </value>
                <value>
                    <key name="@key">STRING_IS_AppId</key>
                    <key name="@value">app_id</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <key_value type="json"
                   part_data=",&quot;size&quot;: 100,&quot;sort&quot;:{&quot;StartTime&quot;: &quot;desc&quot;}"
                   key="@Sort"><!--如有需要修改-->
        </key_value>
    </App_FlowSituation_Simple>
    <!-- 流量分析-连接页签 -->
    <App_Connect_Simple>
        <url type="post">connectinfo_*/_search</url>
        <postdata>{"query":{@Condition}@Sort@Pagination}</postdata>
        <key_value type="json" part_data="&quot;bool&quot;:{&quot;must&quot;:[{&quot;range&quot;:{&quot;StartTime&quot;:{&quot;gt&quot;:@begin_time,&quot;lt&quot;:@end_time}}},{&quot;range&quot;:{&quot;sPort&quot;:{&quot;gt&quot;:@begin_sport,&quot;lt&quot;:@end_sport}}},{&quot;range&quot;:{&quot;dPort&quot;:{&quot;gt&quot;:@begin_dport,&quot;lt&quot;:@end_dport}}}@OtherCondition]}"
                   key="@Condition">
            <key name="@begin_time">begintime</key>
            <key name="@end_time">endtime</key>
            <key name="@begin_sport">beginsport</key>
            <key name="@end_sport">endsport</key>
            <key name="@begin_dport">begindport</key>
            <key name="@end_dport">enddport</key>
            <moudle part_data=",{&quot;term&quot;:{&quot;@key&quot;:&quot;@value&quot;}}" key="@OtherCondition">
                <value>
                    <key name="@key">STRING_IS_sIp</key>
                    <key name="@value">src_ip</key>
                </value>
                <value>
                    <key name="@key">STRING_IS_dIp</key>
                    <key name="@value">dst_ip</key>
                </value>
                <value>
                    <key name="@key">STRING_IS_IPPro</key>
                    <key name="@value">ip_pro</key>
                </value>
                <value>
                    <key name="@key">STRING_IS_AppId</key>
                    <key name="@value">app_id</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <key_value type="json"
                   part_data=",&quot;sort&quot;:{&quot;StartTime&quot;: &quot;desc&quot;}"
                   key="@Sort"><!--如有需要修改-->
        </key_value>
        <key_value type="json" part_data=",&quot;from&quot;:@from,&quot;size&quot;:@size" key="@Pagination">
            <key name="@from">from</key>
            <key name="@size">size</key>
        </key_value>
    </App_Connect_Simple>
    <!-- session_id检测 -->
    <CHECK_SESSIONID_ANALYSIS name="get selet name">
        <url type="post">connectinfo_*/_search</url>
        <postdata>{"query": {"term": {"SessionId": "@s_id"}}}</postdata>
        <key_value type="json" part_data="@_session" key="@s_id">
            <key name="@_session">keyName</key>
        </key_value>
    </CHECK_SESSIONID_ANALYSIS>
    <!-- ES增删改查接口 -->
    <ES_CURD name="get selet name">
        <url type="curd">@i_t</url>
        <postdata>@n_param</postdata>
        <key_value type="json" part_data="@index_type" key="@i_t">
            <key name="@index_type">indexType</key>
        </key_value>
        <!--<key_value type="json" part_data="@curd_name" key="@n_curd">-->
            <!--<key name="@curd_name">curdType</key>-->
        <!--</key_value>-->
        <key_value type="json" part_data="@param_value" key="@n_param">
            <key name="@param_value">paramValue</key>
        </key_value>
    </ES_CURD>
    <!-- 证书分析跳转到连接列表 -->
    <CERT_TO_LINK name="get selet name">
        <url type="two"></url>
        <urlparam>ssl_*/connectinfo_*/SessionId</urlparam>
        <postdata>{"query": {"bool":{"must":{"multi_match":{"fields":["Cert_c_Hash","Cert_s_Hash"],"query":"@_str"}}}},"size":10000}/GeekSec/{"query": {"terms": {"SessionId": [@#@]}},"sort":{"@_sortname":"@_sortorder"},"from":@_from,"size":@_size}</postdata>
        <key_value type="json" part_data="@_str" key="@_str">
            <key name="@_str">keyName</key>
        </key_value>
        <key_value type="json" part_data="@_from" key="@_from">
            <key name="@_from">from</key>
        </key_value>
        <key_value type="json" part_data="@_size" key="@_size">
            <key name="@_size">size</key>
        </key_value>
        <key_value type="json" part_data="@_sortname" key="@_sortname">
            <key name="@_sortname">sortName</key>
        </key_value>
        <key_value type="json" part_data="@_sortorder" key="@_sortorder">
            <key name="@_sortorder">sortOrder</key>
        </key_value>
    </CERT_TO_LINK>
    <!-- 域名分析跳转到连接列表 -->
    <DOMAIN_TO_LINK name="get selet name">
        <url type="two"></url>
        <urlparam>dns_*/connectinfo_*/SessionId</urlparam>
        <postdata>{"query": {"term": {"Domain.keyword": "@_str"}},"size":10000}/GeekSec/{"query": {"terms": {"SessionId": [@#@]}},"sort":{"@_sortname":"@_sortorder"},"from":@_from,"size":@_size}</postdata>
        <key_value type="json" part_data="@_str" key="@_str">
            <key name="@_str">keyName</key>
        </key_value>
        <key_value type="json" part_data="@_from" key="@_from">
            <key name="@_from">from</key>
        </key_value>
        <key_value type="json" part_data="@_size" key="@_size">
            <key name="@_size">size</key>
        </key_value>
        <key_value type="json" part_data="@_sortname" key="@_sortname">
            <key name="@_sortname">sortName</key>
        </key_value>
        <key_value type="json" part_data="@_sortorder" key="@_sortorder">
            <key name="@_sortorder">sortOrder</key>
        </key_value>
    </DOMAIN_TO_LINK>
    <!-- 端口应用跳转到连接列表 -->
    <PORT_APP_TO_LINK name="get selet name">
        <url type="post">connectinfo_*/_search</url>
        <postdata>{"query":{"bool":{"must":[{"term":{"dPort":@p_id}},{"term":{"AppId":@a_id}}]}},"sort":{"@_sortname":"@_sortorder"},"from":@_from,"size":@_size}</postdata>
        <key_value type="json" part_data="@_port" key="@p_id">
            <key name="@_port">port</key>
        </key_value>
        <key_value type="json" part_data="@_app" key="@a_id">
            <key name="@_app">appId</key>
        </key_value>
        <key_value type="json" part_data="@_from" key="@_from">
            <key name="@_from">from</key>
        </key_value>
        <key_value type="json" part_data="@_size" key="@_size">
            <key name="@_size">size</key>
        </key_value>
        <key_value type="json" part_data="@_sortname" key="@_sortname">
            <key name="@_sortname">sortName</key>
        </key_value>
        <key_value type="json" part_data="@_sortorder" key="@_sortorder">
            <key name="@_sortorder">sortOrder</key>
        </key_value>
    </PORT_APP_TO_LINK>
</config>

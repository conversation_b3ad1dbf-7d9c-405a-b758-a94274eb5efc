<config>
    <!-- 连接id与标签关联的标签库 -->
    <FullFlow_Tag name="get selet name">
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/fullflow_tag.sh</url>
        <urlparam>tidb</urlparam>
        <postdata>{"type":"FullFlow_Tag","sessionId":"@s_id"}</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
    </FullFlow_Tag>
    <FullFlow_Analysis_Tag name="get selet name">
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/fullflow_analysis_tag.sh</url>
        <urlparam>tidb</urlparam>
        <postdata>{"type":"FullFlow_Analysis_Tag","sessionId":"@s_id"}</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
    </FullFlow_Analysis_Tag>
    <!-- 连接id与黑白名单 -->
    <FullFlow_BWList name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>SESSION_ID_LIST where session_id = '@s_id'</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
    </FullFlow_BWList>
    <!-- 连接id与备注 -->
    <FullFlow_Remark name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>SESSION_ID_REMARK where session_id = '@s_id'</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
    </FullFlow_Remark>
    <!-- 连接分析-备注新增 Mysql -->
    <FullFlow_REMARKSAVE name="get selet name">
        <url type="put">insert into SESSION_ID_REMARK (session_id,remarks) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@s_id','@rmkName')</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </FullFlow_REMARKSAVE>
    <!-- 连接分析-备注修改 Mysql -->
    <FullFlow_REMARKUPDATE name="get selet name">
        <url type="put">update SESSION_ID_REMARK set </url>
        <urlparam>tidb</urlparam>
        <postdata>remarks = '@rmkName' where session_id = '@s_id'</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </FullFlow_REMARKUPDATE>
    <!-- 连接分析-黑白修正 Mysql -->
    <FullFlow_BWUP name="get selet name">
        <url type="put">update SESSION_ID_LIST set </url>
        <urlparam>tidb</urlparam>
        <postdata>blackList = @black_list,whiteList = @white_list where session_id = '@s_id'</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
        <key_value type="json" part_data="@b_list" key="@black_list">
            <key name="@b_list">blackList</key>
        </key_value>
        <key_value type="json" part_data="@w_list" key="@white_list">
            <key name="@w_list">whiteList</key>
        </key_value>
    </FullFlow_BWUP>
    <!-- 连接分析-黑白修正 Mysql -->
    <FullFlow_BWSAVE name="get selet name">
        <url type="put">insert into SESSION_ID_LIST (session_id,blackList,whiteList) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@s_id','@black_list','@white_list')</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">sessionId</key>
        </key_value>
        <key_value type="json" part_data="@b_list" key="@black_list">
            <key name="@b_list">blackList</key>
        </key_value>
        <key_value type="json" part_data="@w_list" key="@white_list">
            <key name="@w_list">whiteList</key>
        </key_value>
    </FullFlow_BWSAVE>
    <!-- PassiveCert -->
    <IP_ANALYSIS_PC name="get selet name">
        <url type="post">select * from  </url>
        <urlparam>tidb</urlparam>
        <postdata>DATA_PASSICECERT where IP = '@sd_ip' order by @sort_name @sort_order limit @page_now , @page_size </postdata>
        <key_value type="json" part_data="@_sdip" key="@sd_ip">
            <key name="@_sdip">sdIP</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </IP_ANALYSIS_PC>
    <!-- ClientCert -->
    <IP_ANALYSIS_CC name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>DATA_CLIENTCERT where IP = '@sd_ip' order by @sort_name @sort_order limit @page_now , @page_size </postdata>
        <key_value type="json" part_data="@_sdip" key="@sd_ip">
            <key name="@_sdip">sdIP</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </IP_ANALYSIS_CC>
    <!-- IP分析 Mysql -->
    <IP_ANALYSIS_MYSQL name="get selet name">
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/ip_analysis.sh</url>
        <urlparam>tidb</urlparam>
        <postdata>{"type":"IP_ANALYSIS_MYSQL","sdIP":"@sd_ip"}</postdata>
        <key_value type="json" part_data="@_sdip" key="@sd_ip">
            <key name="@_sdip">sdIP</key>
        </key_value>
    </IP_ANALYSIS_MYSQL>
    <!-- IP分析-关联 Mysql -->
    <IP_ANALYSIS_CONN name="get selet name">
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/ip_cert_domain.sh</url>
        <urlparam>tidb</urlparam>
        <postdata>{"type":"IP_ANALYSIS_CONN"@tiaojian}</postdata>
        <key_value type="json" part_data=",@KMoudle" key="@tiaojian">
            <moudle part_data="&quot;@key&quot;:&quot;@value&quot;" key="@KMoudle">
                <value>
                    <key name="@key">STRING_IS_sdIP</key>
                    <key name="@value">sdIP</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
    </IP_ANALYSIS_CONN>
    <!-- IP分析-PassiveDNS Mysql -->
    <IP_ANALYSIS_PassiveDNS name="get selet name">
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/ip_passivedns.sh</url>
        <urlparam>tidb</urlparam>
        <postdata>{"type":"IP_ANALYSIS_PassiveDNS"@tiaojian}</postdata>
        <key_value type="json" part_data=",@KMoudle" key="@tiaojian">
            <moudle part_data="&quot;@key&quot;:&quot;@value&quot;" key="@KMoudle">
                <value>
                    <key name="@key">STRING_IS_sdIP</key>
                    <key name="@value">sdIP</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
    </IP_ANALYSIS_PassiveDNS>
    <!-- IP分析-ClientDNS Mysql -->
    <IP_ANALYSIS_ClientDNS name="get selet name">
        <url type="post">select * from </url>
        <urlparam>tidb</urlparam>
        <postdata>DATA_CLIENTDNS where ClientIP = '@sd_ip' order by @sort_name @sort_order limit @page_now , @page_size </postdata>
        <key_value type="json" part_data="@_sdip" key="@sd_ip">
            <key name="@_sdip">sdIP</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </IP_ANALYSIS_ClientDNS>
    <!-- IP分析-备注新增 Mysql -->
    <IP_ANALYSIS_REMARKSAVE name="get selet name">
        <url type="put">insert into ip_remark (ip,remark) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@sd_ip','@rmkName')</postdata>
        <key_value type="json" part_data="@_sdip" key="@sd_ip">
            <key name="@_sdip">sdIP</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </IP_ANALYSIS_REMARKSAVE>
    <!-- IP分析-备注修改 Mysql -->
    <IP_ANALYSIS_REMARKUPDATE name="get selet name">
        <url type="put">update ip_remark set </url>
        <urlparam>tidb</urlparam>
        <postdata>remark = '@rmkName' where ip = '@sd_ip'</postdata>
        <key_value type="json" part_data="@_sdip" key="@sd_ip">
            <key name="@_sdip">sdIP</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </IP_ANALYSIS_REMARKUPDATE>
    <!-- IP分析-黑白修改 Mysql -->
    <IP_ANALYSIS_BWUP name="get selet name">
        <url type="put">update IP_LIST set </url>
        <urlparam>tidb</urlparam>
        <postdata>blackList = @black_list,whiteList = @white_list where IP = '@sd_ip'</postdata>
        <key_value type="json" part_data="@_sdip" key="@sd_ip">
            <key name="@_sdip">sdIP</key>
        </key_value>
        <key_value type="json" part_data="@b_list" key="@black_list">
            <key name="@b_list">blackList</key>
        </key_value>
        <key_value type="json" part_data="@w_list" key="@white_list">
            <key name="@w_list">whiteList</key>
        </key_value>
    </IP_ANALYSIS_BWUP>
    <!-- 基于标签检索，出现频率页签数据 -->
    <Search_CountPageTag name="get selet name">
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/tag_hz.sh</url>
        <urlparam>tidb</urlparam>
        <postdata>{"key":"@V1","bt":"@V2","et":"@V3","bw":"@begin_whitelist","ew":"@end_whitelist","bb":"@begin_blacklist","eb":"@end_blacklist"}</postdata>
        <key_value type="json" part_data="@K1" key="@V1">
            <moudle part_data="@value" key="@K1">
                <value>
                    <key name="@value">tagName</key>
                </value>
            </moudle>
        </key_value>
        <key_value type="json" part_data="@K2" key="@V2">
            <moudle part_data="@value" key="@K2">
                <value>
                    <key name="@value">begintime</key>
                </value>
            </moudle>
        </key_value>
        <key_value type="json" part_data="@K3" key="@V3">
            <moudle part_data="@value" key="@K3">
                <value>
                    <key name="@value">endtime</key>
                </value>
            </moudle>
        </key_value>
        <key_value type="json" part_data="@b_whitelist" key="@begin_whitelist">
            <key name="@b_whitelist">beginWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@e_whitelist" key="@end_whitelist">
            <key name="@e_whitelist">endWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@b_blacklist" key="@begin_blacklist">
            <key name="@b_blacklist">beginBlackList</key>
        </key_value>
        <key_value type="json" part_data="@e_blacklist" key="@end_blacklist">
            <key name="@e_blacklist">endBlackList</key>
        </key_value>
    </Search_CountPageTag>
    <!-- 流量态势历史数据 -->
    <FullFlow_His name="get selet name">
        <url type="post">select * from </url>
        <urlparam>dbpush</urlparam>
        <postdata>task_statistic order by create_time desc limit 0,10 </postdata>
        <key_value type="json" part_data="@btime" key="@begin_time">
            <key name="@btime">begintime</key>
        </key_value>
        <key_value type="json" part_data="@etime" key="@end_time">
            <key name="@etime">endtime</key>
        </key_value>
    </FullFlow_His>
    <!-- 流量态势启动时间 -->
    <FullFlow_BeginTime name="get selet name">
        <url type="post">select FROM_UNIXTIME(a.time) as updatedtime from </url>
        <urlparam>dbpush</urlparam>
        <postdata>tb_system_time a order by id desc limit 0,1 </postdata>
    </FullFlow_BeginTime>
    <!-- 端口分析=================================================begin-->
    <!-- 根据端口跳转到端口分析页面 -基本信息-备注-黑白名单-标签ID -->
    <Port_Analysis name="get selet name">
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/port_analysis.sh</url>
        <urlparam>tidb</urlparam>
        <postdata>{"type":"Port_Analysis","portName":"@port"}</postdata>
        <key_value type="json" part_data="@_p" key="@port">
            <key name="@_p">portName</key>
        </key_value>
    </Port_Analysis>
    <!-- 端口分析-备注新增 Mysql -->
    <Port_Analysis_REMARKSAVE name="get selet name">
        <url type="put">insert into PORT_REMARK (Port,remarks) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@port','@rmkName')</postdata>
        <key_value type="json" part_data="@_p" key="@port">
            <key name="@_p">portName</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </Port_Analysis_REMARKSAVE>
    <!-- 端口分析-备注修改 Mysql -->
    <Port_Analysis_REMARKUPDATE name="get selet name">
        <url type="put">update PORT_REMARK set </url>
        <urlparam>tidb</urlparam>
        <postdata>remarks = '@rmkName' where Port = '@port'</postdata>
        <key_value type="json" part_data="@_p" key="@port">
            <key name="@_p">portName</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </Port_Analysis_REMARKUPDATE>
    <!-- 端口分析-黑白修改 Mysql -->
    <Port_Analysis_BWUPDATE name="get selet name">
        <url type="put">update PORT_LIST set </url>
        <urlparam>tidb</urlparam>
        <postdata>blackList = @black_list,whiteList = @white_list where Port = '@port'</postdata>
        <key_value type="json" part_data="@_p" key="@port">
            <key name="@_p">portName</key>
        </key_value>
        <key_value type="json" part_data="@b_list" key="@black_list">
            <key name="@b_list">blackList</key>
        </key_value>
        <key_value type="json" part_data="@w_list" key="@white_list">
            <key name="@w_list">whiteList</key>
        </key_value>
    </Port_Analysis_BWUPDATE>
    <!-- 服务页签 -->
    <Port_Analysis_ServerTag name="get selet name">
        <url type="post">select pp.`Port`,pp.Pro_Id,pp.Count,apv.Pro_Name,apv.Pro_Value,apv.Pro_Exp from </url>
        <urlparam>tidb</urlparam>
        <postdata>PRO_PORT pp left join app_pro_value apv on pp.Pro_Id = apv.Pro_Id where pp.`Port` = '@port' order by pp.Count desc limit 0,500 </postdata>
        <key_value type="json" part_data="@_p" key="@port">
            <key name="@_p">portName</key>
        </key_value>
    </Port_Analysis_ServerTag>
    <!-- 端口分析=================================================end-->
    <!-- 应用分析=================================================begin-->
    <!-- 根据应用跳转到应用分析页面 -基本信息-备注-黑白名单-标签ID -->
    <App_Analysis name="get selet name">
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/app_analysis.sh</url>
        <urlparam>tidb</urlparam>
        <postdata>{"type":"App_Analysis","appId":"@app_id"}</postdata>
        <key_value type="json" part_data="@_app" key="@app_id">
            <key name="@_app">appId</key>
        </key_value>
    </App_Analysis>
    <!-- 应用分析-备注新增 Mysql -->
    <App_Analysis_REMARKSAVE name="get selet name">
        <url type="put">insert into APP_REMARK (App_Id,remarks) values </url>
        <urlparam>tidb</urlparam>
        <postdata>(@app_id,'@rmkName')</postdata>
        <key_value type="json" part_data="@_app" key="@app_id">
            <key name="@_app">appId</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </App_Analysis_REMARKSAVE>
    <!-- 应用分析-备注修改 Mysql -->
    <App_Analysis_REMARKUPDATE name="get selet name">
        <url type="put">update APP_REMARK set </url>
        <urlparam>tidb</urlparam>
        <postdata>remarks = '@rmkName' where App_Id = @app_id</postdata>
        <key_value type="json" part_data="@_app" key="@app_id">
            <key name="@_app">appId</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </App_Analysis_REMARKUPDATE>
    <!-- 应用分析-黑白修改 Mysql -->
    <App_Analysis_BWUPDATE name="get selet name">
        <url type="put">update APP_LIST set </url>
        <urlparam>tidb</urlparam>
        <postdata>blackList = @black_list,whiteList = @white_list where App_Id = @app_id</postdata>
        <key_value type="json" part_data="@_app" key="@app_id">
            <key name="@_app">appId</key>
        </key_value>
        <key_value type="json" part_data="@b_list" key="@black_list">
            <key name="@b_list">blackList</key>
        </key_value>
        <key_value type="json" part_data="@w_list" key="@white_list">
            <key name="@w_list">whiteList</key>
        </key_value>
    </App_Analysis_BWUPDATE>
    <!-- 服务页签 -->
    <App_Analysis_ServerTag name="get selet name">
        <url type="post">select pp.Pro_Id,pp.`Port`,pp.Count,piv.Tcp_Pro_Id,piv.Tcp_Pro_Name,piv.Udp_Pro_Id,piv.Udp_Pro_Name,piv.Remark from </url>
        <urlparam>tidb</urlparam>
        <postdata>PRO_PORT pp left join PORT_INFO_VALUE piv on piv.`Port` = pp.`Port` where pp.Pro_Id = @app_id order by pp.Count desc limit 0,500 </postdata>
        <key_value type="json" part_data="@_app" key="@app_id">
            <key name="@_app">appId</key>
        </key_value>
    </App_Analysis_ServerTag>
    <!-- 应用分析=================================================end-->
    <!-- 域名分析=================================================begin-->
    <!-- 根据域名跳转到域名分析页面 -基本信息-备注-黑白名单-标签List -->
    <Domain_Analysis name="get selet name">
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/domain_analysis.sh</url>
        <urlparam>tidb</urlparam>
        <postdata>{"type":"Domain_Analysis","domainName":"@domian_name"}</postdata>
        <key_value type="json" part_data="@d_name" key="@domian_name">
            <key name="@d_name">domainName</key>
        </key_value>
    </Domain_Analysis>
    <!-- 域名分析-备注新增 Mysql -->
    <Domain_Analysis_REMARKSAVE name="get selet name">
        <url type="put">insert into DOMAIN_REMARK (Domain_Name,remarks) values </url>
        <urlparam>tidb</urlparam>
        <postdata>('@domain_name','@rmkName')</postdata>
        <key_value type="json" part_data="@d_name" key="@domain_name">
            <key name="@d_name">domainName</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </Domain_Analysis_REMARKSAVE>
    <!-- 域名分析-备注修改 Mysql -->
    <Domain_Analysis_REMARKUPDATE name="get selet name">
        <url type="put">update DOMAIN_REMARK set </url>
        <urlparam>tidb</urlparam>
        <postdata>remarks = '@rmkName' where Domain_Name = '@domain_name'</postdata>
        <key_value type="json" part_data="@d_name" key="@domain_name">
            <key name="@d_name">domainName</key>
        </key_value>
        <key_value type="json" part_data="@_rmk" key="@rmkName">
            <key name="@_rmk">remarks</key>
        </key_value>
    </Domain_Analysis_REMARKUPDATE>
    <!-- 域名分析-黑白修改 Mysql -->
    <Domain_Analysis_BWUPDATE name="get selet name">
        <url type="put">update DOMAIN_INFO set </url>
        <urlparam>tidb</urlparam>
        <postdata>blackList = @black_list,whiteList = @white_list where Domain_Name = '@domain_name'</postdata>
        <key_value type="json" part_data="@d_name" key="@domain_name">
            <key name="@d_name">domainName</key>
        </key_value>
        <key_value type="json" part_data="@b_list" key="@black_list">
            <key name="@b_list">blackList</key>
        </key_value>
        <key_value type="json" part_data="@w_list" key="@white_list">
            <key name="@w_list">whiteList</key>
        </key_value>
    </Domain_Analysis_BWUPDATE>
    <!-- 域名分析-PassiveDNS页签 Mysql -->
    <Domain_Analysis_PDNS name="get selet name">
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/domain_passivedns.sh</url>
        <urlparam>tidb</urlparam>
        <postdata>{"type":"Domain_Analysis_PDNS"@tiaojian}</postdata>
        <key_value type="json" part_data=",@KMoudle" key="@tiaojian">
            <moudle part_data="&quot;@key&quot;:&quot;@value&quot;" key="@KMoudle">
                <value>
                    <key name="@key">STRING_IS_domainName</key>
                    <key name="@value">domainName</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
    </Domain_Analysis_PDNS>
    <!-- 域名分析-ClientDNS页签 Mysql -->
    <Domain_Analysis_CDNS name="get selet name">
        <url type="post">SELECT * FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>DATA_CLIENTDNS WHERE Domain = '@domain_name' order by @sort_name @sort_order limit @page_now, @page_size</postdata>
        <key_value type="json" part_data="@d_name" key="@domain_name">
            <key name="@d_name">domainName</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Domain_Analysis_CDNS>
    <!-- 域名分析-关联页签 Mysql -->
    <Domain_Analysis_Link name="get selet name">
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/domain_ip_certDomain.sh</url>
        <urlparam>tidb</urlparam>
        <postdata>{"type":"Domain_Analysis_Link"@tiaojian}</postdata>
        <key_value type="json" part_data=",@KMoudle" key="@tiaojian">
            <moudle part_data="&quot;@key&quot;:&quot;@value&quot;" key="@KMoudle">
                <value>
                    <key name="@key">STRING_IS_domainName</key>
                    <key name="@value">domainName</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
    </Domain_Analysis_Link>
    <!-- 域名分析=================================================end-->
    <!-- IP分析确定黑名单接口 -->
    <IP_ANALYSIS_SaveTag name="get selet name">
        <url type="put_btag">insert into ip_tag (ip,tagId,created_time) </url>
        <urlparam>tidb</urlparam>
        <postdata>values ('@sd_ip',@tag_id,UNIX_TIMESTAMP(NOW())) </postdata>
        <key_value type="json" part_data="@s_ip" key="@sd_ip">
            <key name="@s_ip">sdIP</key>
        </key_value>
        <key_value type="json" part_data="@t_id" key="@tag_id">
            <key name="@t_id">tagId</key>
        </key_value>
    </IP_ANALYSIS_SaveTag>
    <!-- 端口分析确定黑名单接口 -->
    <Port_ANALYSIS_SaveTag name="get selet name">
        <url type="put_btag">insert into PORT_TAG (Port,tagId,created_time) </url>
        <urlparam>tidb</urlparam>
        <postdata>values ('@port_name',@tag_id,UNIX_TIMESTAMP(NOW())) </postdata>
        <key_value type="json" part_data="@p_name" key="@port_name">
            <key name="@p_name">portName</key>
        </key_value>
        <key_value type="json" part_data="@t_id" key="@tag_id">
            <key name="@t_id">tagId</key>
        </key_value>
    </Port_ANALYSIS_SaveTag>
    <!-- 应用分析确定黑名单接口 -->
    <App_ANALYSIS_SaveTag name="get selet name">
        <url type="put_btag">insert into APP_TAG (App_Id,tagId,created_time) </url>
        <urlparam>tidb</urlparam>
        <postdata>values (@app_id,@tag_id,UNIX_TIMESTAMP(NOW())) </postdata>
        <key_value type="json" part_data="@a_id" key="@app_id">
            <key name="@a_id">appId</key>
        </key_value>
        <key_value type="json" part_data="@t_id" key="@tag_id">
            <key name="@t_id">tagId</key>
        </key_value>
    </App_ANALYSIS_SaveTag>
    <!-- 连接分析确定黑名单接口 -->
    <FullFlow_SaveTag name="get selet name">
        <url type="put_btag">insert into SESSION_ID_TAG (session_id,tag_id,created_time) </url>
        <urlparam>tidb</urlparam>
        <postdata>values ('@session_id',@tag_id,UNIX_TIMESTAMP(NOW())) </postdata>
        <key_value type="json" part_data="@s_id" key="@session_id">
            <key name="@s_id">sessionId</key>
        </key_value>
        <key_value type="json" part_data="@t_id" key="@tag_id">
            <key name="@t_id">tagId</key>
        </key_value>
    </FullFlow_SaveTag>
    <!-- 域名分析确定黑名单接口 -->
    <Domain_ANALYSIS_SaveTag name="get selet name">
        <url type="put_btag">insert into DOMAIN_TAG (Domain_Name,Tag_Id,created_time) </url>
        <urlparam>tidb</urlparam>
        <postdata>values ('@domian_name',@tag_id,UNIX_TIMESTAMP(NOW())) </postdata>
        <key_value type="json" part_data="@d_name" key="@domian_name">
            <key name="@d_name">domainName</key>
        </key_value>
        <key_value type="json" part_data="@t_id" key="@tag_id">
            <key name="@t_id">tagId</key>
        </key_value>
    </Domain_ANALYSIS_SaveTag>
    <!-- 证书检索- 所有者模糊匹配 -->
    <Cert_Owner name="get selet name">
        <url type="post">SELECT CertOWNName FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>CertOwnInfo WHERE CertOWNName LIKE '%@ow_name%' limit 0,5 </postdata>
        <key_value type="json" part_data="@oname" key="@ow_name">
            <key name="@oname">keyName</key>
        </key_value>
    </Cert_Owner>
    <!-- 证书检索- 域名模糊匹配 -->
    <Cert_Domain name="get selet name">
        <url type="post">SELECT DNName FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>CertDNInfo WHERE DNName LIKE '%@dn_name%' limit 0,5 </postdata>
        <key_value type="json" part_data="@dname" key="@dn_name">
            <key name="@dname">keyName</key>
        </key_value>
    </Cert_Domain>
    <!-- 证书检索- 查询 -->
    <Cert_Search_Qk name="get selet name">
        <!--<url type="post">SELECT cinfo.CertSHA1,cinfo.CertJson,cinfo.blackList,cinfo.whiteList,GROUP_CONCAT('  ',dinfo.DNName) AS CertDomain,(SELECT GROUP_CONCAT(Tag_Text) FROM TAG_INFO WHERE Tag_Id IN (SELECT tagId FROM Cert_TAG WHERE CertSHA1 = cinfo.CertSHA1)) AS CertTag FROM </url>-->
        <url type="post">SELECT cinfo.CertSHA1,cinfo.CertJson,cinfo.blackList,cinfo.whiteList,(SELECT GROUP_CONCAT(Tag_Text) FROM TAG_INFO WHERE Tag_Id IN (SELECT tagId FROM Cert_TAG WHERE CertSHA1 = cinfo.CertSHA1)) AS CertTag FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>
            CertInfo cinfo
            <!--LEFT JOIN CertSha1ToOwnID ownid ON ownid.CertSha1 = cinfo.CertSHA1 LEFT JOIN CertOwnInfo oinfo ON oinfo.CertOWNID = ownid.CertOwnID-->
            <!--LEFT JOIN CertDNToSha1 dnid ON dnid.CertSha1 = cinfo.CertSHA1 LEFT JOIN CertDNInfo dinfo ON dinfo.DNID = dnid.DNID-->
            WHERE
            cinfo.blackList BETWEEN @begin_blacklist AND @end_blacklist AND cinfo.whiteList BETWEEN @begin_whitelist AND @end_whitelist
            @tiaojian
            <!--AND cinfo.CertSHA1 = '@k_name'
            OR cinfo.CertMd5 = '@k_name'
            OR oinfo.CertOWNName = '@k_name'
            OR dinfo.DNName = '@k_name'-->
            <!--group by cinfo.CertSHA1-->
            order by @sort_name @sort_order limit @page_now,@page_size
        </postdata>
        <key_value type="json" part_data="@KMoudle1" key="@tiaojian">
            <!--<moudle part_data=" AND (cinfo.CertSHA1 = '@kname' OR cinfo.CertMd5 = '@kname' OR oinfo.CertOWNName = '@kname' OR dinfo.DNName = '@kname') " key="@KMoudle1">-->
            <moudle part_data=" AND (cinfo.CertJson like '%@kname%') " key="@KMoudle1">
                <value>
                    <key name="@kname">keyName</key>
                </value>
            </moudle> <!-- 替换数据块  -->
        </key_value>
        <key_value type="json" part_data="@b_blist" key="@begin_blacklist">
            <key name="@b_blist">beginBlackList</key>
        </key_value>
        <key_value type="json" part_data="@e_blist" key="@end_blacklist">
            <key name="@e_blist">endBlackList</key>
        </key_value>
        <key_value type="json" part_data="@b_wlist" key="@begin_whitelist">
            <key name="@b_wlist">beginWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@e_wlist" key="@end_whitelist">
            <key name="@e_wlist">endWhiteList</key>
        </key_value>
        <key_value type="json" part_data="@s_name" key="@sort_name">
            <key name="@s_name">sortName</key>
        </key_value>
        <key_value type="json" part_data="@s_order" key="@sort_order">
            <key name="@s_order">sortOrder</key>
        </key_value>
        <key_value type="json" part_data="@p_now" key="@page_now">
            <key name="@p_now">from</key>
        </key_value>
        <key_value type="json" part_data="@p_size" key="@page_size">
            <key name="@p_size">size</key>
        </key_value>
    </Cert_Search_Qk>
    <!-- 证书分析 - 基本信息+详细解析记录 -->
    <Cert_BasicInfo name="get selet name">
        <url type="syn">/opt/GeekSec/web/rule_syn/javascript/cert_analysis.sh</url>
        <urlparam>tidb</urlparam>
        <postdata>{"type":"Cert_BasicInfo","certSha1":"@certsha1"}</postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
    </Cert_BasicInfo>
    <!-- 证书分析 - 基本信息 - 确认黑名单 -->
    <Cert_SaveTag name="get selet name">
        <url type="put_btag">insert into Cert_TAG (CertSHA1,tagId,created_time) </url>
        <urlparam>tidb</urlparam>
        <postdata>values ('@certsha1',@tag_id,UNIX_TIMESTAMP(NOW())) </postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
        <key_value type="json" part_data="@t_id" key="@tag_id">
            <key name="@t_id">tagId</key>
        </key_value>
    </Cert_SaveTag>
    <!-- 证书分析 - 基本信息 - 黑名单权值修改 -->
    <Cert_UpList_Update name="get selet name">
        <url type="put">update CertInfo set </url>
        <urlparam>tidb</urlparam>
        <postdata>blackList = @black_list,whiteList = @white_list where CertSHA1 = '@certsha1'</postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
        <key_value type="json" part_data="@b_list" key="@black_list">
            <key name="@b_list">blackList</key>
        </key_value>
        <key_value type="json" part_data="@w_list" key="@white_list">
            <key name="@w_list">whiteList</key>
        </key_value>
    </Cert_UpList_Update>
    <!-- 证书分析 - 基本信息 - 备注 -->
    <Cert_Remarks_Save name="get selet name">
        <url type="put">update CertInfo set </url>
        <urlparam>tidb</urlparam>
        <postdata>
            remarks = '@certremarks' where CertSHA1 = '@certsha1'
        </postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
        <key_value type="json" part_data="@c_remark" key="@certremarks">
            <key name="@c_remark">certRemarks</key>
        </key_value>
    </Cert_Remarks_Save>
    <!-- 证书分析 - 证书链 -->
    <Cert_Link name="get selet name">
        <url type="post_for">SELECT ck.CertSha1 FROM CertInfo cif LEFT JOIN CertsubjectKeyIdentifier ck ON cif.subjectKeyIdentifier = ck.subjectKeyIdentifier WHERE cif.CertSHA1 = </url>
        <urlparam>tidb</urlparam>
        <postdata>@certsha1</postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
    </Cert_Link>
    <!-- 证书分析 - 关联信息 -->
    <Cert_LinkInfo name="get selet name">
        <url type="post">SELECT cinfo.CertSHA1,oinfo.CertOWNName,dinfo.DNName,(SELECT GROUP_CONCAT(CertSha1) FROM CertDNToSha1 WHERE DNID = dinfo.DNID) AS DN_LinkCert,(SELECT GROUP_CONCAT(CertSha1) FROM CertSha1ToOwnID WHERE CertOwnID = oinfo.CertOWNID) AS OWN_LinkCert FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>
            CertInfo cinfo
            LEFT JOIN CertSha1ToOwnID ownid ON ownid.CertSha1 = cinfo.CertSHA1 LEFT JOIN CertOwnInfo oinfo ON oinfo.CertOWNID = ownid.CertOwnID
            LEFT JOIN CertDNToSha1 dnid ON dnid.CertSha1 = cinfo.CertSHA1 LEFT JOIN CertDNInfo dinfo ON dinfo.DNID = dnid.DNID
            WHERE cinfo.CertSHA1 = '@certsha1'
            group by cinfo.CertSHA1
        </postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
    </Cert_LinkInfo>
    <!-- 证书分析 - PassiveCert -->
    <Cert_Pcert name="get selet name">
        <url type="post">SELECT p.Cert_Sha1,p.IP,p.Count,p.FirstTime,p.LastTime FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>
            DATA_PASSICECERT p WHERE Cert_Sha1 = '@certsha1'
        </postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
    </Cert_Pcert>
    <!-- 证书分析 - ClientCert -->
    <Cert_Ccert name="get selet name">
        <url type="post">SELECT c.Cert_Sha1,c.IP,c.Count,c.FirstTime,c.LastTime FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>
            DATA_CLIENTCERT c WHERE Cert_Sha1 = '@certsha1'
        </postdata>
        <key_value type="json" part_data="@c_sha1" key="@certsha1">
            <key name="@c_sha1">certSha1</key>
        </key_value>
    </Cert_Ccert>
    <CHECK_IP_ANALYSIS name="get selet name">
        <url type="post_count">SELECT count(*) FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>
            IP_INFO WHERE IP = '@keyname'
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
    </CHECK_IP_ANALYSIS>
    <CHECK_PORT_ANALYSIS name="get selet name">
        <url type="post_count">SELECT count(*) FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>
            PRO_PORT WHERE Port = @keyname
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
    </CHECK_PORT_ANALYSIS>
    <CHECK_APP_ANALYSIS name="get selet name">
        <url type="post_count">SELECT count(*) FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>
            PRO_PORT WHERE Pro_Id = @keyname
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
    </CHECK_APP_ANALYSIS>
    <CHECK_DOMAIN_ANALYSIS name="get selet name">
        <url type="post_count">SELECT count(*) FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>
            DOMAIN_INFO WHERE Domain_Name = '@keyname'
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
    </CHECK_DOMAIN_ANALYSIS>
    <CHECK_CERT_ANALYSIS name="get selet name">
        <url type="post_count">SELECT count(*) FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>
            CertInfo WHERE CertSHA1 = '@keyname'
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
    </CHECK_CERT_ANALYSIS>
    <CHECK_MAC_ANALYSIS name="get selet name">
        <url type="post_count">SELECT count(*) FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>
            mac_info WHERE mac = '@keyname'
        </postdata>
        <key_value type="json" part_data="@k_name" key="@keyname">
            <key name="@k_name">keyName</key>
        </key_value>
    </CHECK_MAC_ANALYSIS>
    <!-- 系统信息 -->
    <SYSTEM_INFO name="get selet name">
        <url type="post">SELECT * FROM </url>
        <urlparam>dbpush</urlparam>
        <postdata>
            sys_info a,tb_product_info b  order by id desc limit 0,1
        </postdata>
    </SYSTEM_INFO>
    <IP_ANALYSIS_OS name="get selet name">
        <url type="post">SELECT srcip,IP,port,app,count,begin_time,end_time FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>IP_Client_Port WHERE IP = '@s_id' order by count desc @page_size</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">dstIP</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@page_size">
            <moudle part_data=" limit @p_size " key="@KMoudle1">
                <value>
                    <key name="@p_size">size</key>
                </value>
            </moudle>
        </key_value>
    </IP_ANALYSIS_OS>
    <IP_ANALYSIS_AS name="get selet name">
        <url type="post">SELECT srcip,IP,port,app,count,begin_time,end_time FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>IP_Client_Port WHERE srcip = '@s_id' order by count desc @page_size</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">srcIP</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@page_size">
            <moudle part_data=" limit @p_size " key="@KMoudle1">
                <value>
                    <key name="@p_size">size</key>
                </value>
            </moudle>
        </key_value>
    </IP_ANALYSIS_AS>
    <Port_ANALYSIS_AO name="get selet name">
        <url type="post">SELECT srcip,IP,port,app,count,begin_time,end_time FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>IP_Client_Port WHERE port = @s_id order by count desc @page_size</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">srcPort</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@page_size">
            <moudle part_data=" limit @p_size " key="@KMoudle1">
                <value>
                    <key name="@p_size">size</key>
                </value>
            </moudle>
        </key_value>
    </Port_ANALYSIS_AO>
    <App_ANALYSIS_AO name="get selet name">
        <url type="post">SELECT srcip,IP,port,app,count,begin_time,end_time FROM </url>
        <urlparam>tidb</urlparam>
        <postdata>IP_Client_Port WHERE app = @s_id order by count desc @page_size</postdata>
        <key_value type="json" part_data="@sId" key="@s_id">
            <key name="@sId">appId</key>
        </key_value>
        <key_value type="json" part_data="@KMoudle1" key="@page_size">
            <moudle part_data=" limit @p_size " key="@KMoudle1">
                <value>
                    <key name="@p_size">size</key>
                </value>
            </moudle>
        </key_value>
    </App_ANALYSIS_AO>
</config>

<config> <!-- http 参数解析及数据回填-->
    <filed>type</filed>
    <host>http://elasticsearch:9200/</host>

    <!-- 规则数据量时间 -->
    <server type="RuleSizeTime">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="RuleSizeTime">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">RuleSizeTime</data>
        </response>
    </server>

    <!-- 规则数据量字典 -->
    <server type="RuleSize">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="RuleSize">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">RuleSize</data>
        </response>
    </server>

    <!-- 规则数据量 -->
    <server type="RuleSizeFlow">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="RuleSizeFlow">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">RuleSizeFlow</data>
        </response>
    </server>

    <!-- 世界地图-折线图 -->
    <server type="IP_EXTEMAL_SITUATION_LINE">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="IP_EXTEMAL_SITUATION_LINE">ESHandle</one_do>
        </handle>
        <response>
        </response>
        <response_list> <!-- 存储数据 -->
            <BList>1</BList><!-- 是否为 list  1 为 list  2 为 list   -->
            <valuelist>
                <src_value>
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>byte_list</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>IP_EXTEMAL_SITUATION_LINE/aggregations/by_day/buckets</LPath> <!-- 源数据path  -->
                    <TZhu>1</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>key</KeyPath> <!-- key  值路径-->
                </src_value>
                <value>
                    <src_name>byte_list</src_name> <!-- 取值的 src_value -->
                    <key>daytime</key> <!-- resp key值 存储名称 -->
                    <path>key</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <listValue>1</listValue>
                </value>
                <value>
                    <src_name>byte_list</src_name> <!-- 取值的 src_value -->
                    <key>sBytes</key> <!-- resp key值 存储名称 -->
                    <path>scount/value</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <listValue>1</listValue>
                </value>
                <value>
                    <src_name>byte_list</src_name> <!-- 取值的 src_value -->
                    <key>dBytes</key> <!-- resp key值 存储名称 -->
                    <path>dcount/value</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <listValue>1</listValue>
                </value>
            </valuelist>
        </response_list>
    </server>

    <!-- 世界地图-IP热度 -->
    <server type="IP_EXTEMAL_SITUATION_AREA">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="IP_EXTEMAL_SITUATION_AREA">ESHandle</one_do>
        </handle>
        <response>
        </response>
        <response_list> <!-- 存储数据 -->
            <BList>1</BList><!-- 是否为 list  1 为 list  2 为 list   -->
            <valuelist>
                <src_value>
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>area_list</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>IP_EXTEMAL_SITUATION_AREA/hits/hits</LPath> <!-- 源数据path  -->
                    <TZhu>1</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath></KeyPath> <!-- key  值路径-->
                </src_value>
                <value>
                    <src_name>area_list</src_name> <!-- 取值的 src_value -->
                    <key>srcIp</key> <!-- resp key值 存储名称 -->
                    <path>_source/sIp</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>2</value_type> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>area_list</src_name> <!-- 取值的 src_value -->
                    <key>srcIpLongitude</key> <!-- resp key值 存储名称 -->
                    <path>_source/sIpLongitude</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>1</value_type> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>area_list</src_name> <!-- 取值的 src_value -->
                    <key>srcIpLatitude</key> <!-- resp key值 存储名称 -->
                    <path>_source/sIpLatitude</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>1</value_type> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>area_list</src_name> <!-- 取值的 src_value -->
                    <key>srcIpCountry</key> <!-- resp key值 存储名称 -->
                    <path>_source/sIpCountry</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>2</value_type> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>area_list</src_name> <!-- 取值的 src_value -->
                    <key>srcIpSubdivisions</key> <!-- resp key值 存储名称 -->
                    <path>_source/sIpCsubdivisions</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>2</value_type> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>area_list</src_name> <!-- 取值的 src_value -->
                    <key>srcIpCity</key> <!-- resp key值 存储名称 -->
                    <path>_source/sIpCity</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>2</value_type> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>area_list</src_name> <!-- 取值的 src_value -->
                    <key>dstIp</key> <!-- resp key值 存储名称 -->
                    <path>_source/dIp</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>2</value_type> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>area_list</src_name> <!-- 取值的 src_value -->
                    <key>dstIpLongitude</key> <!-- resp key值 存储名称 -->
                    <path>_source/dIpLongitude</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>1</value_type> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>area_list</src_name> <!-- 取值的 src_value -->
                    <key>dstIpLatitude</key> <!-- resp key值 存储名称 -->
                    <path>_source/dIpLatitude</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>1</value_type> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>area_list</src_name> <!-- 取值的 src_value -->
                    <key>dstIpCountry</key> <!-- resp key值 存储名称 -->
                    <path>_source/dIpCountry</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>2</value_type> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>area_list</src_name> <!-- 取值的 src_value -->
                    <key>dstIpSubdivisions</key> <!-- resp key值 存储名称 -->
                    <path>_source/dIpSubdivisions</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>2</value_type> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>area_list</src_name> <!-- 取值的 src_value -->
                    <key>dstIpCity</key> <!-- resp key值 存储名称 -->
                    <path>_source/dIPCity</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>2</value_type> <!-- 取值类型  1 int 2 string 3  -->
                </value>
            </valuelist>
        </response_list>
    </server>

    <!-- 全流量查询 -->
    <server type="FullFlow">
        <arr_conf><!--取参数  数据替换-->
            <arr key="id">type</arr>
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="FullFlow">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="count" type="json" resp_type="int">FullFlow/hits/total</data>
            <!--<data resp_key="row" type="json" resp_type="int">FullFlow/_shards/total</data>-->
            <data resp_key="data" type="json" resp_type="json">FullFlow/hits/hits</data>
        </response>
        <response_list> <!-- 存储数据 -->
            <BList>2</BList><!-- 是否为 list  1 为 list  2 为 list   -->
            <valuelist>
                <src_value> <!-- 数据来源定义  取值   - -->
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>full_hits</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>FullFlow/hits</LPath> <!-- 源数据path  -->
                    <TZhu>2</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>total</KeyPath> <!-- key  值路径-->
                </src_value>
                <!--<src_value> &lt;!&ndash; 数据来源定义  取值   - &ndash;&gt;
                    <type>2</type>&lt;!&ndash; 定义 1 为主LIST 2 为 fuz  &ndash;&gt;
                    <name>full_shards</name> &lt;!&ndash; 源数据类型 对应的动作类型 &ndash;&gt;
                    <LPath>FullFlow/_shards</LPath> &lt;!&ndash; 源数据path  &ndash;&gt;
                    <TZhu>2</TZhu>&lt;!&ndash; 是否需要转换  1 标识需要转换 0 标识不需要抓换&ndash;&gt;
                    <KeyPath></KeyPath> &lt;!&ndash; key  值路径&ndash;&gt;
                </src_value>-->
                <value>
                    <src_name>full_hits</src_name> <!-- 取值的 src_value -->
                    <path>total</path>
                    <key>count</key> <!-- resp key值 存储名称 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                </value>
                <!--<value>
                    <src_name>full_shards</src_name> &lt;!&ndash; 取值的 src_value &ndash;&gt;
                    <path>total</path>
                    <key>row</key> &lt;!&ndash; resp key值 存储名称 &ndash;&gt;
                    <badd>2</badd>&lt;!&ndash; 取值路径  1 是累加  2  是 &ndash;&gt;
                    <list>1</list>&lt;!&ndash; 取值路径  1 是累加  2  是 &ndash;&gt;
                </value>-->
                <!--<value>
                    <src_name>full_hits</src_name> &lt;!&ndash; 取值的 src_value &ndash;&gt;
                    <path>hits</path>
                    <key>data</key> &lt;!&ndash; resp key值 存储名称 &ndash;&gt;
                    <badd>2</badd>&lt;!&ndash; 取值路径  1 是累加  2  是 &ndash;&gt;
                    <list>2</list>&lt;!&ndash; 取值路径  1 是累加  2  是 &ndash;&gt;
                    <value_type>2</value_type> &lt;!&ndash; 取值类型  1 int 2 string 3  &ndash;&gt;
                </value>-->
            </valuelist>
        </response_list>
    </server>

    <!-- 全流量查询-IP-连接列表 -->
    <server type="FullFlow_Mac_Link">
        <arr_conf><!--取参数  数据替换-->
            <arr key="id">type</arr>
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="FullFlow_Mac_Link">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="count" type="json" resp_type="int">FullFlow_Mac_Link/hits/total</data>
            <data resp_key="data" type="json" resp_type="json">FullFlow_Mac_Link/hits/hits</data>
        </response>
        <response_list> <!-- 存储数据 -->
            <BList>2</BList><!-- 是否为 list  1 为 list  2 为 list   -->
            <valuelist>
                <src_value> <!-- 数据来源定义  取值   - -->
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>full_hits</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>FullFlow_Mac_Link/hits</LPath> <!-- 源数据path  -->
                    <TZhu>2</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>total</KeyPath> <!-- key  值路径-->
                </src_value>
                <value>
                    <src_name>full_hits</src_name> <!-- 取值的 src_value -->
                    <path>total</path>
                    <key>count</key> <!-- resp key值 存储名称 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                </value>
            </valuelist>
        </response_list>
    </server>
    <!-- 全流量查询-IP-连接列表 -->
    <server type="FullFlow_Link">
        <arr_conf><!--取参数  数据替换-->
            <arr key="id">type</arr>
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="FullFlow_Link">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="count" type="json" resp_type="int">FullFlow_Link/hits/total</data>
            <data resp_key="data" type="json" resp_type="json">FullFlow_Link/hits/hits</data>
        </response>
        <response_list> <!-- 存储数据 -->
            <BList>2</BList><!-- 是否为 list  1 为 list  2 为 list   -->
            <valuelist>
                <src_value> <!-- 数据来源定义  取值   - -->
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>full_hits</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>FullFlow_Link/hits</LPath> <!-- 源数据path  -->
                    <TZhu>2</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>total</KeyPath> <!-- key  值路径-->
                </src_value>
                <value>
                    <src_name>full_hits</src_name> <!-- 取值的 src_value -->
                    <path>total</path>
                    <key>count</key> <!-- resp key值 存储名称 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                </value>
            </valuelist>
        </response_list>
    </server>
    <!-- 全流量查询-Port-连接列表 -->
    <server type="FullFlow_Port_Link">
        <arr_conf><!--取参数  数据替换-->
            <arr key="id">type</arr>
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="FullFlow_Port_Link">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="count" type="json" resp_type="int">FullFlow_Port_Link/hits/total</data>
            <data resp_key="data" type="json" resp_type="json">FullFlow_Port_Link/hits/hits</data>
        </response>
        <response_list> <!-- 存储数据 -->
            <BList>2</BList><!-- 是否为 list  1 为 list  2 为 list   -->
            <valuelist>
                <src_value> <!-- 数据来源定义  取值   - -->
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>full_hits</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>FullFlow_Port_Link/hits</LPath> <!-- 源数据path  -->
                    <TZhu>2</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>total</KeyPath> <!-- key  值路径-->
                </src_value>
                <value>
                    <src_name>full_hits</src_name> <!-- 取值的 src_value -->
                    <path>total</path>
                    <key>count</key> <!-- resp key值 存储名称 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                </value>
            </valuelist>
        </response_list>
    </server>
    <!-- 全流量查询-App-连接列表 -->
    <server type="FullFlow_App_Link">
        <arr_conf><!--取参数  数据替换-->
            <arr key="id">type</arr>
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="FullFlow_App_Link">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="count" type="json" resp_type="int">FullFlow_App_Link/hits/total</data>
            <data resp_key="data" type="json" resp_type="json">FullFlow_App_Link/hits/hits</data>
        </response>
        <response_list> <!-- 存储数据 -->
            <BList>2</BList><!-- 是否为 list  1 为 list  2 为 list   -->
            <valuelist>
                <src_value> <!-- 数据来源定义  取值   - -->
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>full_hits</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>FullFlow_App_Link/hits</LPath> <!-- 源数据path  -->
                    <TZhu>2</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>total</KeyPath> <!-- key  值路径-->
                </src_value>
                <value>
                    <src_name>full_hits</src_name> <!-- 取值的 src_value -->
                    <path>total</path>
                    <key>count</key> <!-- resp key值 存储名称 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                </value>
            </valuelist>
        </response_list>
    </server>

    <!-- 全流量查询-连接分析 -->
    <server type="FullFlow_Analysis">
        <arr_conf><!--取参数  数据替换-->
            <arr key="id">type</arr>
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="FullFlow_Analysis">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">FullFlow_Analysis/hits/hits</data>
        </response>
    </server>

    <!-- 全流量查询-连接分析 -->
    <server type="FullFlow_Analysis_App">
        <arr_conf><!--取参数  数据替换-->
            <arr key="id">type</arr>
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="FullFlow_Analysis_App">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">FullFlow_Analysis_App/hits/hits</data>
        </response>
    </server>

    <!-- 连接id与标签关联的标签库 -->
    <server type="FullFlow_Tag">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_MY_Json.xml" node_name="FullFlow_Tag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">FullFlow_Tag</data>
        </response>
    </server>
    <!-- 连接id与标签关联的标签库 -->
    <server type="FullFlow_Analysis_Tag">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_MY_Json.xml" node_name="FullFlow_Analysis_Tag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">FullFlow_Analysis_Tag</data>
        </response>
    </server>

    <!-- 连接id与黑白名单 -->
    <server type="FullFlow_BWList">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_MY_Json.xml" node_name="FullFlow_BWList">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">FullFlow_BWList</data>
        </response>
    </server>

    <!-- 连接id与备注 -->
    <server type="FullFlow_Remark">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_MY_Json.xml" node_name="FullFlow_Remark">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">FullFlow_Remark</data>
        </response>
    </server>
    <!-- 连接分析-备注新增 Mysql -->
    <server type="FullFlow_REMARKSAVE">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="FullFlow_REMARKSAVE">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">FullFlow_REMARKSAVE</data>
        </response>
    </server>
    <!-- 连接分析-备注修改 Mysql -->
    <server type="FullFlow_REMARKUPDATE">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="FullFlow_REMARKUPDATE">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">FullFlow_REMARKUPDATE</data>
        </response>
    </server>
    <!-- 连接分析-黑白修正 Mysql -->
    <server type="FullFlow_BWUP">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="FullFlow_BWUP">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">FullFlow_BWUP</data>
        </response>
    </server>
    <!-- 连接分析-黑白新增 Mysql -->
    <server type="FullFlow_BWSAVE">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="FullFlow_BWSAVE">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">FullFlow_BWSAVE</data>
        </response>
    </server>

    <!-- 开放服务 -->
    <server type="IP_ANALYSIS_OS_BAK">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="IP_ANALYSIS_OS">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 无用-->
            <!--<data resp_key="dst_port_distinct" type="json" resp_type="int">aggregations</data>-->
        </response>
        <response_list> <!-- 存储数据 -->
            <BList>1</BList><!-- 是否为 list  1 为 list  2 为 list   -->
            <valuelist>
                <src_value> <!-- ip聚合 -->
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>ip_list</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>IP_ANALYSIS_OS/aggregations/client_IP/buckets</LPath> <!-- 源数据path  -->
                    <TZhu>1</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>key</KeyPath> <!-- key  值路径-->
                </src_value>
                <!--祥哥需要修改地方 在上面的buckets里面再取下面数据-->
                <value>
                    <src_name>ip_list</src_name> <!-- 取值的 src_value -->
                    <key>IP</key> <!-- resp key值 存储名称 -->
                    <path>key</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>2</value_type> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>ip_list</src_name> <!-- 取值的 src_value -->
                    <key></key> <!-- resp key值 存储名称 -->
                    <path></path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <src_value> <!-- dst端口聚合 -->
                        <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                        <name>port_aggs</name> <!-- 源数据类型 对应的动作类型 -->
                        <LPath>dstport/buckets</LPath> <!-- 源数据path  --> <TZhu>1</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                        <KeyPath>key</KeyPath> <!-- key  值路径-->
                    </src_value>
                    <value>
                        <src_name>port_aggs</src_name> <!-- 取值的 src_value -->
                        <key>port</key> <!-- resp key值 存储名称 -->
                        <path>key</path><!-- 取值路径 -->
                        <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                        <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                        <listValue>1</listValue>
                    </value>
                    <value>
                        <listValue>1</listValue>
                        <src_name>port_aggs</src_name> <!-- 取值的 src_value -->
                        <key>app_info</key> <!-- resp key值 存储名称 -->
                        <path></path><!-- 取值路径 -->
                        <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                        <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                        <src_value> <!-- dst端口聚合 -->
                            <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                            <name>port_aggs_ts</name> <!-- 源数据类型 对应的动作类型 -->
                            <LPath>app/buckets</LPath> <!-- 源数据path  -->
                            <TZhu>1</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                            <KeyPath>key</KeyPath> <!-- key  值路径-->
                        </src_value>
                        <!--app/buckets/key-->
                        <value>
                            <src_name>port_aggs_ts</src_name> <!-- 取值的 src_value -->
                            <key>app_id</key> <!-- resp key值 存储名称 -->
                            <path>key</path><!-- 取值路径 -->
                            <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                            <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                            <listValue>1</listValue>
                        </value>
                        <!--app/buckets/doc_count-->
                        <value>
                            <src_name>port_aggs_ts</src_name> <!-- 取值的 src_value -->
                            <key>connect_num</key> <!-- resp key值 存储名称 -->
                            <path>doc_count</path><!-- 取值路径 -->
                            <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                            <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                        </value>

                        <!--app/buckets/last_found_time/value-->
                        <value>
                            <src_name>port_aggs_ts</src_name> <!-- 取值的 src_value -->
                            <key>last_found_time</key> <!-- resp key值 存储名称 -->
                            <path>last_found_time/value</path><!-- 取值路径 -->
                            <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                            <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                        </value>
                        <!--app/buckets/first_found_time/value-->
                        <value>
                            <src_name>port_aggs_ts</src_name> <!-- 取值的 src_value -->
                            <key>first_found_time</key> <!-- resp key值 存储名称 -->
                            <path>first_found_time/value</path><!-- 取值路径 -->
                            <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                            <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                        </value>
                    </value>
                </value>
            </valuelist>
        </response_list>
    </server>

    <!-- 访问服务 -->
    <server type="IP_ANALYSIS_AS_BAK">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="IP_ANALYSIS_AS">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 无用-->
            <!--<data resp_key="dst_port_distinct" type="json" resp_type="int">aggregations</data>-->
        </response>
        <response_list> <!-- 存储数据 -->
            <BList>1</BList><!-- 是否为 list  1 为 list  2 为 list   -->
            <valuelist>
                <src_value> <!-- ip聚合 -->
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>ip_list</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>IP_ANALYSIS_AS/aggregations/server_IP/buckets</LPath> <!-- 源数据path  -->
                    <TZhu>1</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>key</KeyPath> <!-- key  值路径-->
                </src_value>
                <!--祥哥需要修改地方 在上面的buckets里面再取下面数据-->
                <value>
                    <src_name>ip_list</src_name> <!-- 取值的 src_value -->
                    <key>IP</key> <!-- resp key值 存储名称 -->
                    <path>key</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>2</value_type> <!-- 取值类型  1 int 2 string 3  -->
                </value>
                <value>
                    <src_name>ip_list</src_name> <!-- 取值的 src_value -->
                    <key></key> <!-- resp key值 存储名称 -->
                    <path></path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <src_value> <!-- dst端口聚合 -->
                        <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                        <name>port_aggs</name> <!-- 源数据类型 对应的动作类型 -->
                        <LPath>srcport/buckets</LPath> <!-- 源数据path  --> <TZhu>1</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                        <KeyPath>key</KeyPath> <!-- key  值路径-->
                    </src_value>
                    <value>
                        <src_name>port_aggs</src_name> <!-- 取值的 src_value -->
                        <key>port</key> <!-- resp key值 存储名称 -->
                        <path>key</path><!-- 取值路径 -->
                        <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                        <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                        <listValue>1</listValue>
                    </value>
                    <value>
                        <src_name>port_aggs</src_name> <!-- 取值的 src_value -->
                        <key>app_info</key> <!-- resp key值 存储名称 -->
                        <path></path><!-- 取值路径 -->
                        <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                        <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                        <listValue>1</listValue>
                        <src_value> <!-- dst端口聚合 -->
                            <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                            <name>port_aggs_ts</name> <!-- 源数据类型 对应的动作类型 -->
                            <LPath>app/buckets</LPath> <!-- 源数据path  -->
                            <TZhu>1</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                            <KeyPath>key</KeyPath> <!-- key  值路径-->
                        </src_value>
                        <!--app/buckets/key-->
                        <value>
                            <src_name>port_aggs_ts</src_name> <!-- 取值的 src_value -->
                            <key>app_id</key> <!-- resp key值 存储名称 -->
                            <path>key</path><!-- 取值路径 -->
                            <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                            <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                            <listValue>1</listValue>
                        </value>
                        <!--app/buckets/doc_count-->
                        <value>
                            <src_name>port_aggs_ts</src_name> <!-- 取值的 src_value -->
                            <key>connect_num</key> <!-- resp key值 存储名称 -->
                            <path>doc_count</path><!-- 取值路径 -->
                            <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                            <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                        </value>
                        <!--app/buckets/last_found_time/value-->
                        <value>
                            <src_name>port_aggs_ts</src_name> <!-- 取值的 src_value -->
                            <key>last_found_time</key> <!-- resp key值 存储名称 -->
                            <path>last_found_time/value</path><!-- 取值路径 -->
                            <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                            <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                        </value>
                        <!--app/buckets/first_found_time/value-->
                        <value>
                            <src_name>port_aggs_ts</src_name> <!-- 取值的 src_value -->
                            <key>first_found_time</key> <!-- resp key值 存储名称 -->
                            <path>first_found_time/value</path><!-- 取值路径 -->
                            <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                            <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                        </value>
                    </value>
                </value>
            </valuelist>
        </response_list>
    </server>

    <!-- ClientCert -->
    <server type="IP_ANALYSIS_CC">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_ANALYSIS_CC">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">IP_ANALYSIS_CC</data>
        </response>
    </server>

    <!-- PassiveCert -->
    <server type="IP_ANALYSIS_PC">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_ANALYSIS_PC">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">IP_ANALYSIS_PC</data>
        </response>
    </server>

    <!-- IP检索为空值 Mysql -->
    <server type="IP_SEARCH_NULL">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_SEARCH_NULL">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">IP_SEARCH_NULL</data>
        </response>
    </server>

    <!-- IP检索单多个IP Mysql -->
    <server type="IP_SEARCH_NUM">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_SEARCH_NUM">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">IP_SEARCH_NUM</data>
        </response>
    </server>

    <!-- IP检索范围Mysql -->
    <server type="IP_SEARCH_RANGE">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_SEARCH_RANGE">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">IP_SEARCH_RANGE</data>
        </response>
    </server>

    <!-- IP分析Mysql -->
    <server type="IP_ANALYSIS_MYSQL">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_ANALYSIS_MYSQL">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">IP_ANALYSIS_MYSQL</data>
        </response>
    </server>

    <!-- IP分析-关联 Mysql -->
    <server type="IP_ANALYSIS_CONN">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_ANALYSIS_CONN">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">IP_ANALYSIS_CONN</data>
        </response>
    </server>

    <!-- IP分析-PassiveDNS Mysql -->
    <server type="IP_ANALYSIS_PassiveDNS">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_ANALYSIS_PassiveDNS">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">IP_ANALYSIS_PassiveDNS</data>
        </response>
    </server>

    <!-- IP分析-ClientDNS Mysql -->
    <server type="IP_ANALYSIS_ClientDNS">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_ANALYSIS_ClientDNS">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">IP_ANALYSIS_ClientDNS</data>
        </response>
    </server>

    <!-- IP分析-备注新增 Mysql -->
    <server type="IP_ANALYSIS_REMARKSAVE">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_ANALYSIS_REMARKSAVE">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">IP_ANALYSIS_REMARKSAVE</data>
        </response>
    </server>

    <!-- IP分析-备注修改 Mysql -->
    <server type="IP_ANALYSIS_REMARKUPDATE">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_ANALYSIS_REMARKUPDATE">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">IP_ANALYSIS_REMARKUPDATE</data>
        </response>
    </server>

    <!-- IP分析-黑白名单权值修正 Mysql -->
    <server type="IP_ANALYSIS_BWUP">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_ANALYSIS_BWUP">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">IP_ANALYSIS_BWUP</data>
        </response>
    </server>

    <!-- 二期接口 =============================================================================== mysql -->
    <!-- 应用协议列表 -->
    <server type="Search_AppPro">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_AppPro">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_AppPro</data>
        </response>
    </server>

    <!-- 端口信息列表 -->
    <server type="Search_PortInfo">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_PortInfo">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_PortInfo</data>
        </response>
    </server>

    <!-- 单包协议列表 -->
    <server type="Search_SinglePro">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_SinglePro">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_SinglePro</data>
        </response>
    </server>

    <!-- 负载协议列表 -->
    <server type="Search_LoadPro">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_LoadPro">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_LoadPro</data>
        </response>
    </server>

    <!-- 域名名称输入框模糊匹配TOP10 -->
    <server type="Search_DomainName">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_DomainName">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_DomainName</data>
        </response>
    </server>

    <!-- 域名快速检索 -->
    <server type="Search_Domain_Qk">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_Domain_Qk">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_Domain_Qk</data>
        </response>
    </server>

    <!-- 标签名称输入框模糊匹配TOP10 -->
    <server type="Search_TagName">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_TagName">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_TagName</data>
        </response>
    </server>

    <!-- 基于标签检索，标签页签数据 -->
    <server type="Search_TagPageTag">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_TagPageTag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_TagPageTag</data>
        </response>
    </server>
    <!-- 基于标签检索，域名页签数据 -->
    <server type="Search_DomainPageTag">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_DomainPageTag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_DomainPageTag</data>
        </response>
    </server>
    <!-- 基于标签检索，证书页签数据 -->
    <server type="Search_CertPageTag">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_CertPageTag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_CertPageTag</data>
        </response>
    </server>
    <!-- 基于标签检索，IP页签数据 -->
    <server type="Search_IpPageTag">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_IpPageTag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_IpPageTag</data>
        </response>
    </server>
    <!-- 基于标签检索，连接页签数据 -->
    <!-- 获取连接ID，黑白名单，标签名称字段 -->
    <server type="Search_LinkPageTag_My">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_LinkPageTag_My">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_LinkPageTag_My</data>
        </response>
    </server>
    <!-- 获取连接的原始数据 -->
    <server type="Search_LinkPageTag_Es">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_ES_Json.xml" node_name="Search_LinkPageTag_Es">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_LinkPageTag_Es/hits/hits</data>
        </response>
    </server>
    <!-- 基于标签检索，出现频率页签数据 -->
    <server type="Search_CountPageTag">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_CountPageTag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_CountPageTag</data>
        </response>
    </server>

    <!-- 设备查询 -->
    <server type="Search_EqConfig">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_EqConfig">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_EqConfig</data>
        </response>
    </server>
    <!-- 设备查询所有ID -->
    <server type="Search_EqConfig_ID">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Search_EqConfig_ID">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Search_EqConfig_ID</data>
        </response>
    </server>
    <!-- 设备新增-设备名称 -->
    <server type="Save_EqConfig_Device">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Save_EqConfig_Device">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Save_EqConfig_Device</data>
        </response>
    </server>
    <!-- 设备新增-设备Mac -->
    <server type="Save_EqConfig_Mac">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Save_EqConfig_Mac">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Save_EqConfig_Mac</data>
        </response>
    </server>
    <!-- 设备新增-设备IP -->
    <server type="Save_EqConfig_IP">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Save_EqConfig_IP">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Save_EqConfig_IP</data>
        </response>
    </server>
    <!-- 设备删除 -->
    <server type="Delete_EqConfig">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Delete_EqConfig">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Delete_EqConfig</data>
        </response>
    </server>
    <!-- 设备停用 -->
    <server type="Stop_EqConfig">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Stop_EqConfig">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Stop_EqConfig</data>
        </response>
    </server>

    <!-- 流量态势历史数据 -->
    <server type="FullFlow_His">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="FullFlow_His">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">FullFlow_His</data>
        </response>
    </server>
    <!-- 流量态势启动时间 -->
    <server type="FullFlow_BeginTime">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="FullFlow_BeginTime">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">FullFlow_BeginTime</data>
        </response>
    </server>

    <!-- 端口分析=================================================begin-->
    <!-- 根据端口跳转到端口分析页面 -基本信息-备注-黑白名单-标签id -->
    <server type="Port_Analysis">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Port_Analysis">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Port_Analysis</data>
        </response>
    </server>
    <!-- 根据端口跳转到端口分析页面 - 基本信息 - 备注新增 -->
    <server type="Port_Analysis_REMARKSAVE">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Port_Analysis_REMARKSAVE">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Port_Analysis_REMARKSAVE</data>
        </response>
    </server>
    <!-- 根据端口跳转到端口分析页面 - 基本信息 - 备注修改 -->
    <server type="Port_Analysis_REMARKUPDATE">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Port_Analysis_REMARKUPDATE">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Port_Analysis_REMARKUPDATE</data>
        </response>
    </server>
    <!-- 根据端口跳转到端口分析页面 - 基本信息 - 黑白修改 -->
    <server type="Port_Analysis_BWUPDATE">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Port_Analysis_BWUPDATE">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Port_Analysis_BWUPDATE</data>
        </response>
    </server>
    <!-- 服务页签 -->
    <server type="Port_Analysis_ServerTag">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Port_Analysis_ServerTag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Port_Analysis_ServerTag</data>
        </response>
    </server>
    <!-- 通信页签 -->
    <!-- 开放、访问服务 -->
    <server type="Port_ANALYSIS_AO_BAK">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="Port_ANALYSIS_AO">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 无用-->
            <!--<data resp_key="data" type="json" resp_type="json">Port_ANALYSIS_AO/aggregations/server_IP/buckets</data>-->
        </response>
        <response_list>
            <BList>1</BList>
            <valuelist>
                <src_value>
                    <type>1</type>
                    <name>port_list</name>
                    <LPath>Port_ANALYSIS_AO/aggregations/client_IP/buckets</LPath>
                    <TZhu>1</TZhu>
                    <KeyPath>key</KeyPath>
                </src_value>
                <value>
                    <src_name>port_list</src_name>
                    <key>src_ip</key>
                    <path>key</path>
                    <badd>2</badd>
                    <list>1</list>
                    <value_type>2</value_type>
                </value>
                <value>
                    <src_name>port_list</src_name>
                    <key></key>
                    <path></path>
                    <badd>2</badd>
                    <list>1</list>
                    <src_value>
                        <type>1</type>
                        <name>serverIP_aggs</name>
                        <LPath>server_IP/buckets</LPath>
                        <TZhu>1</TZhu>
                        <KeyPath>key</KeyPath>
                    </src_value>
                    <value>
                        <src_name>serverIP_aggs</src_name>
                        <key>dst_ip</key>
                        <path>key</path>
                        <badd>2</badd>
                        <list>1</list>
                        <value_type>2</value_type>
                    </value>
                    <value>
                        <src_name>serverIP_aggs</src_name>
                        <key>appinfo</key>
                        <path></path>
                        <badd>2</badd>
                        <list>1</list>
                        <listValue>1</listValue>
                        <src_value>
                            <type>1</type>
                            <name>app_aggs</name>
                            <LPath>app/buckets</LPath>
                            <TZhu>1</TZhu>
                            <KeyPath>key</KeyPath>
                        </src_value>
                        <value>
                            <src_name>app_aggs</src_name>
                            <key>appid</key>
                            <path>key</path>
                            <badd>2</badd>
                            <list>1</list>
                            <listValue>1</listValue>
                        </value>
                        <value>
                            <src_name>app_aggs</src_name>
                            <key>connect_num</key>
                            <path>doc_count</path>
                            <badd>2</badd>
                            <list>1</list>
                        </value>
                        <value>
                            <src_name>app_aggs</src_name>
                            <key>last_found_time</key>
                            <path>last_found_time/value</path>
                            <badd>2</badd>
                            <list>1</list>
                        </value>
                        <value>
                            <src_name>app_aggs</src_name>
                            <key>first_found_time</key>
                            <path>first_found_time/value</path>
                            <badd>2</badd>
                            <list>1</list>
                        </value>
                    </value>
                </value>
            </valuelist>
        </response_list>
    </server>
    <!-- 端口分析=================================================end-->

    <!-- 应用分析=================================================begin-->
    <!-- 根据应用id跳转到端口分析页面 -基本信息 -->
    <server type="App_Analysis">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="App_Analysis">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">App_Analysis</data>
        </response>
    </server>
    <!-- 根据应用id跳转到应用分析页面 - 基本信息 - 备注新增 -->
    <server type="App_Analysis_REMARKSAVE">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="App_Analysis_REMARKSAVE">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">App_Analysis_REMARKSAVE</data>
        </response>
    </server>
    <!-- 根据应用id跳转到应用分析页面 - 基本信息 - 备注修改 -->
    <server type="App_Analysis_REMARKUPDATE">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="App_Analysis_REMARKUPDATE">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">App_Analysis_REMARKUPDATE</data>
        </response>
    </server>
    <!-- 根据应用id跳转到应用分析页面 - 基本信息 - 黑白权值修改 -->
    <server type="App_Analysis_BWUPDATE">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="App_Analysis_BWUPDATE">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">App_Analysis_BWUPDATE</data>
        </response>
    </server>
    <!-- 服务页签 -->
    <server type="App_Analysis_ServerTag">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="App_Analysis_ServerTag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">App_Analysis_ServerTag</data>
        </response>
    </server>
    <!-- 通信页签 -->
    <!-- 开放、访问服务 -->
    <server type="App_ANALYSIS_AO_BAK">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="App_ANALYSIS_AO">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 无用-->
            <!--<data resp_key="data" type="json" resp_type="json">App_ANALYSIS_AO/aggregations/client_IP/buckets</data>-->
        </response>
        <response_list>
            <BList>1</BList>
            <valuelist>
                <src_value>
                    <type>1</type>
                    <name>app_list</name>
                    <LPath>App_ANALYSIS_AO/aggregations/client_IP/buckets</LPath>
                    <TZhu>1</TZhu>
                    <KeyPath>key</KeyPath>
                </src_value>
                <value>
                    <src_name>app_list</src_name>
                    <key>src_ip</key>
                    <path>key</path>
                    <badd>2</badd>
                    <list>1</list>
                    <value_type>2</value_type>
                </value>
                <value>
                    <src_name>app_list</src_name>
                    <key></key>
                    <path></path>
                    <badd>2</badd>
                    <list>1</list>
                    <src_value>
                        <type>1</type>
                        <name>serverIP_aggs</name>
                        <LPath>server_IP/buckets</LPath>
                        <TZhu>1</TZhu>
                        <KeyPath>key</KeyPath>
                    </src_value>
                    <value>
                        <src_name>serverIP_aggs</src_name>
                        <key>dst_ip</key>
                        <path>key</path>
                        <badd>2</badd>
                        <list>1</list>
                        <value_type>2</value_type>
                    </value>
                    <value>
                        <src_name>serverIP_aggs</src_name>
                        <key>serverPort</key>
                        <path></path>
                        <badd>2</badd>
                        <list>1</list>
                        <listValue>1</listValue>
                        <src_value>
                            <type>1</type>
                            <name>server_port_aggs</name>
                            <LPath>server_Port/buckets</LPath>
                            <TZhu>1</TZhu>
                            <KeyPath>key</KeyPath>
                        </src_value>
                        <value>
                            <src_name>server_port_aggs</src_name>
                            <key>dst_port</key>
                            <path>key</path>
                            <badd>2</badd>
                            <list>1</list>
                            <listValue>1</listValue>
                        </value>
                        <value>
                            <src_name>server_port_aggs</src_name>
                            <key>connect_num</key>
                            <path>doc_count</path>
                            <badd>2</badd>
                            <list>1</list>
                        </value>
                        <value>
                            <src_name>server_port_aggs</src_name>
                            <key>last_found_time</key>
                            <path>last_found_time/value</path>
                            <badd>2</badd>
                            <list>1</list>
                        </value>
                        <value>
                            <src_name>server_port_aggs</src_name>
                            <key>first_found_time</key>
                            <path>first_found_time/value</path>
                            <badd>2</badd>
                            <list>1</list>
                        </value>
                    </value>
                </value>
            </valuelist>
        </response_list>
    </server>
    <!-- 应用分析=================================================end-->

    <!-- 域名分析=================================================begin-->
    <!-- 根据域名跳转到域名分析页面 -基本信息-备注-黑白名单-标签List -->
    <server type="Domain_Analysis">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Domain_Analysis">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">Domain_Analysis</data>
        </response>
    </server>
    <!-- 域名分析-备注新增 Mysql -->
    <server type="Domain_Analysis_REMARKSAVE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Domain_Analysis_REMARKSAVE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">Domain_Analysis_REMARKSAVE</data>
        </response>
    </server>
    <!-- 域名分析-备注修改 Mysql -->
    <server type="Domain_Analysis_REMARKUPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Domain_Analysis_REMARKUPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">Domain_Analysis_REMARKUPDATE</data>
        </response>
    </server>
    <!-- 域名分析-黑白修改 Mysql -->
    <server type="Domain_Analysis_BWUPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Domain_Analysis_BWUPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">Domain_Analysis_BWUPDATE</data>
        </response>
    </server>
    <!-- 域名分析-PassiveDNS页签 Mysql -->
    <server type="Domain_Analysis_PDNS">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Domain_Analysis_PDNS">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">Domain_Analysis_PDNS</data>
        </response>
    </server>
    <!-- 域名分析-ClientDNS页签 Mysql -->
    <server type="Domain_Analysis_CDNS">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Domain_Analysis_CDNS">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">Domain_Analysis_CDNS</data>
        </response>
    </server>
    <!-- 域名分析-关联页签 Mysql -->
    <server type="Domain_Analysis_Link">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Domain_Analysis_Link">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">Domain_Analysis_Link</data>
        </response>
    </server>
    <!-- 域名分析=================================================end-->

    <!-- 查询用户名密码 -->
    <server type="Query_User">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Query_User">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Query_User</data>
        </response>
    </server>

    <!-- 端口应用检索 -->
    <server type="Query_Port_App">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Query_Port_App">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Query_Port_App</data>
        </response>
    </server>

    <!-- 加载所有应用字典 -->
    <server type="Query_App_Dictionary">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Query_App_Dictionary">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Query_App_Dictionary</data>
        </response>
    </server>

    <!-- 通过标签id转译标签通用方法 -->
    <server type="Tag_Base_Text">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Tag_Base_Text">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Tag_Base_Text</data>
        </response>
    </server>

    <!-- IP分析确定黑名单接口 -->
    <server type="IP_ANALYSIS_SaveTag">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_ANALYSIS_SaveTag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">IP_ANALYSIS_SaveTag</data>
        </response>
    </server>

    <!-- 端口分析确定黑名单接口 -->
    <server type="Port_ANALYSIS_SaveTag">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Port_ANALYSIS_SaveTag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Port_ANALYSIS_SaveTag</data>
        </response>
    </server>

    <!-- 应用分析确定黑名单接口 -->
    <server type="App_ANALYSIS_SaveTag">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="App_ANALYSIS_SaveTag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">App_ANALYSIS_SaveTag</data>
        </response>
    </server>

    <!-- 连接分析确定黑名单接口 -->
    <server type="FullFlow_SaveTag">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="FullFlow_SaveTag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">FullFlow_SaveTag</data>
        </response>
    </server>

    <!-- 域名分析确定黑名单接口 -->
    <server type="Domain_ANALYSIS_SaveTag">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Domain_ANALYSIS_SaveTag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Domain_ANALYSIS_SaveTag</data>
        </response>
    </server>

    <!-- 规则探针同步脚本 -->
    <server type="Rule_Sync_Script">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Rule_Sync_Script">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Rule_Sync_Script</data>
        </response>
    </server>

    <!--流量分析-流量态势 BEGIN-->
    <server type="App_FlowSituation_Simple">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="App_FlowSituation_Simple">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 无用-->
            <!--<data resp_key="data" type="json" resp_type="json">App_FlowSituation_Simple/hits/hits</data>-->
        </response>
        <response_list> <!-- 存储数据 -->
            <BList>1</BList><!-- 是否为 list  1 为 list  2 为 list   -->
            <valuelist>
                <src_value>
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>flow_list</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>App_FlowSituation_Simple/hits/hits</LPath> <!-- 源数据path  -->
                    <TZhu>1</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath></KeyPath> <!-- key  值路径-->
                </src_value>
                <value>
                    <src_name>flow_list</src_name> <!-- 取值的 src_value -->
                    <key>src_ip</key> <!-- resp key值 存储名称 -->
                    <path>_source/sIp</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>2</value_type>
                </value>
                <value>
                    <src_name>flow_list</src_name> <!-- 取值的 src_value -->
                    <key>app_id</key> <!-- resp key值 存储名称 -->
                    <path>_source/AppId</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <listValue>1</listValue>
                </value>
                <value>
                    <src_name>flow_list</src_name> <!-- 取值的 src_value -->
                    <key>dst_port</key> <!-- resp key值 存储名称 -->
                    <path>_source/dPort</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <listValue>1</listValue>
                </value>
                <value>
                    <src_name>flow_list</src_name> <!-- 取值的 src_value -->
                    <key>dst_ip</key> <!-- resp key值 存储名称 -->
                    <path>_source/dIp</path><!-- 取值路径 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                    <value_type>2</value_type>
                </value>
            </valuelist>
        </response_list>
    </server>
    <!--流量分析-连接 BEGIN-->
    <server type="App_Connect_Simple">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="App_Connect_Simple">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 无用-->
            <data resp_key="data" type="json" resp_type="json">App_Connect_Simple/hits/hits</data>
        </response>
    </server>
    <!--流量分析 END-->

    <!-- 证书黑白名单配置- 查询 -->
    <server type="Query_Cert_BW">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Query_Cert_BW">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Query_Cert_BW</data>
        </response>
    </server>

    <!-- 证书黑白名单配置- 查询证书sha1是否存在 -->
    <server type="Query_Cert_Sha1">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Query_Cert_Sha1">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Query_Cert_Sha1</data>
        </response>
    </server>

    <!-- 证书黑白名单配置- 新增 -->
    <server type="Save_Cert_BW">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Save_Cert_BW">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Save_Cert_BW</data>
        </response>
    </server>

    <!-- 证书黑白名单配置- 修改 -->
    <server type="Update_Cert_BW">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Update_Cert_BW">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Update_Cert_BW</data>
        </response>
    </server>

    <!-- 证书黑白名单配置- 删除 -->
    <server type="Delete_Cert_BW">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Delete_Cert_BW">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Delete_Cert_BW</data>
        </response>
    </server>

    <!-- 证书检索- 所有者模糊匹配 -->
    <server type="Cert_Owner">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Cert_Owner">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Cert_Owner</data>
        </response>
    </server>

    <!-- 证书检索- 域名模糊匹配 -->
    <server type="Cert_Domain">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Cert_Domain">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Cert_Domain</data>
        </response>
    </server>

    <!-- 证书检索- 查询 -->
    <server type="Cert_Search_Qk">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Cert_Search_Qk">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Cert_Search_Qk</data>
        </response>
    </server>

    <!-- 证书分析 - 基本信息+详细解析记录 -->
    <server type="Cert_BasicInfo">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Cert_BasicInfo">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Cert_BasicInfo</data>
        </response>
    </server>
    <!-- 证书分析 - 基本信息 - 确认黑名单 -->
    <server type="Cert_SaveTag">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Cert_SaveTag">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Cert_SaveTag</data>
        </response>
    </server>
    <!-- 证书分析 - 基本信息 - 黑名单权值修改 -->
    <server type="Cert_UpList_Update">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Cert_UpList_Update">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Cert_UpList_Update</data>
        </response>
    </server>
    <!-- 证书分析 - 基本信息 - 备注 -->
    <server type="Cert_Remarks_Save">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Cert_Remarks_Save">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Cert_Remarks_Save</data>
        </response>
    </server>
    <server type="Cert_Remarks_Update">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Cert_Remarks_Save">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Cert_Remarks_Update</data>
        </response>
    </server>

    <!-- 证书分析 - 证书链 -->
    <server type="Cert_Link">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Cert_Link">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Cert_Link</data>
        </response>
    </server>

    <!-- 证书分析 - 关联信息 -->
    <server type="Cert_LinkInfo">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Cert_LinkInfo">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Cert_LinkInfo</data>
        </response>
    </server>

    <!-- 证书分析 - PassiveCert -->
    <server type="Cert_Pcert">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Cert_Pcert">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Cert_Pcert</data>
        </response>
    </server>

    <!-- 证书分析 - ClientCert -->
    <server type="Cert_Ccert">
        <arr_conf><!--取参数数据替换-->
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="Cert_Ccert">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">Cert_Ccert</data>
        </response>
    </server>

    <!-- 标签操作接口 -->
    <!-- TAG_INFO -->
    <server type="TAG_DIC_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>

            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_NEBUAL_INSERT">NebulaHandle</one_do>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_DIC_INSERT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_DIC_INSERT</data>
        </response>
    </server>
    <server type="TAG_LINK_NEBUAL_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_LINK_NEBUAL_INSERT">NebulaHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_LINK_NEBUAL_INSERT/data</data>
        </response>
    </server>
    <server type="TAG_DIC_DELETE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_DIC_DELETE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_DIC_DELETE</data>
        </response>
    </server>

    <server type="TAG_DIC_UPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_DIC_UPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_DIC_UPDATE</data>
        </response>
    </server>
    <server type="TAG_DIC_SELECT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_DIC_SELECT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_DIC_SELECT</data>
        </response>
    </server>
    <!-- ip_tag -->
    <server type="TAG_IP_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_IP_INSERT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_IP_INSERT</data>
        </response>
    </server>
    <server type="TAG_IP_DELETE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_IP_DELETE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_IP_DELETE</data>
        </response>
    </server>
    <server type="TAG_IP_UPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_IP_UPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_IP_UPDATE</data>
        </response>
    </server>
    <server type="TAG_IP_SELECT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_IP_SELECT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_IP_SELECT</data>
        </response>
    </server>
    <server type="TAG_FINGER_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_FINGER_INSERT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_FINGER_INSERT</data>
        </response>
    </server>
    <server type="TAG_FINGER_UPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_FINGER_UPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_FINGER_UPDATE</data>
        </response>
    </server>
    <server type="TAG_FINGER_DELETE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_FINGER_DELETE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_FINGER_DELETE</data>
        </response>
    </server>
    <!-- PORT_TAG -->
    <server type="TAG_PORT_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_PORT_INSERT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_PORT_INSERT</data>
        </response>
    </server>
    <server type="TAG_PORT_DELETE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_PORT_DELETE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_PORT_DELETE</data>
        </response>
    </server>
    <server type="TAG_PORT_UPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_PORT_UPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_PORT_UPDATE</data>
        </response>
    </server>
    <server type="TAG_PORT_SELECT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_PORT_SELECT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_PORT_SELECT</data>
        </response>
    </server>
    <!-- APP_TAG -->
    <server type="TAG_APP_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_APP_INSERT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_APP_INSERT</data>
        </response>
    </server>
    <server type="TAG_APP_DELETE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_APP_DELETE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_APP_DELETE</data>
        </response>
    </server>
    <server type="TAG_APP_UPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_APP_UPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_APP_UPDATE</data>
        </response>
    </server>
    <server type="TAG_APP_SELECT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_APP_SELECT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_APP_SELECT</data>
        </response>
    </server>
    <!-- SESSION_TAG -->
    <server type="TAG_SESSION_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_SESSION_INSERT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_SESSION_INSERT</data>
        </response>
    </server>
    <server type="TAG_ES_SESSION_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_ES_SESSION_INSERT">ESHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_ES_SESSION_INSERT/total</data>
        </response>
    </server>
    <server type="DAN_TAG_ES_SESSION_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="DAN_TAG_ES_SESSION_INSERT">ESHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">DAN_TAG_ES_SESSION_INSERT/total</data>
        </response>
    </server>
    <server type="DEL_TAG_ES_SESSION">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="DEL_TAG_ES_SESSION">ESHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">DEL_TAG_ES_SESSION/total</data>
        </response>
    </server>
    <server type="TAG_SESSION_DELETE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_SESSION_DELETE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_SESSION_DELETE</data>
        </response>
    </server>
    <server type="TAG_SESSION_UPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_SESSION_UPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_SESSION_UPDATE</data>
        </response>
    </server>
    <server type="TAG_SESSION_SELECT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_SESSION_SELECT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_SESSION_SELECT</data>
        </response>
    </server>
    <!-- CERT_TAG -->
    <server type="TAG_CERT_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_CERT_INSERT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_CERT_INSERT</data>
        </response>
    </server>
    <server type="TAG_CERT_DELETE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_CERT_DELETE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_CERT_DELETE</data>
        </response>
    </server>
    <server type="TAG_CERT_UPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_CERT_UPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_CERT_UPDATE</data>
        </response>
    </server>
    <server type="TAG_CERT_SELECT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_CERT_SELECT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_CERT_SELECT</data>
        </response>
    </server>
    <!-- DOMAIN_TAG -->
    <server type="TAG_DOMAIN_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_DOMAIN_INSERT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_DOMAIN_INSERT</data>
        </response>
    </server>
    <server type="TAG_DOMAIN_DELETE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_DOMAIN_DELETE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_DOMAIN_DELETE</data>
        </response>
    </server>
    <server type="TAG_DOMAIN_UPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_DOMAIN_UPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_DOMAIN_UPDATE</data>
        </response>
    </server>
    <server type="TAG_DOMAIN_SELECT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="TAG_DOMAIN_SELECT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">TAG_DOMAIN_SELECT</data>
        </response>
    </server>
    <server type="ADD_ALARM">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="ADD_ALARM">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">ADD_ALARM</data>
        </response>
    </server>
    <server type="ADD_ALARM_TARGET">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="ADD_ALARM_TARGET">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">ADD_ALARM_TARGET</data>
        </response>
    </server>
    <server type="ADD_ALARM_EXTEND">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="ADD_ALARM_EXTEND">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">ADD_ALARM_EXTEND</data>
        </response>
    </server>
    <server type="DELETE_ALARM">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="DELETE_ALARM">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">DELETE_ALARM</data>
        </response>
    </server>
    <server type="SELECT_ALARM">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="SELECT_ALARM">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">SELECT_ALARM</data>
        </response>
    </server>
    <server type="ADD_INFO">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="ADD_INFO">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">ADD_INFO</data>
        </response>
    </server>
    <server type="DELETE_INFO">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="DELETE_INFO">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">DELETE_INFO</data>
        </response>
    </server>
    <server type="SELECT_INFO">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="SELECT_INFO">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">SELECT_INFO</data>
        </response>
    </server>
    <server type="LIST_IP_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_IP_INSERT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_IP_INSERT</data>
        </response>
    </server>
    <server type="LIST_IP_DELETE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_IP_DELETE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_IP_DELETE</data>
        </response>
    </server>
    <server type="LIST_IP_UPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_IP_UPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_IP_UPDATE</data>
        </response>
    </server>
    <server type="LIST_IP_SELECT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_IP_SELECT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_IP_SELECT</data>
        </response>
    </server>
    <server type="LIST_PORT_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_PORT_INSERT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_PORT_INSERT</data>
        </response>
    </server>
    <server type="LIST_PORT_DELETE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_PORT_DELETE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_PORT_DELETE</data>
        </response>
    </server>
    <server type="LIST_PORT_UPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_PORT_UPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_PORT_UPDATE</data>
        </response>
    </server>
    <server type="LIST_PORT_SELECT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_PORT_SELECT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_PORT_SELECT</data>
        </response>
    </server>
    <server type="LIST_APP_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_APP_INSERT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_APP_INSERT</data>
        </response>
    </server>
    <server type="LIST_APP_DELETE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_APP_DELETE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_APP_DELETE</data>
        </response>
    </server>
    <server type="LIST_APP_UPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_APP_UPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_APP_UPDATE</data>
        </response>
    </server>
    <server type="LIST_APP_SELECT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_APP_SELECT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_APP_SELECT</data>
        </response>
    </server>
    <server type="LIST_SESSION_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_SESSION_INSERT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_SESSION_INSERT</data>
        </response>
    </server>
    <server type="LIST_SESSION_DELETE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_SESSION_DELETE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_SESSION_DELETE</data>
        </response>
    </server>
    <server type="LIST_SESSION_UPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_SESSION_UPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_SESSION_UPDATE</data>
        </response>
    </server>
    <server type="LIST_SESSION_SELECT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_SESSION_SELECT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_SESSION_SELECT</data>
        </response>
    </server>
    <server type="LIST_CERT_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_CERT_INSERT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_CERT_INSERT</data>
        </response>
    </server>
    <server type="LIST_CERT_DELETE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_CERT_DELETE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_CERT_DELETE</data>
        </response>
    </server>
    <server type="LIST_CERT_UPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_CERT_UPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_CERT_UPDATE</data>
        </response>
    </server>
    <server type="LIST_CERT_SELECT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_CERT_SELECT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_CERT_SELECT</data>
        </response>
    </server>
    <server type="LIST_DOMAIN_INSERT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_DOMAIN_INSERT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_DOMAIN_INSERT</data>
        </response>
    </server>
    <server type="LIST_DOMAIN_DELETE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_DOMAIN_DELETE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_DOMAIN_DELETE</data>
        </response>
    </server>
    <server type="LIST_DOMAIN_UPDATE">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_DOMAIN_UPDATE">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_DOMAIN_UPDATE</data>
        </response>
    </server>
    <server type="LIST_DOMAIN_SELECT">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="LIST_DOMAIN_SELECT">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">LIST_DOMAIN_SELECT</data>
        </response>
    </server>

    <!-- 目标校验 -->
    <server type="CHECK_IP_ANALYSIS">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="CHECK_IP_ANALYSIS">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">CHECK_IP_ANALYSIS</data>
        </response>
    </server>
    <server type="CHECK_PORT_ANALYSIS">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="CHECK_PORT_ANALYSIS">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">CHECK_PORT_ANALYSIS</data>
        </response>
    </server>
    <server type="CHECK_APP_ANALYSIS">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="CHECK_APP_ANALYSIS">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">CHECK_APP_ANALYSIS</data>
        </response>
    </server>
    <server type="CHECK_DOMAIN_ANALYSIS">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="CHECK_DOMAIN_ANALYSIS">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">CHECK_DOMAIN_ANALYSIS</data>
        </response>
    </server>
    <server type="CHECK_CERT_ANALYSIS">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="CHECK_CERT_ANALYSIS">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">CHECK_CERT_ANALYSIS</data>
        </response>
    </server>
    <server type="CHECK_SESSIONID_ANALYSIS">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_ES_Json.xml" node_name="CHECK_SESSIONID_ANALYSIS">ESHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="int">CHECK_SESSIONID_ANALYSIS/hits/total</data>
        </response>
        <response_list>
            <BList>2</BList>
            <valuelist>
                <src_value>
                    <type>1</type>
                    <name>full_hits</name>
                    <LPath>CHECK_SESSIONID_ANALYSIS/hits</LPath>
                    <TZhu>2</TZhu>
                    <KeyPath>total</KeyPath>
                </src_value>
                <value>
                    <src_name>full_hits</src_name>
                    <path>total</path>
                    <key>count</key>
                    <badd>2</badd>
                    <list>1</list>
                </value>
            </valuelist>
        </response_list>
    </server>
    <server type="CHECK_MAC_ANALYSIS">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="CHECK_MAC_ANALYSIS">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">CHECK_MAC_ANALYSIS</data>
        </response>
    </server>

    <!-- 系统信息 -->
    <server type="SYSTEM_INFO">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="SYSTEM_INFO">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">SYSTEM_INFO</data>
        </response>
    </server>

    <!-- 会话分析告警页签 -->
    <server type="SESSION_ALARM">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="SESSION_ALARM">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">SESSION_ALARM</data>
        </response>
    </server>

    <!-- IP分析告警页签 -->
    <server type="IP_ALARM">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_ALARM">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">IP_ALARM</data>
        </response>
    </server>

    <!-- PORT分析告警页签 -->
    <server type="PORT_ALARM">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="PORT_ALARM">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">PORT_ALARM</data>
        </response>
    </server>

    <!-- APP分析告警页签 -->
    <server type="APP_ALARM">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="APP_ALARM">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">APP_ALARM</data>
        </response>
    </server>

    <!-- DOMAIN分析告警页签 -->
    <server type="DOMAIN_ALARM">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="DOMAIN_ALARM">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">DOMAIN_ALARM</data>
        </response>
    </server>

    <!-- CERT分析告警页签 -->
    <server type="CERT_ALARM">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_Json.xml" node_name="CERT_ALARM">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">CERT_ALARM</data>
        </response>
    </server>

    <!-- ES增删改查接口 -->
    <server type="ES_CURD">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_ES_Json.xml" node_name="ES_CURD">ESHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">ES_CURD</data>
        </response>
    </server>

    <!-- 证书跳转到连接列表 -->
    <server type="CERT_TO_LINK">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_ES_Json.xml" node_name="CERT_TO_LINK">ESHandle</one_do>
        </handle>
        <response>
            <data resp_key="count" type="json" resp_type="int">CERT_TO_LINK/hits/total</data>
            <data resp_key="data" type="json" resp_type="json">CERT_TO_LINK/hits/hits</data>
        </response>
        <response_list> <!-- 存储数据 -->
            <BList>2</BList><!-- 是否为 list  1 为 list  2 为 list   -->
            <valuelist>
                <src_value> <!-- 数据来源定义  取值   - -->
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>full_hits</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>CERT_TO_LINK/hits</LPath> <!-- 源数据path  -->
                    <TZhu>2</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>total</KeyPath> <!-- key  值路径-->
                </src_value>
                <value>
                    <src_name>full_hits</src_name> <!-- 取值的 src_value -->
                    <path>total</path>
                    <key>count</key> <!-- resp key值 存储名称 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                </value>
            </valuelist>
        </response_list>
    </server>

    <!-- 域名跳转到连接列表 -->
    <server type="DOMAIN_TO_LINK">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_ES_Json.xml" node_name="DOMAIN_TO_LINK">ESHandle</one_do>
        </handle>
        <response>
            <data resp_key="count" type="json" resp_type="int">DOMAIN_TO_LINK/hits/total</data>
            <data resp_key="data" type="json" resp_type="json">DOMAIN_TO_LINK/hits/hits</data>
        </response>
        <response_list> <!-- 存储数据 -->
            <BList>2</BList><!-- 是否为 list  1 为 list  2 为 list   -->
            <valuelist>
                <src_value> <!-- 数据来源定义  取值   - -->
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>full_hits</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>DOMAIN_TO_LINK/hits</LPath> <!-- 源数据path  -->
                    <TZhu>2</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>total</KeyPath> <!-- key  值路径-->
                </src_value>
                <value>
                    <src_name>full_hits</src_name> <!-- 取值的 src_value -->
                    <path>total</path>
                    <key>count</key> <!-- resp key值 存储名称 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                </value>
            </valuelist>
        </response_list>
    </server>

    <server type="IP_ANALYSIS_OS">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_ANALYSIS_OS">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">IP_ANALYSIS_OS</data>
        </response>
    </server>
    <server type="IP_ANALYSIS_AS">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_MY_Json.xml" node_name="IP_ANALYSIS_AS">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">IP_ANALYSIS_AS</data>
        </response>
    </server>
    <server type="Port_ANALYSIS_AO">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_MY_Json.xml" node_name="Port_ANALYSIS_AO">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">Port_ANALYSIS_AO</data>
        </response>
    </server>
    <server type="App_ANALYSIS_AO">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_MY_Json.xml" node_name="App_ANALYSIS_AO">MySqlHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json">App_ANALYSIS_AO</data>
        </response>
    </server>
    <!-- 端口应用跳转到连接列表 -->
    <server type="PORT_APP_TO_LINK">
        <arr_conf><!--取参数  数据替换-->
            <arr key="id">type</arr>
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_ES_Json.xml" node_name="PORT_APP_TO_LINK">ESHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="count" type="json" resp_type="int">PORT_APP_TO_LINK/hits/total</data>
            <data resp_key="data" type="json" resp_type="json">PORT_APP_TO_LINK/hits/hits</data>
        </response>
        <response_list> <!-- 存储数据 -->
            <BList>2</BList><!-- 是否为 list  1 为 list  2 为 list   -->
            <valuelist>
                <src_value> <!-- 数据来源定义  取值   - -->
                    <type>1</type><!-- 定义 1 为主LIST 2 为 fuz  -->
                    <name>full_hits</name> <!-- 源数据类型 对应的动作类型 -->
                    <LPath>PORT_APP_TO_LINK/hits</LPath> <!-- 源数据path  -->
                    <TZhu>2</TZhu><!-- 是否需要转换  1 标识需要转换 0 标识不需要抓换-->
                    <KeyPath>total</KeyPath> <!-- key  值路径-->
                </src_value>
                <value>
                    <src_name>full_hits</src_name> <!-- 取值的 src_value -->
                    <path>total</path>
                    <key>count</key> <!-- resp key值 存储名称 -->
                    <badd>2</badd><!-- 取值路径  1 是累加  2  是 -->
                    <list>1</list><!-- 取值路径  1 是累加  2  是 -->
                </value>
            </valuelist>
        </response_list>
    </server>

    <server type="GetAlarmId">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="GetAlarmId">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">GetAlarmId</data>
        </response>
    </server>
    <server type="GetTagId">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="GetTagId">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">GetTagId</data>
        </response>
    </server>
    <server type="GetNetPortInfo">
        <arr_conf><!--取参数  数据替换-->
        </arr_conf>
        <!-- 数据处理配置 -->
        <handle> <!-- 每个都输出结构都是在    执行的组件类型 及对应的配置文件 -->
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="GetNetPortInfo">MySqlHandle</one_do>
        </handle>
        <response> <!--  key 路径 ，type 原始数据类型  resp_type 回填类型 , 数据回填 -->
            <data resp_key="data" type="json" resp_type="json">GetNetPortInfo</data>
        </response>
    </server>
    <server type="ALARM_INSERT_ES">
        <arr_conf>
        </arr_conf>
        <handle>
            <one_do config="resources/Th_MY_TAG_Json.xml" node_name="ALARM_INSERT_ES">ESHandle</one_do>
        </handle>
        <response>
            <data resp_key="data" type="json" resp_type="json" func="error_func">ALARM_INSERT_ES/errors</data>
        </response>
    </server>
</config>

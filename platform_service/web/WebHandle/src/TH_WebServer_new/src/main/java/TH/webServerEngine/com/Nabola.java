package TH.webServerEngine.com;
import com.alibaba.fastjson.JSONArray;
import com.vesoft.nebula.Date;
import com.vesoft.nebula.ErrorCode;
import com.vesoft.nebula.Value;


import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.data.CASignedSSLParam;
import com.vesoft.nebula.client.graph.data.HostAddress;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.data.SelfSignedSSLParam;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import java.io.UnsupportedEncodingException;
//import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.json.JSONObject;
//import sun.security.krb5.internal.HostAddress;

public class Nabola {
    private  NebulaPool pool = new NebulaPool();
    private  Session session  = null ;
    private  int bInit = 0;
    private  String IP = "127.0.0.1";
    private  int port  = 9669;
    private  String UserName ="root";
    private  String  Passwd = "nebula";
    private  String SpaceName ="gs_analysis_graph";
    //private  NebulaPool pool = new NebulaPool();
    private  NebulaPoolConfig nebulaPoolConfig = new NebulaPoolConfig();
    public   Nabola () {
        init();
    }
    private void init() {
        try {
            // 读取 配置文件
            JSONObject jsonObj = new JSONObject(JsonUtils.readJsonFile("resources/config.json"));
            if (jsonObj.has("NebualIP"))
            {
                IP=jsonObj.getString("NebualIP");
            }
            if (jsonObj.has("NebualPort")) {
                port=jsonObj.getInt("NebualPort");
            }
            if (jsonObj.has("NebualUser")) {
                UserName = jsonObj.getString("NebualUser");
            }
            if (jsonObj.has("NebualPasswd")) {
                Passwd = jsonObj.getString("NebualPasswd");
            }
            if (jsonObj.has("NebualSpace")) {
                SpaceName = jsonObj.getString("NebualSpace");
            }

            if (bInit == 1) {
                try {

                    nebulaPoolConfig.setMaxConnSize(100);
                    List<HostAddress> addresses = Arrays.asList(new HostAddress(IP, port));
                    Boolean initResult = pool.init(addresses, nebulaPoolConfig);
                    if (!initResult) {
                        System.out.println("pool init failed.");
                        return;
                    }


                } catch(Exception e){
                    e.printStackTrace();
                }

            }

        }catch (Exception e) {
            e.printStackTrace();
        }
    }
    private  List<Object> RestResult(ResultSet resultSet) throws UnsupportedEncodingException {
        List<String> colNames = resultSet.keys();
        for (String name : colNames) {
            System.out.printf("%15s |", name);
        }
        List<Object> Resp = new ArrayList<Object>();

        System.out.println();
        for (int i = 0; i < resultSet.rowsSize(); i++) {
            ResultSet.Record record = resultSet.rowValues(i);
            Map<String ,Object> obj = new HashMap<String ,Object>();
            int  num  = 0 ;
            for (ValueWrapper value : record.values()) {
                if (value.isLong()) {
                    System.out.printf("%15s |", value.asLong());
                    obj.put(colNames.get(num),value.asLong());
                }
                if (value.isBoolean()) {
                    System.out.printf("%15s |", value.asBoolean());
                    obj.put(colNames.get(num),value.asBoolean());
                }
                if (value.isDouble()) {
                    System.out.printf("%15s |", value.asDouble());
                    obj.put(colNames.get(num),value.asDouble());
                }
                if (value.isString()) {
                    System.out.printf("%15s |", value.asString());
                    obj.put(colNames.get(num),value.asString());
                }
                if (value.isTime()) {
                    System.out.printf("%15s |", value.asTime());
                    obj.put(colNames.get(num),value.isTime());
                }
                if (value.isDate()) {
                    System.out.printf("%15s |", value.asDate());
                    obj.put(colNames.get(num),value.asDate());
                }
                if (value.isDateTime()) {
                    System.out.printf("%15s |", value.asDateTime());
                    obj.put(colNames.get(num),value.asDateTime());

                }
                if (value.isVertex()) {
                    System.out.printf("%15s |", value.asNode());
                    obj.put(colNames.get(num),value.asNode());
                }
                if (value.isEdge()) {
                    System.out.printf("%15s |", value.asRelationship());
                    obj.put(colNames.get(num),value.asRelationship());
                }
                if (value.isPath()) {
                    System.out.printf("%15s |", value.asPath());
                    obj.put(colNames.get(num),value.asPath());
                }
                if (value.isList()) {
                    System.out.printf("%15s |", value.asList());
                    obj.put(colNames.get(num),value.asList());
                }
                if (value.isSet()) {
                    System.out.printf("%15s |", value.asSet());
                    obj.put(colNames.get(num),value.asSet());
                }
                if (value.isMap()) {
                    System.out.printf("%15s |", value.asMap());
                    obj.put(colNames.get(num),value.asMap());
                }
                num ++ ;
                Resp.add(obj);
            }
            System.out.println();
        }
        return Resp;
    }

    private   String SuessfulRest() {
        return "{\"data\":1}";
    }
    private   String FailRest() {
        return "{\"data\":0}";
    }
    public  String  InsertNabal(String  sql) {
        try {
            //String insertVertexes = "INSERT VERTEX person(name, age) VALUES "
           //         + "'Bob':('Bob', 10), "
          //          + "'Lily':('Lily', 9), "
            //        + "'Tom':('Tom', 10), "
            //        + "'Jerry':('Jerry', 13), "
            //        + "'John':('John', 11);";
            if (bInit == 0 ) {
                bInit=1;
                init();
                bInit=2;

            }
            session = pool.getSession(UserName, Passwd, true);
            if (session == null) {
                init();
                session = pool.getSession(UserName, Passwd, true);
            }
            ResultSet resp = session.execute("USE " + SpaceName + ";");
            if (!resp.isSucceeded()) {

                //System.exit(1);
                session.release();
                return FailRest();
            }
            System.out.println(sql);
            ResultSet resp1 = session.execute( sql);
            if (!resp1.isSucceeded()) {

                //System.exit(1);
                session.release();
                return FailRest();
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        session.release();
        return SuessfulRest();
    }

    public String SelectNabal(String query) {
        try {
            //String query = "GO FROM \"Bob\" OVER like "
              //      + "YIELD $^.person.name, $^.person.age, like.likeness";
            if (bInit == 0 ) {
                bInit=1;
                init();
                bInit=2;

            }
            session = pool.getSession(UserName, Passwd, true);
            if (session == null) {
                init();
                session = pool.getSession(UserName, Passwd, true);
            }
            ResultSet resp = session.execute("USE " + SpaceName + ";");
            if (!resp.isSucceeded()) {

                //System.exit(1);
                session.release();
                return FailRest();
            }
            System.out.println(query);
            ResultSet resp1 = session.execute(query);
            if (!resp1.isSucceeded()) {
                System.out.println(String.format("Execute: `%s', failed: %s",
                        query, resp1.getErrorMessage()));
                System.exit(1);
            }
            List<Object> respArr  =   RestResult(resp);
            if (respArr.size() == 0) {
                session.release();
                return SuessfulRest();
            }
            session.release();
            return  JSONArray.toJSON(respArr).toString();

        }catch (Exception e) {
            e.printStackTrace();
        }
        return FailRest();
    }

}

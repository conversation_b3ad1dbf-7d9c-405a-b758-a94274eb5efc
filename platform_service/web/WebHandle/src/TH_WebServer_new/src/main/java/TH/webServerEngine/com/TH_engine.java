package TH.webServerEngine.com;
import com.fasterxml.jackson.databind.JsonNode;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.QName;
import org.dom4j.io.SAXReader;
import org.json.JSONObject;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
//import java.io.File;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
//
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.FileOutputStream;
import java.io.InputStreamReader;
// 处理模式基类
class TH_Data_req
{
    public JsonNode obj  = null ;
    public Map<String, String>   ArrayMap = new HashMap<String, String>();
}
class TH_handle_base
{
    public void init(String config,String name)
    {}
    public void Handle(TH_Data_req req , JsonNode resp,HttpResp ObjJsonRest)
    {}
}
class handle_factory  // 工厂类  -----
{
    public TH_handle_base factory()
    {
        return new TH_handle_base();
    }
}
class handle_marge
{
    public  TH_handle_base Factory(String id,String config,String name)
    {
//        System.out.println("id == "+id +"  config == "+ config);
        TH_handle_base m = FactoryMap.get(id).factory();
        m.init(config,name);
        return m;
    }
    public void TH_init()
    {
        FactoryMap.put("ESHandle" ,(handle_factory)new ESHandleFortary());
        FactoryMap.put("MySqlHandle" ,(handle_factory)new MysqlHandleFortary());
        FactoryMap.put("NebulaHandle",(handle_factory) new NabolaHandleFortary());
    }
    private static handle_marge instance=null;
    private handle_marge(){
        TH_init();
    }
    public static synchronized handle_marge getInstance(){
        if(instance==null){
            instance=new handle_marge();
        }
        return instance;
    }
    private Map<String, handle_factory>   FactoryMap = new HashMap<String, handle_factory>();
}
class ServerConf
{
    public List<TH_handle_base> HanldeList = new ArrayList();
    public TH_handle_base define = null;
    public HttpArreyToMap conf = null;
    public HttpResponeJson resp_handle = null;
    public String TypeFiled = "";
    public ESRetData p_ESRetData = null ;//new ESRetData();
    public List<HttpResponeJson> HttpResponeJsonList = new ArrayList();

    public void Init(Element rootElement)
    {
        QName qarryname = new QName("arr_conf");
        List<Element> arry_list = rootElement.elements(qarryname);
        for (int i = 0; i < arry_list.size(); i++)
        {
            Element e = (Element)arry_list.get(i);
            this.conf = new HttpArreyToMap();
            this.conf.init(e);
        }
        QName qname = new QName("handle");
        List<Element> list = rootElement.elements(qname);
        for (int i = 0; i < list.size(); i++)
        {

            Element e_hondle = (Element)list.get(i);
            QName Doqname = new QName("one_do");
            List<Element> Dolist = e_hondle.elements(Doqname);
            for (int ii = 0; ii < Dolist.size(); ii++)
            {
                Element e = (Element)Dolist.get(ii);

                String config = e.attribute("config").getText();
                String name = e.attribute("node_name").getText();
                String id = e.getText();
                System.out.println("id == " + id + "  config == " + config);

                TH_handle_base p = handle_marge.getInstance().Factory(id, config, name);

                this.HanldeList.add(p);
            }
        }
        Element element_resp = rootElement.element("response") ;
        QName qrespname = new QName("data");
        List<Element> RespList = element_resp.elements(qrespname);
        for (int i = 0; i < RespList.size(); i++)
        {
            Element e = (Element)RespList.get(i);
            this.resp_handle = new HttpResponeJson();
            this.resp_handle.init(e);
            this.HttpResponeJsonList.add( this.resp_handle);
        }

        Element aml =  rootElement.element("response_list");
        if(aml != null) {
            p_ESRetData = new ESRetData();
            p_ESRetData.init(aml);
        }

    }

    public JsonNode handle(TH_Data_req req, JsonNode resp)
    {

        this.conf.handle(req, resp);
        HttpResp Objresp = new HttpResp();
        for (int i = 0; i < this.HanldeList.size(); i++)
        {
            TH_handle_base p = (TH_handle_base)this.HanldeList.get(i);
            p.Handle(req, resp, Objresp);
        }
        //this.resp_handle.Handle(req, resp, Objresp);
        //去取data 部分
        for (int j = 0; j < this.HttpResponeJsonList.size(); j++)
        {
            HttpResponeJson p = (HttpResponeJson)this.HttpResponeJsonList.get(j);
            p.Handle(req, resp, Objresp);
        }
        if (p_ESRetData != null)
        {
            return p_ESRetData.Handle(Objresp.resp,req.ArrayMap);
        }
        return  Objresp.resp;
    }
}
// 处理引擎
class TH_engine
{
    // 数据类型 数据处理流程
    private Map<String, ServerConf> ServerMap = new HashMap();
    private ServerConf define = null;
    private HttpGetJsonData JsonData = null;
    private String ConfFiled = "";
    public JsonNode Handle(TH_Data_req req, JsonNode resp)
    {
        System.out.println("ConfFiled ==== " + this.ConfFiled);
        String sType = JacksonHandle.getInstance().GetValue(req.obj, this.ConfFiled);
        ServerConf p = (ServerConf)this.ServerMap.get(sType);
        if (p == null)
        {
            System.out.println(" : " + sType + "");
            return resp;
        }
        resp = p.handle(req, resp);
        return resp;
    }
    public void init(String config)
    {
//        System.out.println("读取ES " + config);
        try
        {
//            System.getProperty("user.dir");
//            System.out.println(config);
            String tempFileName = config;
            File file = new File(tempFileName);
            if (!file.exists())
            {
                System.out.println("ES  " + config + "读取失败");
                System.exit(1);
            }
//            System.out.println("ES " + config + "读取文件成功");
            SAXReader reader = new SAXReader();
            Document doc = reader.read(file);
            Element rootElement = doc.getRootElement();
            this.ConfFiled = rootElement.element("filed").getStringValue();
//            ConfText.getInstance().sHost = rootElement.element("host").getStringValue();
            ConfText.getInstance().sHost = (String) new JSONObject(JsonUtils.readJsonFile("resources/config.json")).get("es_url");
            System.out.println("host = " + ConfText.getInstance().sHost + "   ConfFiled =" + this.ConfFiled);
            QName qname = new QName("server");
            List<Element> list = rootElement.elements(qname);
//            System.out.println("list.size(); === " + list.size());
            for (int i = 0; i < list.size(); i++)
            {
                Element e = (Element)list.get(i);
                String key = e.attribute("type").getText();
                System.out.println("解析服务   ： " + key + "开始");
                ServerConf p = new ServerConf();
                p.Init(e);
                System.out.println("解析服务   ： " + key + "结束");
                this.ServerMap.put(key, p);
            }
        }
        catch (DocumentException e)
        {
            System.out.println("ES engine 错误");
            e.printStackTrace();
        }
    }
}

package TH.webServerEngine.com;

import java.io.Serializable;
import java.util.List;

public class ResultMsg<T> implements Serializable {
	private List<T> list;
	private long count;
	public List<T> getList() {
		return list;
	}
	public void setList(List<T> list) {
		this.list = list;
	}
	public long getCount() {
		return count;
	}
	public void setCount(long count) {
		this.count = count;
	}
	@Override
	public String toString() {
		return "ResultMsg [list=" + list + ", count=" + count + "]";
	}
}

package TH.webServerEngine.com;
// 基础数据解析类
// 单例模式
//
public class ConfText {
    private static ConfText instance;
    private ConfText(){
    }
    public static synchronized ConfText getInstance(){
        if(instance==null){
            instance=new ConfText();
        }
        return instance;
    }
    public  String  sDbType ;
    public  String  sHost;
    public  String  sUserName ;
    public  String  sPasswd ;
}

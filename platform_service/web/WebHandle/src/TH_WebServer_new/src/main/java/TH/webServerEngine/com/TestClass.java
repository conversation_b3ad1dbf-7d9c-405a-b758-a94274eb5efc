package TH.webServerEngine.com;
import org.json.JSONArray;
import org.json.JSONObject;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class TestClass {

    public static void main(String[] args) throws NoSuchAlgorithmException {
//        System.out.println(str("{'type':'IP_ANALYSIS_PassiveDNS','param':'www.baidu.com'}"));
//        System.out.println(str1("123,39,116,121,112,101,39,58,39,73,80,95,65,78,65,76,89,83,73,83,95,80,97,115,115,105,118,101,68,78,83,39,44,39,112,97,114,97,109,39,58,39,119,119,119,46,98,97,105,100,117,46,99,111,109,39,125"));
        JSONObject jsonObj = new JSONObject(JsonUtils.readJsonFile("resources/config.json"));
        System.out.print(jsonObj.get("port"));
    }

    public static String sha1(String data) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(data.getBytes());
        StringBuffer buf = new StringBuffer();
        byte[] bits = md.digest();
        for(int i=0;i<bits.length;i++){
            int a = bits[i];
            if(a<0) a+=256;
            if(a<16) buf.append("0");
            buf.append(Integer.toHexString(a));
        }
        return buf.toString();
    }

    public static String str(String s) {
        String string = "";
        char[] chs = s.toCharArray();
        for (int i = 0; i < chs.length; i ++) {
            Integer value = Integer.valueOf(chs[i]);
            if (i == 0) {
                string = string + value;
            } else {
                string = string + "," + value;
            }
        }
        return string;
    }

    public static String str1(String s) {
        String string = "";
        String[] strArr =  s.split(",");
        for (int i = 0; i < strArr.length; i ++) {
            string = string + String.valueOf((char) Integer.parseInt(strArr[i]));
        }
        return string;
    }

}

package TH.webServerEngine.com;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonEncoding;
import com.fasterxml.jackson.core.JsonParseException;

import java.util.Arrays;
import java.util.List;

// JacksonH 处理封装
public class JacksonHandle {
    private static JacksonHandle instance=null;
    private JacksonHandle() {}
    public static synchronized JacksonHandle getInstance(){
        if(instance==null){
            instance=new JacksonHandle();
        }
        return instance;
    }
    public JsonNode NewEmtpyJson( )
    {
        try {
            String str = "{}";
            ObjectMapper mapper = new ObjectMapper();
            return  mapper.readTree(str);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }
    // 字符串 转 json 对象
    public JsonNode JsonParse( String str)
    {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(str);
            return root;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
       return null;
    }
    public JsonNode getObjValue(JsonNode obj  , String Path)
    {
        try {
            List<String> result = Arrays.asList(Path.split("/"));
            JsonNode obj_L = obj;
            for (int i = 0; i < result.size(); i++) {
                // 判断有没有符号
                String Filed = result.get(i);
                int Nt1 = Filed.indexOf("[");
                int Nt2 = Filed.indexOf("]");
                if(Nt1 != -1 && Nt2 != -1)
                {
                    String sArrFiled = Filed.substring(0,Nt1-1);
                    int Num = Integer.parseInt(Filed.substring(Nt1+1,Nt2-1));
                    obj_L = obj_L.get(Filed).get(Num);
                }
                else {
                    if (obj_L == null) {
                        return null;
                    }
                    obj_L = obj_L.get(Filed);
                }
            }
            return obj_L;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null ;
    }
    // 设置值 JsonNode
    public void  SetValue(JsonNode obj  , String Path,String Key,JsonNode value)
    {
        ObjectNode obj_L =  (ObjectNode)getObjValue(obj,Path);
        obj_L.put(Key ,obj_L.toString());
    }
    public void  SetValue(JsonNode obj ,String Key,JsonNode value)
    {
        ObjectNode obj_L =  (ObjectNode)obj;
        obj_L.put(Key,value);
    }
    // 定稿
    public void   SetValue(JsonNode obj  , String Key ,String value)
    {
        ObjectNode obj_L =  (ObjectNode)obj;
        obj_L.put(Key ,value);
    }
    // 定稿
    public void   SetValue(JsonNode obj ,String Path ,String Key,String value)
    {
        ObjectNode obj_L =  (ObjectNode)getObjValue(obj,Path);
        obj_L.put(Key ,value);
    }
    // 定稿
    public void   SetValue(JsonNode obj  , String  key, boolean value)
    {
        ObjectNode obj_L =  (ObjectNode)obj;
        obj_L.put(key ,value);
    }
    public void   SetValue(JsonNode obj  , String  Key, int value)
    {
        ObjectNode obj_L =  (ObjectNode)obj;
        obj_L.put(Key ,value);
    }
    public void   SetValue(JsonNode obj  , String  Key, float value)
    {
        ObjectNode obj_L =  (ObjectNode)obj;
        obj_L.put(Key ,value);
    }
    public String GetValue(JsonNode obj  , String Path)
    {
        JsonNode obj_L =  JacksonHandle.getInstance().getObjValue(obj,Path);
        if(obj_L == null)
        {
            System.out.println("obj ==== " + obj.toString());
            System.out.println("Path ==== " + Path);
            System.out.println(Path +"配置错误 !");
            System.out.println("错误定位  : "+obj_L.toString());
            System.exit(1);
        }
        if(obj_L.isTextual())
        {
            System.out.println("STRING  取值  : "+obj_L.toString());
            return   obj_L.textValue();
        }
       else  if (obj_L.isInt() ) {
            return String.valueOf(obj_L.asInt());
        }
        else  if (obj_L.isDouble() || obj_L.isFloat() ) {
            double t = obj.asDouble();
            long  iPart = (long) t;
            if ( t - iPart  > 0) {
                return obj_L.toString();
            } else {
                return  String.valueOf(iPart);
            }

        }
        else
        {
            System.out.println("取值  : "+obj_L.toString());
            return   obj_L.toString();
        }
    }
}

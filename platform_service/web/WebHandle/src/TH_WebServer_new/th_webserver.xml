<?xml version="1.0" encoding="UTF-8"?>
<project name="th_webserver" default="all">
  
  
  <property file="th_webserver.properties"/>
  <!-- Uncomment the following property if no tests compilation is needed -->
  <!-- 
  <property name="skip.tests" value="true"/>
   -->
  
  <!-- Compiler options -->
  
  <property name="compiler.debug" value="on"/>
  <property name="compiler.generate.no.warnings" value="off"/>
  <property name="compiler.args" value=""/>
  <property name="compiler.max.memory" value="700m"/>
  <patternset id="ignored.files">
    <exclude name="**/*.hprof/**"/>
    <exclude name="**/*.pyc/**"/>
    <exclude name="**/*.pyo/**"/>
    <exclude name="**/*.rbc/**"/>
    <exclude name="**/*.yarb/**"/>
    <exclude name="**/*~/**"/>
    <exclude name="**/.DS_Store/**"/>
    <exclude name="**/.git/**"/>
    <exclude name="**/.hg/**"/>
    <exclude name="**/.svn/**"/>
    <exclude name="**/CVS/**"/>
    <exclude name="**/__pycache__/**"/>
    <exclude name="**/_svn/**"/>
    <exclude name="**/vssver.scc/**"/>
    <exclude name="**/vssver2.scc/**"/>
  </patternset>
  <patternset id="library.patterns">
    <include name="*.egg"/>
    <include name="*.jar"/>
    <include name="*.ear"/>
    <include name="*.swc"/>
    <include name="*.war"/>
    <include name="*.ane"/>
    <include name="*.zip"/>
  </patternset>
  <patternset id="compiler.resources">
    <exclude name="**/?*.java"/>
    <exclude name="**/?*.form"/>
    <exclude name="**/?*.class"/>
    <exclude name="**/?*.groovy"/>
    <exclude name="**/?*.scala"/>
    <exclude name="**/?*.flex"/>
    <exclude name="**/?*.kt"/>
    <exclude name="**/?*.clj"/>
    <exclude name="**/?*.aj"/>
  </patternset>
  
  <!-- JDK definitions -->
  
  <property name="jdk.bin.1.8" value="${jdk.home.1.8}/bin"/>
  <path id="jdk.classpath.1.8">
    <fileset dir="${jdk.home.1.8}">
      <include name="jre/lib/charsets.jar"/>
      <include name="jre/lib/deploy.jar"/>
      <include name="jre/lib/ext/access-bridge-64.jar"/>
      <include name="jre/lib/ext/cldrdata.jar"/>
      <include name="jre/lib/ext/dnsns.jar"/>
      <include name="jre/lib/ext/jaccess.jar"/>
      <include name="jre/lib/ext/jfxrt.jar"/>
      <include name="jre/lib/ext/localedata.jar"/>
      <include name="jre/lib/ext/nashorn.jar"/>
      <include name="jre/lib/ext/sunec.jar"/>
      <include name="jre/lib/ext/sunjce_provider.jar"/>
      <include name="jre/lib/ext/sunmscapi.jar"/>
      <include name="jre/lib/ext/sunpkcs11.jar"/>
      <include name="jre/lib/ext/zipfs.jar"/>
      <include name="jre/lib/javaws.jar"/>
      <include name="jre/lib/jce.jar"/>
      <include name="jre/lib/jfr.jar"/>
      <include name="jre/lib/jfxswt.jar"/>
      <include name="jre/lib/jsse.jar"/>
      <include name="jre/lib/management-agent.jar"/>
      <include name="jre/lib/plugin.jar"/>
      <include name="jre/lib/resources.jar"/>
      <include name="jre/lib/rt.jar"/>
    </fileset>
  </path>
  
  <property name="project.jdk.home" value="${jdk.home.1.8}"/>
  <property name="project.jdk.bin" value="${jdk.bin.1.8}"/>
  <property name="project.jdk.classpath" value="jdk.classpath.1.8"/>
  
  
  <!-- Project Libraries -->
  
  <path id="library.maven:_asm:asm:3.1.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/asm/asm/3.1/asm-3.1.jar"/>
  </path>
  
  <path id="library.maven:_com.alibaba:fastjson:1.2.28.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.28/fastjson-1.2.28.jar"/>
  </path>
  
  <path id="library.maven:_com.fasterxml.jackson.core:jackson-annotations:2.3.2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.3.2/jackson-annotations-2.3.2.jar"/>
  </path>
  
  <path id="library.maven:_com.fasterxml.jackson.core:jackson-core:2.3.2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.3.2/jackson-core-2.3.2.jar"/>
  </path>
  
  <path id="library.maven:_com.fasterxml.jackson.core:jackson-databind:2.3.2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.3.2/jackson-databind-2.3.2.jar"/>
  </path>
  
  <path id="library.maven:_com.fasterxml.jackson.jaxrs:jackson-jaxrs-base:2.3.2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/com/fasterxml/jackson/jaxrs/jackson-jaxrs-base/2.3.2/jackson-jaxrs-base-2.3.2.jar"/>
  </path>
  
  <path id="library.maven:_com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:2.3.2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/com/fasterxml/jackson/jaxrs/jackson-jaxrs-json-provider/2.3.2/jackson-jaxrs-json-provider-2.3.2.jar"/>
  </path>
  
  <path id="library.maven:_com.fasterxml.jackson.module:jackson-module-jaxb-annotations:2.3.2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.3.2/jackson-module-jaxb-annotations-2.3.2.jar"/>
  </path>
  
  <path id="library.maven:_com.sun.jersey:jersey-client:1.18.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/com/sun/jersey/jersey-client/1.18/jersey-client-1.18.jar"/>
  </path>
  
  <path id="library.maven:_com.sun.jersey:jersey-core:1.18.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/com/sun/jersey/jersey-core/1.18/jersey-core-1.18.jar"/>
  </path>
  
  <path id="library.maven:_com.sun.jersey:jersey-grizzly2:1.18.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/com/sun/jersey/jersey-grizzly2/1.18/jersey-grizzly2-1.18.jar"/>
  </path>
  
  <path id="library.maven:_com.sun.jersey:jersey-json:1.18.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/com/sun/jersey/jersey-json/1.18/jersey-json-1.18.jar"/>
  </path>
  
  <path id="library.maven:_com.sun.jersey:jersey-server:1.18.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/com/sun/jersey/jersey-server/1.18/jersey-server-1.18.jar"/>
  </path>
  
  <path id="library.maven:_com.sun.xml.bind:jaxb-impl:2.2.3-1.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/com/sun/xml/bind/jaxb-impl/2.2.3-1/jaxb-impl-2.2.3-1.jar"/>
  </path>
  
  <path id="library.maven:_commons-beanutils:commons-beanutils:1.7.0.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.7.0/commons-beanutils-1.7.0.jar"/>
  </path>
  
  <path id="library.maven:_commons-codec:commons-codec:1.9.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/commons-codec/commons-codec/1.9/commons-codec-1.9.jar"/>
  </path>
  
  <path id="library.maven:_commons-collections:commons-collections:3.1.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/commons-collections/commons-collections/3.1/commons-collections-3.1.jar"/>
  </path>
  
  <path id="library.maven:_commons-dbcp:commons-dbcp:1.2.2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/commons-dbcp/commons-dbcp/1.2.2/commons-dbcp-1.2.2.jar"/>
  </path>
  
  <path id="library.maven:_commons-io:commons-io:2.4.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar"/>
  </path>
  
  <path id="library.maven:_commons-lang:commons-lang:2.5.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/commons-lang/commons-lang/2.5/commons-lang-2.5.jar"/>
  </path>
  
  <path id="library.maven:_commons-logging:commons-logging:1.2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar"/>
  </path>
  
  <path id="library.maven:_commons-pool:commons-pool:1.3.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/commons-pool/commons-pool/1.3/commons-pool-1.3.jar"/>
  </path>
  
  <path id="library.maven:_dom4j:dom4j:1.5.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/dom4j/dom4j/1.5/dom4j-1.5.jar"/>
  </path>
  
  <path id="library.maven:_javax.activation:activation:1.1.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar"/>
  </path>
  
  <path id="library.maven:_javax.annotation:javax.annotation-api:1.2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.2/javax.annotation-api-1.2.jar"/>
  </path>
  
  <path id="library.maven:_javax.servlet:javax.servlet-api:3.1.0.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar"/>
  </path>
  
  <path id="library.maven:_javax.validation:validation-api:1.1.0.final.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar"/>
  </path>
  
  <path id="library.maven:_javax.ws.rs:javax.ws.rs-api:2.0.1.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/javax/ws/rs/javax.ws.rs-api/2.0.1/javax.ws.rs-api-2.0.1.jar"/>
  </path>
  
  <path id="library.maven:_javax.xml.bind:jaxb-api:2.2.2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.2.2/jaxb-api-2.2.2.jar"/>
  </path>
  
  <path id="library.maven:_javax.xml.stream:stax-api:1.0-2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/javax/xml/stream/stax-api/1.0-2/stax-api-1.0-2.jar"/>
  </path>
  
  <path id="library.maven:_javax.xml:jsr173:1.0.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/javax/xml/jsr173/1.0/jsr173-1.0.jar"/>
  </path>
  
  <path id="library.maven:_jaxen:jaxen:1.1.6.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/jaxen/jaxen/1.1.6/jaxen-1.1.6.jar"/>
  </path>
  
  <path id="library.maven:_jaxme:jaxme-api:0.3.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/jaxme/jaxme-api/0.3/jaxme-api-0.3.jar"/>
  </path>
  
  <path id="library.maven:_junit:junit:4.8.1.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/junit/junit/4.8.1/junit-4.8.1.jar"/>
  </path>
  
  <path id="library.maven:_msv:relaxngdatatype:20030807.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/msv/relaxngDatatype/20030807/relaxngDatatype-20030807.jar"/>
  </path>
  
  <path id="library.maven:_msv:xsdlib:20030807.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/msv/xsdlib/20030807/xsdlib-20030807.jar"/>
  </path>
  
  <path id="library.maven:_mysql:mysql-connector-java:5.1.38.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/mysql/mysql-connector-java/5.1.38/mysql-connector-java-5.1.38.jar"/>
  </path>
  
  <path id="library.maven:_net.sf.ezmorph:ezmorph:1.0.3.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/net/sf/ezmorph/ezmorph/1.0.3/ezmorph-1.0.3.jar"/>
  </path>
  
  <path id="library.maven:_net.sf.json-lib:json-lib:jdk15:2.4.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/net/sf/json-lib/json-lib/2.4/json-lib-2.4-jdk15.jar"/>
  </path>
  
  <path id="library.maven:_org.apache.httpcomponents:httpclient:4.4.1.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.4.1/httpclient-4.4.1.jar"/>
  </path>
  
  <path id="library.maven:_org.apache.httpcomponents:httpcore:4.4.1.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.1/httpcore-4.4.1.jar"/>
  </path>
  
  <path id="library.maven:_org.codehaus.jackson:jackson-core-asl:1.9.2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.9.2/jackson-core-asl-1.9.2.jar"/>
  </path>
  
  <path id="library.maven:_org.codehaus.jackson:jackson-jaxrs:1.9.2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.9.2/jackson-jaxrs-1.9.2.jar"/>
  </path>
  
  <path id="library.maven:_org.codehaus.jackson:jackson-mapper-asl:1.9.2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.9.2/jackson-mapper-asl-1.9.2.jar"/>
  </path>
  
  <path id="library.maven:_org.codehaus.jackson:jackson-xc:1.9.2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-xc/1.9.2/jackson-xc-1.9.2.jar"/>
  </path>
  
  <path id="library.maven:_org.codehaus.jettison:jettison:1.1.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar"/>
  </path>
  
  <path id="library.maven:_org.eclipse.jetty:jetty-http:9.3.7.v20160115.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.3.7.v20160115/jetty-http-9.3.7.v20160115.jar"/>
  </path>
  
  <path id="library.maven:_org.eclipse.jetty:jetty-io:9.3.7.v20160115.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.3.7.v20160115/jetty-io-9.3.7.v20160115.jar"/>
  </path>
  
  <path id="library.maven:_org.eclipse.jetty:jetty-security:9.3.7.v20160115.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.3.7.v20160115/jetty-security-9.3.7.v20160115.jar"/>
  </path>
  
  <path id="library.maven:_org.eclipse.jetty:jetty-server:9.3.7.v20160115.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.3.7.v20160115/jetty-server-9.3.7.v20160115.jar"/>
  </path>
  
  <path id="library.maven:_org.eclipse.jetty:jetty-servlet:9.3.7.v20160115.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.3.7.v20160115/jetty-servlet-9.3.7.v20160115.jar"/>
  </path>
  
  <path id="library.maven:_org.eclipse.jetty:jetty-util:9.3.7.v20160115.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.3.7.v20160115/jetty-util-9.3.7.v20160115.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.grizzly:grizzly-framework:2.2.16.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/grizzly/grizzly-framework/2.2.16/grizzly-framework-2.2.16.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.grizzly:grizzly-http-server:2.2.16.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/grizzly/grizzly-http-server/2.2.16/grizzly-http-server-2.2.16.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.grizzly:grizzly-http:2.2.16.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/grizzly/grizzly-http/2.2.16/grizzly-http-2.2.16.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.grizzly:grizzly-rcm:2.2.16.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/grizzly/grizzly-rcm/2.2.16/grizzly-rcm-2.2.16.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.hk2.external:aopalliance-repackaged:2.4.0-b10.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/hk2/external/aopalliance-repackaged/2.4.0-b10/aopalliance-repackaged-2.4.0-b10.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.hk2.external:javax.inject:2.4.0-b10.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/hk2/external/javax.inject/2.4.0-b10/javax.inject-2.4.0-b10.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.hk2:hk2-api:2.4.0-b10.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-api/2.4.0-b10/hk2-api-2.4.0-b10.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.hk2:hk2-locator:2.4.0-b10.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-locator/2.4.0-b10/hk2-locator-2.4.0-b10.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.hk2:hk2-utils:2.4.0-b10.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-utils/2.4.0-b10/hk2-utils-2.4.0-b10.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.hk2:osgi-resource-locator:1.0.1.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/hk2/osgi-resource-locator/1.0.1/osgi-resource-locator-1.0.1.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.bundles.repackaged:jersey-guava:2.17.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/jersey/bundles/repackaged/jersey-guava/2.17/jersey-guava-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.containers:jersey-container-servlet-core:2.17.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/jersey/containers/jersey-container-servlet-core/2.17/jersey-container-servlet-core-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.containers:jersey-container-servlet:2.17.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/jersey/containers/jersey-container-servlet/2.17/jersey-container-servlet-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.core:jersey-client:2.17.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-client/2.17/jersey-client-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.core:jersey-common:2.17.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-common/2.17/jersey-common-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.core:jersey-server:2.17.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-server/2.17/jersey-server-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.ext:jersey-entity-filtering:2.17.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/jersey/ext/jersey-entity-filtering/2.17/jersey-entity-filtering-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.media:jersey-media-jaxb:2.17.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/jersey/media/jersey-media-jaxb/2.17/jersey-media-jaxb-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.glassfish.jersey.media:jersey-media-json-jackson:2.17.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/glassfish/jersey/media/jersey-media-json-jackson/2.17/jersey-media-json-jackson-2.17.jar"/>
  </path>
  
  <path id="library.maven:_org.javassist:javassist:3.18.1-ga.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/javassist/javassist/3.18.1-GA/javassist-3.18.1-GA.jar"/>
  </path>
  
  <path id="library.maven:_org.json:json:20160810.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/json/json/20160810/json-20160810.jar"/>
  </path>
  
  <path id="library.maven:_org.mybatis:mybatis:3.2.8.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/mybatis/mybatis/3.2.8/mybatis-3.2.8.jar"/>
  </path>
  
  <path id="library.maven:_org.springframework:spring-aop:4.3.0.release.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/springframework/spring-aop/4.3.0.RELEASE/spring-aop-4.3.0.RELEASE.jar"/>
  </path>
  
  <path id="library.maven:_org.springframework:spring-beans:4.3.0.release.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/springframework/spring-beans/4.3.0.RELEASE/spring-beans-4.3.0.RELEASE.jar"/>
  </path>
  
  <path id="library.maven:_org.springframework:spring-context:4.3.0.release.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/springframework/spring-context/4.3.0.RELEASE/spring-context-4.3.0.RELEASE.jar"/>
  </path>
  
  <path id="library.maven:_org.springframework:spring-core:4.3.0.release.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/springframework/spring-core/4.3.0.RELEASE/spring-core-4.3.0.RELEASE.jar"/>
  </path>
  
  <path id="library.maven:_org.springframework:spring-expression:4.3.0.release.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/springframework/spring-expression/4.3.0.RELEASE/spring-expression-4.3.0.RELEASE.jar"/>
  </path>
  
  <path id="library.maven:_org.springframework:spring-web:4.3.0.release.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/springframework/spring-web/4.3.0.RELEASE/spring-web-4.3.0.RELEASE.jar"/>
  </path>
  
  <path id="library.maven:_org.springframework:spring-webmvc:4.3.0.release.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/org/springframework/spring-webmvc/4.3.0.RELEASE/spring-webmvc-4.3.0.RELEASE.jar"/>
  </path>
  
  <path id="library.maven:_pull-parser:pull-parser:2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/pull-parser/pull-parser/2/pull-parser-2.jar"/>
  </path>
  
  <path id="library.maven:_xml-apis:xml-apis:1.0.b2.classpath">
    <pathelement location="${basedir}/../../../Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar"/>
  </path>
  
  
  <!-- Application Server Libraries -->
  <!-- Register Custom Compiler Taskdefs -->
  <property name="javac2.home" value="${idea.home}/lib"/>
  <path id="javac2.classpath">
    <pathelement location="${javac2.home}/javac2.jar"/>
    <pathelement location="${javac2.home}/jdom.jar"/>
    <pathelement location="${javac2.home}/asm-all.jar"/>
    <pathelement location="${javac2.home}/jgoodies-forms.jar"/>
  </path>
  <target name="register.custom.compilers">
    <taskdef name="javac2" classname="com.intellij.ant.Javac2" classpathref="javac2.classpath"/>
    <taskdef name="instrumentIdeaExtensions" classname="com.intellij.ant.InstrumentIdeaExtensions" classpathref="javac2.classpath"/>
  </target>
  
  <!-- Modules -->
  
  <import file="${basedir}/module_th_webserver.xml"/>
  
  <target name="init" description="Build initialization">
    <!-- Perform any build initialization in this target -->
  </target>
  
  <target name="clean" depends="clean.module.th_webserver" description="cleanup all"/>
  
  <target name="build.modules" depends="init, clean, compile.module.th_webserver" description="build all modules"/>
  
  <target name="all" depends="build.modules" description="build all"/>
</project>
##
# @file network_list.py
# @brief :  1  获取网卡列表   2 网卡配置信息
# <AUTHOR>
# @version 0.1.00
# @date 2022-10-13
### ifconfig -a | grep RUNNING  | grep -v docker  | grep -v veth  | grep -v lo
import os,json
def do_cmd(cmd):
     p=os.popen(cmd)
     x=p.read()
     xl = x.split("\n")
     p.close()
     return xl
if __name__=='__main__':
    cmd = "ifconfig -a | grep RUNNING  | grep -v docker  | grep -v veth  | grep -v lo  | awk '{print $1}'"
    results = do_cmd(cmd)
    network_list = []
    for i in results:
        t = i.replace(":","",1)
        if len(t) > 1:
            network_list.append(t)
    print(json.dumps(network_list))
    



##
# @file threat_info_rule_sync.py
# @brief : docker 中拷贝文件到 探针的规则 
# <AUTHOR>
# @version 0.1.00
# @date 2022-09-01

import os,json,pymysql.cursors,sys,base64
import sys

def do_cmd(cmd):
     p=os.popen(cmd)
     x=p.read()
     xl = x.split("\n")
     p.close()
     return xl[0]
def save():
    global config_json
if __name__=='__main__':
    if len(sys.argv) ==2:
        cp_json=json.loads(sys.argv[1])
        file=cp_json["file"]
        res = do_cmd("docker exec -it py_analysis  /bin/bash -c \" ls  "+file +"\"")
        #if res == "" or  "No such file or directory" in res or "No such container: py_analysis" in res:
        if True:
            print("true")
            os.system("mkdir -p /opt/GeekSec/th/bin/conf/0/JsonRule/BasicRule/UserRule/PrivateRule/")
            os.system("mkdir -p /opt/GeekSec/th/bin/conf/1/JsonRule/BasicRule/UserRule/PrivateRule/")
            os.system("docker cp py_analysis:"+file + " /opt/GeekSec/th/bin/conf/0/JsonRule/BasicRule/UserRule/PrivateRule/")
            os.system("docker cp py_analysis:"+file + " /opt/GeekSec/th/bin/conf/1/JsonRule/BasicRule/UserRule/PrivateRule/")
            os.system("cd /opt/GeekSec/th/bin/ &&  ./thd.all.restart")
        else:
            print("false")

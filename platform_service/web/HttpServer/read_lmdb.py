# -*- coding: utf-8 -*-
import lmdb,json
import  os,sys
import base64
import pickle
lmdbpath = "/data/lmdb/"
def getData(Db,key) :
    try :
        #print("db =====" ,lmdbpath+Db+"/")
        #print("key  =====" ,key)
        env = lmdb.Environment(lmdbpath + Db+"/")
        txn = env.begin()
        value = txn.get(key)
        #print(value)
        env.close()
        return value
    except  lmdb.VersionMismatchError:
        return None
def print_debug(Db):
    try :
        print("db =====" ,lmdbpath+Db+"/")
        env = lmdb.Environment(lmdbpath + Db+"/")
        txn = env.begin()
        num = 0
        for key, value in txn.cursor():     # 通过cursor()遍历所有数据和键值
            if num < 10:
                num += 1
            else:
                break
            print(key, value)
        #value = txn.get(key)
        print(value)
        env.close()
        return value
    except  lmdb.VersionMismatchError:
        return None
def db_scan(dbname):
    dblist = []
    dlist =  os.listdir(lmdbpath)
    for name in dlist:
        if name.startswith(dbname) and name.endswith(".pkl"):
            dblist.append(name)
    return dblist 
def get_key_data(key):
    keylist = key.split("_")
    #print(keylist)
    db = keylist[1]+"_"+keylist[2]+"_"+keylist[3]
    dblist = db_scan(db)
    for dbt in dblist:
        #### ####
        #keyfile_path =os.path.join(lmdbpath,dbt)
        tnum = 0
        b = key.encode('utf-8')
        #print_debug(dbt)
        bkey = getData(dbt , b)
        if bkey != None:
            #print("find key   tttttt ")
            ddb = dbt[0:len(dbt)-4]
            #print_debug(ddb)
            v = getData(ddb,bkey)
            if (v != None):
                return base64.b64encode(v).decode()
            else:
                return v
    return ""

if __name__=='__main__':
    ret = {}
    if (len(sys.argv) >= 2):
        num = 0 
        for i in sys.argv:
            if num  == 0 :
                num +=1 
                continue
            ret[i] = get_key_data(i)
    print(json.dumps(ret))

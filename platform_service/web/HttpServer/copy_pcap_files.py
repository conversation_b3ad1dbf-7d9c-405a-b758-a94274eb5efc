import sys
import shutil


def copy_files(source_files, destination_path):
    for file_path in source_files:
        try:
            shutil.copy(file_path, destination_path)
        except FileNotFoundError:
            print(f"File {file_path} not found.")
        except Exception as e:
            print(f"An error occurred while copying {file_path}: {str(e)}")


if __name__ == "__main__":
    # 获取命令行参数（排除第一个参数，即脚本文件名）
    args = sys.argv[1:]

    # 将参数解析为文件路径列表
    file_paths = [arg for arg in args if not arg.startswith("-")]

    # 指定服务器路径
    destination_path = "/data/cert_anay/pcap_source/"

    # 执行文件复制操作
    copy_files(file_paths, destination_path)

    print(f"Successfully copied files")
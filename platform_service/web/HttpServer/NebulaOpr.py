##
# @file NebulaOpr.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2022-09-27

import time
import json,os,sys

from nebula3.gclient.net import ConnectionPool

from nebula3.Config import Config
from nebula3.common import *
#from FormatResp import print_resp
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
def create_sql(opr_msg):
    print(opr_msg)
    opr_type=opr_msg["type"]
    vertex =opr_msg["vertex"]
    tag = opr_msg["tag"]
    msg = None
    if "msg" in opr_msg:
       msg = opr_msg["msg"]
    if opr_type == "DELETE":
        sql = "DELETE  VERTEX '"+tag+"'"
        return sql
    elif opr_type == "UPDATE":
        set_sql  = ""
        for key  in msg:
            if set_sql != "":
                set_sql += ","
            set_sql +=  key +"= '"+msg[key]+"'"
        sql = "UPDATE VERTEX ON "+vertex + " '"+tag+ "' SET " + set_sql  
        return sql
    elif opr_type == "INSERT":
        filed_sql = ""
        vaule_sql = ""
        for key  in msg:
            if filed_sql != "":
                filed_sql += ","
                vaule_sql += ","
            filed_sql += key
            vaule_sql +="'"+ msg[key]+"'"
        sql = "INSERT VERTEX " + vertex + " ("+filed_sql+") VALUES '"+ tag+"':("+vaule_sql+")"
        return sql

if __name__ == '__main__':
    client = None
    try:
        if len(sys.argv) != 2:
            print("参数错误")
            sys.exit(1)
        opr_json = json.loads(sys.argv[1])
        config = Config()
        config.max_connection_pool_size = 2
        # init connection pool
        connection_pool = ConnectionPool()
        # IP & 端口使用外部参数配置
        if "nebula_host"  not in base_json:
            base_json["nebula_host"] = "127.0.0.1"
        if "nebula_port"  not in base_json:
            base_json["nebula_port"] = 9669
        assert connection_pool.init([(base_json["nebula_host"], base_json["nebula_port"])], config)

        # get session from the pool
        client = connection_pool.get_session('root', 'nebula')
        assert client is not None

        # get the result in json format
        #resp_json = client.execute_json("yield 1")
        #json_obj = json.loads(resp_json)
        #print(json.dumps(json_obj, indent=2, sort_keys=True))

        # 图空间创建语句
        # 分区参数以及备份参数外部参数配置
        # create vertex and edge need to sleep after create schema space
        time.sleep(5);
        resp = client.execute('use gs_analysis_graph;')
        assert resp.is_succeeded(),resp.error_msg();
        sql = create_sql(opr_json)
        print(sql)
        resp = client.execute(sql)
        assert resp.is_succeeded(),resp.error_msg();
    except Exception as x:
        import traceback

        print(traceback.format_exc())
        if client is not None:
            client.release()
        exit(1)

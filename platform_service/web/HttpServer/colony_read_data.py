##
# @file colony_read_data.py
# @brief : 集群读取文件
# <AUTHOR>
# @version 0.1.00
# @date 2022-11-02
import select
import socket
from urllib.parse import urlparse
import json,os,sys


#
class Crawler:
    def __init__(self):
        self.socket_list = []
        self.read_list = list()
        self.write_list = list()
        self.exec_list = list()
        self.req_info = dict()
        self.msg_list = dict()
    #不断的事件循环
    def loop(self,handle,msg_list): 
        loop_num = len(self.req_info)
        while True:
            #
            rlist, wlist, xlist = select.select(self.read_list, self.write_list, self.exec_list)
            for i in wlist:
                # 侦测到写事件 就去发送报文
                host, path  , post_data = self.req_info[i]
                data = "POST {} HTTP/1.1\r\nHost:{}\r\nContent-Length: {}\r\nConnection:close\r\nUser-Agent:Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36\r\n\r\n{}".format(
                    path, host,str(len(post_data)),post_data).encode('utf8')
                #print("data ===== ", data)
                i.send(data)
                self.write_list.remove(i)
                self.read_list.append(i)
                #侦测到读时间就去获取报文
            for i in rlist:
                msg = i.recv(65536)
                if i not in self.msg_list.keys():
                    self.msg_list[i] = msg
                else:
                    self.msg_list[i] += msg
                    #考虑到网页的大小 一次不可能接受完毕 所以当没有数据的时候才关闭连接
                if not msg:
                    self.read_list.remove(i)
                    #print(self.msg_list[i].decode('utf8'))
                    handle(self.msg_list[i].decode('utf8'),msg_list)
                    del (self.msg_list[i])
            if len(self.read_list) ==0:
               return 
    def connect_fd(self,ip,port):
        ss = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        ss.connect((ip, port))
        ss.setblocking(False)
        self.write_list.append(ss)
        # 把socket加入socket
        self.socket_list.append(ss)
        return ss
    # 初始化http请求 向类里面不断的添加新的http需求,然后初始化好socket对象 添加给时间循环
    def add_url(self,hostlist , path,data):
        # 初始化socket请求
        #url_dict = urlparse(url)
        for host in hostlist:
            ss = self.connect_fd(host,58888)
            self.req_info[ss] = (host, path,json.dumps(data))

def HttpRespHandle(data,msg_list):
    json_str = data[data.find("\r\n\r\n")+4:len(data)]
    if len(json_str) >  2:
       resp_json = json.loads(json_str)
       msg = resp_json["message"] 
       for i in msg:
          if i not in msg_list:
             msg_list[i] = msg[i]
          if msg[i] != "":
             msg_list[i] = msg[i]
if __name__ == '__main__':
    msg_list = {}
    crawler = Crawler()
    hostlist = ["127.0.0.1"]
    ### 读取 host 列表
    keylist = []
    for i in range(1,len(sys.argv)):
       keylist.append(sys.argv[i])
    crawler.add_url(hostlist,'/system/localpb',keylist)
    crawler.loop(HttpRespHandle,msg_list)
    print(json.dumps(msg_list))


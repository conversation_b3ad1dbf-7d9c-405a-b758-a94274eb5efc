# Last Update:2020-08-21 13:57:58
##
# @file mod_ip.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-08-19
import json,os
# 设置IP  / 包括修改
Deflaut_Dev = "eno1"
config_path = ".conf.json"
config_json = {}
def do_cmd(cmd):
     p=os.popen(cmd)
     x=p.read()
     xl = x.split("\n")
     print (xl[0])
     p.close()
     return xl[0]
def save():
    global config_json
    json.dump(config_json,open(config_path,"w+"))
def init():
    global config_json
    dev=Deflaut_Dev
    if os.path.exists(config_path): 
        config_json = json.load(open(config_path,"r+"))
    if "NTP" not in config_json :
        config_json["NTP"] = ""
        ip_cmd = "cat  /etc/sysconfig/network-scripts/ifcfg-"+dev+"  | grep IPADDR "
        config_json["IP"] = do_cmd(ip_cmd).replace("IPADDR=","",1).replace("\n","")
        dns_cmd = " cat  /etc/sysconfig/network-scripts/ifcfg-"+dev+"  | grep DNS1"
        config_json["DNS"] = do_cmd(dns_cmd).replace("DNS1=","",1).replace("\n","")
        gateway_cmd = " cat  /etc/sysconfig/network-scripts/ifcfg-"+dev+"  | grep GATEWAY"
        config_json["GATEWAY"] = do_cmd(gateway_cmd).replace("GATEWAY=","",1).replace("\n","")
        config_json["PREFIX"] = "*************"
        save()
    return config_json



def mod_ip(do_json):
    global config_json
    if "NTP" not in config_json:
        init()
    # 获取就IP 
    dev = Deflaut_Dev
    if "DEV" in do_json:
        dev = do_json["DEV"]  # 网卡
    ip_cmd = " cat  /etc/sysconfig/network-scripts/ifcfg-"+dev+"  | grep IPADDR "
    old_ip=do_cmd(ip_cmd).replace("IPADDR=","",1).replace("\n","")
    print("old_ip + "+ old_ip)

    ip = do_json["IP"]   # IP 
    print("ip  ",ip)
    print("/bin/bash ModifyIp.sh "+old_ip + " " + ip)
    ####  nebula 修改IP #####
    #os.system("/bin/bash ModifyIp.sh "+old_ip + " " + ip)
    prefix = do_json["PREFIX"]
    cmd = "bash network_conf.sh -f "+dev+" -i " + ip + " -m " + prefix
    if 'GATEWAY' in  do_json and  do_json["GATEWAY"] != "" :
        cmd = cmd + " -g " + do_json["GATEWAY"]
        config_json["GATEWAY"] = do_json["GATEWAY"]
    if  'DNS' in do_json and  do_json["DNS"] != "":
        cmd = cmd + " -d " + do_json["DNS"]
        config_json["DNS"] = do_json["DNS"]
    print(cmd)
    os.system(cmd)
    #os.system("/etc/init.d/network restart")
    config_json["DEV"] = dev 
    config_json["PREFIX"] = prefix
    config_json["IP"] = ip  
    #os.system(cmd)
    #os.system("bash network_conf.sh -f enp59s0f0 -i ************** -m ************* -g ************* -d *************** ")
# 设置 NTP 服务器
    save()
def set_ntp(do_json):
    global config_json
    if "NTP" not in config_json:
        init()
    net_server_ip = do_json["ntp_server"]
    cmd = "cd ./ntp_client/ &&  ./ntp_client.sh  "+ net_server_ip
    print(cmd)
    config_json["NTP"] =  net_server_ip
    save()
    #os.system(cmd)
def  get_config_info():
    global config_json
    if "NTP" not in config_json:
        init()
    return config_json


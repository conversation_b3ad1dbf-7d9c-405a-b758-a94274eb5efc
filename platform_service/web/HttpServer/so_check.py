##
# @file so_check.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2022-07-08
import os,json,pymysql.cursors,sys,base64
from loadMysqlPasswd import  mysql_passwd
import  os
def do_cmd(cmd):
     p=os.popen(cmd)
     x=p.read()
     xl = x.split("\n")
     p.close()
     return xl[0]
def save():
    global config_json
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def write_file(filename , context):
    os.system("rm -rf "+filename)
    fnew = open(filename, "w+")
    fnew.write(context)
    fnew.close()
def write_base64_file(filename , encode_str):
    context =  base64.decodebytes(bytes(encode_str, 'utf-8')) 
    fp = open(filename, "wb")
    fp.write(context)
    fp.close()

def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def rule_so_dlsym_check(ruleid) :

    base_json = {}
    passwd = mysql_passwd()
    with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
        base_json = json.load(load_f)
    db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
    cursor = db.cursor()
    sql = "select lib_data_so from tb_rule where rule_id = "+str(ruleid)
    results  = s_mysql(sql , cursor)
    for row in results :
        sobytes = row["lib_data_so"]
        if sobytes == None:
            return "成功"
        write_base64_file("/tmp/rule_so_test.so",sobytes)
        t = do_cmd("/bin/bash judge_so.sh /tmp/rule_so_test.so ")
        os.system("rm -rf  /tmp/rule_so_test.so")
        if (t == "成功"):
            return True
        else:
            return False
        
        
if __name__=='__main__':
    if len(sys.argv)  == 2:
       ruleid = sys.argv[1]
       if ( rule_so_dlsym_check(ruleid) ) :
           print("true")
       else:
           print("false")
               

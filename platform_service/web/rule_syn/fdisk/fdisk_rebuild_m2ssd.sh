#!/bin/bash
sleep 1
/bin/bash hdd-replace.sh stopAll
umount /data
mv /opt/GeekSec/web/gocron_bak  /opt/GeekSec/web/gocron
#python3 fdisk_check.py
/bin/bash hdd-replace.sh 
python3  rebuild_fdisk_m2ssd.py
/bin/bash hdd-replace.sh  startCreateAll
sleep 3
/opt/GeekSec/STL/es_res.sh
/opt/GeekSec/STL/es_template_V3.sh
passwd=`cat /opt/GeekSec/pubconfig/.mysql`
mysql -uroot  -h127.0.0.1 -P23306 -p$passwd push_database -e  'SET SQL_SAFE_UPDATES = 0;'
mysql -uroot  -h127.0.0.1 -P23306 -p$passwd push_database -e  'update  tb_disk_type  set state = 1 ;'
#cd sql
# ./new_sql_install.sh
reboot

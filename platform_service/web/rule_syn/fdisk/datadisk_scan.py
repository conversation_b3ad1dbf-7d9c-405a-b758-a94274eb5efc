#!/usr/bin/env python2

import os
import json
import time
import sys

vds = []
slots = []
ava_vds = []
ava_slots = []
used_vds = []
used_slots = []

#Delete Foreign configuration
os.system('./storcli /c0/fall del nolog >/dev/null 2>&1')

if len(sys.argv) > 1 and sys.argv[1] == '-w':
    #clean offline vds
    fileobj = os.popen('./storcli /c0/vall show all J nolog')
    jsonobj = json.loads(fileobj.read())
    if jsonobj['Controllers'][0].has_key('Response Data'):
        for key in jsonobj['Controllers'][0]['Response Data']:
            if key.startswith(('/c0/v')):
                if jsonobj['Controllers'][0]['Response Data'][key][0]['State'] == 'OfLn' or jsonobj['Controllers'][0]['Response Data'][key][0]['State'] == 'Dgrd' or jsonobj['Controllers'][0]['Response Data'][key][0]['State'] == 'Pdgd':
                    cmd = './storcli ' + key + ' del force nolog >/dev/null 2>&1'
                    os.system(cmd)

    #clean ubad slots
    fileobj = os.popen('./storcli /c0/eall/sall show J nolog')
    jsonobj = json.loads(fileobj.read())
    if jsonobj['Controllers'][0].has_key('Response Data'):
        if jsonobj['Controllers'][0]['Response Data'].has_key('Drive Information'):
            for i in range(len(jsonobj['Controllers'][0]['Response Data']['Drive Information'])):
                drive_info = jsonobj['Controllers'][0]['Response Data']['Drive Information'][i]
                if drive_info['State'] == 'UBad':
                    eid = drive_info['EID:Slt'].split(':')[0]
                    sid = drive_info['EID:Slt'].split(':')[1]
                    cmd = './storcli /c0/e' + eid + '/s' + sid + ' set good force nolog >/dev/null 2>&1'
                    os.system(cmd)
    
    os.system('./storcli /c0/fall del nolog >/dev/null 2>&1')

fileobj = os.popen('./storcli /c0/vall show all J nolog')
jsonobj = json.loads(fileobj.read())
#print(jsonobj)
if jsonobj['Controllers'][0].has_key('Response Data'):
    for key in jsonobj['Controllers'][0]['Response Data']:
        if key.startswith(('/c0/v')):
            if jsonobj['Controllers'][0]['Response Data'][key][0]['TYPE'][:4] == 'RAID' and jsonobj['Controllers'][0]['Response Data'][key][0]['State'] == 'Optl':
                vds.append(key[5:])

fileobj = os.popen('./storcli /c0/eall/sall show J nolog')
jsonobj = json.loads(fileobj.read())
if jsonobj['Controllers'][0].has_key('Response Data'):
    if jsonobj['Controllers'][0]['Response Data'].has_key('Drive Information'):
        for i in range(len(jsonobj['Controllers'][0]['Response Data']['Drive Information'])):
            drive_info = jsonobj['Controllers'][0]['Response Data']['Drive Information'][i]
            if drive_info['State'] == 'UGood' or drive_info['State'] == 'JBOD':
                slots.append(drive_info['EID:Slt'])

if len(vds) > 0:
    for vdid in vds:
        times = 0
        while times < 5:
            fileobj = os.popen('./storcli /c0/v' + vdid + ' show all J nolog')
            jsonobj = json.loads(fileobj.read())
            if jsonobj['Controllers'][0].has_key('Response Data'):
                key = 'VD' + vdid + ' Properties'
                if jsonobj['Controllers'][0]['Response Data'].has_key(key):
                    device = jsonobj['Controllers'][0]['Response Data'][key]['OS Drive Name']
                    if device.startswith('/dev/sd'):
                        key = '/c0/v' + vdid
                        vdinfo = {'dev':device, 'size':jsonobj['Controllers'][0]['Response Data'][key][0]['Size'], 'vdid':vdid, 'type':jsonobj['Controllers'][0]['Response Data'][key][0]['TYPE'], 'slots':[]}
                        key = 'PDs for VD ' + vdid
                        for i in range(len(jsonobj['Controllers'][0]['Response Data'][key])):
                            vdinfo['slots'].append({'slot':jsonobj['Controllers'][0]['Response Data'][key][i]['EID:Slt'], 'size':jsonobj['Controllers'][0]['Response Data'][key][i]['Size'], 'intf':jsonobj['Controllers'][0]['Response Data'][key][i]['Intf'], 'med':jsonobj['Controllers'][0]['Response Data'][key][i]['Med'], 'dev':'N/A', 'type':'N/A'})
                        cmd = 'lsblk --output NAME,MOUNTPOINT ' + device + ' | grep \'/\' >/dev/null 2>&1'
                        if 0 != os.system(cmd):
                            ava_vds.append(vdinfo)
                        else:
                            cmd = 'lsblk --output NAME,MOUNTPOINT ' + device + ' | grep \'/var/ftp\' >/dev/null 2>&1'
                            if 0 == os.system(cmd):
                                vdinfo['datadisk'] = True
                                cmd = 'lsblk --output NAME,MOUNTPOINT ' + device + ' | grep \'/var/ftp\' | awk \'{print $NF}\' | xargs df | tail -n 1 | awk \'{print $5}\''
                                used_per = os.popen(cmd).read().strip()
                                vdinfo['usedp'] = used_per
                            else:
                                vdinfo['datadisk'] = False
                            used_vds.append(vdinfo)
                        break
                    else:
                        time.sleep(5)
            times += 1

if len(slots) > 0:
    for slot in slots:
        eid = slot.split(':')[0]
        sid = slot.split(':')[1]
        fileobj = os.popen('./storcli /c0/e' + eid + '/s' + sid + ' show all J nolog')
        jsonobj = json.loads(fileobj.read())
        if jsonobj['Controllers'][0].has_key('Response Data'):
            key1 = 'Drive /c0/e' + eid + '/s' + sid
            if jsonobj['Controllers'][0]['Response Data'].has_key(key1):
                if jsonobj['Controllers'][0]['Response Data'][key1][0]['State'] == 'UGood':
                    ava_slots.append({'slot':slot, 'size':jsonobj['Controllers'][0]['Response Data'][key1][0]['Size'], 'type':'UGood', 'intf':jsonobj['Controllers'][0]['Response Data'][key1][0]['Intf'], 'med':jsonobj['Controllers'][0]['Response Data'][key1][0]['Med'], 'dev':'N/A'})
                elif jsonobj['Controllers'][0]['Response Data'][key1][0]['State'] == 'JBOD':
                    key2 = 'Drive /c0/e' + eid + '/s' + sid + ' - Detailed Information'
                    key3 = 'Drive /c0/e' + eid + '/s' + sid + ' Device attributes'
                    if jsonobj['Controllers'][0]['Response Data'].has_key(key2):
                        if jsonobj['Controllers'][0]['Response Data'][key2].has_key(key3):
                            # wwn = jsonobj['Controllers'][0]['Response Data'][key2][key3]['WWN'].lower()[:-1]
                            # cmd = 'ls -l /dev/disk/by-id/ | grep ' + wwn + ' | grep -v part | grep -v grep | awk \'{print $NF}\' | sort | uniq | tail -n 1 | awk -F/ \'{print $NF}\''
                            cmd = ''
                            if jsonobj['Controllers'][0]['Response Data'][key1][0]['Intf'] == 'SAS':
                                wwn = int(jsonobj['Controllers'][0]['Response Data'][key2][key3]['WWN'], 16) // 4 * 4
                                cmd = 'ls -l /dev/disk/by-id/ | grep -e ' + ('{:02x}'.format(wwn)) + ' -e ' + ('{:02x}'.format(wwn+1)) + ' -e ' + ('{:02x}'.format(wwn+2)) + ' -e ' + ('{:02x}'.format(wwn+3)) + ' | grep -v part | grep -v grep | awk \'{print $NF}\' | sort | uniq | tail -n 1 | awk -F/ \'{print $NF}\''
                            elif jsonobj['Controllers'][0]['Response Data'][key1][0]['Intf'] == 'SATA':
                                wwn = jsonobj['Controllers'][0]['Response Data'][key2][key3]['WWN'].lower()
                                cmd = 'ls -l /dev/disk/by-id/ | grep ' + wwn + ' | grep -v part | grep -v grep | awk \'{print $NF}\' | sort | uniq | tail -n 1 | awk -F/ \'{print $NF}\''
                            if cmd == '':
                                continue
                            dev = os.popen(cmd).read().strip()
                            if dev.startswith('sd'):
                                cmd = 'lsblk --output NAME,MOUNTPOINT /dev/' + dev + ' | grep \'/\' >/dev/null 2>&1'
                                if 0 != os.system(cmd):
                                    ava_slots.append({'slot':slot, 'size':jsonobj['Controllers'][0]['Response Data'][key1][0]['Size'], 'type':'JBOD', 'intf':jsonobj['Controllers'][0]['Response Data'][key1][0]['Intf'], 'med':jsonobj['Controllers'][0]['Response Data'][key1][0]['Med'], 'dev':'/dev/'+dev})
                                else:
                                    cmd = 'lsblk --output NAME,MOUNTPOINT /dev/' + dev + ' | grep \'/var/ftp\' >/dev/null 2>&1'
                                    if 0 == os.system(cmd):
                                        cmd = 'lsblk --output NAME,MOUNTPOINT /dev/' + dev + ' | grep \'/var/ftp\' | awk \'{print $NF}\' | xargs df | tail -n 1 | awk \'{print $5}\''
                                        used_per = os.popen(cmd).read().strip()
                                        used_slots.append({'slot':slot, 'size':jsonobj['Controllers'][0]['Response Data'][key1][0]['Size'], 'type':'JBOD', 'intf':jsonobj['Controllers'][0]['Response Data'][key1][0]['Intf'], 'med':jsonobj['Controllers'][0]['Response Data'][key1][0]['Med'], 'dev':'/dev/'+dev, 'datadisk':True, 'usedp':used_per})
                                    else:
                                        used_slots.append({'slot':slot, 'size':jsonobj['Controllers'][0]['Response Data'][key1][0]['Size'], 'type':'JBOD', 'intf':jsonobj['Controllers'][0]['Response Data'][key1][0]['Intf'], 'med':jsonobj['Controllers'][0]['Response Data'][key1][0]['Med'], 'dev':'/dev/'+dev, 'datadisk':False})
bcahe_dev = []
def bcache():
    cmd = "lsblk   | awk '{print $1 }'"
    dev_str = os.popen(cmd).read().strip()
    dev_list = dev_str.split("\n")
    for i  in range(1,len(dev_list) -1 ):
        dev = dev_list[i]
        last_dev = dev_list[i+1]
        if "bcache" in last_dev:
            bcahe_dev.append(dev)
def fdisk_use(disk_list):
    for  dev_info in  disk_list :
        #dev_info = disk_list[i]
        dev_name = dev_info["dev"]
        dev_bcache_name  = dev_name[dev_name.rfind("/")+1:len(dev_name)]
        if dev_bcache_name in bcahe_dev:
            dev_name = "/dev/bcache"
        cmd = "df -T  | grep  "+dev_name+" | grep -v var | awk '{print $6}' "
        dev_list = os.popen(cmd).read().strip().split("\n")
        if (len(dev_list) == 0):
            dev_info["usr"] = "0%"
        else:
            dev_info["usr"] = dev_list[0]
            if dev_info["usr"]  == "":
                dev_info["usr"]  = "0%"
bcache()
fdisk_use(used_vds)
fdisk_use(used_slots)
           
ret = {'raid':ava_vds, 'noraid':ava_slots, 'used':{'raid':used_vds, 'noraid':used_slots}}
print(json.dumps(ret))

import sys,os
import json,time

def readfile(filename):
    fp=open(filename)
    context = fp.read()
    fp.close()
    return context
def docmd(cmd_str):
    p = os.popen(cmd_str)
    f = p.read()
    return f
  
def hdd_fdtab(hdd):
    os.system("mkdir -p /data/")
    uuid=docmd("blkid "+hdd+" | awk '{print $2}' | awk -F '=' '{print $2}' | sed 's/\"//g'").replace("\n","",10)
    if uuid  not in readfile("/etc/fstab"):
        print("echo \"UUID="+ uuid + "  /data   xfs     defaults        0 0\"  >> /etc/fstab " )
def bcache_fdtab(sdd):
    uuid=docmd("blkid "+sdd+" | awk '{print $2}' | awk -F '=' '{print $2}' | sed 's/\"//g'").replace("\n","",10)
    os.system("mkdir -p /data/.es")
    if uuid  not in readfile("/etc/fstab"):
         print("echo \"UUID="+ uuid + "  /data/.es   xfs     defaults        0 0\" >> /etc/fstab" )
hdd_fdtab("/dev/sdc")
bcache_fdtab("/dev/bcache0")

#!/usr/bin/env python2

import os
import json
import sys
import time

slots = []
add_vd_cmd_es = './storcli /c0 add vd type=raid5 drives='
add_vd_cmd_data = './storcli /c0 add vd type=raid5 drives='
jsonobj = {}

#if len(sys.argv) > 1:
#    jsonobj = json.loads(file(sys.argv[1]).read())
#else:
#    jsonobj = json.loads(sys.stdin.read())

slotnum = 0
rebuild = 1
if jsonobj.has_key('raid') and jsonobj.has_key('noraid'):
    for i in range(len(jsonobj['raid'])):
        slotnum += len(jsonobj['raid'][i]['slots'])
    slotnum += len(jsonobj['noraid'])
    if len(jsonobj['raid']) == 1 and jsonobj['raid'][0]['type'] == 'RAID5' and len(jsonobj['noraid']) == 0:
        rebuild = 0
if 0 == rebuild:
    exit(0)
elif slotnum < 3:
    exit(1)

if jsonobj.has_key('raid'):
    for i in range(len(jsonobj['raid'])):
        cmd = './storcli /c0/v' + jsonobj['raid'][i]['vdid'] + ' del force nolog >/dev/null 2>&1'
        os.system(cmd)
        time.sleep(5)
        for j in range(len(jsonobj['raid'][i]['slots'])):
            if (jsonobj['raid'][i]['slots'][j]['med'] != "SSD"):
                slots.append({'e':jsonobj['raid'][i]['slots'][j]['slot'].split(':')[0],'s':jsonobj['raid'][i]['slots'][j]['slot'].split(':')[1]})
if jsonobj.has_key('noraid'):
    #### create time key 
    for i in range(len(jsonobj['noraid'])):
        if (jsonobj['noraid'][i]['med'] != "SSD"):
            slots.append({'e':jsonobj['noraid'][i]['slot'].split(':')[0],'s':jsonobj['noraid'][i]['slot'].split(':')[1]})
            if jsonobj['noraid'][i]['type'] == 'JBOD':
                cmd = './storcli /c0/e' + jsonobj['noraid'][i]['slot'].split(':')[0] + '/s' + jsonobj['noraid'][i]['slot'].split(':')[1] + ' set good force nolog >/dev/null 2>&1'
                os.system(cmd)
                time.sleep(5)
if len(slots) < 6:
    error={"error":"磁盘数量不对，换盘数据量最少三"}
    print(json.dumps(error))
    exit(1)

es_fen=1
data_fen=1
if os.path.exists("/opt/GeekSec/pubconfig/.datafisk.json"):
    datadisk_fen=json.load(open("/opt/GeekSec/pubconfig/.datafisk.json"))
    es_fen = datadisk_fen["es"]
    data_fen =datadisk_fen["data"]
es_num = int(len(slots)* (es_fen/(es_fen+data_fen)))
if es_num < 3:
    es_num = 3 
if  len(slots) - es_num  < 3:
    es_num = len(slots) - 3
if len(slots) > 6:
    for i in range(0,es_num):
        add_vd_cmd_es += (slots[i]['e']+':'+slots[i]['s']+',')
    add_vd_cmd_es += (slots[len(slots)-1]['e']+':'+slots[len(slots)-1]['s'])
    add_vd_cmd_es += ' nolog >/dev/null 2>&1'
    os.system(add_vd_cmd_es)
    time.sleep(5)
    for i in range(es_num,len(slots)-1):
        add_vd_cmd_data += (slots[i]['e']+':'+slots[i]['s']+',')
    add_vd_cmd_data += (slots[len(slots)-1]['e']+':'+slots[len(slots)-1]['s'])
    add_vd_cmd_data += ' nolog >/dev/null 2>&1'
    os.system(add_vd_cmd_data)
    time.sleep(5)
    data={"es_fdisk":es_num , "hdd_num":len(slots) - es_num}
    json.dump(data,open("/opt/GeekSeck/pubconfig/fdisk_num.json","w+"))
    exit(0)
else:
    exit(1)

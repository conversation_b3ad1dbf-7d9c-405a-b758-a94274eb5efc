import sys,os
import json,time
### 构建 raid5 
#os.system("python3  datadisk_rebuild.py ")
base_json = {}
m2_ssd = "/dev/nvme0n1"
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
print(base_json)
if "bcache" in base_json:
    m2_ssd = base_json["bcache"]
if  os.path.exists(m2_ssd) == False:
    print("es 缓存盘不存在")
    exit(1)
#### 挂载  ### 
def readfile(filename):
    fp=open(filename)
    context = fp.read()
    fp.close()
    return context
def docmd(cmd_str):
    p = os.popen(cmd_str)
    f = p.read()
    return f 
  
def hdd_fdtab(hdd):
    os.system("mkdir -p /data/")
    uuid=docmd("blkid "+hdd+" | awk '{print $2}' | awk -F '=' '{print $2}' | sed 's/\"//g'").replace("\n","",10)
    if len(uuid) > 64:
         sys.exit(1)
    if uuid  not in readfile("/etc/fstab"):
        print("echo \"UUID="+ uuid + "  /data   xfs     defaults        0 0\"  >> /etc/fstab " )
        os.system("echo \"UUID="+ uuid + "  /data   xfs     defaults        0 0\"  >> /etc/fstab " )

def bcache_fdtab(sdd):
    uuid=docmd("blkid "+sdd+" | awk '{print $2}' | awk -F '=' '{print $2}' | sed 's/\"//g'").replace("\n","",10)
    if len(uuid) > 64:
         sys.exit(1)
    os.system("mkdir -p /data/.es")
    if uuid  not in readfile("/etc/fstab"):
         print("echo \"UUID="+ uuid + "  /data/.es   xfs     defaults        0 0\" >> /etc/fstab" )
         os.system("echo \"UUID="+ uuid + "  /data/.es   xfs     defaults        0 0\" >> /etc/fstab" )
if os.path.exists("/opt/GeekSec/pubconfig/fdisk_num.json"):
   fdisk_info = json.loads(open("/opt/GeekSec/pubconfig/fdisk_num.json"))
else:
   fdisk_info = {"es_num":1,"dev_num":1}
raid5_json = json.loads(docmd("python2 datadisk_scan.py -w ").replace("\n","",10))
hdd_dev =""
es_dev =  ""
#####
slots = []
add_vd_cmd_es = './storcli /c0 add vd type=raid5 drives='
add_vd_cmd_data = './storcli /c0 add vd type=raid5 drives='
jsonobj =  raid5_json
print(json.dumps(jsonobj))
os.system("systemctl stop docker")
####  raid 中没有 挂载的 ###### 
if "raid" in raid5_json :
    for raid_info in raid5_json["raid"]:
        #### 解除绑定 #####
        #if raid_info["type"] == "RAID0":
        #     continue
        #else:
            i = raid_info
            cmd = './storcli /c0/v' + i['vdid'] + ' del force nolog >/dev/null 2>&1'
            os.system(cmd)
            print(cmd)
            time.sleep(5)
            for j in i['slots']:
                 slots.append({'e':j['slot'].split(':')[0],'s':j['slot'].split(':')[1]})
        
if 'noraid' in jsonobj:
    #### create time key 
    for i in jsonobj['noraid']:
        if i["dev"] == m2_ssd:
             continue
        if i["med"]  == "SSD":
             if i['type'] != 'JBOD':
                  cmd = './storcli /c0/e' + i['slot'].split(':')[0] + '/s' + i['slot'].split(':')[1] + ' set jbod >/dev/null 2>&1'
             continue
        slots.append({'e':i['slot'].split(':')[0],'s':i['slot'].split(':')[1]})
        if i['type'] == 'UGood':
             cmd = './storcli /c0/e' + i['slot'].split(':')[0] + '/s' + i['slot'].split(':')[1] + ' set good force nolog >/dev/null 2>&1'
             os.system(cmd)
             slots.append({'e':i['slot'].split(':')[0],'s':i['slot'].split(':')[1]})
        if i['type'] == 'JBOD':
            cmd = './storcli /c0/e' + i['slot'].split(':')[0] + '/s' + i['slot'].split(':')[1] + ' set good force nolog >/dev/null 2>&1'
            os.system(cmd)
            time.sleep(5)
            slots.append({'e':i['slot'].split(':')[0],'s':i['slot'].split(':')[1]})
t = slots
slots = []
for i in t:
    if i not in slots :
        slots.append(i)

#slots =list(set(slots))
print(slots)
if len(slots) < 3:
    print("磁盘数量不够，最少需要六块磁盘")
    print("rebluidfail")
    sys.exit(1)
es_fen=fdisk_info["es_num"]
data_fen=fdisk_info["dev_num"]
#es_num = int(len(slots)* (es_fen/(es_fen+data_fen)))
#if es_num < 3:
 #   es_num = 3 
#if  len(slots) - es_num  < 3:
#    data_num = len(slots) - 3
num = 0
for i in range(0,len(slots)):
    if num  == 0 :
         add_vd_cmd_data += (slots[i]['e']+':'+slots[i]['s'])
    else :
         add_vd_cmd_data += (','+slots[i]['e']+':'+slots[i]['s'])
    num +=1 
    print("num ==== " , num ," i ==== ", i)
add_vd_cmd_data += ' nolog >/dev/null 2>&1'
print(add_vd_cmd_data)
os.system(add_vd_cmd_data)
time.sleep(5)
raid5_json = json.loads(docmd("python2 datadisk_scan.py -w ").replace("\n","",10))
print(json.dumps(raid5_json))
lenraid5 = 0 
raid5id = 0 
if "raid" in raid5_json:
   if len(raid5_json["raid"]) > 0 : 
     for  i in range(0,len(raid5_json["raid"])):
       v = raid5_json["raid"][i]
       if v["type"]  == "RAID5":
           lenraid5 += 1
           raid5id =  i
       if len(raid5_json["raid"]) == 1:
          hdd_dev = raid5_json["raid"][raid5id]["dev"]
       else:
           print("错误： raid 数据不对")
           sys.exit(1)
if  hdd_dev != "" :
   os.system("mkfs.xfs -f "+hdd_dev  +"  && mkdir -p /data/  && mount " +hdd_dev+ " /data/")
   os.system("mkdir -p /data/.es")
   os.system("mount " +m2_ssd + " /data/.es/")
   ### 重新构建 bcache ###
  

##### 添加开机自启动
os.system("systemctl restart docker")
os.system("/bin/bash hdd-replace.sh satrtCreateAll")
os.system("sed -i '/data/d' /etc/fstab  ")
hdd_fdtab(hdd_dev)
bcache_fdtab(m2_ssd)
#bcache_fdtab("/dev/bcache0")

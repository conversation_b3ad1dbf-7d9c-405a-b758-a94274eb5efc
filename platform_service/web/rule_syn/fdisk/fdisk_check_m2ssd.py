import sys,os
import json,time
def docmd(cmd_str):
    p = os.popen(cmd_str)
    f = p.read()
    return f 

m2_ssd = "/dev/nvme0n1"
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
if "bcache" in base_json:
    m2_ssd = base_json["bcache"]
######## ####### 
fidls= docmd(" lsblk -r ").split("\n")
#### 
num = 0
bcache_fdisk =  {}
fdsik =[]
for i in fidls:
   if i.startswith("bcache"):
        if num > 0:
           fdisk = fidls[num -1].split(" ")
           fdisk_name  = fdisk[0]
           bcache_info = i.split(" ") 
           if bcache_info[0] not in bcache_fdisk :
               bcache_fdisk[bcache_info[0]]= {"sum":bcache_info[3],"fdisk":[fdisk_name],"mount":bcache_info[len(bcache_info) -1]} 
           else:
               bcache_fdisk[bcache_info[0]]["fdisk"].append(fdisk_name)
              
   num +=1
if len(bcache_fdisk)  > 0:
    for i  in bcache_fdisk:
       fb = bcache_fdisk[i]
       if len(fb["fdisk"])  == 2:
           os.system("./stop_bcache.sh "+i+" /dev/"+fb["fdisk"][0] + " /dev/"+fb["fdisk"][1])      
           os.system("./stop_bcache.sh "+i+" /dev/"+fb["fdisk"][1] + " /dev/"+fb["fdisk"][0])      
       for dev in fb["fdisk"]:
           os.system("dd if=/dev/zero of=/dev/"+dev+" bs=2M count=1")
    os.system("sed -i '/data/d' /etc/fstab")
    print("reboot")
    os.system("reboot")

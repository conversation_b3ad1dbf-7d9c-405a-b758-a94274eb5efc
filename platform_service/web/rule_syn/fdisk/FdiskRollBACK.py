##
# @file FdiskRollBACK.py
# @brief: 磁盘回滚 
# <AUTHOR>
# @version 0.1.00
# @date 2022-05-23
#### 按日期 会馆
import json,os,time,sys
from elasticsearch import Elasticsearch
import psutil
import datetime as DT
import datetime 
from loadMysqlPasswd import mysql_passwd
from elasticsearch import Elasticsearch
passwd = mysql_passwd()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
es_ip = base_json["es_es"]
es = Elasticsearch([es_ip])
#index_list = ["connectinfo","http","dns","ssl","ssh","s7","modbus","noip","mac"]
def del_es(index_name):
    cmd  =" curl -XDELETE "+es_ip+"/"+index_name
    #print(cmd)
    os.system(cmd)
def def_es_other_data():
    cmd  =" curl -XDELETE "+es_ip+"/*19700101*"
    #print(cmd)
    os.system(cmd)
    ### 删除其他没有删除索引的  
def reset_es_index():
    del_es("es_index")
    os.system("python3 /opt/GeekSec/web/gocron/script/elasticsearch_meta_index_rebuilder.py")

def is_number(s):
    try:
        float(s)
        return True
    except ValueError:
        pass
    return False
def del_es_index(its,task_id = None):
    if task_id == None:
         body_ac={"query":{"bool":{"must":{"range":{"last_time":{"lt":its}}}}}}
    else:
        body_ac={"query":{"bool":{"must":[{"range":{"last_time":{"lt":its}}},{"terms":{"task":task_id}}]}}}
    #print(body_ac)
    es.delete_by_query(index="es_index",body=body_ac)

## 
def del_index_nodel_date(its,task_id = None ):
    if task_id == None:
        body_ac={"query":{"bool":{"must":{"range":{"StartTime":{"lt":its}}}}}}
    else:
        body_ac = {"query":{"bool":{"must":[{"range":{"last_time":{"lt":its}}},{"term":{"task":{"value":task_id}}}]}}}
    es.delete_by_query(index="connectinfo_*",body=body_ac)
    
def str_to_timestamp(str_time=None, format='%Y-%m-%d %H:%M:%S'):
    if str_time:
        time_tuple = time.strptime(str_time, format)  # 把格式化好的时间转换成元祖
        result = time.mktime(time_tuple)  # 把时间元祖转换成时间戳
        return int(result)
    return 0

def del_index_fdisk(date_list,mydir):
    for filename in os.listdir(mydir):
        filepath=os.path.join(mydir,filename)
        if os.path.isdir(filepath) :
            if filename in date_list:
                print("rm -rf "+filepath)
                os.system("rm -rf "+filepath)
            else:
                del_index_fdisk(date_list , filepath)
def del_pcap(st_l):
    #st =  st_l[0:3]+"-"+st_l[4:5]+"-"+st_l[6:7]+" 00:00:00"
    #print(st)
    #hosur_list = []
    hosur  = int(ts/(3600*4))
    for i  in range(6):
        hosur_list = [str(hosur+i)]
        #print("hosur_list ===== ",hosur_list) 
        del_index_fdisk(hosur_list,"/data/pcapfiles/")
        del_index_fdisk(hosur_list,"/data/pbfiles/")
        del_index_fdisk(hosur_list,"/data/json_file_send_done/")

def del_index_fdisk_2(date_l,mydir):
    for filename in os.listdir(mydir):
        filepath=os.path.join(mydir,filename)
        if os.path.isdir(filepath) :
            if len(filename ) > 6  and filename.isnumeric() == True:
                if int(filename) < date_l:
                    print("rm -rf "+filepath)
                    os.system("rm -rf "+filepath)
                else:
                  del_index_fdisk_2(date_l , filepath)
            else:
                del_index_fdisk_2(date_l , filepath)

def del_pcap_2(ts):
    #st =  st_l[0:3]+"-"+st_l[4:5]+"-"+st_l[6:7]+" 00:00:00"
    #print(st)
    #hosur_list = []
    hosur  = int(ts/(3600*4))
    #for i  in range(6):

    del_index_fdisk_2(hosur,"/data/pcapfiles/")
    del_index_fdisk_2(hosur,"/data/pbfiles/")
    del_index_fdisk_2(hosur,"/data/json_file_send_done/")
def del_json_data(ts,bFullFlow,bToRuleSave,task_id): 
    date_list=[st_l]
    del_index_fdisk(date_list,"/data//")
def Full2Rule(filepath):
    pathlist = filepath.split("/")
    num = 0
    filename = pathlist[len(pathlist)-1]
    #print("filename ==== ", filename)
    path = filepath.replace("full_flow","rule",1)
    path = path[0:path.rfind("/")]
    #print(path)
    os.system("mkdir -p "+path)  
    cmd = "/opt/GeekSec/th/bin/pcap_filter -r "+filepath+" -w "+os.path.join(path,filename)+" -rule"
    #print(cmd)
    os.system(cmd)
#  ts 删除的时间   bFullFlow 是全流量数据还是规则留存数据  bToRuleSave 是否
hbase_last_time = 0
hbase_begin_time = 0 
def get_del_hbase_time(filename , date_1) :
    global hbase_last_time 
    global hbase_begin_time
    begin_time  = int(filename)
    last_time = int(date_1)
    if hbase_last_time < last_time:
        hbase_last_time = last_time 
    if hbase_begin_time > begin_time:
        hbase_begin_time = begin_time
def startTimeDay(timeStamp_checkpoint):
    timeArray = time.localtime(timeStamp_checkpoint)
    checkpoint = time.strftime("%Y%m%d", timeArray)
    return checkpoint
def del_hbase_data():
    if hbase_last_time == hbase_begin_time :
        return 
    else :
        hbtime = hbase_begin_time * 4 * 3600 
        hbltime = hbase_last_time  * 4 * 3600
        for i in range(hbtime, hbltime,24*3600):
            date_str  =  startTimeDay(hbtime + 1)
            cmd = " docker  exec  webhandleD  java -jar HbaseDel-1.0-SNAPSHOT.jar " + date_str
            print(cmd)
            os.system(cmd)

def del_pcap_new(path , bFullFlow , bToRuleSave ,date_1):
    #print("del_pcap_new " , path)
    if os.path.exists(path) == False:
           return 
    if os.path.isdir(path) == False:
           return  
    #print("del_pcap_new " , path)
    for filename in os.listdir(path):
        filepath=os.path.join(path,filename)
        if os.path.isdir(filepath) :
            if len(filename ) > 5  and filename.isnumeric() == True:
                #print(filename   , " ==== " , date_1)
                if int(filename) < date_1:
                    get_del_hbase_time(filename , date_1 )
                    if bFullFlow == True  and bToRuleSave == True:
                       filelist = os.listdir(filepath)
                       for fname  in filelist:
                          Full2Rule(os.path.join(filepath,fname))
                    #print("rm -rf "+filepath)
                    os.system("rm -rf "+filepath)
            else:
                del_pcap_new(filepath,bFullFlow,bToRuleSave , date_1)
def del_pcap_task_id(hours,bFullFlow , bToRuleSave,task_id):
    for task_id_one in task_id:
       path = os.path.join("/data/",str(task_id_one))
       batchlist = os.listdir(path)
       for i in batchlist :
           batchpath = os.path.join(path,i,"pcapfiles")
           threadlist = os.listdir(batchpath)
           for thread in threadlist:
               threadpath = os.path.join(batchpath,thread)
               if os.path.isdir(threadpath):
                  if bFullFlow == True :  ### 删除全流量数据
                      del_pcap_new(os.path.join(threadpath,"full_flow"),True,bToRuleSave,hours)
                  else:              #### 删除规则数据
                      print("path ========" ,  threadpath)
                      del_pcap_new(os.path.join(threadpath,"rule"),False,False , hours)
def del_pcap_merage(st , bFullFlow , bToRuleSave ,task_id = None ):
    hosur  = int(st/(3600*4))
    print("st ======= " ,st,"hosur ==== ", hosur)
    if task_id == None :
        pathlist = os.listdir("/data")
        for i in pathlist :
            if is_number(i) == True:
                del_pcap_task_id(hosur,bFullFlow , bToRuleSave , i)
    else:
        del_pcap_task_id(hosur,bFullFlow , bToRuleSave , task_id)

    # 删除文件
def  del_pb_data(): 
     today = DT.date.today()
     ts   = int(int(time.time())/(3600*24) - 7 ) * 3600*24
def get_del_date(i):
    global ts
    ts   = int(int(time.time())/(3600*24) - i ) * 3600*24
def get_del_time(i):
    global ts
    ts   = int(time.time())  - i
### 取 7 天前的时间
def scan_es_new(num_day,bFullFlow , bToRuleSave ,tasl_id = None):
     print("num_day =====   ", num_day)
     if num_day   > 3600:
          get_del_time(num_day)
          print("ts ==== ",ts)
          time.sleep(1)
     else:
          get_del_date(num_day)
     del_pcap_merage(ts , bFullFlow , bToRuleSave ,task_id )
     if task_id == None :
         body_ac = {"query":{"bool":{"must":[{"range":{"last_time":{"lt":ts}}}],"must_not":[{"range":{"first_time":{"lte":0}}}]}},"aggs":{"min_time":{"min":{"field":"first_time"}}}}
     else:
         #body_ac = {"query":{"bool":{"must":[{"range":{"last_time":{"lt":ts}}},{"terms":{"task":{"value":task_id}}}],"must_not":[{"range":{"first_time":{"lte":0}}}]}},"aggs":{"min_time":{"min":{"field":"first_time"}}}}
         body_ac = {"query":{"bool":{"must":[{"range":{"last_time":{"lt":ts}}},{"terms":{"task":task_id}}],"must_not":[{"range":{"first_time":{"lte":0}}}]}},"aggs":{"min_time":{"min":{"field":"first_time"}}}}
     #print(json.dumps(body_ac))
     result =  es.search(index="es_index",body=body_ac)
     index_list = []
     #print(result) 
     for  hits in result["hits"]["hits"]:
         index_list.append(hits["_source"]["index"])
     #index_first_time  = int(int(result["aggregations"]["min_time"]["value"])/(24*3600))*3600*24
     timeArray = time.localtime(ts)
     day = time.strftime("%Y%m%d", timeArray)
     print("del === ",day)
         #del_pcap_new(i)
     for index_name in index_list:
         del_es(index_name)
     def_es_other_data()
     del_es_index(ts)
     #del_hbase_data()
## 旧版索引删除数据
# 
def cleanDateFdisk(day,fdiskS,bFullFlow , bToRuleSave , task_id):
    t = day
    if len(task_id) == 0 :
         scan_es_new(day,bFullFlow , bToRuleSave ,None)
    else:
         for task in  task_id:
             scan_es_new(day,bFullFlow , bToRuleSave ,task)
    t = t -1
    print("t ===== " , t) 
    time.sleep(1)
    while(t >  0):
       fdisk=psutil.disk_usage('/data')
       print("fdisk.percent  ===  ",fdisk.percent )
       time.sleep(1)
    
       if  fdisk.percent > fdiskS:
          if len(task_id) == 0 :
               scan_es_new(t,bFullFlow , bToRuleSave ,None)
          else:
              for task in  task_id:
                 scan_es_new(t,bFullFlow , bToRuleSave ,task)
       else:
           break
       t = t-1
          #else:
          #  fdisk=psutil.disk_usage('/data')
          #  if  fdisk.percent > fdiskS:
          #    if len(task_id) == 0 :
          #        scan_es_new(1,bFullFlow , bToRuleSave ,None )
          #    else:
          #        for task in  task_id:
          #            scan_es_new(day,bFullFlow , bToRuleSave ,task)
          #  else:
          #      break
    #####  删除 分规则后的数据 
    if bFullFlow ==True:
        t = day
        print(" t ===== ", t)
        while(t > 0):
           fdisk=psutil.disk_usage('/data')
           print("fdisk.percent  ===== " ,fdisk.percent )
           if (fdisk.percent  < fdiskS) :
               break
           else:
              if len(task_id) == 0 :
                   scan_es_new(t, False, bToRuleSave ,None)
              else:
                 for task in  task_id:
                      scan_es_new(t,False, bToRuleSave ,task)
           t = t-1
    print("del day data ")
    time.sleep(1)
    fdisk=psutil.disk_usage('/data')
    t = 3600* 24
    if (fdisk.percent  > fdiskS) :
         while(t > 0): 
           if (fdisk.percent  < fdiskS) :
               break
           else:
              tss = int((int(time.time()) -  t)/(3600*4))*(3600*4) 
              tss += 4*3600
              if len(task_id) == 0 :
                  del_pcap_merage(tss , bFullFlow ,bToRuleSave ,task_id )
              else:
                 for task in  task_id:
                      scan_es_new(tss,bFullFlow, bToRuleSave ,task)
                      del_pcap_merage(tss , True ,False ,task_id )
           t = t - 3600 * 4
           print("======= t  ===== ", t)
  #### type :目前只支持date  num: 保留数据的天数  如果 保留很多天这样可以停歇 10000 ， fdisk 删除完成后存储保持的高玻璃   bFullFlow : 是否是开启全流量留存储  bToRuleSave: 是否抽取全流量到规则留存 , TaskId 删除的任务ID :
####{\"type\":\"date\",\"num\":7,\"fdisk\":80,\"bFullFlow\":true,\"bToRuleSave\":true,\"TaskId\":[0,1]}
if __name__=='__main__':
    if len(sys.argv) == 2:
        del_info = json.loads(sys.argv[1])
        if del_info["type"]  == "date":
            bFullFlow = del_info["bFullFlow"]
            bToRuleSave = del_info["bToRuleSave"]
            task_id = del_info["TaskId"]
            if task_id == -1:
                task_id = None
            day = del_info["num"]
            fdiskS =  del_info["fdisk"]
            cleanDateFdisk(day,fdiskS,bFullFlow , bToRuleSave , task_id)
            reset_es_index()
            #del_hbase_data()
    else:
        print("参数错误")
        sys.exit(1)

##
# @file mount_fdisk.py
# @brief  : 重新挂载 数据盘
# <AUTHOR>
# @version 0.1.00
# @date 2022-08-03

import sys,os
import json,time
def docmd(cmd_str):
    p = os.popen(cmd_str)
    f = p.read()
    return f 

def readfile(filename):
    fp=open(filename)
    context = fp.read()
    fp.close()
    return context
m2_ssd = "/dev/nvme0n1"
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
if "bcache" in base_json:
    m2_ssd = base_json["bcache"]
######## ####### 
def hdd_fdtab(hdd):
    os.system("mkdir -p /data/")
    uuid=docmd("blkid "+hdd+" | awk '{print $2}' | awk -F '=' '{print $2}' | sed 's/\"//g'").replace("\n","",10)
    if len(uuid) > 64:
        sys.exit(1)
    if uuid  not in readfile("/etc/fstab"):
        #print("echo \"UUID="+ uuid + "  /data   xfs     defaults        0 0\"  >> /etc/fstab " )
        os.system("echo \"UUID="+ uuid + "  /data   xfs     defaults        0 0\"  >> /etc/fstab " )

fidls= docmd(" lsblk -r ").split("\n")
#### 
num = 0
bcache_fdisk =  {}
fdisk_bcache =  {}
bcache_list = []
fdsik =[]
for i in fidls:
   if i.startswith("bcache"):
        if num > 0:
           fdisk = fidls[num -1].split(" ")
           fdisk_name  = fdisk[0]
           bcache_info = i.split(" ") 
           if bcache_info[0] not in bcache_fdisk :
               bcache_fdisk[bcache_info[0]]= {"sum":bcache_info[3],"fdisk":[fdisk_name],"mount":bcache_info[len(bcache_info) -1]} 
               bcache_list.append(bcache_info[0])
           else:
               bcache_fdisk[bcache_info[0]]["fdisk"].append(fdisk_name)
              
   num +=1
#print(bcache_fdisk)
def isBcache(hdd):
     for i in bcache_fdisk:
        for vi in bcache_fdisk[i]["fdisk"]:
            #vi = bcache_fdisk[i]["fdisk"]
            if  hdd == vi :
              return True
     return False
raid5_json = json.loads(docmd("python2 datadisk_scan.py ").replace("\n","",10))
hddlist = []
if "raid" in raid5_json :
    for raid_info in raid5_json["raid"]:
        dev_name_list =  raid_info["dev"].split("/")
        dev_name = dev_name_list[2]
        if  raid_info["type"] == "RAID0":
            break
        else:
            hddlist.append(dev_name)
if len(hddlist) == 1:
    os.system("  /bin/bash /bin/bash hdd-replace.sh stopAll ")
        #### 解除绑定 #####
    hdd_fdtab("/dev/"+hddlist[0])
    os.system("mount  /dev/"+hddlist[0] + " /data")
    #bcache_fdtab("/dev/"+bcache_list[0])
    #os.system("mount  /dev/"+bcache_list[0] + " /data/.es/")
    os.system(" rm -rf  /data/.es/    &&  cd /data &&   mv .es_bak   .es ")
    os.system("chown -R admin:root /data/.es")
    os.system("  /bin/bash hdd-replace.sh startCreateAll ")
    os.system("mv /opt/GeekSec/web/gocron  /opt/GeekSec/web/gocron_bak ")
    res={"status":"true","msg":"挂载成功"}
    print(json.dumps(res))
else:
    res={"status":"false","msg":"非正常分析磁盘"}
    print(json.dumps(res))

#######    ############ 
#os.system("/bin/bash  set_database.sh")

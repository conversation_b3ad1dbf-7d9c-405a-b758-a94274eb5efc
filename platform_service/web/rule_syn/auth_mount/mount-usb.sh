#!/bin/sh
echo "aaaa   ">>/mnt/usb/mount.log
if [ $ACTION == "add" ]
then
    mkdir -p /mnt/usb/$1
    chmod 777 /mnt/usb/$1
    mount  /dev/$1 /mnt/usb/$1
    sync
    echo "add /mnt/usb/$1">>/mnt/usb/mount.log
elif [ $ACTION == "remove" ]
then
    sync
    fuser -ck /mnt/usb/$1
    umount -f /mnt/usb/$1
    umount  /mnt/usb/$1
    rm -rf /mnt/usb/$1
    echo "remove /mnt/usb/$1">>/mnt/usb/mount.log
fi

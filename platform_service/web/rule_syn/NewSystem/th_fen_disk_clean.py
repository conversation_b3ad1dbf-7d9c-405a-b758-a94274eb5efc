#!/bin/python3
import json
import psutil 
import sys,os,time
from full2rule import full2rule

def  do_cmd (cmd):
    r = os.popen(cmd).read()
    print(r)
    return r
begin_time = 0
endtime = 0
# 检测时间和索引-
def local_time_str(ts):
    timeArray = time.localtime(ts)
    otherStyleTime = time.strftime("%Y%m%d", timeArray)
    return  otherStyleTime
def file_max_and_min_time(path):
    global begin_time
    global endtime
    fi = do_cmd("ls -l "+path+" | awk {'print $9'}")
    fi_list = fi.split("\n")
    fi_list = fi_list[1:len(fi_list)-1]
    if len(fi_list) > 0:
        begin_time  = (int(fi_list[0])) * 3600 * 4
        endtime = int(fi_list[len(fi_list) - 1]  ) *3600 * 4
        return True
    return False


def thd_pb_file(path,name):
    if path.endswith(name) or path.endswith(name+"/"):
        return True;
    else :
        if "/"+name+"/" in path :
            path_ = path[0:path.find("/"+name+"/")+len("/"+name+"/")]
            fpath = path[0:path.rfind("/")]
            if file_max_and_min_time(fpath) == True:
                  return True
    return False

def scan_file_type(file_path,b_rule=False):
        pbfile_path = file_path
        if "pbfiles" not in file_path :
            pbfile_path = os.path.join(file_path, "pbfiles")
        file_list = os.listdir(pbfile_path)
        for file in file_list:
            if file == "rule/" or file == "rule":
                if b_rule == False:
                    continue
                else:
                    continue

                
            file_path_ = os.path.join(pbfile_path, file)
            if os.path.isdir(file_path_) or  os.path.islink(file_path_) :
                ret = scan_file_type(file_path_)
                if ret != "" and ret != None: 
                    return "3"+ret[1:len(ret)]
            else :
                file_str= str(file)
                file_name_list = file_str.split(".")
                file_exp = file_name_list[-1]
                if file_exp  == "pb":
                    if  thd_pb_file(pbfile_path,"pbfiles") == True :
                        return  "3|"+str(begin_time)+"|"+str(endtime)
def del_files(path,filed,b_rule=False):
    cmd = "find " + path + " -name "+ filed
    ipath =  do_cmd(cmd)
    ipathList = ipath.split("\n")
    for row in ipathList :
        if row == "":
            continue
        if row.find("/rule/")> 0 :
            if b_rule == False:
                continue 
            else :
                continue

        print("rm -rf "+row)
        os.system("rm -rf "+row)
def del_rule_disk(path):
    print("del_rule_disk    ["+path+"]")
    while True:
        fdisk=psutil.disk_usage(path+"/")
        print(fdisk.percent)
        #if int(fdisk.percent) >= 0 :
        if fdisk.percent > 70 :
            print("path === ", path)
            scan_file_type(path,True)
            date_str = local_time_str(begin_time)
            ### 前7天停止删除
            if int(time.time()) -  begin_time  < 3600* 24 - 7: 
                return

            if date_str != "":
                os.system("python3 del_es.py "+date_str)
            ##### 查询ES 所有命中规则的 session id 
            del_beg_file = int(begin_time /3600/4)
            del_end_file = del_beg_file + 6
            for i in range(del_beg_file , del_end_file):
                if path == "":
                    continue
                del_files(os.path.join(path ,"pbfiles"),str(i),True)
        else :
            break
def del_disk(path):
    print("del_disk    ["+path+"]")
    while True:
        fdisk=psutil.disk_usage(path+"/")
        print(fdisk.percent)
        #if int(fdisk.percent) >= 0 :
        if fdisk.percent > 70 :
            print("path === ", path)
            scan_file_type(path)
            date_str = local_time_str(begin_time)
            ### 前三天停止删除
            if int(time.time()) -  begin_time  < 3600* 24 -4 : 
                del_rule_disk(path)
                return 

            if date_str != "":
                os.system("python3 del_es.py "+date_str)
            ##### 查询ES 所有命中规则的 session id 
            del_beg_file = int(begin_time /3600/4)
            del_end_file = del_beg_file + 6
            full2rule(del_beg_file,del_end_file,os.path.join(path,"pcapfiles"))
            for i in range(del_beg_file , del_end_file):
                if path == "":
                    continue
                del_files(os.path.join(path ,"pbfiles"),str(i))
                del_files(os.path.join(path,"pcapfiles"),str(i))
            time.sleep(10)
        else :
            break
#  删除 攻击数据
def del_attack_pcap(path):
    scan_file_type(path)
    btime = int(begin_time /3600/4)
    etime = int(endtime /3600/4)+6
    ####  ####
    for  i in range(btime,etime,6):
        timeArray = time.localtime(i * 3600*4)
        otherStyleTime = time.strftime("%Y-%m-%d %H:%M:%S",timeArray)
        print("python3 dell2pcap.py \""+ otherStyleTime + "\" \"attack\" \""+path+"\"")
        os.system("python3 dell2pcap.py \""+ otherStyleTime + "\" \"attack\" \""+path+"\"")
        os.system("python3 dell2pcap.py \""+ otherStyleTime + "\" \"noip_packet\" \""+path+"\"")
        
def check_fdisk():
    cmd = "df -T | grep /var/ftp  | awk '{print $6 $7}'"
    r = do_cmd(cmd)
    rlist = r.split("\n")
    print(rlist)
    for row in rlist:
       if row =="":
           continue
       ik=  row.split("%")
       print(ik)
       print(int(ik[0]))
       if int(ik[0]) > 80:
           del_attack_pcap(ik[1])
       if int(ik[0]) > 80:
           del_disk(ik[1])
check_fdisk()

# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2022-05-23

import happybase
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
def delete_habsedata(index_name ,data_list):
    conn = happybase.Connection(base_json["hbase_ip"], base_json["hbase_port"])
    print (conn.tables())
    table = conn.table("PbData")
    for date_s in data_list :
          table.deleteall(row=inex_name+"_"+date_s)

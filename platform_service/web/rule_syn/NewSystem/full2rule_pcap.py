##
# @file full2rule_pcap.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2022-08-09

import json,os,time,sys,json
from elasticsearch import Elasticsearch
import pymysql 
import psutil
import datetime as DT
import datetime
from loadMysqlPasswd import mysql_passwd
import psutil
passwd = mysql_passwd()
base_json = {}
def  cmd_run(cmd_str):
    print(cmd_str)
    p=os.popen(cmd_str)
    x=p.read()
    p.close()
    return x
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
es_ip = base_json["es_es"]
es = Elasticsearch([es_ip])
def is_number(s):
    try:
        float(s)
        return True
    except ValueError:
        pass
    return False
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def es_session_search_rule(mints  , maxts ):
    session_id_list  = []
    body_ac = {"_source":["SessionId"],"query": {
    "bool": {
     "must": [
       {
         "range": {
           "Labels": {
             "gte": 35000,
             "lte": 150000
           }
         }
       },
       {
         "range": {
           "StartTime": {
             "lte": mints,
           }
         }
       },{
           "range": {
             "EndTime":{
             "gte": maxts,
             }
         }
       }
     ]
   }
 }
}
    index_name= "connect*"
    result = es.search(index=index_name,body=body_ac,request_timeout=120)
    
    for row in result["hits"]["hits"]:
        session_id_list.append(row["_source"]["SessionId"])
    return session_id_list
def CreateTagList()  :
    ## 
    SaveTagId = []
    db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
    cursor = db.cursor()
    sql  = "select tag_id  from th_analysis.tb_tag_info where default_black_list > 0 and (tag_target_type = 7 or tag_target_type = 9999);" 
    reslt = s_mysql(sql ,cursor)
    for row in reslt:
        SaveTagId.append(row["tag_id"])
    return SaveTagId
tag_list = CreateTagList()
def es_session_search_tag(mints , maxts ):
    session_id  = []
    body_ac  = {
  "_source": ["SessionId"],
 "query": {
   "bool": {
     "must": [
       {
         "terms": {
           "Labels": tag_list
         }
       },
       {
         "range": {
           "StartTime": {
             "lte": mints,
           }
         }
       },{
           "range": {
             "EndTime":{
             "gte": maxts,
             }
         }
       }
     ]
   }
 }
}
    index_name = "connectinfo*"
    result = es.search(index=index_name,body=body_ac,request_timeout=120)
    for row in result["hits"]["hits"]:
        session_id.append(row["_source"]["SessionId"])
    return session_id

def GetRuleSessionList(mints , maxts):
    rule_session_list = es_session_search_rule(mints,maxts)
    tag_session_list = es_session_search_tag(mints , maxts)
    session_list = rule_session_list + tag_session_list 
    return list(set(session_list))
#### 文件 时间
def Full2Rule(filepath,mints , maxts ):
    session_id = GetRuleSessionList(mints,maxts)
    if len(session_id) == 0:
        return
    f = open(".session_id_list","w")
    for  s in session_id: 
        f.write(s+"\n")
    f.close()
    pathlist = filepath.split("/")
    num = 0
    filename = pathlist[len(pathlist)-1]
    #print("filename ==== ", filename)
    path = filepath.replace("full_flow","rule",1)
    path = path[0:path.rfind("/")]
    #print(path)
    os.system("mkdir -p "+path)  
    cmd = "/opt/GeekSec/th/bin/pcap_filter -r "+filepath+" -w "+os.path.join(path,filename)+" -ssidfile  .session_id_list"
    print(cmd)
    os.system(cmd)
    os.system("rm -rf .session_id_list")
def startTimeDay(timeStamp_checkpoint):
    timeArray = time.localtime(timeStamp_checkpoint)
    checkpoint = time.strftime("%Y%m%d", timeArray)
    return checkpoint

def del_pcap_new(path , date_1):
    if os.path.exists(path) == False:
           return 
    if os.path.isdir(path) == False:
           return  
    mints_t = date_1 * 4*3600
    maxts_t = date_1 +1 * 4*3600
  
    for filename in os.listdir(path):
        filepath=os.path.join(path,filename)
        if os.path.isdir(filepath) :
            if len(filename ) > 5  and filename.isnumeric() == True:
                file_list = []
                file_tt_list = []
                if int(filename) ==  date_1:
                   filelist = os.listdir(filepath)
                   for fname  in filelist:
                       file_tt_list.append(fname.split("_")[0])
                       file_list.append(os.path.join(filepath,fname))
                   print(file_list)
                   for i in range(0,len(file_list)):     
                      mints = file_tt_list[i] 
                      if i < len(file_tt_list) -1:
                          maxts = file_tt_list[i+1]
                      else:
                          maxts = maxts_t
                        
                      Full2Rule(file_list[i],mints , maxts)
            else:
                del_pcap_new(filepath, date_1)
def del_pcap_task_id(hours,task_id):
    for task_id_one in task_id:
       path = os.path.join("/data/",str(task_id_one))
       batchlist = os.listdir(path)
       for i in batchlist :
           batchpath = os.path.join(path,i,"pcapfiles")
           threadlist = os.listdir(batchpath)
           for thread in threadlist:
               threadpath = os.path.join(batchpath,thread)
               if os.path.isdir(threadpath):
                   del_pcap_new(os.path.join(threadpath,"full_flow"),hours)
def del_pcap_merage(st ,task_id = None ):
    hosur  = int(st/(3600*4)) -1
    ### 
    print("st ======= " ,st,"hosur ==== ", hosur)
    if task_id == None :
        pathlist = os.listdir("/data")
        for i in pathlist :
            if is_number(i) == True:
                del_pcap_task_id(hosur,i)

def BeginDone(ts):
    hosur  = int(ts/(3600*4)) -1
    if os.path.exists(".full2rule_pcap_done.txt"):
        done_t = json.load(open(".full2rule_pcap_done.txt"))
        print(hosur ,"=====" ,  done_t["done"] )
        if hosur <= done_t["done"] :
            print("handle do done")
            return False
    ####### 系统信息判断 
    h = ts - (3600*4) - (hosur * 3600*4)
    if  h > 3600 * 2:
        done_t = {"done":hosur}
        json.dump(done_t,open(".full2rule_pcap_done.txt","w"))
        return True
    else:
        cpu_status = psutil.cpu_percent(0)
        iostate_str = cmd_run("iostat  -x  | grep sdb  | awk '{print $14}'").replace("\n","",10)
        print(iostate_str)
        iostate = int(iostate_str.split(".")[0])
        print("iostate ====" , iostate)
        print("cpu  ====" , cpu_status)
        if iostate <  80 and cpu_status < 80:
            print("done ----- ")
            done_t = {"done":hosur}
            json.dump(done_t,open(".full2rule_pcap_done.txt","w"))
            return True
    return False
         
if __name__=='__main__':
    ts = int(time.time())
    if BeginDone(ts):
       del_pcap_merage(ts)

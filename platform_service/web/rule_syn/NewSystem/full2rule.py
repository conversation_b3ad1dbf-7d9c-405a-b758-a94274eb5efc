##
# @file full2rule.py
# @brief :  清理指定时间的数据 ， 包括## ES  /  全流量 流程数据 
# <AUTHOR>
# @version 0.1.00
# @date 2021-03-03


## 确定需求清理数据的日期 

import json, os, pymysql.cursors, time,sys

#sys.setrecursionlimit(1000000)
#from elasticsearch import Elasticsearch
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)

es_ip = base_json["es_es"]
#es =Elasticsearch([es_ip])

def is_number(s):
   try:
       float(s)
       return True
   except ValueError:
       pass

   try:
       import unicodedata
       unicodedata.numeric(s)
       return True
   except (TypeError, ValueError):
      pass
   return False
#
def marget_pcap(src_file_list , terget_file) :
    if len(src_file_list) == 0:
        return 
    #bash mergecap_ext.sh -w output.pcap 1.pcap 2.pcap 3.pcap
    cmd = "/bin/bash mergecap_ext.sh -w "+terget_file +" "
    for i in src_file_list:
        cmd += " "+i
    print(cmd)
    os.system(cmd)

num = 0
def transfPcap(src_path):
    print("transfPcap ***********  \n ")
    sfilelist = os.listdir(src_path) #list(set(os.listdir(src_path)))
    sfilelist.sort() #list(set(os.listdir(src_path)))
    print("sfilelist  ====== " , sfilelist)
    sum_fsize =  0
    begtime = ""
    global num
    src_file_list  = []
    tmp_file = "/data/file" 
    terget_file = ""
    os.system("mkdir -p "+ tmp_file)
    for file_name in sfilelist:
        if file_name.endswith(".pcap") :
            if True:
                if begtime == "":
                    begtime = file_name.split(".")[0]
                    src_file = os.path.join(src_path,file_name)
                    terget_file = src_file.replace("full_flow","rule")
                src_file = os.path.join(src_path,file_name)
                dst_file = tmp_file + "/"+str(num)+"_"+file_name 
                num += 1
                #os.system("mkdir -p "+dst_file[:dst_file.rfind["/"]])
                os.system("/opt/GeekSec/th/bin/pcap_filter -r "+src_file+" -w "+dst_file+" -rule")
                print("/opt/GeekSec/th/bin/pcap_filter -r "+src_file+" -w "+dst_file+" -rule")
                if os.path.exists(dst_file) == False:
                    break
                sum_fsize += os.path.getsize(dst_file)
                print("sum_fsize    ======   ",sum_fsize,"****************** ")
                src_file_list.append(dst_file)
                if sum_fsize > 1024*1024 * 100:
                    print("marget_pcap   sum_fsize   === ",sum_fsize)
                    marget_pcap(src_file_list,terget_file)
                    sum_fsize = 0
                    src_file_list=[]
                    begtime = ""
                    os.system("rm -rf  "+ tmp_file + "/*")
                os.system("rm -rf "+src_file)
                print("rm -rf "+ src_file)
    if len(src_file) != 0 :
        print("marget_pcap         path end ")
        marget_pcap(src_file_list,terget_file)
        sum_fsize = 0
        src_file_list=[]
        begtime = ""
    #os.system("rm -rf  "+ tmp_file + "/*")
    os.system("rm -rf "+src_path)
    print("wwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww")

def full2rule(beg_file,end_file,src_path):
    dlist = os.listdir(src_path)
    for ipath in dlist:
        if ipath == "rule":
            continue 
        dpath = os.path.join(src_path,ipath)
        if os.path.isdir(dpath) == False:
            break
        if is_number(ipath ) == True:
            ipath_num = int(ipath)
            if beg_file <= ipath_num and  end_file >=  ipath_num:
                 transfPcap(dpath)
                 continue
        full2rule(beg_file,end_file,dpath)
        # 删除 文件

if __name__ == '__main__':
    if len(sys.argv) != 2 :
        sys.exit(1)
    date = sys.argv[1]
    ts = int(time.mktime(time.strptime(date,'%Y-%m-%d %H:%M:%S')))
    beg_file = int(ts/3600/4)
    end_file = int(ts/3600/4) + 6
    full2rule(beg_file , end_file,"/data/pcapfiles")

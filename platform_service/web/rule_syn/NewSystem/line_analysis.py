# Last Update:2021-06-23 15:04:38
##
# @file lineparse.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2021-03-31


import json, os, pymysql.cursors, time
from elasticsearch import Elasticsearch
from loadMysqlPasswd import mysql_passwd
passwd = mysql_passwd()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
es_ip = base_json["es_es"]
es = Elasticsearch([es_ip])
def MAC_IP_Port_agg() :
    line = {}
    mac_ip = {}
    mac_port ={}
    mac_info = {}
    mac_2_mac ={}
    line_anayzer = {"LegalMac":[],"Line":[],"Mac":[]}
    body_ac = {"size":0,"query":{"bool":{"must":[{"range":{"pkt.pkt_dnum":{"gt":0}}}]}},"aggs":{"dMac":{"terms":{"field":"dMac.keyword","size":100000},"aggs":{"sMac":{"terms":{"field":"sMac.keyword","size":100000},"aggs":{"dIp":{"terms":{"field":"dIp","size":100000},"aggs":{"sIp":{"terms":{"field":"sIp","size":100000},"aggs":{"dport":{"terms":{"field":"dPort","size":100000}},"aggs":{"sum":{"field":"pkt.pkt_snum"}}}}}},"aggs":{"sum":{"field":"pkt.pkt_snum"}}}},"aggs":{"sum":{"field":"pkt.pkt_dnum"}}}}}}
    index = "connectinfo_*"
    result = es.search(index=index , body = body_ac)
    #3print(json.dumps(body_ac))
    for mac_buckets in result["aggregations"]["dMac"]["buckets"]:
        dmac = mac_buckets["key"]
        PacketNum = mac_buckets["aggs"]["value"]
        if dmac not in  mac_info:
            mac_info[dmac] = {"PacketNum":PacketNum ,"IP":{} , "SegmengtNum":1,"TCPPort":{},"Type":"Internet"}
        else:
            mac_info[dmac]["PacketNum"] +=  PacketNum
            mac_info[dmac]["SegmengtNum"] += mac_buckets["doc_count"]

        for smac_buckets  in mac_buckets["sMac"]["buckets"]:
            sPacketNum = smac_buckets["aggs"]["value"]
            smac = smac_buckets["key"]
            if smac  not in mac_info:
                mac_info[smac] = {"PacketNum":sPacketNum  , "SegmengtNum":0,"TCPPort":{},"Type":"Internet", "IP":{}}
            else:
                mac_info[smac]["PacketNum"]  += sPacketNum
            ### mac_to_mac 
            mmkey = dmac + "_"+ smac
            if mmkey not  in mac_2_mac:
                mac_2_mac[mmkey] ={"Mac_1":dmac ,"Mac_2":smac, "Num": smac_buckets["doc_count"]}
            # dip ====
            for dip_buckets in smac_buckets["dIp"]["buckets"]:
                dip = dip_buckets["key"]
                dip_list = dip.split(".")
                if  len(dip_list) != 4 :
                    continue

                dip_mesk  = dip.split(".")[0]+"."+dip.split(".")[1]+".0.0"
                if dip_mesk not in mac_info[dmac]["IP"]:
                    mac_info[dmac]["IP"][dip_mesk]={"Num":dip_buckets["doc_count"],"TCPPort":[]}
                else:
                    mac_info[dmac]["IP"][dip_mesk]["Num"] += dip_buckets["doc_count"]
                for sip_buckets in dip_buckets["sIp"]["buckets"]:
                    sip = sip_buckets["key"]
                    sip_mesk =  sip.split(".")[0]+"."+sip.split(".")[1]+".0.0"
                    if sip_mesk not in mac_info[smac]["IP"]:
                        mac_info[smac]["IP"][sip_mesk]={"Num":sip_buckets["doc_count"],"TCPPort":[]}
                    else:
                        mac_info[smac]["IP"][sip_mesk]["Num"] += sip_buckets["doc_count"]
                    for dport_buckets in sip_buckets["dport"]["buckets"]:
                        dport = str(dport_buckets["key"])
                        if dport not in mac_info[dmac]["TCPPort"]:
                            mac_info[dmac]["TCPPort"][dport] = {"Receive":1,"Send":1}
                        else:
                            mac_info[dmac]["TCPPort"][dport]["Receive"] += 1
                            mac_info[dmac]["TCPPort"][dport]["Send"] += 1
                        mac_info[dmac]["IP"][dip_mesk]["TCPPort"].append(dport)
    # mac 
    for mac in mac_info:
        line_anayzer["LegalMac"].append(mac)
    # line
    for mmkey in mac_2_mac:
        line_anayzer["Line"].append(mac_2_mac[mmkey])
    for mac in mac_info:
        is_mac_t = mac_info[mac]
        mac_t = {"Mac":mac}
        mac_t["PacketNum"] = mac_info[mac]["PacketNum"]
        mac_t["SegmengtNum"] = mac_info[mac]["SegmengtNum"]
        mac_t["TCPPort"] = mac_info[mac]["TCPPort"]
        print("MAC :  ",mac , "IP:" , len(is_mac_t["IP"]))
        num_ip = 0 
        '''
        for i in is_mac_t["IP"]:
            print("ip is " , i  )
            num_ip += 1
            if  num_ip > 100 :
                break;
        '''
        if len(is_mac_t["IP"]) > 50:
            mac_t["type"] = "Internet"
        else:
            mac_t["type"] = "Intra"
            mac_t["IP"] = []
            mac_t["MacInfor"] = {"IPMask":[],"Mac":mac,"Sign":["IntraMac"],"TCPPort":mac_info[mac]["TCPPort"]}

            for ip in  is_mac_t["IP"]:
                mac_t["MacInfor"]["IPMask"].append({"IP":ip,"Mask":"***********"})
                mac_t["IP"].append(ip)

        line_anayzer["Mac"].append(mac_t)
    return line_anayzer
def line_anayzer_begin():
    os.system("rm -rf /opt/GeekSec/th/bin/SegmentInfor.txt")
    db = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
    cursor = db.cursor()
    cursor.execute("update tb_line_analyze set text = '' , type = 0 ")
    db.commit()
    db.close()
def line_anayzer_end():
    db = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
    cursor = db.cursor()
    cursor.execute("select type_name,text,type from tb_line_analyze;")
    results = cursor.fetchall()
    # 更新启动线路分析mysql的json
    lineJson = ''
    if results[0]['type'] == 0:
        if os.path.exists("/opt/GeekSec/th/bin/SegmentInfor.txt"):
            f = open("/opt/GeekSec/th/bin/SegmentInfor.txt","r")
            lineJson = f.readline()
            f.close()
            cursor.execute("update tb_line_analyze set text = '" + lineJson + "'")
            db.commit()
       # 更新启动网络探针mysql的json
    if results[0]['type'] == 1:
        cursor.execute("update tb_line_analyze set text = ''")
        db.commit()
    db.close()
def line_anayzer_done():
    line_anayzer_begin()
    for i in range(0,15):
        time.sleep(60)
        #os.system("cd  /opt/GeekSec/PyTrafficDetection/  && export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/opt/GeekSec/STL/ExportData/lib  && python TaskCustom/DetectInnerMac.pyc")
        os.system("cd /opt/GeekSec/PyTrafficDetection/ && export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/opt/GeekSec/STL/ExportData/lib && /root/miniconda3/bin/python3 -m PyGksec.GkTaskCustom.DetectInnerMac")
    #line_anayzer = MAC_IP_Port_agg()
    #print(line_anayzer)
    #json.dump(line_anayzer,open("SegmentInfor.txt","w+"))
    #os.system("\cp -rf SegmentInfor.txt /opt/GeekSec/th/bin/")
    line_anayzer_end()

if __name__=="__main__":
    ts = int(time.time())
    pid = os.getpid()
    cmd = "ps -eaf | grep line_analysis.py | grep -v grep | grep -v  "+str(pid)+" | awk '{print $2}'  | xargs kill -9 >/dev/null 2>&1"
    os.system(cmd)
    json.dump({"line_anayzer_start_time":ts},open(".line_anayzer_start_time","w+"))
    line_anayzer_done()

# Last Update:2021-04-16 13:00:18
##
# @file line_analysis_status.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2021-04-01

import json,os,time
if os.path.exists(".line_anayzer_start_time"):
    tt = json.load(open(".line_anayzer_start_time"))
    ts_now = int(time.time())
    ts_end = tt["line_anayzer_start_time"] + 15 * 60
    if ts_now < ts_end :
        if ts_now >  ts_end :
            ts_now  = ts_end 
        print(ts_end - ts_now)
    else:
        print(0)
else:
    print(0)





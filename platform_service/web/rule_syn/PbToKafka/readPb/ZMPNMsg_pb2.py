# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ZMPNMsg.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='ZMPNMsg.proto',
  package='',
  syntax='proto2',
  serialized_options=None,
  serialized_pb=_b('\n\rZMPNMsg.proto\"\xb6\x03\n\x06JKNmsg\x12\x0c\n\x04type\x18\x01 \x01(\x05\x12\x15\n\x03\x64ns\x18\x05 \x01(\x0b\x32\x08.dns_msg\x12\x15\n\x03\x65sp\x18> \x01(\x0b\x32\x08.esp_msg\x12%\n\x0bhttp_header\x18Q \x01(\x0b\x32\x10.http_header_msg\x12\x17\n\x04l2tp\x18\x1b \x01(\x0b\x32\t.l2tp_msg\x12\x15\n\x03ssl\x18\x1e \x01(\x0b\x32\x08.ssl_msg\x12+\n\x0esingle_session\x18\x1f \x01(\x0b\x32\x13.single_session_msg\x12\x13\n\x02s7\x18) \x01(\x0b\x32\x07.s7_msg\x12\x1b\n\x06modbus\x18* \x01(\x0b\x32\x0b.modbus_msg\x12\x15\n\x03\x65ip\x18+ \x01(\x0b\x32\x08.eip_msg\x12\x1b\n\x06iec104\x18, \x01(\x0b\x32\x0b.iec104_msg\x12\x15\n\x03opc\x18- \x01(\x0b\x32\x08.opc_msg\x12\x1e\n\x07mac_con\x18\xe8\x07 \x01(\x0b\x32\x0c.mac_con_msg\x12\x1f\n\x08noip_con\x18\xe9\x07 \x01(\x0b\x32\x0c.mac_con_msg\x12\x16\n\x03rdp\x18\xea\x07 \x01(\x0b\x32\x08.rdp_msg\x12\x16\n\x03ssh\x18\xeb\x07 \x01(\x0b\x32\x08.ssh_msg\"p\n\x07rdp_msg\x12\x1b\n\x08\x63omm_msg\x18\x01 \x01(\x0b\x32\t.Comm_msg\x12#\n\nrdp_client\x18\x02 \x01(\x0b\x32\x0f.rdp_client_msg\x12#\n\nrdp_server\x18\x03 \x01(\x0b\x32\x0f.rdp_server_msg\"\x98\x06\n\x0erdp_client_msg\x12\x15\n\rversion_major\x18\x01 \x01(\r\x12\x15\n\rversion_minor\x18\x02 \x01(\r\x12\x15\n\rdesktop_width\x18\x03 \x01(\r\x12\x16\n\x0e\x64\x65sktop_height\x18\x04 \x01(\r\x12\x13\n\x0b\x63olor_depth\x18\x05 \x01(\r\x12\x14\n\x0csas_sequence\x18\x06 \x01(\r\x12\x17\n\x0fkeyboard_layout\x18\x07 \x01(\r\x12\x14\n\x0c\x63lient_build\x18\x08 \x01(\r\x12\x13\n\x0b\x63lient_name\x18\t \x01(\t\x12\x15\n\rkeyboard_type\x18\n \x01(\r\x12\x18\n\x10keyboard_subtype\x18\x0b \x01(\r\x12\x18\n\x10keyboard_funckey\x18\x0c \x01(\r\x12\x14\n\x0cime_filename\x18\r \x01(\t\x12\x16\n\x0epb2_colordepth\x18\x0e \x01(\r\x12\x18\n\x10\x63lient_productid\x18\x0f \x01(\r\x12\x12\n\nserial_num\x18\x10 \x01(\r\x12\x17\n\x0fhigh_colordepth\x18\x11 \x01(\r\x12\x1a\n\x12support_colordepth\x18\x12 \x01(\r\x12\x14\n\x0c\x65\x61rly_cflags\x18\x13 \x01(\r\x12\x19\n\x11\x63lient_dproductid\x18\x14 \x01(\t\x12\x17\n\x0f\x63onnection_type\x18\x15 \x01(\r\x12\x11\n\tpad1octet\x18\x16 \x01(\r\x12\x1a\n\x12sselected_protocol\x18\x17 \x01(\r\x12\x15\n\rcluster_flags\x18\x18 \x01(\r\x12\x1a\n\x12redirect_sessionid\x18\x19 \x01(\r\x12\x1a\n\x12\x65ncryption_methods\x18\x1a \x01(\r\x12\x1b\n\x13\x65xt_encrytionmethod\x18\x1b \x01(\r\x12\x15\n\rchannel_count\x18\x1c \x01(\r\x12#\n\nchanneldef\x18\x1d \x03(\x0b\x32\x0f.rdp_channeldef\x12\x19\n\x11request_protocols\x18\x1e \x01(\r\x12\x12\n\nrdp_c_flag\x18\x1f \x01(\r\x12\x0e\n\x06\x63ookie\x18  \x01(\t\".\n\x0erdp_channeldef\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06option\x18\x02 \x01(\r\"\xc3\x02\n\x0erdp_server_msg\x12\x15\n\rversion_major\x18\x01 \x01(\r\x12\x15\n\rversion_minor\x18\x02 \x01(\r\x12\x15\n\rmcs_channelid\x18\x03 \x01(\r\x12\x15\n\rchannel_count\x18\x04 \x01(\r\x12\x12\n\nchannel_id\x18\x05 \x03(\r\x12\x19\n\x11\x65ncryption_method\x18\x06 \x01(\r\x12\x18\n\x10\x65ncryption_level\x18\x07 \x01(\r\x12\x18\n\x10server_randomlen\x18\x08 \x01(\r\x12\x16\n\x0eserver_certlen\x18\t \x01(\r\x12\x15\n\rserver_random\x18\n \x01(\t\x12\x13\n\x0bserver_cert\x18\x0b \x01(\t\x12\x1a\n\x12selected_protocols\x18\x0c \x01(\r\x12\x12\n\nrdp_s_flag\x18\r \x01(\r\"\xbc\x01\n\x0bmac_con_msg\x12\r\n\x05mac_a\x18\x01 \x01(\t\x12\r\n\x05mac_b\x18\x02 \x01(\t\x12\x12\n\nbegin_time\x18\x03 \x01(\r\x12\x10\n\x08\x65nd_time\x18\x04 \x01(\r\x12\x0f\n\x07pkt_a2b\x18\x05 \x01(\r\x12\x0f\n\x07pkt_b2a\x18\x06 \x01(\r\x12\x11\n\tdevice_id\x18\x07 \x01(\r\x12\x11\n\tthread_id\x18\x08 \x01(\r\x12\x0f\n\x07task_id\x18\t \x01(\r\x12\x10\n\x08\x62\x61tch_id\x18\x0b \x01(\r\"\x84\x02\n\x08\x43omm_msg\x12\x0e\n\x06src_ip\x18\x01 \x01(\t\x12\x0e\n\x06\x64st_ip\x18\x02 \x01(\t\x12\x10\n\x08src_port\x18\x03 \x01(\r\x12\x10\n\x08\x64st_port\x18\x04 \x01(\r\x12\r\n\x05ippro\x18\x05 \x01(\r\x12\x12\n\nsession_id\x18\x06 \x01(\t\x12\x12\n\nbegin_time\x18\x08 \x01(\r\x12\x11\n\tserver_ip\x18\x0c \x01(\t\x12\x0e\n\x06\x61pp_id\x18\x0e \x01(\r\x12\x10\n\x08\x61pp_name\x18\x0f \x01(\t\x12\x11\n\tthread_id\x18\x12 \x01(\r\x12\x0f\n\x07task_id\x18\x13 \x01(\r\x12\x12\n\nbegin_nsec\x18\x1b \x01(\r\x12\x10\n\x08\x62\x61tch_id\x18! \x01(\r\"\xa2\x02\n\rhttp_comm_msg\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x0f\n\x07referer\x18\x02 \x01(\t\x12\x0e\n\x06\x61\x63\x63\x65pt\x18\x03 \x01(\t\x12\x17\n\x0f\x61\x63\x63\x65pt_language\x18\x04 \x01(\t\x12\x12\n\nuser_agent\x18\x05 \x01(\t\x12\x17\n\x0f\x61\x63\x63\x65pt_encoding\x18\x06 \x01(\t\x12\x0c\n\x04host\x18\x07 \x01(\t\x12\x0e\n\x06\x63ookie\x18\x08 \x01(\t\x12\x16\n\x0e\x61\x63\x63\x65pt_charset\x18\t \x01(\t\x12\x0b\n\x03via\x18\n \x01(\t\x12\x16\n\x0e\x63ontent_length\x18\x0b \x01(\t\x12\x14\n\x0c\x63ontent_type\x18\x0c \x01(\t\x12\x17\n\x0fx_forwarded_for\x18\r \x01(\t\x12\x13\n\x0bresp_server\x18\x0e \x01(\t\"\xe0\x01\n\x07\x64ns_msg\x12\x1b\n\x08\x63omm_msg\x18\x01 \x01(\x0b\x32\t.Comm_msg\x12\x0e\n\x06\x64ns_id\x18\x02 \x01(\r\x12\x11\n\tdns_flags\x18\x03 \x01(\r\x12\x0f\n\x07\x64ns_que\x18\x04 \x01(\r\x12\x0f\n\x07\x64ns_ans\x18\x05 \x01(\r\x12\x10\n\x08\x64ns_auth\x18\x06 \x01(\r\x12\x0f\n\x07\x64ns_add\x18\x07 \x01(\r\x12\x12\n\ndns_domain\x18\x08 \x01(\t\x12\x15\n\rdns_domain_ip\x18\t \x01(\t\x12\x11\n\tdns_query\x18\n \x01(\t\x12\x12\n\ndns_answer\x18\x0b \x01(\t\"\xd4\x01\n\x07\x65sp_msg\x12\x1b\n\x08\x63omm_msg\x18\x01 \x01(\x0b\x32\t.Comm_msg\x12\x17\n\x0fprotocol_family\x18\x02 \x01(\r\x12\x1a\n\x12\x63ommunication_rate\x18\x03 \x01(\x01\x12\x11\n\tdirection\x18\x04 \x01(\r\x12\x1a\n\x12\x65ncapsulation_mode\x18\x05 \x01(\r\x12\x0f\n\x07\x65sp_spi\x18\x06 \x01(\r\x12\x0f\n\x07\x65sp_seq\x18\x07 \x01(\r\x12\x14\n\x0c\x65sp_data_len\x18\x08 \x01(\r\x12\x10\n\x08\x65sp_data\x18\t \x01(\t\"\'\n\x0bkey_val_msg\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x0b\n\x03val\x18\x02 \x01(\t\"\x98\x02\n\x0fhttp_header_msg\x12\x1b\n\x08\x63omm_msg\x18\x01 \x01(\x0b\x32\t.Comm_msg\x12\x0b\n\x03\x61\x63t\x18\x04 \x01(\t\x12\x0b\n\x03url\x18\x05 \x01(\t\x12\x0c\n\x04host\x18\x06 \x01(\t\x12\x10\n\x08response\x18\x07 \x01(\t\x12\x15\n\rhttp_c_finger\x18\x08 \x01(\x04\x12\x15\n\rhttp_s_finger\x18\t \x01(\x04\x12$\n\x0ehttp_client_kv\x18\n \x03(\x0b\x32\x0c.key_val_msg\x12\x19\n\x11http_client_title\x18\x0b \x03(\t\x12$\n\x0ehttp_server_kv\x18\x0c \x03(\x0b\x32\x0c.key_val_msg\x12\x19\n\x11http_server_title\x18\r \x03(\t\"\x98\x03\n\x08l2tp_msg\x12\x1b\n\x08\x63omm_msg\x18\x01 \x01(\x0b\x32\t.Comm_msg\x12\x17\n\x0fprotocol_family\x18\x02 \x01(\r\x12\x1a\n\x12\x63ommunication_rate\x18\x03 \x01(\x01\x12\x11\n\tdirection\x18\x04 \x01(\r\x12\x18\n\x10protocol_version\x18\x05 \x01(\r\x12\x1c\n\x14\x66raming_capabilities\x18\x06 \x01(\r\x12\x1b\n\x13\x62\x65\x61rer_capabilities\x18\x07 \x01(\r\x12\x17\n\x0fserver_hostname\x18\x08 \x01(\t\x12\x17\n\x0f\x63lient_hostname\x18\t \x01(\t\x12\x19\n\x11server_vendorname\x18\n \x01(\t\x12\x19\n\x11\x63lient_vendorname\x18\x0b \x01(\t\x12\x16\n\x0e\x63\x61lling_number\x18\x0c \x01(\t\x12\x19\n\x11proxy_authen_type\x18\r \x01(\r\x12\x19\n\x11proxy_authen_name\x18\x0e \x01(\t\x12\x1c\n\x14is_negotiate_success\x18\x0f \x01(\r\"\xff\x02\n\x0bssh_kex_msg\x12\x10\n\x08protocol\x18\x01 \x01(\t\x12\x0e\n\x06\x63ookie\x18\x02 \x01(\t\x12\x16\n\x0ekex_algorithms\x18\x03 \x03(\t\x12\"\n\x1aserver_host_key_algorithms\x18\x04 \x03(\t\x12.\n&encryption_algorithms_client_to_server\x18\x05 \x03(\t\x12.\n&encryption_algorithms_server_to_client\x18\x06 \x03(\t\x12\'\n\x1fmac_algorithms_client_to_server\x18\x07 \x03(\t\x12\'\n\x1fmac_algorithms_server_to_client\x18\x08 \x03(\t\x12/\n\'compression_algorithms_client_to_server\x18\t \x03(\t\x12/\n\'compression_algorithms_server_to_client\x18\n \x03(\t\"\x8f\x04\n\x07ssh_msg\x12\x1b\n\x08\x63omm_msg\x18\x01 \x01(\x0b\x32\t.Comm_msg\x12\x1c\n\x06\x63lient\x18\x02 \x01(\x0b\x32\x0c.ssh_kex_msg\x12\x1c\n\x06server\x18\x03 \x01(\x0b\x32\x0c.ssh_kex_msg\x12\x0c\n\x04\x64h_e\x18\x16 \x01(\t\x12\x0c\n\x04\x64h_f\x18\x17 \x01(\t\x12\x12\n\ndh_gex_min\x18\x18 \x01(\t\x12\x14\n\x0c\x64h_gex_nbits\x18\x19 \x01(\t\x12\x12\n\ndh_gex_max\x18\x1a \x01(\t\x12\x10\n\x08\x64h_gex_p\x18\x1b \x01(\t\x12\x10\n\x08\x64h_gex_g\x18\x1c \x01(\t\x12\x10\n\x08\x65\x63\x64h_q_c\x18\x1d \x01(\t\x12\x10\n\x08\x65\x63\x64h_q_s\x18\x1e \x01(\t\x12\x15\n\rhost_key_type\x18\x1f \x01(\t\x12\x16\n\x0ehost_key_rsa_e\x18  \x01(\t\x12\x16\n\x0ehost_key_rsa_n\x18! \x01(\t\x12\x19\n\x11host_key_ecdsa_id\x18\" \x01(\t\x12\x18\n\x10host_key_ecdsa_q\x18# \x01(\t\x12\x16\n\x0ehost_key_dsa_p\x18$ \x01(\t\x12\x16\n\x0ehost_key_dsa_q\x18% \x01(\t\x12\x16\n\x0ehost_key_dsa_g\x18& \x01(\t\x12\x16\n\x0ehost_key_dsa_y\x18\' \x01(\t\x12\x1a\n\x12host_key_eddsa_key\x18( \x01(\t\x12\x11\n\tkex_h_sig\x18) \x01(\t\"\x8f\n\n\x07ssl_msg\x12\x1b\n\x08\x63omm_msg\x18\x01 \x01(\x0b\x32\t.Comm_msg\x12\x13\n\x0bssl_version\x18\x02 \x01(\r\x12\x15\n\rssl_c_version\x18\x03 \x01(\r\x12\x1b\n\x13ssl_hello_c_version\x18\x04 \x01(\r\x12\x18\n\x10ssl_hello_c_time\x18\x05 \x01(\r\x12\x1a\n\x12ssl_hello_c_random\x18\x06 \x01(\t\x12\x1d\n\x15ssl_hello_c_sessionid\x18\x07 \x01(\t\x12 \n\x18ssl_hello_c_sessionidlen\x18\x08 \x01(\r\x12\x1e\n\x16ssl_hello_c_ciphersuit\x18\t \x01(\t\x12!\n\x19ssl_hello_c_ciphersuitnum\x18\n \x01(\r\x12%\n\x1dssl_hello_c_compressionmethod\x18\x0b \x01(\t\x12(\n ssl_hello_c_compressionmethodlen\x18\x0c \x01(\r\x12 \n\x18ssl_hello_c_extentionnum\x18\r \x01(\r\x12\x1d\n\x15ssl_hello_c_extention\x18\x0e \x01(\t\x12\x18\n\x10ssl_hello_c_alpn\x18\x0f \x01(\t\x12\x1e\n\x16ssl_hello_c_servername\x18\x10 \x01(\t\x12\"\n\x1assl_hello_c_servernametype\x18\x11 \x01(\r\x12!\n\x19ssl_hello_c_sessionticket\x18\x12 \x01(\t\x12\x16\n\x0essl_cert_c_num\x18\x13 \x01(\r\x12\x17\n\x0fssl_cert_c_hash\x18\x14 \x01(\t\x12\x1b\n\x13ssl_hello_s_version\x18\x15 \x01(\r\x12\x18\n\x10ssl_hello_s_time\x18\x16 \x01(\r\x12\x1a\n\x12ssl_hello_s_random\x18\x17 \x01(\t\x12\x1d\n\x15ssl_hello_s_sessionid\x18\x18 \x01(\t\x12 \n\x18ssl_hello_s_sessionidlen\x18\x19 \x01(\r\x12\x1e\n\x16ssl_hello_s_cipersuite\x18\x1a \x01(\t\x12%\n\x1dssl_hello_s_compressionmethod\x18\x1b \x01(\t\x12 \n\x18ssl_hello_s_extentionnum\x18\x1c \x01(\r\x12\x1d\n\x15ssl_hello_s_extention\x18\x1d \x01(\t\x12\x18\n\x10ssl_hello_s_alpn\x18\x1e \x01(\t\x12!\n\x19ssl_hello_s_sessionticket\x18\x1f \x01(\t\x12\x16\n\x0essl_cert_s_num\x18  \x01(\r\x12\x17\n\x0fssl_cert_s_hash\x18! \x01(\t\x12\'\n\x1fssl_s_newsessionticket_lifetime\x18\" \x01(\r\x12%\n\x1dssl_s_newsessionticket_ticket\x18# \x01(\t\x12(\n ssl_s_newsessionticket_ticketlen\x18$ \x01(\r\x12\x1c\n\x14ssl_c_keyexchangelen\x18% \x01(\r\x12\x19\n\x11ssl_c_keyexchange\x18& \x01(\t\x12\x1c\n\x14ssl_s_keyexchangelen\x18\' \x01(\r\x12\x19\n\x11ssl_s_keyexchange\x18( \x01(\t\x12\x14\n\x0cssl_c_finger\x18) \x01(\x04\x12\x14\n\x0cssl_s_finger\x18* \x01(\x04\"I\n\x08rule_msg\x12\x14\n\x0c\x63onnect_rule\x18\x01 \x03(\r\x12\x18\n\x10\x63onnect_rule_num\x18\x02 \x02(\r\x12\r\n\x05level\x18\x03 \x02(\r\"[\n\x0bsingle_http\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x0b\n\x03\x61\x63t\x18\x02 \x01(\t\x12\x0c\n\x04host\x18\x03 \x01(\t\x12\x10\n\x08response\x18\x04 \x01(\t\x12\x12\n\nuser_agent\x18\x05 \x01(\t\"0\n\x11single_dns_answer\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"S\n\nsingle_dns\x12\x0e\n\x06\x64omain\x18\x01 \x01(\t\x12\x11\n\tdomain_ip\x18\x02 \x01(\t\x12\"\n\x06\x61nswer\x18\x03 \x03(\x0b\x32\x12.single_dns_answer\"\xaf\x01\n\nsingle_ssl\x12\x15\n\rch_ciphersuit\x18\x01 \x01(\t\x12\x19\n\x11\x63h_ciphersuit_num\x18\x02 \x01(\x05\x12\x16\n\x0e\x63h_server_name\x18\x03 \x01(\t\x12\x0f\n\x07\x63h_alpn\x18\x04 \x01(\t\x12\x0e\n\x06\x63_cert\x18\x05 \x01(\t\x12\x12\n\nc_cert_num\x18\x06 \x01(\x05\x12\x0e\n\x06s_cert\x18\x07 \x01(\t\x12\x12\n\ns_cert_num\x18\x08 \x01(\x05\"\x87\x01\n\x0css_basic_msg\x12\x0c\n\x04smac\x18\x01 \x01(\t\x12\x0c\n\x04\x64mac\x18\x02 \x01(\t\x12\x17\n\x04rule\x18\x03 \x01(\x0b\x32\t.rule_msg\x12\x10\n\x08rule_num\x18\x04 \x01(\r\x12\x12\n\nrule_level\x18\x05 \x01(\r\x12\x0b\n\x03syn\x18\x06 \x01(\t\x12\x0f\n\x07syn_ack\x18\x07 \x02(\t\"\xd6\x06\n\x0css_stats_msg\x12\x18\n\x10stats_stotalsign\x18\x01 \x01(\r\x12\x18\n\x10stats_dtotalsign\x18\x02 \x01(\r\x12\x17\n\x0fstats_distbytes\x18\x03 \x01(\t\x12\x1a\n\x12stats_distbytesnum\x18\x04 \x01(\x04\x12\x15\n\rstats_distcsq\x18\x05 \x01(\x01\x12\x16\n\x0estats_distcsqt\x18\x06 \x01(\x01\x12\x16\n\x0estats_sdistlen\x18\x07 \x03(\r\x12\x16\n\x0estats_ddistlen\x18\x08 \x03(\r\x12\x16\n\x0estats_sdistdur\x18\t \x01(\r\x12\x16\n\x0estats_ddistdur\x18\n \x01(\r\x12\x19\n\x11stats_prolist_num\x18\x0b \x01(\r\x12\x15\n\rstats_prolist\x18\x0c \x01(\t\x12\x10\n\x08sio_sign\x18\r \x01(\r\x12\x10\n\x08\x64io_sign\x18\x0e \x01(\r\x12\x10\n\x08\x65xt_json\x18\x0f \x01(\t\x12\x15\n\rstats_src_mss\x18\x10 \x01(\r\x12\x15\n\rstats_dst_mss\x18\x11 \x01(\r\x12\x1e\n\x16stats_src_window_scale\x18\x12 \x01(\r\x12\x1e\n\x16stats_dst_window_scale\x18\x13 \x01(\r\x12\x1d\n\x15stats_spayload_maxlen\x18\x14 \x01(\r\x12\x1d\n\x15stats_dpayload_maxlen\x18\x15 \x01(\r\x12!\n\x19stats_sack_payload_maxlen\x18\x16 \x01(\r\x12!\n\x19stats_dack_payload_maxlen\x18\x17 \x01(\r\x12!\n\x19stats_sack_payload_minlen\x18\x18 \x01(\r\x12!\n\x19stats_dack_payload_minlen\x18\x19 \x01(\r\x12#\n\x0estats_tcp_info\x18\x1a \x03(\x0b\x32\x0b.md_tcp_msg\x12\x15\n\rstats_distdur\x18\x1b \x03(\r\x12\x0f\n\x07syn_seq\x18\x1c \x03(\r\x12\x13\n\x0bsyn_seq_num\x18\x1d \x01(\r\x12\x1a\n\x12stats_sipid_offset\x18\x1e \x03(\r\x12\x1a\n\x12stats_dipid_offset\x18\x1f \x03(\r\x12\x14\n\x0c\x62lock_cipher\x18  \x03(\r\"\x85\x01\n\nmd_tcp_msg\x12\r\n\x05\x62ytes\x18\x01 \x01(\x03\x12\x12\n\npacket_num\x18\x02 \x01(\r\x12\x0f\n\x07psh_num\x18\x03 \x01(\r\x12\x17\n\x0f\x61\x63knowledgement\x18\x04 \x01(\r\x12\x14\n\x0cmin_sequence\x18\x05 \x01(\r\x12\x14\n\x0cmax_sequence\x18\x06 \x01(\r\"H\n\x0fpacket_info_msg\x12\r\n\x05\x63ount\x18\x01 \x01(\r\x12\x0b\n\x03sec\x18\x06 \x02(\r\x12\x0c\n\x04nsec\x18\n \x02(\r\x12\x0b\n\x03len\x18\x0c \x01(\x05\"\xba\x07\n\nss_pkt_msg\x12\x13\n\x0bpkt_smaxlen\x18\x01 \x01(\r\x12\x13\n\x0bpkt_dmaxlen\x18\x02 \x01(\r\x12\x10\n\x08pkt_snum\x18\x03 \x01(\r\x12\x10\n\x08pkt_dnum\x18\x04 \x01(\r\x12\x17\n\x0fpkt_spayloadnum\x18\x05 \x01(\r\x12\x17\n\x0fpkt_dpayloadnum\x18\x06 \x01(\r\x12\x12\n\npkt_sbytes\x18\x07 \x01(\x04\x12\x12\n\npkt_dbytes\x18\x08 \x01(\x04\x12\x19\n\x11pkt_spayloadbytes\x18\t \x01(\x04\x12\x19\n\x11pkt_dpayloadbytes\x18\n \x01(\x04\x12\x13\n\x0bpkt_sfinnum\x18\x0b \x01(\r\x12\x13\n\x0bpkt_dfinnum\x18\x0c \x01(\r\x12\x13\n\x0bpkt_srstnum\x18\r \x01(\r\x12\x13\n\x0bpkt_drstnum\x18\x0e \x01(\r\x12\x13\n\x0bpkt_ssynnum\x18\x0f \x01(\r\x12\x13\n\x0bpkt_dsynnum\x18\x10 \x01(\r\x12\x15\n\rpkt_ssynbytes\x18\x11 \x01(\r\x12\x15\n\rpkt_dsynbytes\x18\x12 \x01(\r\x12\x13\n\x0bpkt_sttlmax\x18\x13 \x01(\r\x12\x13\n\x0bpkt_dttlmax\x18\x14 \x01(\r\x12\x13\n\x0bpkt_sttlmin\x18\x15 \x01(\r\x12\x13\n\x0bpkt_dttlmin\x18\x16 \x01(\r\x12\x13\n\x0bpkt_sdurmax\x18\x17 \x01(\r\x12\x13\n\x0bpkt_ddurmax\x18\x18 \x01(\r\x12\x13\n\x0bpkt_sdurmin\x18\x19 \x01(\r\x12\x13\n\x0bpkt_ddurmin\x18\x1a \x01(\r\x12\x15\n\rpkt_sdisorder\x18\x1b \x01(\r\x12\x15\n\rpkt_ddisorder\x18\x1c \x01(\r\x12\x13\n\x0bpkt_sresend\x18\x1d \x01(\r\x12\x13\n\x0bpkt_dresend\x18\x1e \x01(\r\x12\x11\n\tpkt_slost\x18\x1f \x01(\r\x12\x11\n\tpkt_dlost\x18  \x01(\r\x12\x13\n\x0bpkt_spshnum\x18! \x01(\r\x12\x13\n\x0bpkt_dpshnum\x18\" \x01(\r\x12\x12\n\npkt_pronum\x18# \x01(\r\x12\x19\n\x11pkt_unkonw_pronum\x18$ \x01(\r\x12#\n\tpkt_infor\x18% \x03(\x0b\x32\x10.packet_info_msg\x12\x14\n\x0cpkt_syn_data\x18& \x01(\r\x12\x13\n\x0bpkt_sbadnum\x18\' \x01(\r\x12\x13\n\x0bpkt_dbadnum\x18( \x01(\r\x12\x12\n\napp_pkt_id\x18) \x01(\r\x12\x14\n\x0cpkt_spayload\x18* \x03(\t\x12\x14\n\x0cpkt_dpayload\x18+ \x03(\t\"\xe9\x01\n\x16tcp_finger_feature_msg\x12\x12\n\necn_ip_ect\x18\x01 \x02(\x08\x12\x14\n\x0cqk_dfnz_ipid\x18\x02 \x02(\x08\x12\x10\n\x08\x66lag_cwr\x18\x03 \x02(\x08\x12\x10\n\x08\x66lag_ece\x18\x04 \x02(\x08\x12\x17\n\x0fqk_opt_zero_ts1\x18\x05 \x02(\x08\x12\x0b\n\x03ttl\x18\x06 \x02(\r\x12\x19\n\x11tcpopt_eol_padnum\x18\x07 \x02(\r\x12\x15\n\rtcpopt_wscale\x18\x08 \x02(\r\x12\x12\n\nqk_win_mss\x18\t \x02(\r\x12\x15\n\rtcpopt_layout\x18\n \x02(\t\"\xf7\x05\n\x12single_session_msg\x12\x1b\n\x08\x63omm_msg\x18\x01 \x02(\x0b\x32\t.Comm_msg\x12\x1f\n\x08ss_basic\x18\x02 \x01(\x0b\x32\r.ss_basic_msg\x12\x1f\n\x08ss_stats\x18\x03 \x01(\x0b\x32\r.ss_stats_msg\x12\x1b\n\x06ss_pkt\x18\x04 \x01(\x0b\x32\x0b.ss_pkt_msg\x12\x14\n\x0ctcp_c_finger\x18\x05 \x01(\x04\x12\x14\n\x0ctcp_s_finger\x18\x06 \x01(\x04\x12\x15\n\rhttp_c_finger\x18\x07 \x01(\x04\x12\x15\n\rhttp_s_finger\x18\x08 \x01(\x04\x12\x14\n\x0cssl_c_finger\x18\t \x01(\x04\x12\x14\n\x0cssl_s_finger\x18\n \x01(\x04\x12\x35\n\x14tcp_c_finger_feature\x18\x0b \x01(\x0b\x32\x17.tcp_finger_feature_msg\x12\x35\n\x14tcp_s_finger_feature\x18\x0c \x01(\x0b\x32\x17.tcp_finger_feature_msg\x12\x11\n\tport_list\x18\r \x03(\r\x12\x1a\n\x04http\x18\x0e \x03(\x0b\x32\x0c.single_http\x12\x18\n\x03\x64ns\x18\x0f \x03(\x0b\x32\x0b.single_dns\x12\x18\n\x03ssl\x18\x10 \x03(\x0b\x32\x0b.single_ssl\x12\x10\n\x08\x65nd_time\x18\x14 \x01(\r\x12\x14\n\x0c\x66irst_sender\x18\x15 \x01(\t\x12\x10\n\x08\x64uration\x18\x16 \x01(\r\x12\x11\n\tdevice_id\x18\x17 \x01(\r\x12\x13\n\x0b\x66irst_proto\x18\x18 \x01(\r\x12\x10\n\x08proxy_ip\x18\x19 \x01(\t\x12\x12\n\nproxy_port\x18\x1a \x01(\r\x12\x10\n\x08\x65nd_nsec\x18\x1c \x01(\r\x12\x17\n\x0fproxy_real_host\x18\x1d \x01(\t\x12\x12\n\nproxy_type\x18\x1e \x01(\r\x12\x19\n\x11handle_begin_time\x18\x1f \x01(\r\x12\x17\n\x0fhandle_end_time\x18  \x01(\r\x12\x13\n\x0brule_labels\x18\" \x03(\r\"\x89\x03\n\x06s7_msg\x12\x1b\n\x08\x63omm_msg\x18\x01 \x01(\x0b\x32\t.Comm_msg\x12\x14\n\x0ctpkt_version\x18\x02 \x01(\r\x12\x11\n\tcotp_type\x18\x03 \x01(\r\x12\x0f\n\x07s7_type\x18\x04 \x01(\r\x12\x13\n\x0bs7_function\x18\x05 \x01(\r\x12\x13\n\x0bsystem_type\x18\x06 \x01(\r\x12\x1d\n\x15system_group_function\x18\x07 \x01(\r\x12\x1b\n\x13system_sub_function\x18\x08 \x01(\r\x12\x0f\n\x07\x64st_ref\x18\t \x01(\r\x12\x0f\n\x07src_ref\x18\n \x01(\r\x12\x10\n\x08pdu_size\x18\x0b \x01(\r\x12\x18\n\x10src_connect_type\x18\x0c \x01(\r\x12\x10\n\x08src_rack\x18\r \x01(\r\x12\x10\n\x08src_slot\x18\x0e \x01(\r\x12\x18\n\x10\x64st_connect_type\x18\x0f \x01(\r\x12\x10\n\x08\x64st_rack\x18\x10 \x01(\r\x12\x10\n\x08\x64st_slot\x18\x11 \x01(\r\x12\x12\n\npacket_c2s\x18\x12 \x01(\r\"\x89\x01\n\nmodbus_msg\x12\x1b\n\x08\x63omm_msg\x18\x01 \x01(\x0b\x32\t.Comm_msg\x12\x10\n\x08trans_id\x18\x02 \x01(\r\x12\x13\n\x0bprotocol_id\x18\x03 \x01(\r\x12\x10\n\x08slave_id\x18\x04 \x01(\r\x12\x11\n\tfunc_code\x18\x05 \x01(\r\x12\x12\n\npacket_c2s\x18\x06 \x01(\r\"r\n\x07\x65ip_msg\x12\x1b\n\x08\x63omm_msg\x18\x01 \x01(\x0b\x32\t.Comm_msg\x12\x10\n\x08trans_id\x18\x02 \x01(\r\x12\x13\n\x0bprotocol_id\x18\x03 \x01(\r\x12\x10\n\x08slave_id\x18\x04 \x01(\r\x12\x11\n\tfunc_code\x18\x05 \x01(\r\"u\n\niec104_msg\x12\x1b\n\x08\x63omm_msg\x18\x01 \x01(\x0b\x32\t.Comm_msg\x12\x10\n\x08trans_id\x18\x02 \x01(\r\x12\x13\n\x0bprotocol_id\x18\x03 \x01(\r\x12\x10\n\x08slave_id\x18\x04 \x01(\r\x12\x11\n\tfunc_code\x18\x05 \x01(\r\"r\n\x07opc_msg\x12\x1b\n\x08\x63omm_msg\x18\x01 \x01(\x0b\x32\t.Comm_msg\x12\x10\n\x08trans_id\x18\x02 \x01(\r\x12\x13\n\x0bprotocol_id\x18\x03 \x01(\r\x12\x10\n\x08slave_id\x18\x04 \x01(\r\x12\x11\n\tfunc_code\x18\x05 \x01(\r')
)




_JKNMSG = _descriptor.Descriptor(
  name='JKNmsg',
  full_name='JKNmsg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='JKNmsg.type', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dns', full_name='JKNmsg.dns', index=1,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='esp', full_name='JKNmsg.esp', index=2,
      number=62, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='http_header', full_name='JKNmsg.http_header', index=3,
      number=81, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='l2tp', full_name='JKNmsg.l2tp', index=4,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl', full_name='JKNmsg.ssl', index=5,
      number=30, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='single_session', full_name='JKNmsg.single_session', index=6,
      number=31, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s7', full_name='JKNmsg.s7', index=7,
      number=41, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='modbus', full_name='JKNmsg.modbus', index=8,
      number=42, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='eip', full_name='JKNmsg.eip', index=9,
      number=43, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='iec104', full_name='JKNmsg.iec104', index=10,
      number=44, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opc', full_name='JKNmsg.opc', index=11,
      number=45, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mac_con', full_name='JKNmsg.mac_con', index=12,
      number=1000, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='noip_con', full_name='JKNmsg.noip_con', index=13,
      number=1001, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rdp', full_name='JKNmsg.rdp', index=14,
      number=1002, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssh', full_name='JKNmsg.ssh', index=15,
      number=1003, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18,
  serialized_end=456,
)


_RDP_MSG = _descriptor.Descriptor(
  name='rdp_msg',
  full_name='rdp_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='comm_msg', full_name='rdp_msg.comm_msg', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rdp_client', full_name='rdp_msg.rdp_client', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rdp_server', full_name='rdp_msg.rdp_server', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=458,
  serialized_end=570,
)


_RDP_CLIENT_MSG = _descriptor.Descriptor(
  name='rdp_client_msg',
  full_name='rdp_client_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='version_major', full_name='rdp_client_msg.version_major', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='version_minor', full_name='rdp_client_msg.version_minor', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='desktop_width', full_name='rdp_client_msg.desktop_width', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='desktop_height', full_name='rdp_client_msg.desktop_height', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='color_depth', full_name='rdp_client_msg.color_depth', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sas_sequence', full_name='rdp_client_msg.sas_sequence', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='keyboard_layout', full_name='rdp_client_msg.keyboard_layout', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='client_build', full_name='rdp_client_msg.client_build', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='client_name', full_name='rdp_client_msg.client_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='keyboard_type', full_name='rdp_client_msg.keyboard_type', index=9,
      number=10, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='keyboard_subtype', full_name='rdp_client_msg.keyboard_subtype', index=10,
      number=11, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='keyboard_funckey', full_name='rdp_client_msg.keyboard_funckey', index=11,
      number=12, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ime_filename', full_name='rdp_client_msg.ime_filename', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pb2_colordepth', full_name='rdp_client_msg.pb2_colordepth', index=13,
      number=14, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='client_productid', full_name='rdp_client_msg.client_productid', index=14,
      number=15, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='serial_num', full_name='rdp_client_msg.serial_num', index=15,
      number=16, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='high_colordepth', full_name='rdp_client_msg.high_colordepth', index=16,
      number=17, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='support_colordepth', full_name='rdp_client_msg.support_colordepth', index=17,
      number=18, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='early_cflags', full_name='rdp_client_msg.early_cflags', index=18,
      number=19, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='client_dproductid', full_name='rdp_client_msg.client_dproductid', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='connection_type', full_name='rdp_client_msg.connection_type', index=20,
      number=21, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pad1octet', full_name='rdp_client_msg.pad1octet', index=21,
      number=22, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sselected_protocol', full_name='rdp_client_msg.sselected_protocol', index=22,
      number=23, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cluster_flags', full_name='rdp_client_msg.cluster_flags', index=23,
      number=24, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='redirect_sessionid', full_name='rdp_client_msg.redirect_sessionid', index=24,
      number=25, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='encryption_methods', full_name='rdp_client_msg.encryption_methods', index=25,
      number=26, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ext_encrytionmethod', full_name='rdp_client_msg.ext_encrytionmethod', index=26,
      number=27, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channel_count', full_name='rdp_client_msg.channel_count', index=27,
      number=28, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channeldef', full_name='rdp_client_msg.channeldef', index=28,
      number=29, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_protocols', full_name='rdp_client_msg.request_protocols', index=29,
      number=30, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rdp_c_flag', full_name='rdp_client_msg.rdp_c_flag', index=30,
      number=31, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cookie', full_name='rdp_client_msg.cookie', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=573,
  serialized_end=1365,
)


_RDP_CHANNELDEF = _descriptor.Descriptor(
  name='rdp_channeldef',
  full_name='rdp_channeldef',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='rdp_channeldef.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='option', full_name='rdp_channeldef.option', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1367,
  serialized_end=1413,
)


_RDP_SERVER_MSG = _descriptor.Descriptor(
  name='rdp_server_msg',
  full_name='rdp_server_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='version_major', full_name='rdp_server_msg.version_major', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='version_minor', full_name='rdp_server_msg.version_minor', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mcs_channelid', full_name='rdp_server_msg.mcs_channelid', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channel_count', full_name='rdp_server_msg.channel_count', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channel_id', full_name='rdp_server_msg.channel_id', index=4,
      number=5, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='encryption_method', full_name='rdp_server_msg.encryption_method', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='encryption_level', full_name='rdp_server_msg.encryption_level', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='server_randomlen', full_name='rdp_server_msg.server_randomlen', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='server_certlen', full_name='rdp_server_msg.server_certlen', index=8,
      number=9, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='server_random', full_name='rdp_server_msg.server_random', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='server_cert', full_name='rdp_server_msg.server_cert', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='selected_protocols', full_name='rdp_server_msg.selected_protocols', index=11,
      number=12, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rdp_s_flag', full_name='rdp_server_msg.rdp_s_flag', index=12,
      number=13, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1416,
  serialized_end=1739,
)


_MAC_CON_MSG = _descriptor.Descriptor(
  name='mac_con_msg',
  full_name='mac_con_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='mac_a', full_name='mac_con_msg.mac_a', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mac_b', full_name='mac_con_msg.mac_b', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='begin_time', full_name='mac_con_msg.begin_time', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='mac_con_msg.end_time', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_a2b', full_name='mac_con_msg.pkt_a2b', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_b2a', full_name='mac_con_msg.pkt_b2a', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='device_id', full_name='mac_con_msg.device_id', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='thread_id', full_name='mac_con_msg.thread_id', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='task_id', full_name='mac_con_msg.task_id', index=8,
      number=9, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='mac_con_msg.batch_id', index=9,
      number=11, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1742,
  serialized_end=1930,
)


_COMM_MSG = _descriptor.Descriptor(
  name='Comm_msg',
  full_name='Comm_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='src_ip', full_name='Comm_msg.src_ip', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dst_ip', full_name='Comm_msg.dst_ip', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='src_port', full_name='Comm_msg.src_port', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dst_port', full_name='Comm_msg.dst_port', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ippro', full_name='Comm_msg.ippro', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='session_id', full_name='Comm_msg.session_id', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='begin_time', full_name='Comm_msg.begin_time', index=6,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='server_ip', full_name='Comm_msg.server_ip', index=7,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='app_id', full_name='Comm_msg.app_id', index=8,
      number=14, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='app_name', full_name='Comm_msg.app_name', index=9,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='thread_id', full_name='Comm_msg.thread_id', index=10,
      number=18, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='task_id', full_name='Comm_msg.task_id', index=11,
      number=19, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='begin_nsec', full_name='Comm_msg.begin_nsec', index=12,
      number=27, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='Comm_msg.batch_id', index=13,
      number=33, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1933,
  serialized_end=2193,
)


_HTTP_COMM_MSG = _descriptor.Descriptor(
  name='http_comm_msg',
  full_name='http_comm_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='url', full_name='http_comm_msg.url', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='referer', full_name='http_comm_msg.referer', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accept', full_name='http_comm_msg.accept', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accept_language', full_name='http_comm_msg.accept_language', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_agent', full_name='http_comm_msg.user_agent', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accept_encoding', full_name='http_comm_msg.accept_encoding', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host', full_name='http_comm_msg.host', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cookie', full_name='http_comm_msg.cookie', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accept_charset', full_name='http_comm_msg.accept_charset', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='via', full_name='http_comm_msg.via', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='content_length', full_name='http_comm_msg.content_length', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='content_type', full_name='http_comm_msg.content_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='x_forwarded_for', full_name='http_comm_msg.x_forwarded_for', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='resp_server', full_name='http_comm_msg.resp_server', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2196,
  serialized_end=2486,
)


_DNS_MSG = _descriptor.Descriptor(
  name='dns_msg',
  full_name='dns_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='comm_msg', full_name='dns_msg.comm_msg', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dns_id', full_name='dns_msg.dns_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dns_flags', full_name='dns_msg.dns_flags', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dns_que', full_name='dns_msg.dns_que', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dns_ans', full_name='dns_msg.dns_ans', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dns_auth', full_name='dns_msg.dns_auth', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dns_add', full_name='dns_msg.dns_add', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dns_domain', full_name='dns_msg.dns_domain', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dns_domain_ip', full_name='dns_msg.dns_domain_ip', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dns_query', full_name='dns_msg.dns_query', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dns_answer', full_name='dns_msg.dns_answer', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2489,
  serialized_end=2713,
)


_ESP_MSG = _descriptor.Descriptor(
  name='esp_msg',
  full_name='esp_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='comm_msg', full_name='esp_msg.comm_msg', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='protocol_family', full_name='esp_msg.protocol_family', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='communication_rate', full_name='esp_msg.communication_rate', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direction', full_name='esp_msg.direction', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='encapsulation_mode', full_name='esp_msg.encapsulation_mode', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='esp_spi', full_name='esp_msg.esp_spi', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='esp_seq', full_name='esp_msg.esp_seq', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='esp_data_len', full_name='esp_msg.esp_data_len', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='esp_data', full_name='esp_msg.esp_data', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2716,
  serialized_end=2928,
)


_KEY_VAL_MSG = _descriptor.Descriptor(
  name='key_val_msg',
  full_name='key_val_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='key_val_msg.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='val', full_name='key_val_msg.val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2930,
  serialized_end=2969,
)


_HTTP_HEADER_MSG = _descriptor.Descriptor(
  name='http_header_msg',
  full_name='http_header_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='comm_msg', full_name='http_header_msg.comm_msg', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='act', full_name='http_header_msg.act', index=1,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='http_header_msg.url', index=2,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host', full_name='http_header_msg.host', index=3,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='response', full_name='http_header_msg.response', index=4,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='http_c_finger', full_name='http_header_msg.http_c_finger', index=5,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='http_s_finger', full_name='http_header_msg.http_s_finger', index=6,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='http_client_kv', full_name='http_header_msg.http_client_kv', index=7,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='http_client_title', full_name='http_header_msg.http_client_title', index=8,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='http_server_kv', full_name='http_header_msg.http_server_kv', index=9,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='http_server_title', full_name='http_header_msg.http_server_title', index=10,
      number=13, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2972,
  serialized_end=3252,
)


_L2TP_MSG = _descriptor.Descriptor(
  name='l2tp_msg',
  full_name='l2tp_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='comm_msg', full_name='l2tp_msg.comm_msg', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='protocol_family', full_name='l2tp_msg.protocol_family', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='communication_rate', full_name='l2tp_msg.communication_rate', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direction', full_name='l2tp_msg.direction', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='protocol_version', full_name='l2tp_msg.protocol_version', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='framing_capabilities', full_name='l2tp_msg.framing_capabilities', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bearer_capabilities', full_name='l2tp_msg.bearer_capabilities', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='server_hostname', full_name='l2tp_msg.server_hostname', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='client_hostname', full_name='l2tp_msg.client_hostname', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='server_vendorname', full_name='l2tp_msg.server_vendorname', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='client_vendorname', full_name='l2tp_msg.client_vendorname', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calling_number', full_name='l2tp_msg.calling_number', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='proxy_authen_type', full_name='l2tp_msg.proxy_authen_type', index=12,
      number=13, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='proxy_authen_name', full_name='l2tp_msg.proxy_authen_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_negotiate_success', full_name='l2tp_msg.is_negotiate_success', index=14,
      number=15, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3255,
  serialized_end=3663,
)


_SSH_KEX_MSG = _descriptor.Descriptor(
  name='ssh_kex_msg',
  full_name='ssh_kex_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='protocol', full_name='ssh_kex_msg.protocol', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cookie', full_name='ssh_kex_msg.cookie', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='kex_algorithms', full_name='ssh_kex_msg.kex_algorithms', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='server_host_key_algorithms', full_name='ssh_kex_msg.server_host_key_algorithms', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='encryption_algorithms_client_to_server', full_name='ssh_kex_msg.encryption_algorithms_client_to_server', index=4,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='encryption_algorithms_server_to_client', full_name='ssh_kex_msg.encryption_algorithms_server_to_client', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mac_algorithms_client_to_server', full_name='ssh_kex_msg.mac_algorithms_client_to_server', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mac_algorithms_server_to_client', full_name='ssh_kex_msg.mac_algorithms_server_to_client', index=7,
      number=8, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='compression_algorithms_client_to_server', full_name='ssh_kex_msg.compression_algorithms_client_to_server', index=8,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='compression_algorithms_server_to_client', full_name='ssh_kex_msg.compression_algorithms_server_to_client', index=9,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3666,
  serialized_end=4049,
)


_SSH_MSG = _descriptor.Descriptor(
  name='ssh_msg',
  full_name='ssh_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='comm_msg', full_name='ssh_msg.comm_msg', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='client', full_name='ssh_msg.client', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='server', full_name='ssh_msg.server', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dh_e', full_name='ssh_msg.dh_e', index=3,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dh_f', full_name='ssh_msg.dh_f', index=4,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dh_gex_min', full_name='ssh_msg.dh_gex_min', index=5,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dh_gex_nbits', full_name='ssh_msg.dh_gex_nbits', index=6,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dh_gex_max', full_name='ssh_msg.dh_gex_max', index=7,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dh_gex_p', full_name='ssh_msg.dh_gex_p', index=8,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dh_gex_g', full_name='ssh_msg.dh_gex_g', index=9,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ecdh_q_c', full_name='ssh_msg.ecdh_q_c', index=10,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ecdh_q_s', full_name='ssh_msg.ecdh_q_s', index=11,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_key_type', full_name='ssh_msg.host_key_type', index=12,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_key_rsa_e', full_name='ssh_msg.host_key_rsa_e', index=13,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_key_rsa_n', full_name='ssh_msg.host_key_rsa_n', index=14,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_key_ecdsa_id', full_name='ssh_msg.host_key_ecdsa_id', index=15,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_key_ecdsa_q', full_name='ssh_msg.host_key_ecdsa_q', index=16,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_key_dsa_p', full_name='ssh_msg.host_key_dsa_p', index=17,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_key_dsa_q', full_name='ssh_msg.host_key_dsa_q', index=18,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_key_dsa_g', full_name='ssh_msg.host_key_dsa_g', index=19,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_key_dsa_y', full_name='ssh_msg.host_key_dsa_y', index=20,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_key_eddsa_key', full_name='ssh_msg.host_key_eddsa_key', index=21,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='kex_h_sig', full_name='ssh_msg.kex_h_sig', index=22,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4052,
  serialized_end=4579,
)


_SSL_MSG = _descriptor.Descriptor(
  name='ssl_msg',
  full_name='ssl_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='comm_msg', full_name='ssl_msg.comm_msg', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_version', full_name='ssl_msg.ssl_version', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_c_version', full_name='ssl_msg.ssl_c_version', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_version', full_name='ssl_msg.ssl_hello_c_version', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_time', full_name='ssl_msg.ssl_hello_c_time', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_random', full_name='ssl_msg.ssl_hello_c_random', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_sessionid', full_name='ssl_msg.ssl_hello_c_sessionid', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_sessionidlen', full_name='ssl_msg.ssl_hello_c_sessionidlen', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_ciphersuit', full_name='ssl_msg.ssl_hello_c_ciphersuit', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_ciphersuitnum', full_name='ssl_msg.ssl_hello_c_ciphersuitnum', index=9,
      number=10, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_compressionmethod', full_name='ssl_msg.ssl_hello_c_compressionmethod', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_compressionmethodlen', full_name='ssl_msg.ssl_hello_c_compressionmethodlen', index=11,
      number=12, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_extentionnum', full_name='ssl_msg.ssl_hello_c_extentionnum', index=12,
      number=13, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_extention', full_name='ssl_msg.ssl_hello_c_extention', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_alpn', full_name='ssl_msg.ssl_hello_c_alpn', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_servername', full_name='ssl_msg.ssl_hello_c_servername', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_servernametype', full_name='ssl_msg.ssl_hello_c_servernametype', index=16,
      number=17, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_c_sessionticket', full_name='ssl_msg.ssl_hello_c_sessionticket', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_cert_c_num', full_name='ssl_msg.ssl_cert_c_num', index=18,
      number=19, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_cert_c_hash', full_name='ssl_msg.ssl_cert_c_hash', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_s_version', full_name='ssl_msg.ssl_hello_s_version', index=20,
      number=21, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_s_time', full_name='ssl_msg.ssl_hello_s_time', index=21,
      number=22, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_s_random', full_name='ssl_msg.ssl_hello_s_random', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_s_sessionid', full_name='ssl_msg.ssl_hello_s_sessionid', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_s_sessionidlen', full_name='ssl_msg.ssl_hello_s_sessionidlen', index=24,
      number=25, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_s_cipersuite', full_name='ssl_msg.ssl_hello_s_cipersuite', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_s_compressionmethod', full_name='ssl_msg.ssl_hello_s_compressionmethod', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_s_extentionnum', full_name='ssl_msg.ssl_hello_s_extentionnum', index=27,
      number=28, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_s_extention', full_name='ssl_msg.ssl_hello_s_extention', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_s_alpn', full_name='ssl_msg.ssl_hello_s_alpn', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_hello_s_sessionticket', full_name='ssl_msg.ssl_hello_s_sessionticket', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_cert_s_num', full_name='ssl_msg.ssl_cert_s_num', index=31,
      number=32, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_cert_s_hash', full_name='ssl_msg.ssl_cert_s_hash', index=32,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_s_newsessionticket_lifetime', full_name='ssl_msg.ssl_s_newsessionticket_lifetime', index=33,
      number=34, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_s_newsessionticket_ticket', full_name='ssl_msg.ssl_s_newsessionticket_ticket', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_s_newsessionticket_ticketlen', full_name='ssl_msg.ssl_s_newsessionticket_ticketlen', index=35,
      number=36, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_c_keyexchangelen', full_name='ssl_msg.ssl_c_keyexchangelen', index=36,
      number=37, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_c_keyexchange', full_name='ssl_msg.ssl_c_keyexchange', index=37,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_s_keyexchangelen', full_name='ssl_msg.ssl_s_keyexchangelen', index=38,
      number=39, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_s_keyexchange', full_name='ssl_msg.ssl_s_keyexchange', index=39,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_c_finger', full_name='ssl_msg.ssl_c_finger', index=40,
      number=41, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_s_finger', full_name='ssl_msg.ssl_s_finger', index=41,
      number=42, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4582,
  serialized_end=5877,
)


_RULE_MSG = _descriptor.Descriptor(
  name='rule_msg',
  full_name='rule_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='connect_rule', full_name='rule_msg.connect_rule', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='connect_rule_num', full_name='rule_msg.connect_rule_num', index=1,
      number=2, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='level', full_name='rule_msg.level', index=2,
      number=3, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5879,
  serialized_end=5952,
)


_SINGLE_HTTP = _descriptor.Descriptor(
  name='single_http',
  full_name='single_http',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='url', full_name='single_http.url', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='act', full_name='single_http.act', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host', full_name='single_http.host', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='response', full_name='single_http.response', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_agent', full_name='single_http.user_agent', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5954,
  serialized_end=6045,
)


_SINGLE_DNS_ANSWER = _descriptor.Descriptor(
  name='single_dns_answer',
  full_name='single_dns_answer',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='single_dns_answer.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='single_dns_answer.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6047,
  serialized_end=6095,
)


_SINGLE_DNS = _descriptor.Descriptor(
  name='single_dns',
  full_name='single_dns',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='domain', full_name='single_dns.domain', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain_ip', full_name='single_dns.domain_ip', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='answer', full_name='single_dns.answer', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6097,
  serialized_end=6180,
)


_SINGLE_SSL = _descriptor.Descriptor(
  name='single_ssl',
  full_name='single_ssl',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ch_ciphersuit', full_name='single_ssl.ch_ciphersuit', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ch_ciphersuit_num', full_name='single_ssl.ch_ciphersuit_num', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ch_server_name', full_name='single_ssl.ch_server_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ch_alpn', full_name='single_ssl.ch_alpn', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='c_cert', full_name='single_ssl.c_cert', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='c_cert_num', full_name='single_ssl.c_cert_num', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_cert', full_name='single_ssl.s_cert', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_cert_num', full_name='single_ssl.s_cert_num', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6183,
  serialized_end=6358,
)


_SS_BASIC_MSG = _descriptor.Descriptor(
  name='ss_basic_msg',
  full_name='ss_basic_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='smac', full_name='ss_basic_msg.smac', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dmac', full_name='ss_basic_msg.dmac', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rule', full_name='ss_basic_msg.rule', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rule_num', full_name='ss_basic_msg.rule_num', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rule_level', full_name='ss_basic_msg.rule_level', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='syn', full_name='ss_basic_msg.syn', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='syn_ack', full_name='ss_basic_msg.syn_ack', index=6,
      number=7, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6361,
  serialized_end=6496,
)


_SS_STATS_MSG = _descriptor.Descriptor(
  name='ss_stats_msg',
  full_name='ss_stats_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='stats_stotalsign', full_name='ss_stats_msg.stats_stotalsign', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_dtotalsign', full_name='ss_stats_msg.stats_dtotalsign', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_distbytes', full_name='ss_stats_msg.stats_distbytes', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_distbytesnum', full_name='ss_stats_msg.stats_distbytesnum', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_distcsq', full_name='ss_stats_msg.stats_distcsq', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_distcsqt', full_name='ss_stats_msg.stats_distcsqt', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_sdistlen', full_name='ss_stats_msg.stats_sdistlen', index=6,
      number=7, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_ddistlen', full_name='ss_stats_msg.stats_ddistlen', index=7,
      number=8, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_sdistdur', full_name='ss_stats_msg.stats_sdistdur', index=8,
      number=9, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_ddistdur', full_name='ss_stats_msg.stats_ddistdur', index=9,
      number=10, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_prolist_num', full_name='ss_stats_msg.stats_prolist_num', index=10,
      number=11, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_prolist', full_name='ss_stats_msg.stats_prolist', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sio_sign', full_name='ss_stats_msg.sio_sign', index=12,
      number=13, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dio_sign', full_name='ss_stats_msg.dio_sign', index=13,
      number=14, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ext_json', full_name='ss_stats_msg.ext_json', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_src_mss', full_name='ss_stats_msg.stats_src_mss', index=15,
      number=16, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_dst_mss', full_name='ss_stats_msg.stats_dst_mss', index=16,
      number=17, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_src_window_scale', full_name='ss_stats_msg.stats_src_window_scale', index=17,
      number=18, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_dst_window_scale', full_name='ss_stats_msg.stats_dst_window_scale', index=18,
      number=19, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_spayload_maxlen', full_name='ss_stats_msg.stats_spayload_maxlen', index=19,
      number=20, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_dpayload_maxlen', full_name='ss_stats_msg.stats_dpayload_maxlen', index=20,
      number=21, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_sack_payload_maxlen', full_name='ss_stats_msg.stats_sack_payload_maxlen', index=21,
      number=22, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_dack_payload_maxlen', full_name='ss_stats_msg.stats_dack_payload_maxlen', index=22,
      number=23, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_sack_payload_minlen', full_name='ss_stats_msg.stats_sack_payload_minlen', index=23,
      number=24, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_dack_payload_minlen', full_name='ss_stats_msg.stats_dack_payload_minlen', index=24,
      number=25, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_tcp_info', full_name='ss_stats_msg.stats_tcp_info', index=25,
      number=26, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_distdur', full_name='ss_stats_msg.stats_distdur', index=26,
      number=27, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='syn_seq', full_name='ss_stats_msg.syn_seq', index=27,
      number=28, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='syn_seq_num', full_name='ss_stats_msg.syn_seq_num', index=28,
      number=29, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_sipid_offset', full_name='ss_stats_msg.stats_sipid_offset', index=29,
      number=30, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stats_dipid_offset', full_name='ss_stats_msg.stats_dipid_offset', index=30,
      number=31, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='block_cipher', full_name='ss_stats_msg.block_cipher', index=31,
      number=32, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6499,
  serialized_end=7353,
)


_MD_TCP_MSG = _descriptor.Descriptor(
  name='md_tcp_msg',
  full_name='md_tcp_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bytes', full_name='md_tcp_msg.bytes', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packet_num', full_name='md_tcp_msg.packet_num', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='psh_num', full_name='md_tcp_msg.psh_num', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='acknowledgement', full_name='md_tcp_msg.acknowledgement', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_sequence', full_name='md_tcp_msg.min_sequence', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_sequence', full_name='md_tcp_msg.max_sequence', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7356,
  serialized_end=7489,
)


_PACKET_INFO_MSG = _descriptor.Descriptor(
  name='packet_info_msg',
  full_name='packet_info_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='packet_info_msg.count', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sec', full_name='packet_info_msg.sec', index=1,
      number=6, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='nsec', full_name='packet_info_msg.nsec', index=2,
      number=10, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='len', full_name='packet_info_msg.len', index=3,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7491,
  serialized_end=7563,
)


_SS_PKT_MSG = _descriptor.Descriptor(
  name='ss_pkt_msg',
  full_name='ss_pkt_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pkt_smaxlen', full_name='ss_pkt_msg.pkt_smaxlen', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dmaxlen', full_name='ss_pkt_msg.pkt_dmaxlen', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_snum', full_name='ss_pkt_msg.pkt_snum', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dnum', full_name='ss_pkt_msg.pkt_dnum', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_spayloadnum', full_name='ss_pkt_msg.pkt_spayloadnum', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dpayloadnum', full_name='ss_pkt_msg.pkt_dpayloadnum', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_sbytes', full_name='ss_pkt_msg.pkt_sbytes', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dbytes', full_name='ss_pkt_msg.pkt_dbytes', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_spayloadbytes', full_name='ss_pkt_msg.pkt_spayloadbytes', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dpayloadbytes', full_name='ss_pkt_msg.pkt_dpayloadbytes', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_sfinnum', full_name='ss_pkt_msg.pkt_sfinnum', index=10,
      number=11, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dfinnum', full_name='ss_pkt_msg.pkt_dfinnum', index=11,
      number=12, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_srstnum', full_name='ss_pkt_msg.pkt_srstnum', index=12,
      number=13, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_drstnum', full_name='ss_pkt_msg.pkt_drstnum', index=13,
      number=14, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_ssynnum', full_name='ss_pkt_msg.pkt_ssynnum', index=14,
      number=15, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dsynnum', full_name='ss_pkt_msg.pkt_dsynnum', index=15,
      number=16, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_ssynbytes', full_name='ss_pkt_msg.pkt_ssynbytes', index=16,
      number=17, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dsynbytes', full_name='ss_pkt_msg.pkt_dsynbytes', index=17,
      number=18, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_sttlmax', full_name='ss_pkt_msg.pkt_sttlmax', index=18,
      number=19, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dttlmax', full_name='ss_pkt_msg.pkt_dttlmax', index=19,
      number=20, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_sttlmin', full_name='ss_pkt_msg.pkt_sttlmin', index=20,
      number=21, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dttlmin', full_name='ss_pkt_msg.pkt_dttlmin', index=21,
      number=22, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_sdurmax', full_name='ss_pkt_msg.pkt_sdurmax', index=22,
      number=23, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_ddurmax', full_name='ss_pkt_msg.pkt_ddurmax', index=23,
      number=24, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_sdurmin', full_name='ss_pkt_msg.pkt_sdurmin', index=24,
      number=25, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_ddurmin', full_name='ss_pkt_msg.pkt_ddurmin', index=25,
      number=26, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_sdisorder', full_name='ss_pkt_msg.pkt_sdisorder', index=26,
      number=27, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_ddisorder', full_name='ss_pkt_msg.pkt_ddisorder', index=27,
      number=28, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_sresend', full_name='ss_pkt_msg.pkt_sresend', index=28,
      number=29, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dresend', full_name='ss_pkt_msg.pkt_dresend', index=29,
      number=30, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_slost', full_name='ss_pkt_msg.pkt_slost', index=30,
      number=31, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dlost', full_name='ss_pkt_msg.pkt_dlost', index=31,
      number=32, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_spshnum', full_name='ss_pkt_msg.pkt_spshnum', index=32,
      number=33, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dpshnum', full_name='ss_pkt_msg.pkt_dpshnum', index=33,
      number=34, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_pronum', full_name='ss_pkt_msg.pkt_pronum', index=34,
      number=35, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_unkonw_pronum', full_name='ss_pkt_msg.pkt_unkonw_pronum', index=35,
      number=36, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_infor', full_name='ss_pkt_msg.pkt_infor', index=36,
      number=37, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_syn_data', full_name='ss_pkt_msg.pkt_syn_data', index=37,
      number=38, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_sbadnum', full_name='ss_pkt_msg.pkt_sbadnum', index=38,
      number=39, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dbadnum', full_name='ss_pkt_msg.pkt_dbadnum', index=39,
      number=40, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='app_pkt_id', full_name='ss_pkt_msg.app_pkt_id', index=40,
      number=41, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_spayload', full_name='ss_pkt_msg.pkt_spayload', index=41,
      number=42, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pkt_dpayload', full_name='ss_pkt_msg.pkt_dpayload', index=42,
      number=43, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7566,
  serialized_end=8520,
)


_TCP_FINGER_FEATURE_MSG = _descriptor.Descriptor(
  name='tcp_finger_feature_msg',
  full_name='tcp_finger_feature_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ecn_ip_ect', full_name='tcp_finger_feature_msg.ecn_ip_ect', index=0,
      number=1, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qk_dfnz_ipid', full_name='tcp_finger_feature_msg.qk_dfnz_ipid', index=1,
      number=2, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='flag_cwr', full_name='tcp_finger_feature_msg.flag_cwr', index=2,
      number=3, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='flag_ece', full_name='tcp_finger_feature_msg.flag_ece', index=3,
      number=4, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qk_opt_zero_ts1', full_name='tcp_finger_feature_msg.qk_opt_zero_ts1', index=4,
      number=5, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ttl', full_name='tcp_finger_feature_msg.ttl', index=5,
      number=6, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tcpopt_eol_padnum', full_name='tcp_finger_feature_msg.tcpopt_eol_padnum', index=6,
      number=7, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tcpopt_wscale', full_name='tcp_finger_feature_msg.tcpopt_wscale', index=7,
      number=8, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qk_win_mss', full_name='tcp_finger_feature_msg.qk_win_mss', index=8,
      number=9, type=13, cpp_type=3, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tcpopt_layout', full_name='tcp_finger_feature_msg.tcpopt_layout', index=9,
      number=10, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8523,
  serialized_end=8756,
)


_SINGLE_SESSION_MSG = _descriptor.Descriptor(
  name='single_session_msg',
  full_name='single_session_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='comm_msg', full_name='single_session_msg.comm_msg', index=0,
      number=1, type=11, cpp_type=10, label=2,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ss_basic', full_name='single_session_msg.ss_basic', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ss_stats', full_name='single_session_msg.ss_stats', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ss_pkt', full_name='single_session_msg.ss_pkt', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tcp_c_finger', full_name='single_session_msg.tcp_c_finger', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tcp_s_finger', full_name='single_session_msg.tcp_s_finger', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='http_c_finger', full_name='single_session_msg.http_c_finger', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='http_s_finger', full_name='single_session_msg.http_s_finger', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_c_finger', full_name='single_session_msg.ssl_c_finger', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl_s_finger', full_name='single_session_msg.ssl_s_finger', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tcp_c_finger_feature', full_name='single_session_msg.tcp_c_finger_feature', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tcp_s_finger_feature', full_name='single_session_msg.tcp_s_finger_feature', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='port_list', full_name='single_session_msg.port_list', index=12,
      number=13, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='http', full_name='single_session_msg.http', index=13,
      number=14, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dns', full_name='single_session_msg.dns', index=14,
      number=15, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ssl', full_name='single_session_msg.ssl', index=15,
      number=16, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='single_session_msg.end_time', index=16,
      number=20, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='first_sender', full_name='single_session_msg.first_sender', index=17,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='duration', full_name='single_session_msg.duration', index=18,
      number=22, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='device_id', full_name='single_session_msg.device_id', index=19,
      number=23, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='first_proto', full_name='single_session_msg.first_proto', index=20,
      number=24, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='proxy_ip', full_name='single_session_msg.proxy_ip', index=21,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='proxy_port', full_name='single_session_msg.proxy_port', index=22,
      number=26, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_nsec', full_name='single_session_msg.end_nsec', index=23,
      number=28, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='proxy_real_host', full_name='single_session_msg.proxy_real_host', index=24,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='proxy_type', full_name='single_session_msg.proxy_type', index=25,
      number=30, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='handle_begin_time', full_name='single_session_msg.handle_begin_time', index=26,
      number=31, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='handle_end_time', full_name='single_session_msg.handle_end_time', index=27,
      number=32, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rule_labels', full_name='single_session_msg.rule_labels', index=28,
      number=34, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8759,
  serialized_end=9518,
)


_S7_MSG = _descriptor.Descriptor(
  name='s7_msg',
  full_name='s7_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='comm_msg', full_name='s7_msg.comm_msg', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tpkt_version', full_name='s7_msg.tpkt_version', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cotp_type', full_name='s7_msg.cotp_type', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s7_type', full_name='s7_msg.s7_type', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s7_function', full_name='s7_msg.s7_function', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='system_type', full_name='s7_msg.system_type', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='system_group_function', full_name='s7_msg.system_group_function', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='system_sub_function', full_name='s7_msg.system_sub_function', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dst_ref', full_name='s7_msg.dst_ref', index=8,
      number=9, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='src_ref', full_name='s7_msg.src_ref', index=9,
      number=10, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pdu_size', full_name='s7_msg.pdu_size', index=10,
      number=11, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='src_connect_type', full_name='s7_msg.src_connect_type', index=11,
      number=12, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='src_rack', full_name='s7_msg.src_rack', index=12,
      number=13, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='src_slot', full_name='s7_msg.src_slot', index=13,
      number=14, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dst_connect_type', full_name='s7_msg.dst_connect_type', index=14,
      number=15, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dst_rack', full_name='s7_msg.dst_rack', index=15,
      number=16, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dst_slot', full_name='s7_msg.dst_slot', index=16,
      number=17, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packet_c2s', full_name='s7_msg.packet_c2s', index=17,
      number=18, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9521,
  serialized_end=9914,
)


_MODBUS_MSG = _descriptor.Descriptor(
  name='modbus_msg',
  full_name='modbus_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='comm_msg', full_name='modbus_msg.comm_msg', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_id', full_name='modbus_msg.trans_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='protocol_id', full_name='modbus_msg.protocol_id', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='slave_id', full_name='modbus_msg.slave_id', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='func_code', full_name='modbus_msg.func_code', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packet_c2s', full_name='modbus_msg.packet_c2s', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9917,
  serialized_end=10054,
)


_EIP_MSG = _descriptor.Descriptor(
  name='eip_msg',
  full_name='eip_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='comm_msg', full_name='eip_msg.comm_msg', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_id', full_name='eip_msg.trans_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='protocol_id', full_name='eip_msg.protocol_id', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='slave_id', full_name='eip_msg.slave_id', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='func_code', full_name='eip_msg.func_code', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10056,
  serialized_end=10170,
)


_IEC104_MSG = _descriptor.Descriptor(
  name='iec104_msg',
  full_name='iec104_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='comm_msg', full_name='iec104_msg.comm_msg', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_id', full_name='iec104_msg.trans_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='protocol_id', full_name='iec104_msg.protocol_id', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='slave_id', full_name='iec104_msg.slave_id', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='func_code', full_name='iec104_msg.func_code', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10172,
  serialized_end=10289,
)


_OPC_MSG = _descriptor.Descriptor(
  name='opc_msg',
  full_name='opc_msg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='comm_msg', full_name='opc_msg.comm_msg', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_id', full_name='opc_msg.trans_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='protocol_id', full_name='opc_msg.protocol_id', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='slave_id', full_name='opc_msg.slave_id', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='func_code', full_name='opc_msg.func_code', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10291,
  serialized_end=10405,
)

_JKNMSG.fields_by_name['dns'].message_type = _DNS_MSG
_JKNMSG.fields_by_name['esp'].message_type = _ESP_MSG
_JKNMSG.fields_by_name['http_header'].message_type = _HTTP_HEADER_MSG
_JKNMSG.fields_by_name['l2tp'].message_type = _L2TP_MSG
_JKNMSG.fields_by_name['ssl'].message_type = _SSL_MSG
_JKNMSG.fields_by_name['single_session'].message_type = _SINGLE_SESSION_MSG
_JKNMSG.fields_by_name['s7'].message_type = _S7_MSG
_JKNMSG.fields_by_name['modbus'].message_type = _MODBUS_MSG
_JKNMSG.fields_by_name['eip'].message_type = _EIP_MSG
_JKNMSG.fields_by_name['iec104'].message_type = _IEC104_MSG
_JKNMSG.fields_by_name['opc'].message_type = _OPC_MSG
_JKNMSG.fields_by_name['mac_con'].message_type = _MAC_CON_MSG
_JKNMSG.fields_by_name['noip_con'].message_type = _MAC_CON_MSG
_JKNMSG.fields_by_name['rdp'].message_type = _RDP_MSG
_JKNMSG.fields_by_name['ssh'].message_type = _SSH_MSG
_RDP_MSG.fields_by_name['comm_msg'].message_type = _COMM_MSG
_RDP_MSG.fields_by_name['rdp_client'].message_type = _RDP_CLIENT_MSG
_RDP_MSG.fields_by_name['rdp_server'].message_type = _RDP_SERVER_MSG
_RDP_CLIENT_MSG.fields_by_name['channeldef'].message_type = _RDP_CHANNELDEF
_DNS_MSG.fields_by_name['comm_msg'].message_type = _COMM_MSG
_ESP_MSG.fields_by_name['comm_msg'].message_type = _COMM_MSG
_HTTP_HEADER_MSG.fields_by_name['comm_msg'].message_type = _COMM_MSG
_HTTP_HEADER_MSG.fields_by_name['http_client_kv'].message_type = _KEY_VAL_MSG
_HTTP_HEADER_MSG.fields_by_name['http_server_kv'].message_type = _KEY_VAL_MSG
_L2TP_MSG.fields_by_name['comm_msg'].message_type = _COMM_MSG
_SSH_MSG.fields_by_name['comm_msg'].message_type = _COMM_MSG
_SSH_MSG.fields_by_name['client'].message_type = _SSH_KEX_MSG
_SSH_MSG.fields_by_name['server'].message_type = _SSH_KEX_MSG
_SSL_MSG.fields_by_name['comm_msg'].message_type = _COMM_MSG
_SINGLE_DNS.fields_by_name['answer'].message_type = _SINGLE_DNS_ANSWER
_SS_BASIC_MSG.fields_by_name['rule'].message_type = _RULE_MSG
_SS_STATS_MSG.fields_by_name['stats_tcp_info'].message_type = _MD_TCP_MSG
_SS_PKT_MSG.fields_by_name['pkt_infor'].message_type = _PACKET_INFO_MSG
_SINGLE_SESSION_MSG.fields_by_name['comm_msg'].message_type = _COMM_MSG
_SINGLE_SESSION_MSG.fields_by_name['ss_basic'].message_type = _SS_BASIC_MSG
_SINGLE_SESSION_MSG.fields_by_name['ss_stats'].message_type = _SS_STATS_MSG
_SINGLE_SESSION_MSG.fields_by_name['ss_pkt'].message_type = _SS_PKT_MSG
_SINGLE_SESSION_MSG.fields_by_name['tcp_c_finger_feature'].message_type = _TCP_FINGER_FEATURE_MSG
_SINGLE_SESSION_MSG.fields_by_name['tcp_s_finger_feature'].message_type = _TCP_FINGER_FEATURE_MSG
_SINGLE_SESSION_MSG.fields_by_name['http'].message_type = _SINGLE_HTTP
_SINGLE_SESSION_MSG.fields_by_name['dns'].message_type = _SINGLE_DNS
_SINGLE_SESSION_MSG.fields_by_name['ssl'].message_type = _SINGLE_SSL
_S7_MSG.fields_by_name['comm_msg'].message_type = _COMM_MSG
_MODBUS_MSG.fields_by_name['comm_msg'].message_type = _COMM_MSG
_EIP_MSG.fields_by_name['comm_msg'].message_type = _COMM_MSG
_IEC104_MSG.fields_by_name['comm_msg'].message_type = _COMM_MSG
_OPC_MSG.fields_by_name['comm_msg'].message_type = _COMM_MSG
DESCRIPTOR.message_types_by_name['JKNmsg'] = _JKNMSG
DESCRIPTOR.message_types_by_name['rdp_msg'] = _RDP_MSG
DESCRIPTOR.message_types_by_name['rdp_client_msg'] = _RDP_CLIENT_MSG
DESCRIPTOR.message_types_by_name['rdp_channeldef'] = _RDP_CHANNELDEF
DESCRIPTOR.message_types_by_name['rdp_server_msg'] = _RDP_SERVER_MSG
DESCRIPTOR.message_types_by_name['mac_con_msg'] = _MAC_CON_MSG
DESCRIPTOR.message_types_by_name['Comm_msg'] = _COMM_MSG
DESCRIPTOR.message_types_by_name['http_comm_msg'] = _HTTP_COMM_MSG
DESCRIPTOR.message_types_by_name['dns_msg'] = _DNS_MSG
DESCRIPTOR.message_types_by_name['esp_msg'] = _ESP_MSG
DESCRIPTOR.message_types_by_name['key_val_msg'] = _KEY_VAL_MSG
DESCRIPTOR.message_types_by_name['http_header_msg'] = _HTTP_HEADER_MSG
DESCRIPTOR.message_types_by_name['l2tp_msg'] = _L2TP_MSG
DESCRIPTOR.message_types_by_name['ssh_kex_msg'] = _SSH_KEX_MSG
DESCRIPTOR.message_types_by_name['ssh_msg'] = _SSH_MSG
DESCRIPTOR.message_types_by_name['ssl_msg'] = _SSL_MSG
DESCRIPTOR.message_types_by_name['rule_msg'] = _RULE_MSG
DESCRIPTOR.message_types_by_name['single_http'] = _SINGLE_HTTP
DESCRIPTOR.message_types_by_name['single_dns_answer'] = _SINGLE_DNS_ANSWER
DESCRIPTOR.message_types_by_name['single_dns'] = _SINGLE_DNS
DESCRIPTOR.message_types_by_name['single_ssl'] = _SINGLE_SSL
DESCRIPTOR.message_types_by_name['ss_basic_msg'] = _SS_BASIC_MSG
DESCRIPTOR.message_types_by_name['ss_stats_msg'] = _SS_STATS_MSG
DESCRIPTOR.message_types_by_name['md_tcp_msg'] = _MD_TCP_MSG
DESCRIPTOR.message_types_by_name['packet_info_msg'] = _PACKET_INFO_MSG
DESCRIPTOR.message_types_by_name['ss_pkt_msg'] = _SS_PKT_MSG
DESCRIPTOR.message_types_by_name['tcp_finger_feature_msg'] = _TCP_FINGER_FEATURE_MSG
DESCRIPTOR.message_types_by_name['single_session_msg'] = _SINGLE_SESSION_MSG
DESCRIPTOR.message_types_by_name['s7_msg'] = _S7_MSG
DESCRIPTOR.message_types_by_name['modbus_msg'] = _MODBUS_MSG
DESCRIPTOR.message_types_by_name['eip_msg'] = _EIP_MSG
DESCRIPTOR.message_types_by_name['iec104_msg'] = _IEC104_MSG
DESCRIPTOR.message_types_by_name['opc_msg'] = _OPC_MSG
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

JKNmsg = _reflection.GeneratedProtocolMessageType('JKNmsg', (_message.Message,), dict(
  DESCRIPTOR = _JKNMSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:JKNmsg)
  ))
_sym_db.RegisterMessage(JKNmsg)

rdp_msg = _reflection.GeneratedProtocolMessageType('rdp_msg', (_message.Message,), dict(
  DESCRIPTOR = _RDP_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:rdp_msg)
  ))
_sym_db.RegisterMessage(rdp_msg)

rdp_client_msg = _reflection.GeneratedProtocolMessageType('rdp_client_msg', (_message.Message,), dict(
  DESCRIPTOR = _RDP_CLIENT_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:rdp_client_msg)
  ))
_sym_db.RegisterMessage(rdp_client_msg)

rdp_channeldef = _reflection.GeneratedProtocolMessageType('rdp_channeldef', (_message.Message,), dict(
  DESCRIPTOR = _RDP_CHANNELDEF,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:rdp_channeldef)
  ))
_sym_db.RegisterMessage(rdp_channeldef)

rdp_server_msg = _reflection.GeneratedProtocolMessageType('rdp_server_msg', (_message.Message,), dict(
  DESCRIPTOR = _RDP_SERVER_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:rdp_server_msg)
  ))
_sym_db.RegisterMessage(rdp_server_msg)

mac_con_msg = _reflection.GeneratedProtocolMessageType('mac_con_msg', (_message.Message,), dict(
  DESCRIPTOR = _MAC_CON_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:mac_con_msg)
  ))
_sym_db.RegisterMessage(mac_con_msg)

Comm_msg = _reflection.GeneratedProtocolMessageType('Comm_msg', (_message.Message,), dict(
  DESCRIPTOR = _COMM_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:Comm_msg)
  ))
_sym_db.RegisterMessage(Comm_msg)

http_comm_msg = _reflection.GeneratedProtocolMessageType('http_comm_msg', (_message.Message,), dict(
  DESCRIPTOR = _HTTP_COMM_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:http_comm_msg)
  ))
_sym_db.RegisterMessage(http_comm_msg)

dns_msg = _reflection.GeneratedProtocolMessageType('dns_msg', (_message.Message,), dict(
  DESCRIPTOR = _DNS_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:dns_msg)
  ))
_sym_db.RegisterMessage(dns_msg)

esp_msg = _reflection.GeneratedProtocolMessageType('esp_msg', (_message.Message,), dict(
  DESCRIPTOR = _ESP_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:esp_msg)
  ))
_sym_db.RegisterMessage(esp_msg)

key_val_msg = _reflection.GeneratedProtocolMessageType('key_val_msg', (_message.Message,), dict(
  DESCRIPTOR = _KEY_VAL_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:key_val_msg)
  ))
_sym_db.RegisterMessage(key_val_msg)

http_header_msg = _reflection.GeneratedProtocolMessageType('http_header_msg', (_message.Message,), dict(
  DESCRIPTOR = _HTTP_HEADER_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:http_header_msg)
  ))
_sym_db.RegisterMessage(http_header_msg)

l2tp_msg = _reflection.GeneratedProtocolMessageType('l2tp_msg', (_message.Message,), dict(
  DESCRIPTOR = _L2TP_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:l2tp_msg)
  ))
_sym_db.RegisterMessage(l2tp_msg)

ssh_kex_msg = _reflection.GeneratedProtocolMessageType('ssh_kex_msg', (_message.Message,), dict(
  DESCRIPTOR = _SSH_KEX_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:ssh_kex_msg)
  ))
_sym_db.RegisterMessage(ssh_kex_msg)

ssh_msg = _reflection.GeneratedProtocolMessageType('ssh_msg', (_message.Message,), dict(
  DESCRIPTOR = _SSH_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:ssh_msg)
  ))
_sym_db.RegisterMessage(ssh_msg)

ssl_msg = _reflection.GeneratedProtocolMessageType('ssl_msg', (_message.Message,), dict(
  DESCRIPTOR = _SSL_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:ssl_msg)
  ))
_sym_db.RegisterMessage(ssl_msg)

rule_msg = _reflection.GeneratedProtocolMessageType('rule_msg', (_message.Message,), dict(
  DESCRIPTOR = _RULE_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:rule_msg)
  ))
_sym_db.RegisterMessage(rule_msg)

single_http = _reflection.GeneratedProtocolMessageType('single_http', (_message.Message,), dict(
  DESCRIPTOR = _SINGLE_HTTP,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:single_http)
  ))
_sym_db.RegisterMessage(single_http)

single_dns_answer = _reflection.GeneratedProtocolMessageType('single_dns_answer', (_message.Message,), dict(
  DESCRIPTOR = _SINGLE_DNS_ANSWER,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:single_dns_answer)
  ))
_sym_db.RegisterMessage(single_dns_answer)

single_dns = _reflection.GeneratedProtocolMessageType('single_dns', (_message.Message,), dict(
  DESCRIPTOR = _SINGLE_DNS,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:single_dns)
  ))
_sym_db.RegisterMessage(single_dns)

single_ssl = _reflection.GeneratedProtocolMessageType('single_ssl', (_message.Message,), dict(
  DESCRIPTOR = _SINGLE_SSL,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:single_ssl)
  ))
_sym_db.RegisterMessage(single_ssl)

ss_basic_msg = _reflection.GeneratedProtocolMessageType('ss_basic_msg', (_message.Message,), dict(
  DESCRIPTOR = _SS_BASIC_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:ss_basic_msg)
  ))
_sym_db.RegisterMessage(ss_basic_msg)

ss_stats_msg = _reflection.GeneratedProtocolMessageType('ss_stats_msg', (_message.Message,), dict(
  DESCRIPTOR = _SS_STATS_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:ss_stats_msg)
  ))
_sym_db.RegisterMessage(ss_stats_msg)

md_tcp_msg = _reflection.GeneratedProtocolMessageType('md_tcp_msg', (_message.Message,), dict(
  DESCRIPTOR = _MD_TCP_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:md_tcp_msg)
  ))
_sym_db.RegisterMessage(md_tcp_msg)

packet_info_msg = _reflection.GeneratedProtocolMessageType('packet_info_msg', (_message.Message,), dict(
  DESCRIPTOR = _PACKET_INFO_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:packet_info_msg)
  ))
_sym_db.RegisterMessage(packet_info_msg)

ss_pkt_msg = _reflection.GeneratedProtocolMessageType('ss_pkt_msg', (_message.Message,), dict(
  DESCRIPTOR = _SS_PKT_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:ss_pkt_msg)
  ))
_sym_db.RegisterMessage(ss_pkt_msg)

tcp_finger_feature_msg = _reflection.GeneratedProtocolMessageType('tcp_finger_feature_msg', (_message.Message,), dict(
  DESCRIPTOR = _TCP_FINGER_FEATURE_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:tcp_finger_feature_msg)
  ))
_sym_db.RegisterMessage(tcp_finger_feature_msg)

single_session_msg = _reflection.GeneratedProtocolMessageType('single_session_msg', (_message.Message,), dict(
  DESCRIPTOR = _SINGLE_SESSION_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:single_session_msg)
  ))
_sym_db.RegisterMessage(single_session_msg)

s7_msg = _reflection.GeneratedProtocolMessageType('s7_msg', (_message.Message,), dict(
  DESCRIPTOR = _S7_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:s7_msg)
  ))
_sym_db.RegisterMessage(s7_msg)

modbus_msg = _reflection.GeneratedProtocolMessageType('modbus_msg', (_message.Message,), dict(
  DESCRIPTOR = _MODBUS_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:modbus_msg)
  ))
_sym_db.RegisterMessage(modbus_msg)

eip_msg = _reflection.GeneratedProtocolMessageType('eip_msg', (_message.Message,), dict(
  DESCRIPTOR = _EIP_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:eip_msg)
  ))
_sym_db.RegisterMessage(eip_msg)

iec104_msg = _reflection.GeneratedProtocolMessageType('iec104_msg', (_message.Message,), dict(
  DESCRIPTOR = _IEC104_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:iec104_msg)
  ))
_sym_db.RegisterMessage(iec104_msg)

opc_msg = _reflection.GeneratedProtocolMessageType('opc_msg', (_message.Message,), dict(
  DESCRIPTOR = _OPC_MSG,
  __module__ = 'ZMPNMsg_pb2'
  # @@protoc_insertion_point(class_scope:opc_msg)
  ))
_sym_db.RegisterMessage(opc_msg)


# @@protoc_insertion_point(module_scope)

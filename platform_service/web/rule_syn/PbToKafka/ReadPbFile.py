import os,sys
from kafka_sender import send_msg_str
from  readPb.ZMPNMsg_pb2 import JKNmsg
import json
def change_task_batch(b,stask_id,sbatch_id):
    task_id  = int(stask_id)
    batch_id  = int(sbatch_id)
    msg = JKNmsg()
    msg.ParseFromString(b)
    if msg.type == 30:
        msg.single_session.comm_msg.task_id=task_id
        msg.single_session.comm_msg.batch_id=batch_id
    elif msg.type == 4:
        msg.dns.comm_msg.task_id=task_id
        msg.dns.comm_msg.batch_id=batch_id
    elif msg.type == 80:
        msg.http_header.comm_msg.task_id=task_id
        msg.http_header.comm_msg.batch_id=batch_id
    elif msg.type == 29:
        msg.ssl.comm_msg.task_id=task_id
        msg.ssl.comm_msg.batch_id=batch_id
    elif msg.type == 28:
        msg.ssh.comm_msg.task_id=task_id
        msg.ssh.comm_msg.batch_id=batch_id
    return msg.SerializeToString()

def readPbFile(fileName,task_id, batch_id):
    f = open(fileName , "rb")
    data = f.read()
    f.close()
    print(type(data))
    datalen = len(data)
    num = 0
    ###
    while(len(data) > 0):
        print(data[0:20])
        if data.startswith(b'data_len:') :
            print(len("data_len:"))
            data  = data[len("data_len:"):len(data)]
            pbslen = 0;
            print(data[0:10])
            for i in data:
                #print(i)
                if i == 10:
                    break
                pbslen += 1
            pb_bytes_len =data[:pbslen]
            data = data[pbslen+1:len(data)]
            pblen = int(pb_bytes_len.decode())
            pb_byts = data[:pblen]
            print("send msg len ",pblen)
            #print("bp_bytes ==== ",pb_byts)
            ###
            pb_pyts_new = change_task_batch(pb_byts,task_id,batch_id)
            send_msg_str(pb_pyts_new)
            data = data[pblen +1 :len(data)]
            print(data[0:10])
            num +=1
        else:
            print("pb error ")
            break
    print("发送消息   pb  " , num) 
def scan_file_name(path):
    print(path)
    file_list = []
    for dirpath,dirnames,filenames in os.walk(path):
        if filenames:
             for filename in filenames:
                print(filename)
                if filename.endswith("pb"):
                    file_list.append(os.path.join(dirpath,filename))

    return file_list
       
if __name__ == '__main__':
   if len(sys.argv) == 4:
      task_id=sys.argv[1]
      batch_id=sys.argv[2]
      path=sys.argv[3] 
      file_list = scan_file_name(path)
      print(file_list)
      for filepb in file_list:
          readPbFile(filepb,task_id,batch_id)
   else:
       print("参数错误")
       


import pymysql,os,sys 
import time,json
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()

def idu_mysql(sql,cur,x_db):
    print(sql)
    cur.execute(sql)
    x_db.commit()
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()

def create_task_mysql(path):
    sql = "select max(task_id) +1  as task_id from tb_task_analysis "
    result = s_mysql(sql , cursor)
    task_id  = str(result[0]["task_id"])
    sql = "select max(batch_id) +1  as batch_id from tb_task_batch "
    result = s_mysql(sql , cursor)
    batch_id  = str(result[0]["batch_id"])
    sql  = "REPLACE INTO `tb_line_analyze`(task_id , type_name , text, type) VALUES ('"+str(task_id)+"','LINE', '{}', 1);"
    idu_mysql(sql,cursor,db)
    sql = "insert ignore   INTO `th_analysis`.`tb_task_analysis` ( `task_id`, `task_name`, `netflow`, `task_remark`, `created_time`, `task_state`, `last_suspend_time`, `suspend_times`) VALUES ('"+str(task_id)+"', '导入pcap任务_"+str(task_id)+"', '[]', '导入pcap任务_"+str(task_id)+"',  unix_timestamp(now()), '1', now(), '0');"
    idu_mysql(sql,cursor,db)
    batch_sql = "insert ignore  INTO `th_analysis`.`tb_task_batch` (`batch_id`, `task_id`, `batch_remark`, `fullflow_state`, `flowlog_state`, `data_type`, `topology_state`, `begin_time`, `end_time`, `data_begin_time`, `data_end_time`, `batch_bytes`, `batch_session`, `batch_alarm`, `importrarnt_target`, `batch_dir`, `report_path`, `screening_conditions`, `avg_byte_pt_ps`, `max_byte_pt_ps`, `addr`, `task_update`, `full_flow_should_log_def`, `parse_proto_should_log_def`, `state`) VALUES ('"+str(batch_id)+"', '"+str(task_id)+"', 'pacp导入', 'ON', 'OFF', '3', 'ON', '1582018910', '1582018910', '1582018910', '1582018910', '0', '0', '0', '0', '"+path+"', '', '', '2147483647', '2147483647', '', '1', '1', '1', '1')"
    idu_mysql(batch_sql,cursor,db)
    ddos_sql  = "insert ignore  INTO `th_analysis`.`tb_ddos_state` ( `task_id`, `state`, `ddos_param_json`) VALUES ('"+str(task_id)+"','1', '{\"Type\":[\"IntraIP\",\"LegalIP\",\"All\"],\"IPv4Num\":500000,\"IPv6Num\":1000,\"RenewTime\":6,\"Param\":{\"TimeInterval_Judge\":6,\"BasicLine_bps\":1000,\"BasicLine_PacketNum\":600,\"CheckSum\":[256,256],\"DDOS_CheckSum\":[4,4],\"DDOS_SYN\":4,\"DDOS_FIN\":4,\"DDOS_DNS\":4,\"DDOS_ICMP\":4,\"DDOS_IGMP\":4,\"DDOS_UDP\":4,\"DDOS_Frag\":4,\"DDOS_Multicast\":4,\"MaxOffset_IP\":1250}}');"
    idu_mysql(ddos_sql,cursor,db)
    filter_sql = "replace INTO `tb_filter_state` (task_id , state)  value('"+str(task_id)+"',1);"
    idu_mysql(filter_sql,cursor,db)
    network_sql  =  "replace INTO `tb_network_state`(task_id  , state,network_param_json) VALUES ('"+str(task_id)+"',0,'{\"TimeInterval_Statistics\": 3600,\"TimeInterval_Alert\": 6, \"Param\": {\"BasicLine_PacketNum\": 60, \"Times_ARP\": 4, \"Times_LLDP\": 4, i\"Times_BroadCast\": 4}}');"
    idu_mysql(network_sql,cursor,db)
    
    ###### sql ####
    print("python  ReadPbFile.py "+str(task_id)+" "+str(batch_id) + " "+path)
    os.system("python  ReadPbFile.py "+str(task_id)+" "+str(batch_id) + " "+path)
    os.system(" cd  /opt/GeekSec/web/rule_syn/ &&  python3 /opt/GeekSec/web/rule_syn/task_end.py "+str(task_id)+" "+str(batch_id)+ " "+path)



if __name__=='__main__':
    if len(sys.argv)   == 2:
        path = sys.argv[1]
        create_task_mysql(path)
    else:
        print("参数错误： ")
        print("     python3 importpcap.py  导入pcap文件路径(可以是文件夹)")



# -* coding:utf8 *-  
from pykafka import KafkaClient  
host = '**************:9092'
#host = '***************:9092'
print("afkaClient(hosts = host)- ")
client = KafkaClient(hosts = host)  
# 生产者  
print("ient.topics  ")
topicdocu = client.topics['metatest']  
print("topicdocu.get_producer ")
producer = topicdocu.get_producer()  
print("send  data  ")
    #producer.produce(str_1.encode('utf-8'))  
def send_msg_str(str_1):
     print(str_1)
     producer.produce(str_1.encode('utf-8'))
def send_msg_bytes(str_1):
     print("send_msg_bytes   *********************")
     producer.produce(str_1)

#for i in range(100):  
#    print (i)  
#    str_1 = 'test message ' + str(i ** 2)
#    buf_protcbuf(i)
#producer.stop()


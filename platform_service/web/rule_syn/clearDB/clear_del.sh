#!/bin/bash
PARAM=$1
SQLDB=""
CONF=""
cd /opt/GeekSec/web/rule_syn/clearDB
if [ ! -n "$PARAM" ]; then
    echo "参数不能为空!"
    exit 1
elif [ "$PARAM" == "db" ]; then
    echo "db数据清理!"
    python3 clear_redis.py
    python3 push_database.py
    python3  statistical_information_base.py 10
    python3 clear_es.py
    SQLDB=10
    CONF=110000
elif [ "$PARAM" == "sys" ]; then
    echo "sys系统重置!"
    python3 clear_redis.py
    python3 clear_redis_db1.py
    python3 push_database.py
    python3  statistical_information_base.py 11
    python3 clear_es.py
    SQLDB=11
    CONF=111111
else
    echo "参数错误!"
    exit 1
fi
#python3 /opt/GeekSec/web/rule_syn/th_restart/th_stop.py
/etc/init.d/thdd stop 
kill -9 `ps -eaf | grep sys_psutil.py | grep -v grep | awk '{print $2}'`
kill -9 `ps -eaf | grep pushMsg2Socket.py | grep -v grep | awk '{print $2}'`
pkill -9 LogInfoToOracle
pkill -9 ExportData
systemctl restart mysqld
# 清理mysql
python3 Syslog.py
cd /opt/GeekSec/web/rule_syn/task
python3 task_conf_clear.py ${CONF}
cd /opt/GeekSec/STL
nohup python3 sys_psutil.py >/dev/null &
cd /opt/GeekSec/STL/pushMsgNew
pkill  pushMsgNew
cd /opt/GeekSec/STL/independence_log_deal/bin && ./run.sh
#cd /opt/GeekSec/STL/ExportData/bin && ./run.sh
# 清理es
kill -9 `ps -eaf | grep JsonFile2ES_watchdog.sh | grep -v grep | awk '{print $2}'`
kill -9 `ps -eaf | grep JsonFile2ES | grep -v grep | awk '{print $2}'`
mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ /home/<USER>/
#curl -XDELETE '127.0.0.1:9200/*_20*'
cd /opt/GeekSec/STL/JsonFile2ES
nohup ./JsonFile2ES_watchdog.sh >/dev/null 2>&1 &
#python3 /opt/GeekSec/web/rule_syn/th_restart/th_start.py
/etc/init.d/thdd restart

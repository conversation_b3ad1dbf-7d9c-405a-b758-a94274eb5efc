# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/8/26 18:32
# 文件名称 : clear_es.py
# 开发工具 : PyCharm
import json,os
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
#print("curl -XDELETE '" + base_json["es_clear"] + "'")
os.system("curl -XDELETE '" + base_json["es_es"] + "/*'")
#os.system("curl -XDELETE '" + base_json["es_es"] + "/*_19*'")
#os.system("curl -XDELETE '" + base_json["es_es"] + "/*save*'")
print("es 清理完成")

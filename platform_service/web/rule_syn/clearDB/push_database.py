#!/usr/bin/python
# -*- coding: UTF-8 -*-

import pymysql.cursors,json,os
from loadMysqlPasswd import mysql_passwd
passwd = mysql_passwd()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host='127.0.0.1',port=base_json["db_port"],user='root',password=passwd,db='push_database',charset='utf8',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
idu_mysql("delete from task_statistic where id <> (select * from (select id from task_statistic order by id desc limit 1) b);",cursor,db)
idu_mysql("TRUNCATE `push_database`.`sys_info`;",cursor,db)
idu_mysql("delete from tb_system_time where id <> (select * from (select id from tb_system_time order by id desc limit 1) b);",cursor,db)
idu_mysql("TRUNCATE `push_database`.`tb_system_fifter`;",cursor,db)
idu_mysql("TRUNCATE `push_database`.`tb_network_drop_fifter_pcap`;",cursor,db)
idu_mysql("TRUNCATE `push_database`.`tb_rule_fifter_pcap`;",cursor,db)
idu_mysql("TRUNCATE `push_database`.`tb_network_fifter_pcap`;",cursor,db)
idu_mysql("TRUNCATE `push_database`.`tb_protocl_data`;",cursor,db)
idu_mysql("TRUNCATE `push_database`.`tb_tcp_port_data`;",cursor,db)
idu_mysql("TRUNCATE `push_database`.`tb_udp_port_data`;",cursor,db)
idu_mysql("TRUNCATE `push_database`.`tb_mac_statistics`;",cursor,db)
idu_mysql("TRUNCATE `push_database`.`tb_protocl_pb_stat`;",cursor,db)
idu_mysql("TRUNCATE `push_database`.`tb_netdev_info`;",cursor,db)
idu_mysql("delete from  `push_database`.`tb_mac_mac_communication`;",cursor,db)
os.system("/opt/GeekSec/web/rule_syn/insertsystime.sh")
db.close()


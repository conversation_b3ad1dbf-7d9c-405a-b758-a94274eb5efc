# Last Update:2021-09-01 14:47:31
##
# @file clear_th_conf.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-05-07
import pymysql.cursors,sys,os,json,time
from loadMysqlPasswd import mysql_passwd
passwd = mysql_passwd()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["db_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
tidb = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    #print(sql)
    cur.execute(sql)
    x_db.commit()
def clear_th_conf():
    idu_mysql("truncate tb_task_analysis;",tidb_cursor, tidb)
    ts= int(time.time())
    if base_json['system'] == 'th':
        idu_mysql("truncate tb_task_analysis;",tidb_cursor, tidb)
        idu_mysql("INSERT INTO tb_task_analysis (task_id,task_name,task_remark,created_time,task_state) VALUES (0, '','',"+str(ts)+",0);",tidb_cursor,tidb)
        idu_mysql("truncate tb_task_batch;",tidb_cursor, tidb)
        sql = "INSERT INTO `tb_task_batch` VALUES (1,0,'test','OFF','ON',3,'ON','',1582018910,1582018910,1582018910,1582018910,0,0,0,0,'','',2147483647,2147483647,'',0);"
        idu_mysql(sql,tidb_cursor,tidb)
clear_th_conf()

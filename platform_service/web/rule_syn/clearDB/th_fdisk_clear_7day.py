# 探针清理数据
import json,os,time,sys
from elasticsearch import Elasticsearch
import psutil
import datetime as DT
import datetime 
from loadMysqlPasswd import mysql_passwd
from elasticsearch import Elasticsearch
passwd = mysql_passwd()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
es_ip = base_json["es_es"]
es = Elasticsearch([es_ip])
#index_list = ["connectinfo","http","dns","ssl","ssh","s7","modbus","noip","mac"]
def del_es(index_name):
    cmd  =" curl -XDELETE "+es_ip+"/"+index_name
    print(cmd)
    os.system(cmd)
def def_es_other_data():
    cmd  =" curl -XDELETE "+es_ip+"/*19700101*"
    print(cmd)
    os.system(cmd)
    ### 删除其他没有删除索引的  


def del_es_index(its):
    body_ac={"query":{"bool":{"must":{"range":{"last_time":{"lt":its}}}}}}
    es.delete_by_query(index="es_index",body=body_ac)



    
def str_to_timestamp(str_time=None, format='%Y-%m-%d %H:%M:%S'):
    if str_time:
        time_tuple = time.strptime(str_time, format)  # 把格式化好的时间转换成元祖
        result = time.mktime(time_tuple)  # 把时间元祖转换成时间戳
        return int(result)
    return 0

def del_index_fdisk(date_list,mydir):
    for filename in os.listdir(mydir):
        filepath=os.path.join(mydir,filename)
        if os.path.isdir(filepath) :
            if filename in date_list:
                print("rm -rf "+filepath)
                os.system("rm -rf "+filepath)
            else:
                del_index_fdisk(date_list , filepath)
def del_pcap(st_l):
    #st =  st_l[0:3]+"-"+st_l[4:5]+"-"+st_l[6:7]+" 00:00:00"
    #print(st)
    #hosur_list = []
    hosur  = ts/(3600*4)
    for i  in range(6):
        hosur_list = [str(hosur+i)]
    print("hosur_list ===== ",hosur_list) 
    del_index_fdisk(hosur_list,"/data/pcapfiles/")


def del_json_data(st_l): 
  
    #date_s = data_str[0:4]+"-"+data_str[4:6]+"-"+data_str[7:9]+ " 00:00:00" 
    #print(date_s)
    #ts =str_to_timestamp(date_s)/(3600*4))
    ts =st_l/(3600*4)
    date_list=[]
    for i in range(6):
         date_list.append(str(ts+i))
    print(date_list)
    del_index_fdisk(date_list,"/data/json_data/")



    # 删除文件
    pass
today = DT.date.today()
def get_del_date(i):
    ts   = int(int(time.time())/(3600*24) - i ) * 3600*24
    return ts 
### 取 7 天前的时间
def scan_es_new(num_day):
     ts = get_del_date(num_day)
     body_ac = {"query":{"bool":{"must":[{"range":{"last_time":{"lt":ts}}}],"must_not":[{"range":{"first_time":{"lte":0}}}]}},"aggs":{"min_time":{"min":{"field":"first_time"}}}}
     result =  es.search(index="es_index",body=body_ac)
     index_list = []
     for  hits in result["hits"]["hits"]:
         index_list.append(hits["_source"]["index"])
     if len(index_list) == 0:
         return 
     index_first_time  = int(int(result["aggregations"]["min_time"]["value"])/(24*3600))*3600*24
     for i in range(index_first_time , ts ,3600*24 ):
         timeArray = time.localtime(i+1)
         day = time.strftime("%Y%m%d", timeArray)
         del_json_data(i)
         del_pcap(i)
     for index_name in index_list:
         del_es(index_name)
     def_es_other_data()
     del_es_index(ts)

     ###
def scan_es_time(ts):
     body_ac = {"query":{"bool":{"must":[{"range":{"last_time":{"lt":ts}}}],"must_not":[{"range":{"first_time":{"lte":0}}}]}},"aggs":{"min_time":{"min":{"field":"first_time"}}}}
     result =  es.search(index="es_index",body=body_ac)
     index_list = []
     for  hits in result["hits"]["hits"]:
         index_list.append(hits["_source"]["index"])
     if len(index_list) == 0:
         return 
     index_first_time  = int(int(result["aggregations"]["min_time"]["value"])/(24*3600))*3600*24
     for i in range(index_first_time , ts ,3600*24 ):
         timeArray = time.localtime(i+1)
         day = time.strftime("%Y%m%d", timeArray)
         del_json_data(i)
         del_pcap(i)
     for index_name in index_list:
         del_es(index_name)
     def_es_other_data()
     del_es_index(ts)

def scan_es_last_time():
    body_ac = {"query":{"bool":{"must_not":[{"range":{"first_time":{"lte":0}}}]}},"aggs":{"min_time":{"min":{"field":"first_time"}}},"size":0}
    print(json.dumps(body_ac))
    result =  es.search(index="es_index",body=body_ac)
    index_first_time = 0
    if "min_time" in  result["aggregations"] and result["aggregations"]["min_time"] != None and  "value" in result["aggregations"]["min_time"] and  result["aggregations"]["min_time"]["value"] != None:
        index_first_time  = int(int(result["aggregations"]["min_time"]["value"])/(24*3600))*3600*24
    return index_first_time 
## 旧版索引删除数据
def scan_es():
    curl_day_index = {}
    cmd = "curl -s '" + es_ip + "/_cat/indices?v'|  awk '{print $3}'"
    p = os.popen(cmd)
    es_es_es = p.read()
    es_list = es_es_es.split("\n")
    for i in range(len(es_list)):
        if i == 0:
            continue 
        else:
            index_name  = es_list[i]
            index_name_list = index_name.split("_")
            day = index_name_list[len(index_name_list) -1]
            if day == "":
                continue
            if day.isdigit() == False:
                continue
            if day not in  curl_day_index :
                curl_day_index[day] = []
            curl_day_index[day].append(index_name)
    a2 = sorted(curl_day_index.items(), key=lambda x: x[0])
    print(a2)
    for  t in  a2:
        day =  t[0] 
        print(day)
        if len(day) != 8:
             continue
        s_w = week_ago.strftime('%Y%m%d')
        print(day)
        print(s_w)
        if day > s_w:
            print("no data del")           
            return ;
        for a1 in t[1]:
            print("del ES" + a1)
            del_es(a1)
        print("del json ",day)
        del_json_data(day)
        time.sleep(10)
# 
  ####   nn
if __name__ == '__main__':
    if len(sys.argv) == 2:
        if sys.argv[1] == "7day":
            scan_es_new(7)
            fdisk=psutil.disk_usage('/var/ftp/')
            if  fdisk.percent > 80:
                scan_es_new(3)
            fdisk=psutil.disk_usage('/var/ftp/')
            if  fdisk.percent > 80:
                scan_es_new(1)
    else:
        tfirsttime =  scan_es_last_time()
        if tfirsttime == 0 :
            sys.exit(1)
        tlasttime = get_del_date()
        for i in range(tfirsttime , tlasttime ,3600*24):
            fdisk=psutil.disk_usage('/var/ftp/')
            if  fdisk.percent > 80:
                scan_es_time(i)
            else:
                break


               

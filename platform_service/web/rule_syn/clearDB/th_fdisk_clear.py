# 探针清理数据
import json,os,time
from elasticsearch import Elasticsearch
import psutil
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
es_ip = base_json["es_es"]
#index_list = ["connectinfo","http","dns","ssl","ssh","s7","modbus","noip","mac"]
def del_es(index_name):
    cmd  =" curl -XDELETE "+es_ip+"/"+index_name
    print(cmd)
    os.system(cmd)
def str_to_timestamp(str_time=None, format='%Y-%m-%d %H:%M:%S'):
    if str_time:
        time_tuple = time.strptime(str_time, format)  # 把格式化好的时间转换成元祖
        result = time.mktime(time_tuple)  # 把时间元祖转换成时间戳
        return int(result)
    return 0

def del_index_fdisk(date_list,mydir):
    for filename in os.listdir(mydir):
        filepath=os.path.join(mydir,filename)
        if os.path.isdir(filepath) :
            if filename in date_list:
                print("rm -rf "+filepath)
                os.system("rm -rf "+filepath)
            else:
                del_index_fdisk(date_list , filepath)
def del_pcap(st_l):
    st =  st_l[0:3]+"-"+st_l[4:5]+"-"+st_l[6:7]+" 00:00:00"
    print(st)
    hosur_list = []
    hosur  = str_toTimestamp(st, "%Y-%m-%d %H:%M:%S")/(3600*4)
    for i  in range(6):
        hosur_list = [str(hosur+i)]
    del_index_fdisk(hosur_list,"/data/pcapfiles/")


def del_json_data(data_str): 
    date_list=[]
    date_list.append(data_str)
    del_index_fdisk(date_list,"/data/json_data/")



    # 删除文件
    pass
def scan_es():
    curl_day_index = {}
    cmd = "curl -s '" + es_ip + "/_cat/indices?v'|  awk '{print $3}'"
    p = os.popen(cmd)
    es_es_es = p.read()
    print(es_es_es)
    es_list = es_es_es.split("\n")
    for i in range(len(es_list)):
        if i == 0:
            continue 
        else:
            index_name  = es_list[i]
            index_name_list = index_name.split("_")
            day = index_name_list[len(index_name_list) -1]
            if day == "":
                continue
            if day.isdigit() == False:
                continue
            if day not in  curl_day_index :
                curl_day_index[day] = []
            curl_day_index[day].append(index_name)
    a2 = sorted(curl_day_index.items(), key=lambda x: x[0])
    print(a2)
    for a1 in a2[0][1]:
        print(a1)
        print(a2[0][0])
        del_es(a1)
    del_json_data(a2[0][0])
    time.sleep(10)
while(True):
    #if base_json['system'] != 'th':
    #    break
    fdisk=psutil.disk_usage('/var/ftp/')
    print(fdisk.percent)
    if  fdisk.percent > 80:
        scan_es() 
    else:
        break

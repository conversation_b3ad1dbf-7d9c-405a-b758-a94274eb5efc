# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/8/21 11:45
# 文件名称 : tag_hz.py
# 开发工具 : PyCharm
import json,pymysql.cursors,sys,hashlib,time
# 将ASC转String
def ascToStr(str):
    strObj = ''
    strArr = str.split(",")
    for row in strArr:
        strObj = strObj + chr(int(row))
    return strObj
# 将String计算Hash
def strToHash(str):
    hhaasshh = hashlib.sha1()
    hhaasshh.update(str.encode("utf8"))
    strHash = hhaasshh.hexdigest()
    return strHash
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)

tidb = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password='root',db='statistical_information_base',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()

# 获取参数
param = sys.argv[1]
paramStr = ascToStr(param)
#paramStr = '{"key":"","bt":"0","et":"2222222222","bw":"0","ew":"100","bb":"0","eb":"100"}'
paramHash = strToHash(paramStr)
paramJson = json.loads(paramStr)
if paramJson["key"] != "":
    paramJson["key"] = "AND t1.Tag_Text = '" + paramJson["key"] + "' "
b_time = 0
e_time = 0
if paramJson["bt"] != "" or paramJson["et"] != "":
    if paramJson["bt"] != "" and paramJson["et"] != "":
        b_time = int(paramJson["bt"])
        e_time = int(paramJson["et"])
    if paramJson["bt"] != "" and paramJson["et"] == "":
        b_time = int(paramJson["bt"])
        e_time = b_time + 7 * 24 * 3600
    if paramJson["bt"] == "" and paramJson["et"] != "":
        e_time = int(paramJson["et"])
        b_time = e_time - 7 * 24 * 3600
else:
    e_time = int(time.time())
    b_time = e_time - 7 * 24 * 3600
objects = {}
new_time_arr = []
new_obj_arr = []
res_ip_tag = s_mysql("SELECT t1.Tag_Text as tag_name,DATE_FORMAT(FROM_UNIXTIME(t2.created_time),'%Y-%m-%d %H') as hours,COUNT(t1.Tag_Text) AS tag_num FROM TAG_INFO t1,ip_tag t2,IP_LIST t3 WHERE t2.ip = t3.IP and t1.Tag_Id = t2.tagId " + paramJson["key"] + "AND t2.created_time BETWEEN " + str(b_time) + " AND " + str(e_time) + " AND t3.blackList BETWEEN " + paramJson["bb"] + " AND " + paramJson["eb"] + " AND t3.whiteList BETWEEN " + paramJson["bw"] + " AND " + paramJson["ew"] + " group by DATE_FORMAT(FROM_UNIXTIME(t2.created_time),'%Y-%m-%d %H'),t1.Tag_Text", tidb_cursor)
res_port_tag = s_mysql("SELECT t1.Tag_Text as tag_name,DATE_FORMAT(FROM_UNIXTIME(t2.created_time),'%Y-%m-%d %H') as hours,COUNT(t1.Tag_Text) AS tag_num FROM TAG_INFO t1,PORT_TAG t2,PORT_LIST t3 WHERE t2.Port = t3.Port and t1.Tag_Id = t2.tagId " + paramJson["key"] + "AND t2.created_time BETWEEN " + str(b_time) + " AND " + str(e_time) + " AND t3.blackList BETWEEN " + paramJson["bb"] + " AND " + paramJson["eb"] + " AND t3.whiteList BETWEEN " + paramJson["bw"] + " AND " + paramJson["ew"] + " group by DATE_FORMAT(FROM_UNIXTIME(t2.created_time),'%Y-%m-%d %H'),t1.Tag_Text", tidb_cursor)
res_app_tag = s_mysql("SELECT t1.Tag_Text as tag_name,DATE_FORMAT(FROM_UNIXTIME(t2.created_time),'%Y-%m-%d %H') as hours,COUNT(t1.Tag_Text) AS tag_num FROM TAG_INFO t1,APP_TAG t2,APP_LIST t3 WHERE t2.App_Id = t3.App_Id and t1.Tag_Id = t2.tagId " + paramJson["key"] + "AND t2.created_time BETWEEN " + str(b_time) + " AND " + str(e_time) + " AND t3.blackList BETWEEN " + paramJson["bb"] + " AND " + paramJson["eb"] + " AND t3.whiteList BETWEEN " + paramJson["bw"] + " AND " + paramJson["ew"] + " group by DATE_FORMAT(FROM_UNIXTIME(t2.created_time),'%Y-%m-%d %H'),t1.Tag_Text", tidb_cursor)
res_session_tag = s_mysql("SELECT t1.Tag_Text as tag_name,DATE_FORMAT(FROM_UNIXTIME(t2.created_time),'%Y-%m-%d %H') as hours,COUNT(t1.Tag_Text) AS tag_num FROM TAG_INFO t1,SESSION_ID_TAG t2,SESSION_ID_LIST t3 WHERE t2.session_id = t3.session_id and t1.Tag_Id = t2.tag_id " + paramJson["key"] + "AND t2.created_time BETWEEN " + str(b_time) + " AND " + str(e_time) + " AND t3.blackList BETWEEN " + paramJson["bb"] + " AND " + paramJson["eb"] + " AND t3.whiteList BETWEEN " + paramJson["bw"] + " AND " + paramJson["ew"] + " group by DATE_FORMAT(FROM_UNIXTIME(t2.created_time),'%Y-%m-%d %H'),t1.Tag_Text", tidb_cursor)
res_cert_tag = s_mysql("SELECT t1.Tag_Text as tag_name,DATE_FORMAT(FROM_UNIXTIME(t2.created_time),'%Y-%m-%d %H') as hours,COUNT(t1.Tag_Text) AS tag_num FROM TAG_INFO t1,Cert_TAG t2,CertInfo t3 WHERE t2.CertSHA1 = t3.CertSHA1 and t1.Tag_Id = t2.tagId " + paramJson["key"] + "AND t2.created_time BETWEEN " + str(b_time) + " AND " + str(e_time) + " AND t3.blackList BETWEEN " + paramJson["bb"] + " AND " + paramJson["eb"] + " AND t3.whiteList BETWEEN " + paramJson["bw"] + " AND " + paramJson["ew"] + " group by DATE_FORMAT(FROM_UNIXTIME(t2.created_time),'%Y-%m-%d %H'),t1.Tag_Text", tidb_cursor)
res_domain_tag = s_mysql("SELECT t1.Tag_Text as tag_name,DATE_FORMAT(FROM_UNIXTIME(t2.created_time),'%Y-%m-%d %H') as hours,COUNT(t1.Tag_Text) AS tag_num FROM TAG_INFO t1,DOMAIN_TAG t2,DOMAIN_INFO t3 WHERE t2.Domain_Name = t3.Domain_Name and t1.Tag_Id = t2.Tag_Id " + paramJson["key"] + "AND t2.created_time BETWEEN " + str(b_time) + " AND " + str(e_time) + " AND t3.blackList BETWEEN " + paramJson["bb"] + " AND " + paramJson["eb"] + " AND t3.whiteList BETWEEN " + paramJson["bw"] + " AND " + paramJson["ew"] + " group by DATE_FORMAT(FROM_UNIXTIME(t2.created_time),'%Y-%m-%d %H'),t1.Tag_Text", tidb_cursor)
res_mac_tag = s_mysql("SELECT t1.Tag_Text as tag_name,DATE_FORMAT(FROM_UNIXTIME(t2.created_time),'%Y-%m-%d %H') as hours,COUNT(t1.Tag_Text) AS tag_num FROM TAG_INFO t1,mac_tag t2,mac_list t3 WHERE t2.mac = t3.mac and t1.Tag_Id = t2.tagId " + paramJson["key"] + "AND t2.created_time BETWEEN " + str(b_time) + " AND " + str(e_time) + " AND t3.blackList BETWEEN " + paramJson["bb"] + " AND " + paramJson["eb"] + " AND t3.whiteList BETWEEN " + paramJson["bw"] + " AND " + paramJson["ew"] + " group by DATE_FORMAT(FROM_UNIXTIME(t2.created_time),'%Y-%m-%d %H'),t1.Tag_Text", tidb_cursor)
res_arr_tag = []
if len(res_ip_tag) > 0:
    res_arr_tag = res_arr_tag + res_ip_tag
if len(res_port_tag) > 0:
    res_arr_tag = res_arr_tag + res_port_tag
if len(res_app_tag) > 0:
    res_arr_tag = res_arr_tag + res_app_tag
if len(res_session_tag) > 0:
    res_arr_tag = res_arr_tag + res_session_tag
if len(res_cert_tag) > 0:
    res_arr_tag = res_arr_tag + res_cert_tag
if len(res_domain_tag) > 0:
    res_arr_tag = res_arr_tag + res_domain_tag
if len(res_mac_tag) > 0:
    res_arr_tag = res_arr_tag + res_mac_tag
if len(res_arr_tag) > 0:
    for row in res_arr_tag:
        if row["hours"] not in new_time_arr:
            tag_hz_obj = {}
            tag = []
            tag_name_num = {}
            tag_name_num["tag_name"] = row["tag_name"]
            tag_name_num["tag_num"] = row["tag_num"]
            tag.append(tag_name_num)
            tag_hz_obj["time"] = row["hours"]
            tag_hz_obj["total_num"] = row["tag_num"]
            tag_hz_obj["tag"] = tag
            new_time_arr.append(row["hours"])
            new_obj_arr.append(tag_hz_obj)
        else:
            break_state = 0
            for row_add in new_obj_arr:
                if row["hours"] == row_add["time"]:
                    row_add["total_num"] = row_add["total_num"] + row["tag_num"]
                    for row_add_tag in row_add["tag"]:
                        if row["tag_name"] == row_add_tag["tag_name"]:
                            row_add_tag["tag_num"] = row_add_tag["tag_num"] + row["tag_num"]
                            break_state = 1
                            break
                    if break_state == 0:
                        tag_name_num_1 = {}
                        tag_name_num_1["tag_name"] = row["tag_name"]
                        tag_name_num_1["tag_num"] = row["tag_num"]
                        row_add["tag"].append(tag_name_num_1)
                    else:
                        break
#print(new_time_arr)
#print(new_obj_arr)
objects["count"] = len(new_obj_arr)
objects["list"] = new_obj_arr
#print(json.dumps(objects,ensure_ascii=False))
flag = s_mysql("select count(*) as num from tb_cache where hash = '" + paramHash + "';", tidb_cursor)
if flag[0]['num'] > 0:
    idu_mysql("update tb_cache set text = '" + json.dumps(objects,ensure_ascii=False) + "',param = '" + paramStr + "' where hash = '" + paramHash + "';", tidb_cursor, tidb)
else:
    idu_mysql("insert into tb_cache (text,param,hash) values ('" + json.dumps(objects,ensure_ascii=False) + "','" + paramStr + "','" + paramHash + "');", tidb_cursor, tidb)
tidb.close()
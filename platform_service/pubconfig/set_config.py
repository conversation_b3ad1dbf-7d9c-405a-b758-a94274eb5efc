# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/8/11 20:20
# 文件名称 : set_config.py
# 开发工具 : PyCharm
import os,json

def get_new_json(filepath,key,value):
    key_ = key.split(".")
    key_length = len(key_)
    with open(filepath, 'rb') as f:
        json_data = json.load(f)
        i = 0
        a = json_data
        while i < key_length :
            if i+1 == key_length :
                a[key_[i]] = value
                i = i + 1
            else :
                a = a[key_[i]]
                i = i + 1
    f.close()
    return json_data

def rewrite_json_file(filepath,json_data):
    with open(filepath, 'w') as f:
        json.dump(json_data,f,indent=4,separators=(',', ': '))
    f.close()

def base(path,key,ips):
    m_json_data = get_new_json(path,key,ips)
    rewrite_json_file(path,m_json_data)

base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)

os.system("cp -r /var/www/html/static/config.json /opt/GeekSec/pubconfig/config/vue_config.json")
os.system("cp -r /opt/GeekSec/web/web_server/bin/config.json /opt/GeekSec/pubconfig/config/web_server_config.json")
os.system("cp -r /opt/GeekSec/web/WebHandle/bin/resources/config.json /opt/GeekSec/pubconfig/config/WebHandle_config.json")
os.system("cp -r /opt/GeekSec/STL/JsonFile2ES/ESconf.json /opt/GeekSec/pubconfig/config/JsonFile2ES_ESconf.json")

try:
    # vue
    base("/opt/GeekSec/pubconfig/config/vue_config.json", "BASE_API", base_json["BASE_API"])
    base("/opt/GeekSec/pubconfig/config/vue_config.json", "WS_API", base_json["WS_API"])
    base("/opt/GeekSec/pubconfig/config/vue_config.json", "WS_API_V2", base_json["WS_API_V2"])
    base("/opt/GeekSec/pubconfig/config/vue_config.json", "WS_API_CERT", base_json["WS_API_CERT"])
    base("/opt/GeekSec/pubconfig/config/vue_config.json", "DOWNLOAD_API", base_json["DOWNLOAD_API"])
    base("/opt/GeekSec/pubconfig/config/vue_config.json", "BASE_API_V2", base_json["BASE_API_V2"])
    base("/opt/GeekSec/pubconfig/config/vue_config.json", "BASE_API_V2_CERT", base_json["BASE_API_V2_CERT"])
    base("/opt/GeekSec/pubconfig/config/vue_config.json", "rulesDownloadIP", base_json["rulesDownloadIP"])
    # web_server
    base("/opt/GeekSec/pubconfig/config/web_server_config.json", "server_port", base_json["webserver_server_port"])
    base("/opt/GeekSec/pubconfig/config/web_server_config.json", "ws_service_port", base_json["webserver_ws_port"])
    base("/opt/GeekSec/pubconfig/config/web_server_config.json", "db_addr", base_json["db_addr"])
    base("/opt/GeekSec/pubconfig/config/web_server_config.json", "es_url", base_json["es_url"])
    base("/opt/GeekSec/pubconfig/config/web_server_config.json", "ws_host", base_json["cert_ws"])
    # WebHandle
    base("/opt/GeekSec/pubconfig/config/WebHandle_config.json", "port", base_json["webhandle_port"])
    base("/opt/GeekSec/pubconfig/config/WebHandle_config.json", "es_url", base_json["es_url"])
    base("/opt/GeekSec/pubconfig/config/WebHandle_config.json", "db_addr", base_json["db_addr"])
    # JsonFile2ES
    base("/opt/GeekSec/pubconfig/config/JsonFile2ES_ESconf.json", "ES_URL", base_json["es_es"])
except Exception as e:
    print(e + "更新配置Error! -- code:001")
    exit(0)

os.system("cp -r /opt/GeekSec/pubconfig/config/vue_config.json /var/www/html/static/config.json")
os.system("cp -r /opt/GeekSec/pubconfig/config/web_server_config.json /opt/GeekSec/web/web_server/bin/config.json")
os.system("pkill -9 api")
os.system("/opt/GeekSec/web/web_server/start.sh")
os.system("cp -r /opt/GeekSec/pubconfig/config/WebHandle_config.json /opt/GeekSec/web/WebHandle/bin/resources/config.json")
os.system("/opt/GeekSec/web/WebHandle/bin/stop.sh")
os.system("cd /opt/GeekSec/web/WebHandle/bin && nohup ./start.sh >/dev/null &")
os.system("cp -r /opt/GeekSec/pubconfig/config/JsonFile2ES_ESconf.json /opt/GeekSec/STL/JsonFile2ES/ESconf.json")
os.system("kill -9 `ps -eaf | grep watchdog.sh | grep -v grep | awk '{print $2}'`")
os.system("kill -9 `ps -eaf | grep JsonFile2ES | grep -v grep | awk '{print $2}'`")
os.system("cd /opt/GeekSec/STL/JsonFile2ES && nohup ./watchdog.sh >/dev/null &")
print("我爱你中国！")

# DiskSpaceReclaimer.py
# Purpose: Data cleanup utility for removing expired data and freeing up disk space.
# Author: hufengkai
# Version: 1.0.0
# Date: 2024-01-25
import argparse
import json
import logging
import os
import shutil
import subprocess
import time
from datetime import datetime, timed<PERSON><PERSON>
from glob import glob
from pathlib import Path

import docker
import psutil
from elasticsearch import Elasticsearch

import es_index_metadata_generator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

now = datetime.now()

# Constants
ES_META_INDEX = "es_index"
DATA_PATH = "/data"
LMDB_PATH = "/data/lmdb"
PCAP_SUBDIR = "pcapfiles"
FULL_FLOW_SUBDIR = "full_flow"
RULE_SUBDIR = "rule"
ATTACK_SUBDIR = "attack"
NOIP_PACKET_SUBDIR = "noip_packet"


def load_config(config_path):
    with open(config_path, 'r') as load_file:
        return json.load(load_file)


def get_es_client(es_url):
    return Elasticsearch([es_url])


def delete_es_index(es, index_name):
    try:
        es.indices.delete(index=index_name, params={"allow_no_indices": True, "ignore_unavailable": True})
    except Exception as e:
        logging.error(f"Failed to delete index {index_name}: {e}")


def delete_alarm_index(es, hb_time, task):
    date = timestamp_to_date(hb_time + 1)
    index_name = f'alarm_{task}_{date}'
    delete_es_index(es, index_name)


def delete_es_other_data(es):
    delete_es_index(es, '*19700101*')


def delete_directories(pattern):
    for directory in glob(pattern):
        try:
            shutil.rmtree(directory)
        except OSError as e:
            logging.error(f"Error deleting directory {directory}: {e.strerror}")


def rebuild_es_meta_index(es):
    es.indices.delete(index=ES_META_INDEX)
    es_index_metadata_generator.main()


def preserve_rule_matched_pcap_file(file_path: Path):
    # pcap文件路径为
    # '/data/0/100001/pcapfiles/1/full_flow/118435/1705470108_0.pcap'
    # 或
    # '/data/0/100001/pcapfiles/1/rule/118435/1705470108_0.pcap'
    # 前者为全流量留存的pcap文件，后者为采集规则命中的流量文件
    file_name = file_path.name
    rule_dir_path = file_path.parents[3] / 'rule' / '/'.join(file_path.parts[4:])
    Path(rule_dir_path).mkdir(parents=True, exist_ok=True)
    cmd = ["/opt/GeekSec/th/bin/pcap_filter", "-r", file_path, "-w", os.path.join(rule_dir_path, file_name), "-rule"]
    subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)


def timestamp_to_date(timestamp):
    return datetime.fromtimestamp(timestamp)


def delete_expired_lmdb_dirs(expiration_date, task):
    """从文件系统中删除所有过期的LMDB目录。
        Args:
            expiration_date (datetime): 截止日期，用于判断LMDB目录是否过期。
            task (str): 任务ID，用于筛选相关的LMDB目录。
        LMDB目录名的格式为"0_100001_20220729_84272731.354@1"，各部分含义如下：
            - 部分1: task_id (任务ID)
            - 部分2: batch_id (批处理ID)
            - 部分3: 日期，格式为'YYYYMMDD'
            - 部分4: 时间戳和序列号(在@后面)
    """
    db_dirs = [db_dir for db_dir in os.listdir(LMDB_PATH) if db_dir.startswith(f'{task}_')]
    for db_dir in db_dirs:
        db_date_str = db_dir.split("_")[2]
        try:
            db_date = datetime.strptime(db_date_str, '%Y%m%d')
        except ValueError:
            continue
        if db_date < expiration_date:
            shutil.rmtree(os.path.join(LMDB_PATH, db_dir))


def get_cutoff_date(retain_days):
    cutoff_date = now - timedelta(days=retain_days)
    return cutoff_date.replace(hour=16, minute=0, second=0, microsecond=0)


def get_cutoff_time(retain_days):
    return int(time.time()) - retain_days


def delete_pcap(pcap_path, is_full_flow_enabled, preserve_by_rule, cutoff_timeslot):
    if not pcap_path.is_dir():
        return
    for item in pcap_path.iterdir():
        if item.is_dir():
            if len(item.name) > 5 and item.name.isnumeric():
                if int(item.name) < cutoff_timeslot:
                    if is_full_flow_enabled and preserve_by_rule:
                        for file_path in item.iterdir():
                            preserve_rule_matched_pcap_file(file_path)
                    shutil.rmtree(item)
            else:
                delete_pcap(item, is_full_flow_enabled, preserve_by_rule, cutoff_timeslot)


def delete_pcap_for_task(cutoff_timeslot, is_full_flow_enabled, preserve_by_rule, task):
    task_path = Path(DATA_PATH) / task
    thread_paths = task_path.glob(f'*/{PCAP_SUBDIR}/*')
    for thread_path in thread_paths:
        if thread_path.is_dir():
            delete_pcap(thread_path / FULL_FLOW_SUBDIR, is_full_flow_enabled, preserve_by_rule, cutoff_timeslot)
            delete_pcap(thread_path / RULE_SUBDIR, False, False, cutoff_timeslot)


def delete_merged_pcap_files(cutoff_timeslot, is_full_flow_enabled, should_preserve_by_rule, task=None):
    if task:
        delete_pcap_for_task(cutoff_timeslot, is_full_flow_enabled, should_preserve_by_rule, task)
    else:
        paths = os.listdir(DATA_PATH)
        for path in paths:
            if path.isnumeric():
                delete_pcap_for_task(cutoff_timeslot, is_full_flow_enabled, should_preserve_by_rule, path)


### 取 7 天前的时间
def delete_data_older_than(es, days, is_full_flow_enabled, preserve_by_rule, task=None):
    delete_directories(f"{DATA_PATH}/*/*/{PCAP_SUBDIR}/*/{ATTACK_SUBDIR}/*/*")
    delete_directories(f"{DATA_PATH}/*/*/{PCAP_SUBDIR}/*/{NOIP_PACKET_SUBDIR}/*/*")

    get_cutoff_time(days) if days > 3600 else get_cutoff_date(days)

    if days > 3600:
        get_cutoff_date(1)

    search_body = {
        "query": {
            "bool": {
                "must": [{"range": {"last_time": {"lt": str(ts)}}}],
                "must_not": [{"range": {"first_time": {"lte": 0}}}]}
        },
        "size": 100000 if task else 10000
    }

    if task:
        search_body["query"]["bool"]["must"].append({"terms": {"task": [task]}})
    else:
        search_body["aggs"] = {"min_time": {"min": {"field": "first_time"}}}

    result = es.search(index="es_index", body=search_body, request_timeout=60)
    index_list = []
    for hits in result["hits"]["hits"]:
        index_list.append(hits["_source"]["index"])
    timeArray = time.localtime(ts)
    day = time.strftime("%Y%m%d", timeArray)
    delete_alarm_index(es, ts, task)
    delete_merged_pcap_files(ts, is_full_flow_enabled, preserve_by_rule, task)
    delete_expired_lmdb_dirs(ts, task)
    for index_name in index_list:
        delete_es_index(es, index_name)
    delete_es_other_data(es)


def is_rule_matched_data_exceed_threshold(tasks, threshold=0.2):
    size = 0
    for task in tasks:
        task_dir = Path(f"{DATA_PATH}/{task}")
        for file_path in task_dir.glob('**/*'):
            if RULE_SUBDIR in file_path.parts:
                size += file_path.stat().st_size
    result = os.statvfs(DATA_PATH)
    return size / float(result.f_frsize * result.f_blocks) > threshold


def get_age_of_oldest_index_in_days(es):
    search_body = {
        "size": 0,
        "aggs": {
            "oldest_index_time": {
                "min": {
                    "field": "first_time"
                }
            }
        }
    }
    response = es.search(index=ES_META_INDEX, body=search_body)
    aggregation_data = response.get("aggregations", {})
    if "oldest_index_time" in aggregation_data:
        oldest_index_time = datetime.fromtimestamp(aggregation_data["oldest_index_time"]["value"])
        days_interval = (now - oldest_index_time).days
        return max(days_interval, 1)
    return 1


def docker_prune():
    client = docker.from_env()
    # 清理停止的容器
    for container in client.containers.list(filters={"status": "exited"}):
        container.remove()
    # 清理未被标记的镜像
    for image in client.images.list(filters={"dangling": True}):
        client.images.remove(image.id)
    # 清理未使用的网络
    for network in client.networks.list(filters={"dangling": True}):
        network.remove()
    # 清理未使用的卷
    for volume in client.volumes.list(filters={"dangling": True}):
        volume.remove()


def execute_data_cleanup(es, retain_days, data_disk_usage, es_disk_threshold, is_full_flow_enabled,
                         preserve_by_rule,
                         tasks):
    for task in tasks or [None]:
        delete_data_older_than(retain_days, is_full_flow_enabled, preserve_by_rule, task)
    # if retain_days > 10:
    #     retain_days = get_days_interval_since_oldest_data(es)

    # data_disk_usage = psutil.disk_usage(DATA_PATH)
    root_disk_usage = psutil.disk_usage('/')
    if root_disk_usage.percent > 70:
        docker_prune()
    # if data_disk_usage.percent > data_disk_threshold:
    #     delete_directories(f"{DATA_PATH}/*/*/{PCAP_SUBDIR}/*/{ATTACK_SUBDIR}/*/*")
    #     delete_directories(f"{DATA_PATH}/*/*/{PCAP_SUBDIR}/*/{NOIP_PACKET_SUBDIR}/*/*")
    # time.sleep(1)

    age_of_oldest_data = get_age_of_oldest_index_in_days(es)
    delete_old_data_until_below_usage(data_disk_usage, es, is_full_flow_enabled, age_of_oldest_data,
                                      preserve_by_rule, tasks)

    #####  删除 分规则后的数据
    if is_full_flow_enabled:
        days = retain_days
        while days > 0:
            data_disk_usage = psutil.disk_usage(DATA_PATH).percent
            es_disk_usage = psutil.disk_usage('/data/.es').percent
            if (data_disk_usage < data_disk_usage) and (es_disk_usage < es_disk_threshold):
                break
            else:
                if tasks:
                    for task in tasks:
                        delete_data_older_than(es, days, False, preserve_by_rule, task)
                else:
                    delete_data_older_than(es, days, False, preserve_by_rule, None)
            days -= 1

    ### 删除规则数据
    data_disk_usage = psutil.disk_usage(DATA_PATH).percent
    hours = 24
    if data_disk_usage > data_disk_usage:
        while hours > 0:
            data_disk_usage = psutil.disk_usage(DATA_PATH).percent
            if data_disk_usage < data_disk_usage:
                break
            else:
                cutoff_timeslot = int((int(time.time()) - hours) / (3600 * 4)) * (3600 * 4)
                cutoff_timeslot += 4
                if tasks:
                    for task in tasks:
                        delete_data_older_than(cutoff_timeslot, is_full_flow_enabled, preserve_by_rule, task)
                        delete_merged_pcap_files(cutoff_timeslot, True, False, task)
                else:
                    delete_merged_pcap_files(cutoff_timeslot, is_full_flow_enabled, preserve_by_rule, tasks)
            hours -= 4

    ###  删除规则命中的
    if preserve_by_rule or is_rule_matched_data_exceed_threshold(tasks):
        days = 365
        while days > 2:
            data_disk_usage = psutil.disk_usage(DATA_PATH).percent
            if data_disk_usage < data_disk_usage:
                break
            else:
                if tasks:
                    for task in tasks:
                        delete_data_older_than(days, False, False, task)
                else:
                    delete_data_older_than(days, False, False, None)
            days -= 1


def delete_old_data_until_below_usage(data_disk_usage, es, full_flow_flag, oldest_data_age,
                                      rule_based_preservation_flag, tasks):
    remaining_days = oldest_data_age - 1
    while remaining_days > 0:
        current_disk_usage = psutil.disk_usage(DATA_PATH).percent
        if current_disk_usage > data_disk_usage:
            if tasks:
                for task in tasks:
                    delete_data_older_than(es, remaining_days, full_flow_flag, rule_based_preservation_flag, task)
            else:
                delete_data_older_than(es, remaining_days, full_flow_flag, rule_based_preservation_flag, None)
        else:
            break
        remaining_days -= 1


def parse_arguments():
    parser = argparse.ArgumentParser(description='Data Purge Tool - Deletes expired data to free up disk space.')
    parser.add_argument('--retain-days', type=int, default=1000, help='Number of days to retain data.')
    parser.add_argument('--data-disk-threshold', type=int, default=80,
                        help='Disk usage threshold to trigger cleanup.')
    parser.add_argument('--es-disk-threshold', type=int, default=60,
                        help='ES Disk usage threshold to trigger cleanup.')
    parser.add_argument('--full-flow-enabled', action='store_true',
                        help='Indicate if full flow retention is enabled.')
    parser.add_argument('--preserve-rule-matched', action='store_true',
                        help='Toggle for preserving rule matched data.')
    parser.add_argument('--tasks', type=str, help='List of task IDs to be deleted, separated by commas')
    return parser.parse_args()


def main():
    args = parse_arguments()
    task_ids = [int(task_id) for task_id in args.tasks.split(',')] if args.tasks else []
    config = load_config("/opt/GeekSec/pubconfig/pubconfig.json")
    es_client = get_es_client(config["es_url"])
    execute_data_cleanup(
        es_client,
        args.retain_days,
        args.data_disk_threshold,
        args.es_disk_threshold,
        args.full_flow_enabled,
        args.preserve_rule_matched,
        task_ids
    )
    rebuild_es_meta_index(es_client)


if __name__ == '__main__':
    main()

module.exports = {
  root: true,
  parserOptions: {
    parser: "babel-eslint",
    sourceType: "module",
  },
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  extends: ["plugin:vue/recommended", "eslint:recommended"],
  rules: {
    eqeqeq: 0, // 要求使用 === 和 !==
    "no-dupe-args": 2, // 禁止 function 定义中出现重名参数
    "no-debugger": 0, // 禁止 function 定义中出现重名参数
    "no-dupe-keys": 2, // 禁止对象字面量中出现重复的 key
    "no-eval": 2, // 禁用 eval()
    "no-self-compare": 2, // 禁止自身比较
    "no-self-assign": 2, // 禁止自我赋值
    "no-unused-vars": 0, // 禁止出现未使用过的变量
    "no-const-assign": 2, // 禁止修改 const 声明的变量
    "no-func-assign": 2, // 禁止对 function 声明重新赋值
    camelcase: 0, // 强制使用骆驼拼写法命名约定
    "no-mixed-spaces-and-tabs": 2, //禁止混用tab和空格
    indent: ["warn", 2], //缩进风格这里不做硬性规定，但是产品组内要达成统一
    quotes: [0, "single"], //要求引号类型 `` ' ''
    semi: [2, "always"], //语句强制分号结尾
    "vue/singleline-html-element-content-newline": 0,
    "vue/html-self-closing": 0,
    "vue/max-attributes-per-line": 0,
    "no-useless-escape": 0,
    "no-case-declarations": 0,
    "vue/prop-name-casing": 0,
  },
};

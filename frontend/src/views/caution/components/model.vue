<template>
  <div class="caution-model">
    <article v-if="setpData.length" class="step">
      <section class="step-main">
        <div
          v-for="(item, index) of setpData"
          :key="index"
          :class="item.active ? 'active' : 'no-active'"
        >
          <div class="title">
            <el-tooltip
              class="item"
              content="点击全选"
              placement="top"
              effect="light"
            >
              <span @click="STEP_CHANGE(item, index)">{{ item.name }}</span>
            </el-tooltip>
            <span>{{ item.num }}</span>
            <span
              v-if="item.name !== '其他' && item.name !== '目标行动'"
              class="bg"
            >
              <div>
                ----------------------------------------------------------------
              </div>
              <div />
            </span>
          </div>

          <div class="tags">
            <div
              v-for="(j, index2) of item.tags"
              :key="index2"
              :class="j.active ? 'tags-item-active' : 'tags-item'"
              @click="TAGS_CHANGE(item, j)"
            >
              {{ j.name }}
            </div>
          </div>
        </div>
      </section>
    </article>
    <article class="table-title">
      <div>
        已选择<span>{{ selectData.length }}</span>条
      </div>
      <div>
        <el-popover
          placement="bottom"
          trigger="click"
          :popper-append-to-body="false"
          :visible-arrow="false"
          popper-class="dialoguepopperbox"
        >
          <el-button slot="reference">
            <svg-icon icon-class="setlist" style="margin-right: 5px" />列设置
          </el-button>
          <div class="content">
            <el-checkbox-group
              v-model="checkedCities"
              @change="handleCheckedCitiesChange"
            >
              <el-checkbox v-for="city in cities" :key="city" :label="city">
                {{
                  city
                }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <div class="foot">
            <el-checkbox
              v-model="checkAll"
              :indeterminate="isIndeterminate"
              @change="handleCheckAllChange"
            >
              全选
            </el-checkbox>
            <el-button style="margin-right: 16px" @click="RESET_CLOUMN">
              重置
            </el-button>
          </div>
        </el-popover>
        <el-button style="width: 150px; margin-left: 10px" @click="ALL_DELETE">
          <svg-icon
            icon-class="icon_16_delete"
            style="margin-right: 3px"
          />全部删除
        </el-button>
        <el-button :disabled="selectData.length < 1" @click="DELETE">
          <svg-icon icon-class="icon_16_delete" style="margin-right: 3px" />删除
        </el-button>
        <el-button
          :disabled="selectData.length < 1"
          @click="GET_CSV('/alarm/getCsv')"
        >
          <svg-icon icon-class="globaldown" style="margin-right: 3px" />导出
        </el-button>
      </div>
    </article>
    <article class="table-box">
      <el-table
        :data="tableData"
        stripe
        border
        :default-sort="{ prop: 'time', order: 'descending' }"
        @selection-change="TABLE_SELECT_CHANGE"
        @sort-change="SORT_CHANGE"
      >
        <el-table-column
          type="selection"
          width="100"
          align="center"
          header-align="center"
        />
        <el-table-column
          label="告警名称"
          prop="alarm_knowledge_id"
          sortable="custom"
          min-width="200"
        >
          <template slot-scope="scoped">
            <div class="name" @click="DETAIL(scoped.row)">
              {{ scoped.row.alarm_name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="告警对象" min-width="150" show-overflow-tooltip>
          <template slot-scope="scoped">
            <div class="sortbox">
              <div class="top">
                {{ FMT_TARGETS(scoped.row.targets) }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="QUICK_SEARCH(scoped.row, 'IP', false)">
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button @click="QUICK_SEARCH(scoped.row, 'IP', true)">
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="受害方" min-width="150" show-overflow-tooltip>
          <template slot-scope="scoped">
            <div v-for="(item, index) of scoped.row.victim" :key="index">
              <div class="sortbox">
                <div class="top">
                  {{ item.ip }}
                </div>
                <div class="down">
                  <div class="sorttoole">
                    <el-popover
                      placement="bottom"
                      width="200"
                      trigger="hover"
                      popper-class="sortpopover"
                    >
                      <span slot="reference" class="sorttoole-r">...</span>
                      <div class="sortbtn">
                        <el-button
                          @click="QUICK_IP_SEARCH(item.ip, 'CAUTION', false)"
                        >
                          <svg-icon
                            icon-class="sort-up"
                            style="margin-right: 15px"
                          />正向检索
                        </el-button>
                        <el-button
                          @click="QUICK_IP_SEARCH(item.ip, 'CAUTION', true)"
                        >
                          <svg-icon
                            icon-class="sort-down"
                            style="margin-right: 15px"
                          />反向检索
                        </el-button>
                      </div>
                    </el-popover>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="攻击方" min-width="150" show-overflow-tooltip>
          <template slot-scope="scoped">
            <div v-for="(item, index) of scoped.row.attacker" :key="index">
              <div class="sortbox">
                <div class="top">
                  {{ item.ip }}
                </div>
                <div class="down">
                  <div class="sorttoole">
                    <el-popover
                      placement="bottom"
                      width="200"
                      trigger="hover"
                      popper-class="sortpopover"
                    >
                      <span slot="reference" class="sorttoole-r">...</span>
                      <div class="sortbtn">
                        <el-button
                          @click="QUICK_IP_SEARCH(item.ip, 'CAUTION', false)"
                        >
                          <svg-icon
                            icon-class="sort-up"
                            style="margin-right: 15px"
                          />正向检索
                        </el-button>
                        <el-button
                          @click="QUICK_IP_SEARCH(item.ip, 'CAUTION', true)"
                        >
                          <svg-icon
                            icon-class="sort-down"
                            style="margin-right: 15px"
                          />反向检索
                        </el-button>
                      </div>
                    </el-popover>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedCities.includes('处理状态')"
          label="处理状态"
          prop="alarm_status"
          sortable="custom"
          width="120"
        >
          <template slot-scope="scoped">
            <el-select
              v-model="scoped.row.alarm_status"
              v-loading="scoped.row.loading"
              placeholder="请选择"
              size="mini"
              @change="STATUS_CHANGE(scoped.row)"
            >
              <el-option
                v-for="item in options1"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedCities.includes('攻击类型')"
          label="攻击类型"
        >
          <template slot-scope="scoped">
            {{ scoped.row.attack_type_name || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedCities.includes('威胁级别')"
          prop="attack_level"
          sortable="custom"
          label="威胁级别"
          width="120"
        >
          <template slot-scope="scoped">
            <div
              v-if="
                scoped.row.attack_level > 60 && scoped.row.attack_level < 81
              "
              class="level1"
            >
              低危
            </div>
            <div
              v-else-if="
                scoped.row.attack_level > 80 && scoped.row.attack_level < 91
              "
              class="level2"
            >
              中危
            </div>
            <div v-else-if="scoped.row.attack_level > 90" class="level3">
              高危
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>

        <el-table-column
          v-if="checkedCities.includes('任务名称')"
          label="任务名称"
          prop="task_id"
          sortable="custom"
          width="120"
        >
          <template slot-scope="scoped">
            {{ scoped.row.task_id === 0 ? "主任务" : "从任务" }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedCities.includes('服务端IP')"
          label="服务端IP"
          prop="dIp"
          width="150"
        >
          <template slot-scope="scoped">
            {{ scoped.row.dIp || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedCities.includes('服务端端口')"
          label="服务端端口"
          prop="dPort"
          width="120"
        >
          <template slot-scope="scoped">
            {{ scoped.row.dPort || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedCities.includes('客户端IP')"
          label="客户端IP"
          prop="sIp"
          width="150"
        >
          <template slot-scope="scoped">
            {{ scoped.row.sIp || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedCities.includes('客户端端口')"
          label="客户端端口"
          prop="sPort"
          width="120"
        >
          <template slot-scope="scoped">
            {{ scoped.row.sPort || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedCities.includes('IP协议')"
          label="IP协议"
          prop="ProName"
          width="120"
        >
          <template slot-scope="scoped">
            {{ scoped.row.ProName || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="时间" sortable="custom" prop="time" width="200">
          <template slot-scope="scoped">
            {{ FMT_TIME(scoped.row) }}
          </template>
        </el-table-column>
      </el-table>
    </article>
    <section class="page">
      <div>*列表展示上限为<span>10,000</span>条</div>
      <div>共{{ pageTotal }}条</div>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100, 200]"
        :page-size="pageSize"
        layout="sizes, prev, pager, next, jumper"
        :total="showTotal"
        @size-change="PAGE_SIZE_CHANGE"
        @current-change="PAGE_CHANGE"
      />
    </section>
    <el-drawer
      :title="detailData2.alarm_name"
      :visible.sync="detailDrawer"
      direction="rtl"
    >
      <section class="cell">
        <div class="title">攻击类型</div>
        <div class="text">{{ detailData.attack_type_name || "-" }}</div>
        <div style="margin-left: auto">
          <el-button @click="PCAP_DOWNLOAD"> 下载pcap </el-button>
        </div>
      </section>
      <section class="cell" style="align-items: center">
        <div class="title">威胁等级</div>
        <div id="tag" :class="TAG().class">
          <img src="@/assets/images/detail-lv.svg" alt="" />
          <div>{{ TAG().text }}</div>
        </div>
      </section>
      <section class="block">
        <div class="block-box">
          <div>
            <aside />
            <span>攻击方</span>
          </div>
          <div>
            <span v-for="(item, index) of detailData.attacker" :key="index">
              {{ item.ip }}
            </span>
          </div>
        </div>
        <aside>
          - - - - - - - - - - - -
          <div />
        </aside>
        <div class="block-box">
          <div>
            <aside />
            <span>受害方</span>
          </div>
          <div>
            <span v-for="(item, index) of detailData.victim" :key="index">
              {{ item.ip }}
            </span>
          </div>
        </div>
      </section>
      <section class="cell">
        <div class="title">相关标签</div>
        <div v-if="detailLabels.length < 1" class="tags">-</div>
        <div v-else class="tags">
          <div
            v-for="(item, index) of detailLabels"
            :key="index"
            :class="FMT_TAG_CLASS(FMT_TAG(item))"
          >
            {{ FMT_TAG(item).tag_text }}
          </div>
        </div>
      </section>
      <section class="cell">
        <div class="title">匹配原因</div>
        <div v-if="detailData2.alarm_reason" class="text-for">
          <div v-for="(item, index) of detailData2.alarm_reason" :key="index">
            {{ item.key }}:{{ item.actual_value }}
          </div>
        </div>
        <div v-else class="text">-</div>
      </section>
      <section class="cell">
        <div class="title">检测原理</div>
        <div class="text">
          {{ detailData.alarm_principle ? detailData.alarm_principle : "-" }}
        </div>
      </section>
      <section class="cell">
        <div class="title">处置建议</div>
        <div class="text">
          {{
            detailData.alarm_handle_method
              ? FMT_ALARM_METHOD(detailData.alarm_handle_method)
              : "-"
          }}
        </div>
      </section>
      <section v-if="$isTraffic" class="cell">
        <aside class="graph">
          <Graph
            v-if="detailGraph"
            :init-id="detailData2._id"
            :init-index="detailData.alarm_index"
            :init-title="'告警研判'"
          />
        </aside>
      </section>
    </el-drawer>
  </div>
</template>

<script>
import dayjs from "dayjs";
import FileSaver from "file-saver";
import axios from "axios";
import Graph from "@/components/graph/index.vue";
import { getToken } from "@/utils/auth";

import {
  alarm_list,
  alarm_put,
  alarm_del,
  alarm_detail,
  deleteAll,
  pcapDownload,
} from "@/api/caution";
const cityOptions = [
  "处理状态",
  "攻击类型",
  "威胁级别",
  "任务名称",
  "服务端IP",
  "服务端端口",
  "客户端IP",
  "客户端端口",
  "IP协议",
];
export default {
  name: "CautionDefense",
  components: {
    Graph,
  },
  // eslint-disable-next-line vue/require-prop-types
  props: ["searchForm", "setpData", "knowledge", "searchType", "tagsData"],
  data() {
    return {
      tableData: [],
      sortName: "time",
      tableSort: false,
      // 勾选
      selectData: [],
      // 分页
      currentPage: 1,
      pageSize: 10,
      pageTotal: 0,
      showTotal: 0,
      options1: [
        {
          name: "未处理",
          value: 0,
        },
        {
          name: "确认",
          value: 1,
        },
        {
          name: "误报",
          value: 2,
        },
      ],
      // 多选删除暂存对象
      delObj: {},
      // 详情开关
      detailDrawer: false,
      detailData: "",
      detailData2: "",
      detailLabels: [],
      detailGraph: false,
      // 详情标题
      title: "",
      checkinput: "",
      checkAll: false,
      checkedCities: [],
      cities: cityOptions,
      isIndeterminate: true,
    };
  },
  watch: {
    checkedCities: {
      deep: true,
      handler(val) {
        localStorage.setItem("cuationColumn1", JSON.stringify(val));
        if(this.checkedCities.length===cityOptions.length){
          this.checkAll=true;
          this.isIndeterminate=false;
        }else{
          this.checkAll=false;
          if(!this.checkedCities.length){
            this.isIndeterminate=false;
          }else{
            this.isIndeterminate=true;
          }
        }
      },
    },
  },
  created() {
    this.checkedCities =
      localStorage.getItem("cuationColumn1") &&
      JSON.parse(localStorage.getItem("cuationColumn1")).length > 0
        ? JSON.parse(localStorage.getItem("cuationColumn1"))
        : cityOptions;
  },
  methods: {
    GET_LIST() {
      const data = {
        task_ids: this.searchForm.task_ids,
        alarm_ids: this.searchForm.alarm_ids,
        target_name: this.searchForm.target_name,
        victim: this.searchForm.victim,
        attack_levels: this.searchForm.attack_level,
        attacker_ip: this.searchForm.attacker_ip,
        alarm_status_list: this.searchForm.alarm_status_list,
        order_field: this.sortName,
        asc: this.tableSort,
      };
      if (this.searchForm.time?.length > 1) {
        data.left = this.searchForm.time[0] / 1000;
        data.right = this.searchForm.time[1] / 1000;
      } else {
        data.left = "";
        data.right = "";
      }
      alarm_list({
        ...data,
        page_size: this.pageSize,
        current_page: this.currentPage,
        alarm_type: "模型",
      }).then((res) => {
        this.tableData = res.data.records;
        this.pageTotal = res.data.total;
        if (res.data.total > 9999) {
          this.showTotal = 10000;
        } else {
          this.showTotal = res.data.total;
        }
        this.$emit("setTabsNum", 1);
        for (const i of this.tableData) {
          i.loading = false;
        }
      });
    },
    // 分页相关
    PAGE_SIZE_CHANGE(val) {
      this.pageSize = val;
      this.GET_LIST();
    },
    PAGE_CHANGE(val) {
      this.currentPage = val;
      this.GET_LIST();
    },
    // 监听告警名称勾选变化
    IDS_CHANGE(val) {
      for (let i = 0; i < this.setpData.length; i++) {
        for (let j = 0; j < this.setpData[i].tags.length; j++) {
          this.setpData[i].tags[j].active = false;
        }
      }
      val.forEach((id) => {
        if (this.setpData.length > 0) {
          for (let i = 0; i < this.setpData.length; i++) {
            if (this.setpData[i].tags.length > 0) {
              let k = true;
              for (let j = 0; j < this.setpData[i].tags.length; j++) {
                if (id === this.setpData[i].tags[j].id) {
                  console.log(this.setpData[i].tags[j].name);
                  this.setpData[i].tags[j].active = true;
                  // this.TAGS_CHANGE(this.setpData[i], this.setpData[i].tags[j])
                }
                if (this.setpData[i].tags[j].active === false) k = false;
              }
              if (k) {
                this.setpData[i].active = true;
              } else {
                this.setpData[i].active = false;
              }
            }
          }
        }
      });
    },
    // 点击流程
    STEP_CHANGE(step, index) {
      if (this.setpData[index].active === true) {
        this.setpData[index].active = false;
        step.tags.forEach((j) => {
          j.active = false;
          const delIndex = this.searchForm.alarm_ids.indexOf(j.id);
          this.searchForm.alarm_ids.splice(delIndex, 1);
        });
      } else {
        this.setpData[index].active = true;
        step.tags.forEach((j) => {
          j.active = true;
          this.searchForm.alarm_ids.push(j.id);
        });
      }
    },
    // 重置流程
    STEP_RESET() {
      this.setpData.forEach((item) => {
        item.active = false;
        item.tags.forEach((j) => {
          j.active = false;
        });
      });
    },
    // 改变流程内标签状态
    TAGS_CHANGE(i, j) {
      if (j.active === true) {
        j.active = false;
        i.active = false;
        const delIndex = this.searchForm.alarm_ids.indexOf(j.id);
        this.searchForm.alarm_ids.splice(delIndex, 1);
      } else {
        j.active = true;
        this.searchForm.alarm_ids.push(j.id);
      }
      let k = true;
      i.tags.forEach((item) => {
        if (item.active === false) k = false;
      });
      if (k) {
        i.active = true;
      }
    },
    // 格式化告警名称
    FMT_NAME(row) {
      for (let i = 0; i < this.knowledge.length; i++) {
        if (row.alarm_knowledge_id === this.knowledge[i].id) {
          // console.log(this.knowledge[i].alarm_name)
          return this.knowledge[i].alarm_name;
        }
      }
      return "-";
    },
    // 格式化受害者
    FMT_VICTIM(row) {
      if (row?.victim && row?.victim?.length > 0) {
        const arr = [];
        row.victim.forEach((i) => {
          arr.push(i.ip);
        });
        return arr.join("，");
      }
      return "-";
    },
    // 格式化攻击者
    FMT_ATTACK(row) {
      if (row?.attacker && row?.attacker?.length > 0) {
        const arr = [];
        row.attacker.forEach((i) => {
          arr.push(i.ip);
        });
        return arr.join("，");
      }
      return "-";
    },
    // 格式化时间
    FMT_TIME(row) {
      return dayjs(row.time * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    // 更改告警状态
    STATUS_CHANGE(row) {
      console.log(row);
      row.loading = true;
      alarm_put({
        task_id: row.task_id,
        id: row._id,
        alarm_status: row.alarm_status,
      })
        .then((res) => {
          console.log(res);
          if (res.err === 0) {
            this.$message.success("修改成功！");
            this.$emit("search");
            row.loading = false;
          } else {
            this.$emit("search");
            row.loading = false;
          }
        })
        .catch((res) => {
          this.$emit("search");
        });
    },
    // 表格勾选变化时
    TABLE_SELECT_CHANGE(data) {
      this.selectData = data;
      console.log(data);
      const arr = {
        0: [],
        1: [],
      };
      data.forEach((i) => {
        console.log(i);
        if (i.task_id === 0) {
          arr["0"].push(i._id);
        }
        if (i.task_id === 1) {
          arr["1"].push(i._id);
        }
      });
      this.delObj = arr;
    },
    // 删除
    DELETE() {
      this.$confirm("此操作将会删除本条信息，请确认?", "删除提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          alarm_del(this.delObj).then((res) => {
            if (res.err === 0) {
              this.$message({
                type: "success",
                message: res.msg,
              });
            } else {
              this.$message({
                type: "warning",
                message: res.msg,
              });
            }
            this.$emit("search");
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 全部删除，危险操作
    ALL_DELETE() {
      this.$confirm(
        "此操作将会删除所有告警数据，请确认！！！！！",
        "危险操作!",
        {
          confirmButtonText: "确认删除全部告警数据！",
          cancelButtonText: "取消",
          type: "error",
        }
      )
        .then(() => {
          deleteAll().then((res) => {
            if (res.err === 0) {
              this.$message({
                type: "success",
                message: res.msg,
              });
            } else {
              this.$message({
                type: "warning",
                message: res.msg,
              });
            }
            this.$emit("search");
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // csv导出
    GET_CSV(url) {
      let baseURL;
      if (process.env.VUE_APP_BASE_API === "") {
        baseURL = `${process.env.VUE_APP_BASE_API}/api`;
      } else {
        baseURL = process.env.VUE_APP_BASE_API;
      }
      const ids = [];
      this.selectData.forEach((i) => {
        ids.push(i._id);
      });
      axios({
        method: "POST",
        url: baseURL + url,
        responseType: "blob", // 设置接受的流格式
        headers: {
          token: getToken(),
        },
        data: { ...this.searchForm, ids, alarm_type: this.searchType },
      }).then((res) => {
        if (res.err === 40005) {
          this.$message.error(res.msg);
        } else {
          const blob = new Blob([res.data], { type: "application/json" });
          FileSaver.saveAs(blob, res.headers["content-disposition"]);
        }
      });
    },
    // 详情
    DETAIL(row) {
      this.detailDrawer = true;
      this.detailGraph = false;
      alarm_detail({
        alarm_id: row._id,
        alarm_index: row.alarm_index,
      })
        .then((res) => {
          if (res.err === 0) {
            this.detailData = row;
            this.detailLabels = res.data.alarm_related_label;
            this.detailData2 = res.data;
            this.detailGraph = true;
          }
        })
        .catch(() => {});
    },
    // 格式化处置建议
    FMT_ALARM_METHOD(str) {
      return str.replace(/\\n/g, "\n");
    },
    // 格式化详情中的标签
    FMT_TAG(tag) {
      for (let j = 0; j < this.tagsData.length; j++) {
        if (tag == this.tagsData[j].tag_id) {
          return this.tagsData[j];
        }
      }
      return {
        tag_text: "未知",
      };
    },
    // 格式化详情中标签颜色
    FMT_TAG_CLASS(tag) {
      if (
        tag?.black_list >= 1 &&
        tag?.black_list <= 100 &&
        tag?.white_list !== 100
      ) {
        if (tag.black_list >= 80) {
          console.log("danger");
          return "danger";
        } else {
          console.log("warning");
          return "warning";
        }
      }
      if (
        tag?.white_list &&
        tag?.white_list >= 1 &&
        tag?.white_list <= 100 &&
        tag?.black_list === 0
      ) {
        if (tag?.white_list === 100) {
          console.log("success");
          return "success";
        } else {
          console.log("info");
          return "info";
        }
      }
      if (tag?.white_list === 0 && tag?.black_list === 0) {
        console.log("info");
        return "info";
      }
      return "info";
    },
    // 格式化 原因
    FMT_REASON(detail) {
      console.log(detail);
      if (detail.alarm_reason && detail.alarm_reason.length > 0) {
        const arr = [];
        for (const i of detail.alarm_reason) {
          arr.push(`${i.key}：${i.actual_value}`);
        }
        return arr;
      } else {
        return "-";
      }
    },
    // 格式化 告警对象
    FMT_TARGETS(targets) {
      if (targets && targets.length > 0) {
        return targets[0].name;
      } else {
        return "-";
      }
    },

    // 威胁等级标签
    TAG() {
      const row = this.detailData;
      if (row.attack_level > 60 && row.attack_level < 81) {
        return {
          class: "lv1",
          text: "低危",
        };
      } else if (row.attack_level > 80 && row.attack_level < 91) {
        return {
          class: "lv2",
          text: "中危",
        };
      } else if (row.attack_level > 90) {
        return {
          class: "lv3",
          text: "高危",
        };
      } else {
        return {
          class: "",
          text: "-",
        };
      }
    },
    // 排序监听
    SORT_CHANGE({ column, prop, order }) {
      console.log(column, prop, order);
      this.sortName = prop;
      if (order === "ascending") {
        this.tableSort = true;
      } else {
        this.tableSort = false;
      }
      this.GET_LIST();
    },
    // 快速检索
    QUICK_SEARCH(data, type, choose) {
      console.log(data, type, choose);
      if (data.targets && data.targets.length > 0) {
        data.type = type;
        data.choose = choose;
        this.$router.push({
          path: "/SessionAnalyse",
          query: {
            quickData: data,
          },
        });
      }
    },
    // IP 快速检索
    QUICK_IP_SEARCH(ip, type, choose) {
      const data = {
        ip,
        type,
        choose,
      };
      this.$router.push({
        path: "/SessionAnalyse",
        query: {
          quickData: data,
        },
      });
    },
    // 列选择相关
    handleCheckAllChange(val) {
      this.checkedCities = val ? cityOptions : [];
      this.isIndeterminate = false;
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.cities.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.cities.length;
    },
    RESET_CLOUMN() {
      this.checkedCities = cityOptions;
    },
    // pcap下载
    PCAP_DOWNLOAD() {
      console.log(this.detailData);
      console.log(this.detailData2);
      pcapDownload({
        alarm_session_list: this.detailData.alarm_session_list,
        // session_id: this.detailData.alarm_session_list[0],
        alarm_type: "模型",
        alarm_time: this.detailData.time,
        user_id: 1,
      }).then((res) => {
        if (res.err === 0) {
          this.$message.success("下载成功，请进入顶部下载列表查看进度");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.caution-model {
  width: 100%;
  padding: 0 24px;
  position: relative;

  .step {
    width: 100%;
    min-height: 122px;
    display: flex;

    > section {
      height: 100%;
      display: flex;
      background: #ffffff;
    }

    &-main {
      width: 100%;

      > div:nth-last-of-type(1) {
        border-left: 1px solid #d1e3ff;
        border: 1px solid #f2f3f7;
      }

      .active {
        width: 17%;
        border: 2px solid #116ef9 !important;
      }

      .no-active {
        width: 17%;
        border: 2px solid #d1e3ff;
      }

      > div {
        padding-top: 8px;
        padding-left: 14px;

        .title {
          width: 100%;
          display: flex;
          position: relative;
          box-sizing: border-box;

          > span {
            height: 22px;
            display: flex;
            align-items: center;
          }

          > span:nth-of-type(1) {
            color: #116ef9;
            font-size: 14px;
            cursor: pointer;
            padding-right: 4px;
            white-space: nowrap;
            z-index: 20;
            background-color: #fff;
          }

          > span:nth-of-type(2) {
            color: #4a97ff;
            font-size: 14px;
            cursor: pointer;
            white-space: nowrap;
            z-index: 20;
            border-radius: 10px;
            padding: 0px 8px;
            background: #e7f0fe;
            border-radius: 10px;
          }

          .bg {
            width: 100%;
            color: #d1e3ff;
            font-size: 20px;
            overflow: hidden;
            position: absolute;
            z-index: 19;

            > div:nth-of-type(1) {
              width: 100%;
              padding: 0 5px;
              padding-right: 10px;
              white-space: nowrap;
              overflow: hidden;
            }

            > div:nth-of-type(2) {
              width: 0;
              height: 0;
              border-top: 5px solid transparent;
              border-left: 10px solid #d1e3ff;
              border-bottom: 5px solid transparent;
              position: absolute;
              right: 0;
              top: 7px;
            }
          }
        }

        .tags {
          width: 100%;
          min-height: 80px;
          display: flex;
          box-sizing: border-box;
          flex-wrap: wrap;
          // justify-content: space-between;
          overflow-y: auto;
          align-content: flex-start;
          padding-right: 12px;

          > div {
            flex-shrink: 0;
            max-width: 300px;
            height: 24px;
            padding: 0 12px;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 2px;
            box-sizing: border-box;
            color: #2c2c35;
            font-size: 14px;
            line-height: 22px;
            text-align: center;
            margin: 3px 4px 3px 0;
            cursor: pointer;
            overflow: hidden;
            /*对超出容器的部分强制截取，高度不确定则换行*/
            text-overflow: ellipsis;
            /*显示省略符号来代表被修剪的文本。*/
            white-space: nowrap;
            /*禁止换行*/
          }

          &-item {
            background: #ffffff;
            border: 1px solid #dee0e7;
          }

          &-item-active {
            border: 1px solid #116ef9;
            color: #116ef9 !important;
          }
        }
      }
    }

    &-other {
      width: 145px;
      background: #ffffff;
      border: 1px solid #f2f3f7;
      border-radius: 0px 4px 4px 0px;

      > div {
        flex: 1;
        padding-top: 12px;
        padding-left: 16px;

        .title {
          > span:nth-of-type(1) {
            color: #116ef9;
            font-size: 14px;
            cursor: pointer;
            margin-right: 4px;
          }

          > span:nth-of-type(2) {
            color: #4a97ff;
            font-size: 14px;
            cursor: pointer;
          }
        }
      }
    }

    &-more {
      width: 36px;
      background: #ffffff;
      border: 1px solid #f2f3f7;
      border-radius: 0px 4px 4px 0px;
      margin-left: auto;
    }
  }

  .table-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;

    > div:nth-of-type(1) {
      font-size: 14px;
      color: #9999a1;
    }
    .sessionList-box-top-l {
      display: inline-block;
      margin-right: 10px;
    }
    .el-button {
      width: 80px;
      height: 32px;
      font-size: 14px;
      padding: 0;
      font-weight: 400;
      letter-spacing: 3px;
      // color: #FFFFFF;
    }

    .el-button.is-disabled {
      background: #f7f8fa;
      border: 1px solid #dee0e7;
      border-radius: 4px;
      color: #cecece;
    }
  }

  .table-box {
    // padding-bottom: 60px;
    ::v-deep .el-table__header-wrapper {
      height: 44px;
    }

    ::v-deep .el-table__row {
      height: 56px;

      td {
        padding: 0;
      }
    }

    ::v-deep .el-table__row--striped td {
      background: #f7f8fa;
    }

    ::v-deep.el-input__inner {
      width: 80px;
      // height: 20px;
      padding-left: 10px;
      font-size: 12px;
    }

    // ::v-deep.el-table th > .cell {
    //   text-align: left;
    // }
    // ::v-deep.el-table__body tr,
    // .el-table__body td {
    //   .cell {
    //     text-align: left;
    //   }
    // }
    ::v-deep.cell {
      word-break: normal;

      .el-loading-spinner {
        margin-top: -8px;
      }

      .el-loading-spinner .circular {
        width: 20px;
        height: 20px;
      }
    }

    .name {
      color: #116ef9;
      cursor: pointer;
    }

    .name:hover {
      text-decoration: underline;
    }

    .level1 {
      width: 32px;
      height: 20px;
      background: #f9f3df;
      border-radius: 2px;
      font-size: 12px;
      color: #c29217;
      text-align: center;
    }

    .level2 {
      width: 32px;
      height: 20px;
      background: #f9eddf;
      border-radius: 2px;
      font-size: 12px;
      color: #b76f1e;
      text-align: center;
    }

    .level3 {
      width: 32px;
      height: 20px;
      background: #fce7e7;
      border-radius: 2px;
      font-size: 12px;
      color: #a41818;
      text-align: center;
    }
  }

  .page {
    width: 100%;
    height: 56px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background-color: #ffffff;
    position: sticky;
    bottom: 0px;
    z-index: 500;

    > div:nth-of-type(1) {
      color: #9999a1;
      margin-right: auto;
      font-size: 14px;

      > span {
        color: #000;
      }
    }

    > div:nth-of-type(2) {
      color: #606266;
      font-size: 14px;
    }
  }
}

::v-deep .el-drawer {
  width: 580px !important;
  height: auto;
  overflow-y: auto;

  .el-drawer__body {
    font-size: 14px;
    background-image: url("../../../assets/images/detail-bg.png");
    background-size: contain;
    background-repeat: no-repeat;
    padding-top: 25px;

    .cell {
      width: 100%;
      min-height: 22px;
      padding-left: 24px;
      padding-right: 24px;
      display: flex;
      align-items: flex-start;
      margin-bottom: 16px;

      .title {
        min-width: 60px;
        color: #9999a1;
        margin-right: 32px;
      }

      .text {
        color: #2c2c35;
        width: auto;
        white-space: pre-line;
      }

      .text-for {
        width: 100%;
        color: #2c2c35;
        display: flex;
        flex-direction: column;

        > div {
          word-break: break-all;
          margin-bottom: 10px;
        }
      }

      .tags {
        display: flex;
        flex-wrap: wrap;

        > div {
          padding: 2px 4px;
          margin-right: 8px;
          color: #767684;
          font-size: 12px;
          background: #f2f3f7;
          border-radius: 2px;
          margin-bottom: 8px;
        }

        .danger {
          background: #fce7e7;
          color: #903737;
        }

        .warning {
          background: #f9eddf;
          color: #b76f1e;
        }

        .success {
          background: #e1eaf8;
          color: #354a80;
        }

        .info {
          color: #767684;
          background: #f2f3f7;
        }
      }

      #tag {
        width: 62px;
        height: 22px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: space-evenly;

        > img {
          width: 16px;
          height: 14px;
          object-fit: cover;
        }
      }

      .lv1 {
        background: #f9f3df;
        color: #c29217;
      }

      .lv2 {
        background: #f9eddf;
        color: #b76f1e;
      }

      .lv3 {
        background: #fce7e7;
        color: #a41818;
      }
    }

    .graph {
      width: 100%;
      height: 664px;
      background: #f7f8fa;
      border-radius: 8px;
    }

    .block {
      width: 100%;
      height: auto;
      padding-left: 24px;
      padding-right: 24px;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      // position: relative;

      &-box {
        width: 220px;
        min-height: 130px;
        background: #f8fbff;
        border-radius: 8px;
        background-image: url("../../../assets/images/detail-bg2.png");
        background-size: 100%;
        background-repeat: no-repeat;
        padding-top: 16px;

        > div:nth-of-type(1) {
          width: 100%;
          height: 22px;
          display: flex;
          align-items: center;

          > aside {
            width: 2px;
            height: 12px;
            background: #116ef9;
            margin-left: 1px;
          }

          > span {
            font-size: 14px;
            color: #9999a1;
            margin-left: 14px;
          }
        }

        > div:nth-of-type(2) {
          font-size: 16px;
          color: #2c2c35;
          padding-left: 16px;
          display: flex;
          flex-direction: column;

          > span {
            margin: 4px 0;
            word-wrap: break-word;
            // white-space: nowrap;
            // overflow: hidden;
            // text-overflow: ellipsis;
          }
        }
      }

      > aside {
        width: 92px;
        height: 10px;
        color: #116ef9;
        overflow: hidden;
        font-size: 14px;
        // position: relative;
        line-height: 10px;
        display: flex;

        > div {
          // position: absolute;
          width: 0;
          height: 0;
          right: 0;
          border-top: 5px solid transparent;
          border-left: 10px solid #116ef9;
          border-bottom: 5px solid transparent;
          margin-left: auto;
        }
      }
    }
  }
}

::v-deep .el-drawer__header {
  padding: 16px 24px;
  height: 56px;
  margin: 0;

  > :first-child {
    font-size: 16px;
    color: #000000;
  }
}

.sortbox {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  // position: relative;
  .top {
    margin-right: 5px;
    cursor: pointer;
    color: rgb(17, 110, 249);
  }

  .down {
    width: 10px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .sorttoole {
    display: none;
    padding-bottom: 5px;
    color: #116ef9;
    // position: absolute;
    // top: -2px;
    // right: 0;
  }
}

.sortbox:hover {
  .sorttoole {
    display: block;
    cursor: pointer;
  }
}
.head {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content {
  margin: 0 16px;
  height: 220px;
  overflow: auto;

  ::v-deep .el-checkbox {
    color: #2c2c35;
    margin-bottom: 8px;
  }
  .el-checkbox-group {
    display: flex;
    flex-direction: column;
    padding-top: 10px;
  }
  ::v-deep .is-checked + .el-checkbox__label {
    color: #2c2c35;
  }

  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
  }
}

.foot {
  height: 48px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f8f8;
  border-radius: 0px 0px 2px 2px;

  .el-button {
    height: 24px;
    width: 58px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  ::v-deep .el-checkbox {
    color: #2c2c35;
    // margin-bottom: 8px;
  }

  ::v-deep .is-checked + .el-checkbox__label {
    color: #2c2c35;
  }

  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
  }

  .el-button:focus,
  .el-button:hover {
    background-color: #116ef9;
    border-color: #116ef9;
    color: #ffffff;
  }
}

.box {
  ::v-deep {
    .el-button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 94px;
      height: 32px;
    }

    .el-checkbox-group {
      display: flex;
      flex-direction: column;
    }
  }
}
::v-deep .el-table--scrollable-x .el-table__body-wrapper {
  overflow-x: auto !important;
}
</style>

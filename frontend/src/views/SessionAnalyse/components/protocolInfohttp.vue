<template>
  <div class="listbox">
    <div class="listbox-head">
      <el-tooltip
        content="到“下载列表”的“日志导出”中下载"
        placement="top"
        effect="dark"
      >
        <el-button :disabled="tableData.length < 1 || tableData == [] || !tableData" @click="logderive">
          <i class="el-icon--left">
            <svg-icon icon-class="globaldown" />
          </i>
          日志导出
        </el-button>
      </el-tooltip>
      <protocolInfofliter
        style="width: 96px; margin-bottom: 10px; margin-left: 10px"
        @checkedHeaders1="checkedHeaders"
      >
      </protocolInfofliter>
    </div>
    <div class="listbox-from">
      <el-table
        ref="HTTPTable"
        :data="tableData"
        style="width: 100%"
        stripe
        tooltip-effect="light"
        border
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          fixed
          width="50"
        ></el-table-column>
        <el-table-column prop="SessionId" min-width="200" fixed label="会话ID">
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <el-tooltip
                  :content="scope.row.SessionId ? scope.row.SessionId : '--'"
                  placement="top"
                  effect="light"
                  popper-class="sessionidTooltip"
                >
                  <div
                    style="cursor: pointer; color: #116ef9"
                    @click="opensessionDrawer(scope,6)"
                  >
                    {{
                      scope.row.SessionId
                        ? scope.row.SessionId
                        : "--"
                    }}
                  </div>
                </el-tooltip>
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'SessionId')">
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'SessionId')">
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="sIp"
          label="客户端IP"
          min-width="140"
          sortable="custom"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div
                class="top"
                style="cursor: pointer; color: #116ef9"
                @click="opneipDetail(scope.row.sIp,0)"
              >
                {{ scope.row.sIp }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="210"
                    trigger="hover"
                    popper-class="sortpopover1"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>

                    <div class="sortbtn">
                      <div class="sortbtn-top" style="position: relative">
                        <svg-icon icon-class="sort-up" class="advanceicon" />
                        <div class="title">IP正向检索</div>
                        <el-button @click="forwardsort(scope, 'dip')">
                          服务端IP正向检索
                        </el-button>
                        <el-button @click="forwardsort(scope, 'sip')">
                          客户端IP正向检索
                        </el-button>
                      </div>
                      <div class="sortbtn-down" style="position: relative">
                        <svg-icon icon-class="sort-down" class="advanceicon" />
                        <div class="title">IP反向检索</div>
                        <el-button @click="reversesort(scope, 'dip')">
                          服务端IP反向检索
                        </el-button>
                        <el-button @click="reversesort(scope, 'sip')">
                          客户端IP反向检索
                        </el-button>
                      </div>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="dPort"
          min-width="160"
          label="客户端IP端口"
          sortable="custom"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                {{ scope.row.dPort }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'dPort')">
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'dPort')">
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- sortable="custom" -->
        <el-table-column
          prop="dIp"
          label="服务端IP"
          min-width="130"
          show-overflow-tooltip
          sortable="custom"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div
                class="top"
                style="cursor: pointer; color: #116ef9"
                @click="opneipDetail(scope.row.dIp,0)"
              >
                {{ scope.row.dIp }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover1"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <div class="sortbtn-top" style="position: relative">
                        <svg-icon class="advanceicon" icon-class="sort-up" />
                        <div class="title">IP正向检索</div>
                        <el-button @click="forwardsort(scope, 'dip')">
                          服务端IP正向检索
                        </el-button>
                        <el-button @click="forwardsort(scope, 'sip')">
                          客户端IP正向检索
                        </el-button>
                      </div>
                      <div class="sortbtn-down" style="position: relative">
                        <svg-icon icon-class="sort-down" class="advanceicon" />
                        <div class="title">IP反向检索</div>
                        <el-button @click="reversesort(scope, 'dip')">
                          服务端IP反向检索
                        </el-button>
                        <el-button @click="reversesort(scope, 'sip')">
                          客户端IP反向检索
                        </el-button>
                      </div>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="sPort"
          label="服务端IP端口"
          min-width="130"
          sortable="custom"
        >
          <template slot="header">
            <!-- <httprangefilter
              ref="sPort"
              prop="sPort"
              label="源端口"
              :order_prop="order.order_prop"
              :reset_stamp="reset_stamp"
              @filterHandler="rangeFilterHandler"
              @reset="resetFilter"
            /> -->
          </template>
        </el-table-column>
     
   

        <el-table-column
          v-if="checkedHeader.includes('网址')"
          prop="Url"
          width="160"
          show-overflow-tooltip
          label="网址"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <el-tooltip
                  :disabled="scope.row.Url === '/' || !scope.row.Url"
                  class="item"
                  effect="light"
                  placement="top"
                  popper-class="sessionidTooltip"
                >
                  <div slot="content">
                    {{ scope.row.Url }}
                  </div>
                  <div>
                    {{
                      scope.row.Url && scope.row.Url !== "/"
                        ? scope.row.Url.slice(0, 20) + "..."
                        : "--"
                    }}
                  </div>
                </el-tooltip>
              </div>
              <div v-if="scope.row.Url && scope.row.Url !== '/'" class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'Url')">
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'Url')">
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('请求类型')"
          prop="Act"
          width="100"
          label="请求类型"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('主站')"
          prop="Host"
          show-overflow-tooltip
          min-width="160"
          label="主站"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <span
                  style="color: #116ef9"
                  :style="
                    ipverify_http(scope.row.Host).sign
                      ? 'cursor: not-allowed'
                      : 'cursor: pointer'
                  "
                  @click="
                    ipverify_http(scope.row.Host).sign
                      ? ''
                      : opneDomainDetail(scope.row.Host,3)
                  "
                >
                  {{ ipverify_http(scope.row.Host).host || "--" }}
                </span>
              </div>
              <div v-if="ipverify_http(scope.row.Host).host" class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'HTTP.Host')">
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'HTTP.Host')">
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('回应类型')"
          prop="Response"
          min-width="160"
          show-overflow-tooltip
          label="回应类型"
        >
        </el-table-column>
        <!-- <el-table-column prop="Client.UserAgent" min-width="180" v-if="checkedHeader.includes('源端HTTP指纹')"
          label="源端HTTP指纹">
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <el-tooltip :disabled="!scope.row.Client.UserAgent" class="item" effect="light" placement="top"
                  popper-class="sessionidTooltip">
                  <div slot="content">
                    {{ scope.row.Client.UserAgent }}
                  </div>
                  <div>
                    {{
                        scope.row.Client.UserAgent
                          ? scope.row.Client.UserAgent.slice(0, 10) + "..."
                          : "--"
                    }}
                  </div>
                </el-tooltip>
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover placement="bottom" width="200" trigger="hover" popper-class="sortpopover">
                    <span class="sorttoole-r" slot="reference">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'Client.User-Agent')">
                        <svg-icon icon-class="sort-up" style="margin-right: 15px" />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'Client.User-Agent')">
                        <svg-icon icon-class="sort-down" style="margin-right: 15px" />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column> -->
        <!-- <el-table-column prop="dHTTPFinger" min-width="180" v-if="checkedHeader.includes('目的HTTP指纹')" label="目的HTTP指纹">

          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                {{
                    scope.row.dHTTPFinger == '0'
                      ? "--"
                      : scope.row.dHTTPFinger
                }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover placement="bottom" width="200" trigger="hover" popper-class="sortpopover">
                    <span class="sorttoole-r" slot="reference">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'dHTTPFinger')">
                        <svg-icon icon-class="sort-up" style="margin-right: 15px" />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'dHTTPFinger')">
                        <svg-icon icon-class="sort-down" style="margin-right: 15px" />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column> -->
        <el-table-column
          v-if="checkedHeader.includes('客户端.内容类型')"
          prop="Client.Content-Type"
          min-width="150"
          show-overflow-tooltip
          label="客户端.内容类型"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('客户端.支持页面类型')"
          show-overflow-tooltip
          prop="Client.Accept"
          min-width="150"
          label="客户端.支持页面类型"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('客户端.站点信息')"
          prop="Client.Host"
          min-width="180"
          label="客户端.站点信息"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <span
                  style="cursor: pointer; color: #116ef9"
                  @click="opneDomainDetail(scope.row.Client.Host,3)"
                >
                  {{ scope.row.Client.Host || "--" }}
                </span>
              </div>
              <div v-if="scope.row.Client.Host" class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button
                        @click="forwardSearch(scope.row, 'Client.Host')"
                      >
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button
                        @click="reverseSearch(scope.row, 'Client.Host')"
                      >
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('客户端.客户端信息')"
          prop="Client.UserAgent"
          min-width="160"
          :show-overflow-tooltip="true"
          label="客户端.客户端信息"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <el-tooltip
                  :disabled="!scope.row.Client.UserAgent"
                  class="item"
                  effect="light"
                  placement="top"
                  popper-class="sessionidTooltip"
                >
                  <div slot="content">
                    {{ scope.row.Client.UserAgent }}
                  </div>
                  <div>
                    {{
                      scope.row.Client.UserAgent
                        ? scope.row.Client.UserAgent.slice(0, 10) + "..."
                        : "--"
                    }}
                  </div>
                </el-tooltip>
              </div>
              <div v-if="scope.row.Client.UserAgent" class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button
                        @click="forwardSearch(scope.row, 'Client.User-Agent')"
                      >
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button
                        @click="reverseSearch(scope.row, 'Client.User-Agent')"
                      >
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('客户端.标头列表')"
          prop="Client.Title"
          min-width="160"
          label="客户端.标头列表"
        >
          <template slot-scope="scope">
            <el-tooltip
              :disabled="!scope.row.Client.Title"
              class="item"
              effect="light"
              placement="top"
              popper-class="sessionidTooltip"
            >
              <div slot="content">
                {{ scope.row.Client.Title }}
              </div>
              <div>
                {{
                  scope.row.Client.Title
                    ? scope.row.Client.Title.slice(0, 2) + "..."
                    : "--"
                }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('服务端.内容类型')"
          prop="Server.Content-Type"
          min-width="160"
          show-overflow-tooltip
          label="服务端.内容类型"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('服务端.时间')"
          prop="Server.Date"
          min-width="160"
          align="center"
          label="服务端.时间"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('服务端.修改时间')"
          prop="Server.Last-Modified"
          min-width="160"
          align="center"
          label="服务端.修改时间"
        >
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('服务端.内容')"
          prop="Server.Payload"
          min-width="160"
          label="服务端.内容"
        >
          <template slot-scope="scope">
            <el-tooltip
              :disabled="!scope.row.Server.Payload"
              class="item"
              effect="light"
              placement="top"
              popper-class="sessionidTooltip"
            >
              <div slot="content">
                {{ scope.row.Server.Payload }}
              </div>
              <div>
                {{
                  scope.row.Server.Payload
                    ? scope.row.Server.Payload.slice(0, 9) + "..."
                    : "--"
                }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeader.includes('服务端.标头列表')"
          prop="Server.Title"
          min-width="160"
          label="服务端.标头列表"
        >
          <template slot-scope="scope">
            <el-tooltip
              :disabled="!scope.row.Server.Title"
              class="item"
              effect="light"
              placement="top"
              popper-class="sessionidTooltip"
            >
              <div slot="content">
                {{ scope.row.Server.Title }}
              </div>
              <div>
                {{
                  scope.row.Server.Title
                    ? scope.row.Server.Title.slice(0, 2) + "..."
                    : "--"
                }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-show="showfoot" class="listbox-foot">
      <div class="listbox-foot-top">
        <tablescroll :table-ref="$refs.HTTPTable"></tablescroll>
      </div>
      <div class="listbox-foot-down">
        <div class="listbox-foot-down-l">
          *元数据_HTTP上限为<span>10,000</span>条
        </div>
        <div class="listbox-foot-down-r">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="page_size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <div class="sessionDrawerbox">
      <el-drawer
        :visible.sync="sessionDrawer"
        title="会话详情"
        :modal="false"
        size="40%"
        destroy-on-close
        @close="sessionclose"
      >
        <!-- <span>我来啦!</span> -->
        <sessionDetails :tag_target_type="tag_target_type"></sessionDetails>
      </el-drawer>
    </div>
    <!-- ip详情抽屉 -->
    <div class="Drawerbox">
      <ipdetails
        :ip="detailIp"
        :tag_target_type="tag_target_type"
        :ip-detail-drawer="ipDetailDrawer"
        @closeDrawer="closeDrawer"
      />
    </div>
    <!-- Domain域名详情抽屉 -->
    <div class="Drawerbox">
      <Domaindetails
        :domain-list="domainList"
        :tag_target_type="tag_target_type"
        :domain-detail-drawer="DomainDetailDrawer"
        @DomaincloseDrawer="DomaincloseDrawer"
      />
    </div>
  </div>
</template>

<script>
import {
  sessionAnalysis,
  getsessiondata,
  alllogderive,
  GetIpInfo,
  GetDomainInfo,
} from "@/api/sessionList/sessionlist";
import { common } from "./listfn/Detailfn";
import protocolInfofliter from "./protocolInfofliter.vue";
import sessionDetails from "../components/sessionDetails/index.vue";
import tablescroll from "../../../components/TableScroll/idnex.vue";
import ipdetails from "../components/details/IPdetails.vue";
import Domaindetails from "../components/details/Domaindetails.vue";
const cityOptions = ["源端口", "目的端口", "IP协议"];
export default {
  components: {
    protocolInfofliter,
    sessionDetails,
    tablescroll,
    ipdetails,
    Domaindetails,
  },
  // 调用 mixin 将组件js与共用js合并 ---
  mixins: [common],
  props: ["searchData"],
  // -------------------------------
  data() {
    return {
      showfoot: false,
      order: {
        order_prop: "",
        order_field: "EndTime",
        asc: true,
      },
      reset_stamp: 0,
      tableData: [],
      total: 0,
      current_page: 1,
      page_size: 10,
      order_field: "",
      asc: true,
      currentPage: 1,
      checkedHeader: [],
      display: "none", //json框
      session_id: "", //json框
      filterData: {},
      sessionParam: {},
      //会话详情抽屉页配置项
      sessionDrawer: false,
      sessiondata: [], //点击获取到单列表数据
      // ===================
      basicHkey: "",
      num: 0,
      // 控制抽屉出现位置
      direction: "rtl",
      // ip详情抽屉
      ipDetailDrawer: false,
      ipdata: [],
      // Domain详情抽屉
      DomainDetailDrawer: false,
      domainList: {},
      detailIp: "",
      tag_target_type:9999
    };
  },
  watch: {
    searchData: {
      handler(val) {
        let value = JSON.parse(JSON.stringify(val));
        this.current_page = 1;
        if (JSON.stringify(value) == "{}") {
          //  console.log('kong')
        } else {
          //  console.log('value',value)
          this.initData(value);
        }
      },
      deep: true,
      // 配置立即执行属性
      immediate: true,
    },
  },

  mounted() {
    // this.initData();
  },
  methods: {
    // 日志导出
    logderive() {
      const param = {
        condition: {
          current_page: this.currentPage,
          page_size: this.page_size,
          order_field: 'StartTime',
          asc: this.order.asc,
          query: [],
          data_key:'HTTP'
        },
        user_id: 1,
        task_type: 3,
      };
      if (JSON.stringify(this.filterData) != "{}") {
        const arr = [];
        for (const i in this.filterData) {
          arr.push(this.filterData[i]);
        }
        param.condition.query = param.condition.query.concat(arr);
      }
      if (this.searchData.query.length > 0) {
        param.condition.query = param.condition.query.concat(
          this.searchData.query
        );
      }
      this.searchData.task_id
        ? (param.condition.task_id = this.searchData.task_id)
        : null;
      this.searchData.time_range
        ? (param.condition.time_range = this.searchData.time_range)
        : null;
      const arr = {
        and: [],
        not: [],
      };
      for (let i = 0; i < param.condition.query.length; i++) {
        for (let j = 0; j < param.condition.query[i].search.length; j++) {
          if (param.condition.query[i].bool_search === "and") {
            if (param.condition.query[i].search[j].target === "Labels") {
              arr.and.push(param.condition.query[i].search[j].val);
            }
          }
          if (param.condition.query[i].bool_search === "not") {
            if (param.condition.query[i].search[j].target === "Labels") {
              arr.not.push(param.condition.query[i].search[j].val);
            }
          }
        }
      }
      param.condition.tag_query = arr;
      alllogderive(param).then((res) => {
        if (res.err == 0) {
          this.$store.commit("conversational/setdowndot", true);
          this.$message("成功加入日志导出队列");
        }
      });
    },
    resetFilter(prop) {
      delete this.filterData[prop];
      this.order = {
        order_prop: "",
        order_field: "",
        asc: true,
      };
      (this.current_page = 1), (this.page_size = 10), this.initData();
    },
    rangeFilterHandler(prop, min, max, sort) {
      if (prop.includes("sPort")) {
        this.filterData[prop] = {
          bool_search: "and",
          search: [
            {
              target: prop === "sPort" ? "SrcRangeOfPort" : "DstRangeOfPort",
              left: String(min) || String(0),
              right: String(max) || String(65535),
            },
          ],
        };
      }
      if (prop.includes("dPort")) {
        this.filterData[prop] = {
          bool_search: "and",
          search: [
            {
              target: prop === "sPort" ? "SrcRangeOfPort" : "DstRangeOfPort",
              left: String(min) || String(0),
              right: String(max) || String(65535),
            },
          ],
        };
      }
      this.order.order_prop = prop;
      this.order.asc = sort;
      switch (prop) {
      case "sPort ":
        this.order.order_prop = "sPort";
        break;
      case "dport":
        this.order.order_prop = "dPort";
        break;
        // case 'ip_pro':
        //   this.order.order_field = 'IPPro';
        //   break;
      }
      if (sort === null) {
        this.order.order_prop = "";
        this.order.order_field = "";
        this.order.asc = null;
      }
      (this.current_page = 1), (this.page_size = 10), this.initData();
    },
    getJson(val) {
      this.display = "block";
      this.session_id = val;
    },
    checkedHeaders(data) {
      this.checkedHeader = data;
    },
    indexMethod(index) {
      return (this.current_page - 1) * this.page_size + index + 1;
    },
    handleSizeChange(val) {
      // console.log(val);
      this.page_size = val;
      this.page_size = val;
      this.initData();
    },
    handleSelectionChange(val) {
      this.checkConfigList = val;
    },
    handleSortChange({ column, prop, order }) {
      this.order.order_prop = prop;
      this.order.asc = order === "descending" ? false : true;
      this.initData();
    },
    handleCurrentChange(val) {
      this.current_page = val;
      this.currentPage = val;
      this.initData();
    },
    ipFilterHandler(prop, checked_filters, sort, checkAll, search_filter) {
      // console.log(prop, checked_filters, sort, checkAll, search_filter);
      let ppid = [];
      for (let i in checked_filters) {
        ppid.push(String(checked_filters[i]));
      }
      this.filterData[prop] = {
        bool_search: "and",
        search: [
          {
            target:
              prop === "src_ip" ? "sIp" : prop === "dst_ip" ? "dIp" : "Tag",
            val: ppid,
          },
        ],
      };
      this.order.order_prop = prop;
      this.order.asc = sort;
      this.fuzzy_match = null;
      switch (prop) {
      case "src_ip":
        this.order.order_field = "sIp";
        break;
      case "dst_ip":
        this.order.order_field = "dIp";
        break;
        // case 'tags':
        //   this.order.order_prop = 'begin_time';
        //   this.order.order_field = 'StartTime';
        //   this.order.asc = true;
        //   break;
      }
      if (sort === null) {
        this.order.order_prop = "";
        this.order.order_field = "";
        this.order.asc = null;
      }
      if (checkAll) {
        this.fuzzy_match = {};
        this.fuzzy_match[prop] = search_filter;
      }
      (this.current_page = 1), (this.page_size = 10), this.initData();
    },
    initData(val) {
      let param = {
        // "top": this.top,
        current_page: this.current_page,
        page_size: this.page_size,
        order_field: this.order.order_prop,
        asc: this.order.asc,
        query: [],
        task_id: this.searchData.task_id,
        data_key: "HTTP",
        time_range: val?.time_range,
      };
      if (JSON.stringify(this.filterData) != "{}") {
        let arr = [];
        for (let i in this.filterData) {
          arr.push(this.filterData[i]);
        }
        param.query = param.query.concat(arr);
      }
      if (this.searchData.query.length > 0) {
        param.query = param.query.concat(this.searchData.query);
      }
      if (val?.time_range == undefined) {
        param.time_range = this.searchData.time_range;
      }
      param.aggr_query = false;
      // for (let i of param.query) {
      //   for (let j of i.search) {
      //     if (j.target === 'Labels') {
      //       param.aggr_query = true
      //     }
      //   }
      // }
      let arr = {
        and: [],
        not: [],
      };
      for (let i = 0; i < param.query.length; i++) {
        for (let j = 0; j < param.query[i].search.length; j++) {
          if (param.query[i].bool_search === "and") {
            if (param.query[i].search[j].target === "Labels") {
              arr.and.push(param.query[i].search[j].val);
            }
          }
          if (param.query[i].bool_search === "not") {
            if (param.query[i].search[j].target === "Labels") {
              arr.not.push(param.query[i].search[j].val);
            }
          }
        }
      }
      param.tag_query = arr;
      this.sessionParam = param;
      sessionAnalysis(param).then((res) => {
        if (res.err === 0) {
          this.tableData = res.data.records||[];
          if (res.data.records) {
            this.showfoot = true;
          }
          for (let i in this.tableData) {
            for (let I in this.tableData[i].Client) {
              if (I === "User-Agent") {
                let item = {
                  UserAgent: this.tableData[i].Client["User-Agent"],
                };
                Object.assign(this.tableData[i].Client, item);
              }
            }
          }
          this.total = res.data.total;
          this.total = res.data.total > 10000 ? 10000 : res.data.total;
          this.$emit("update:fatherValue", res.data.total);
        } else {
          this.$message.error(res.msg);
          this.total = 0;
          this.$emit("update:fatherValue", 0);
        }
      });
    },
    // 打开会话详情抽屉页面
    async opensessionDrawer(data,type) {
      this.sessiondata = data.row;
      this.tag_target_type=type;
      let param1 = {
        sub_type: "basic",
        search: data.row.SessionId,
        es_index: data.row.es_index
      };
      let param2 = {
        sub_type: "session_log",
        search: data.row.SessionId,
        Hkey: data.row.Hkey,
        es_index: data.row.es_index,
      };
      let param3 = {
        sub_type: "protocol_meta_data",
        search: data.row.SessionId,
        Hkey: data.row.Hkey,
        limit: 50,
      };
      let param4 = {
        sub_type: "packet_histogram",
        search: data.row.SessionId,
        Hkey: data.row.Hkey,
        limit: 50,
      };
      // 请求基础五元组信息
      await getsessiondata(param1).then((res) => {
        if (res.err == 0) {
          this.basicHkey = res.data.hkey;
          this.$store.commit("conversational/sessionDetailData", res.data);
          this.num++;
          this.addnum();
        }
      });
      // 请求会话日志
      await getsessiondata(param2).then((res) => {
        if (res.err == 0) {
          if (res.data == '查询会话详情信息为空') {
            this.$store.commit('conversational/sessionDetailLog', {});
          } else {
            this.$store.commit(
              'conversational/sessionDetailLog',
              res.data
            );
          }
          this.num++;
          this.addnum();
        }
      });
      // 请求协议元数据
      await getsessiondata(param3).then((res) => {
        console.log(res, '协议元数据');
        if (res.err == 0) {
          this.$store.commit('conversational/sessionDetailAgreement', res.data);
          this.num++;
          this.addnum();
        }
      });
      // 请求包分析数据
      await getsessiondata(param4).then((res) => {
        if (res.err == 0) {
          this.$store.commit('conversational/sessionDetailHistogram', res.data);
          this.num++;
          this.addnum();
        }
        
      });
    },
    addnum() {
      if (this.num == 4) {
        this.sessionDrawer = true;
        this.num = 0;
      }
    },
    // 关闭会话详情抽屉页面
    sessionclose() {
      this.$store.commit("conversational/sessionDetailData", {});
      this.$store.commit("conversational/sessionDetailLog", {});
      this.$store.commit("conversational/sessionDetailAgreement", {});
      this.$store.commit("conversational/sessionDetailHistogram", {});
    },
    // 打开ip详情
    opneipDetail(ip,type) {
      this.detailIp = ip;
      this.ipLoading = true;
      this.ipDetailDrawer = true;
      this.tag_target_type=type;
    },
    closeDrawer() {
      this.ipDetailDrawer = false;
    },

    // 打开域名详情
    opneDomainDetail(domain,type) {
      this.domainList = {};
      GetDomainInfo(`str=${domain}`).then((res) => {
        if (res.err == 0) {
          this.domainList = this.DomaininfoDandle(res.data);
          this.DomainDetailDrawer = true;
          this.tag_target_type=type;
        }
      });
    },
    DomaincloseDrawer() {
      this.DomainDetailDrawer = false;
    },
    // 客户端IP正向检索
    forwardsort(data, sign) {
      data.row.value='HTTP';
      if (sign == "ip") {
        data.row.sort = false;
        data.row.sortname = sign;

        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "sip") {
        data.row.sort = false;
        data.row.sortname = sign;
        data.row.src_ip = data.row.sIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "dip") {
        data.row.sort = false;
        data.row.sortname = sign;
        data.row.dst_ip = data.row.dIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
    },
    // 客户端IP反向检索
    reversesort(data, sign) {
      data.row.value='HTTP';
      if (sign == "ip") {
        data.row.sort = true;
        data.row.sortname = sign;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "sip") {
        data.row.sort = true;
        data.row.sortname = sign;
        data.row.src_ip = data.row.sIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "dip") {
        data.row.sort = true;
        data.row.sortname = sign;
        data.row.dst_ip = data.row.dIp;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
    },
    // 正向快速检索
    forwardSearch(data, sign) {
      data.value='HTTP';
      data.sort = false;
      data.sortname = sign;
      data.AppName = data.appName;
      data.dst_port = data.dPort;
      // data.dCertHash = data.dCertHashStr;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 反向快速检索
    reverseSearch(data, sign) {
      data.sort = true;
      data.sortname = sign;
      data.value='HTTP';
      data.AppName = data.appName;
      data.dst_port = data.dPort;
      // data.dCertHash = data.dCertHashStr;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
  },
};
</script>

<style lang="scss" scoped>
.head {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content {
  margin: 0 16px;
  height: 220px;
  overflow: auto;
}

.foot {
  height: 48px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f8f8;
  border-radius: 0px 0px 2px 2px;

  .el-button {
    height: 24px;
    width: 58px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.listbox {
  &-head {
    display: flex;
    justify-content: flex-end;

    ::v-deep {
      .el-button {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 94px;
        height: 32px;
      }

      .el-checkbox-group {
        display: flex;
        flex-direction: column;
      }
    }
  }

  &-from {
    .sortbox {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      // position: relative;
      .top {
        margin-right: 5px;
      }

      .down {
        width: 10px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .sorttoole {
        display: none;
        padding-bottom: 5px;
        color: #116ef9;
        // position: absolute;
        // top: -2px;
        // right: 0;
      }
    }

    .sortbox:hover {
      .sorttoole {
        display: block;
        cursor: pointer;
      }
    }
  }

  &-foot {
    z-index: 999;
    padding: 10px 0;
    position: sticky;
    // right: 34px;
    bottom: 0px;
    width: 100%;
    background: #ffffff;
    border-radius: 8px;

    &-top {
      margin-bottom: 10px;
    }

    &-down {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-bottom: 20;

      &-l {
        font-size: 12px;
        color: #9999a1;

        span {
          color: #000;
        }
      }
    }
  }
}

.sessionDrawerbox {
  ::v-deep {
    .el-drawer.rtl {
      overflow: scroll;
    }

    .el-drawer__header {
      font-weight: 500;
      font-size: 16px;
      color: #000000;
    }
  }
}
</style>
export const common = {
  // 组件共用方法 ---------------------------------------------
  methods: {
    // 获取到ip详情进行数据处理
    datainfoDandle(val) {
      let data = {};
      data.ip = val.ip;
      data.taskname =val.taskNames && val.taskNames[0] || '--';
      // 城市和国家都没有的情况下，显示未知
      if (!val.city && !val.country) {
        data.country_city = "未知";
      } else {
        if(val.country && val.city){
          data.country_city = val.country + "-" + val.city;
        }else{
          data.country_city = val.country + "" + val.city;
        }
        
      }
      // 判断ipKey是否有特殊符号
      if (val.ipKey?.includes("_")) {
        data.ipKey = `${val.taskNames[0]}-内网`;
      } else {
        data.ipKey = `${data.country_city}`;
      }

      if (val.blackList == 0) {
        data.blackList = val.blackList = "安全";
      } else if (val.blackList >= 1 && val.blackList < 60) {
        data.blackList = val.blackList = "未知";
      } else if (val.blackList >= 60 && val.blackList < 90) {
        data.blackList = val.blackList = "可疑";
      } else if (val.blackList >= 90 && val.blackList <= 100) {
        data.blackList = val.blackList = "恶意";
      }
      data.ipAddr = val.ipAddr;
      data.labels = [];
      let tag_id_list = this.$store.state.conversational.taglist01;
      // 获取到标签id列表，对id在标签库进行循环获取完整标签信息
      for (let i = 0; i < tag_id_list.length; i++) {
        for (let j = 0; j < val.labels?.length; j++) {
          if (tag_id_list[i].tag_id == val.labels[j]) {
            data.labels.push(tag_id_list[i]);
          }
        }
      }
      data.labels = this.formatTag(data.labels);
      data.remark = val.remark; //备注
      data.remarks = val.remarks; //备注
      data.blackList = val.blackList; //黑名单等级(威胁)
      data.whiteList = val.whiteList; //白名单等级
      let list = [
        {
          name: "平均流量",
          value: `${this.formatSize(Math.abs(val.averageBps))}/s`,
        },
        {
          name: "开放端口数",
          value: val.openPortNum,
        },
        {
          name: "访问端口数",
          value: val.accessPortNum,
        },
        {
          name: "所属锚域名数",
          value: val.fdomainNum,
        },
        {
          name: "发送负载量",
          value: this.formatSize(Math.abs(val.sendBytes)),
        },
        {
          name: "接收负载量",
          value: this.formatSize(Math.abs(val.recvBytes)),
        },
        {
          name: "最早出现时间",
          value: this.formatDate(val.firstTime),
        },
        {
          name: "最后出现时间",
          value: this.formatDate(val.lastTime),
        },
      ];
      data.list = list;
      console.log(data, "data");
      return data;
    },
    // 时间戳转时间单位秒
    formatDate(val) {
      if(!val){return '';}
      let date = new Date(val * 1000);
      let Y = date.getFullYear() + "-";
      let M =
        (date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1) + "-";
      let D =
        (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
      let h =
        (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
      let m =
        (date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) +
        ":";
      let s =
        date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      return Y + M + D + h + m + s;
    },
    // 数据容量单位转换
    formatSize(val) {
      if (val < 1024) {
        return val + "B";
      } else if (val < 1024 * 1024) {
        return (val / 1024).toFixed(2) + "KB";
      } else if (val < 1024 * 1024 * 1024) {
        return (val / 1024 / 1024).toFixed(2) + "MB";
      } else if (val < 1024 * 1024 * 1024 * 1024) {
        return (val / 1024 / 1024 / 1024).toFixed(2) + "GB";
      } else {
        return (val / 1024 / 1024 / 1024 / 1024).toFixed(2) + "TB";
      }
    },
    // 对标签列表进行数据处理
    formatTag(arr) {
      arr.sort((a, b) => {
        if (a.black_list > b.black_list) {
          return b.black_list - a.black_list;
        } else if (a.black_list == 0 && b.black_list == 0) {
          return b.white_list - a.white_list;
        }
      });

      let tags = [];

      tags = arr.map((val) => {
        // console.log(val);
        let type = "";
        let tag_level = "";
        if (
          val.black_list >= 1 &&
          val.black_list <= 100 &&
          val.white_list !== 100
        ) {
          if (val.black_list >= 80) {
            type = "danger";
            tag_level = "danger";
          } else {
            type = "warning";
            tag_level = "warning";
          }
        }
        if (
          val.white_list >= 1 &&
          val.white_list <= 100 &&
          val.black_list === 0
        ) {
          if (val.white_list === 100) {
            type = "success";
            tag_level = "success";
          } else {
            type = "";
            tag_level = "";
          }
        }
        if (val.white_list === 0 && val.black_list === 0) {
          type = "info";
          tag_level = "info";
        }
        let tag_info = [val.tag_text, type, val.tag_id || 0, val.type];
        // tags.push(tag_info)

        return {
          // id: val.tag_id || 0,
          tag_text: val.tag_text,
          type,
          tag_level,
          targetType: val.type,
          tagID: val.tag_id,
          tag_explain: val.tag_explain,
          attribute_name: val.attribute_name,
          exist: val.exist,
          tagText: val.tag_text,
          tag_id: val.tag_id,
        };
      });
      // 标签去重
      let fjLists = [];

      const res = new Map();

      fjLists = tags.filter(
        (item) => !res.has(item.tagText) && res.set(item.tagText, 1)
      );
      return fjLists;
    },
    // =============================

    //获取到域名数据进行处理
    DomaininfoDandle(val) {
      let data = {};
      data.domain = val.domain || '';
      data.alexa_rank = val.alexaRank;
      data.taskname = val.taskNames[0] || '';

      if (val.blackList == 0) {
        console.log("安全安全安全安全");
        data.blackList = val.blackList = "安全";
      } else if (val.blackList >= 1 && val.blackList < 60) {
        data.blackList = val.blackList = "未知";
      } else if (val.blackList >= 60 && val.blackList < 90) {
        data.blackList = val.blackList = "可疑";
      } else if (val.blackList >= 90 && val.blackList <= 100) {
        data.blackList = val.blackList = "恶意";
      }
      data.labels = [];
      let tag_id_list = this.$store.state.conversational.taglist01;
      console.log(tag_id_list);
      // 获取到标签id列表，对id在标签库进行循环获取完整标签信息
      if (val.labels != null) {
        for (let i = 0; i < tag_id_list.length; i++) {
          for (let j = 0; j < val.labels.length; j++) {
            if (tag_id_list[i].tag_id == val.labels[j]) {
              data.labels.push(tag_id_list[i]);
            }
          }
        }
        data.labels = this.formatTag(data.labels);
      } else {
        data.labels = [];
      }
      data.whoIs = val.whoIs;
      data.brotherNum = val.brotherNum;
      data.clientHeat = val.clientHeat;
      data.fdomain = val.fdomain != null ? val.fdomain : "--";
      data.remarks = val.remarks;
      data.toIpNum = val.toIpNum;
      data.whiteList = val.whiteList;
      let list = [
        {
          name: "Alex排名",
          value: val.alexaRank,
        },
        {
          name: "兄弟域名数量",
          value: val.brotherNum,
        },
        {
          name: "指向IP数量",
          value: val.toIpNum,
        },
        {
          name: "WhoIS",
          value: val.whoIs,
        },
        {
          name: "客户端热度",
          value: val.clientHeat,
        },
        {
          name: "关联证书数量",
          value: val.certNum,
        },
        {
          name: "最早出现时间",
          value: this.formatDate(val.firstTime),
        },
        {
          name: "最后出现时间",
          value: this.formatDate(val.lastTime),
        },
      ];
      data.list = list;
      return data;
    },
    // =============================
    // 获取详情进行数据处理
    CertinfoDandle(val) {
      console.log(val);
      let data = {};
      data.cert = val.cert;

      if (Array.isArray(val.taskNames) && val.taskNames.length > 0) {
        data.taskname = val.taskNames[0];
      } else {
        data.taskname = "--";
      }

      if (val.blackList == 0) {
        console.log("安全安全安全安全");
        data.blackList = val.blackList = "安全";
      } else if (val.blackList >= 1 && val.blackList < 60) {
        data.blackList = val.blackList = "未知";
      } else if (val.blackList >= 60 && val.blackList < 90) {
        data.blackList = val.blackList = "可疑";
      } else if (val.blackList >= 90 && val.blackList <= 100) {
        data.blackList = val.blackList = "恶意";
      }
      data.whiteList = val.whiteList;
      data.labels = [];
      let tag_id_list = this.$store.state.conversational.taglist01;
      console.log(tag_id_list);
      // 获取到标签id列表，对id在标签库进行循环获取完整标签信息
      for (let i = 0; i < tag_id_list.length; i++) {
        for (let j = 0; j < val.labels.length; j++) {
          if (tag_id_list[i].tag_id == val.labels[j]) {
            data.labels.push(tag_id_list[i]);
          }
        }
      }
      data.labels = this.formatTag(data.labels);
      data.remark = val.remark; //备注
      data.remarks = val.remarks; //备注
      let list = [
        {
          name: "签发机构",
          value: val.issuerO != null ? val.issuerO : "--",
        },
        {
          name: "所有者",
          value: val.subjectO != null ? val.subjectO : "--",
        },
        {
          name: "签发时间",
          value: val.notBefore != null ? val.notBefore : "--",
        },
        {
          name: "有效时间",
          value: val.notAfter != null ? val.notAfter : "--",
        },
        {
          name: "服务器热度",
          value: val.serverHeat != null ? val.serverHeat : "--",
        },
        {
          name: "客户端热度",
          value: val.clientHeat != null ? val.clientHeat : "--",
        },
        {
          name: "首次出现时间",
          value: this.formatDate(val.firstTime),
        },
        {
          name: "末次出现时间",
          value: this.formatDate(val.lastTime),
        },
      ];
      data.list = list;
      console.log(data, "data");
      return data;
    },
    // 元数据http主站过滤方法
    ipverify_http(v) {
      let item = {
        host: v,
        sign: false,
      };
      const reg = new RegExp(
        /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
      );
      const pattIp =
      new RegExp(/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/);
      const pattIp1 = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/;
      const pattIp2 = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/;
      const DomainIP = /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;
      if (pattIp.test(v) || reg.test(v) || pattIp1.test(v) || pattIp2.test(v)) {
        item.sign = true;
      }
      if (!DomainIP.test(v)) {
        item.sign = true;
      }
      return item;
    },
    // 元数据dns域名过滤方法
    ipverify_dns(v) {
      let item = {
        host: v,
        sign: false,
      };
      const DomainIP = /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;
      // 
      if (!DomainIP.test(v)) {
        item.sign = true;
      } else {
        if (v.match(/\.arpa(.*)/) != null) {
          item.sign = true;
        }
      }
      return item;
    },
  },
};

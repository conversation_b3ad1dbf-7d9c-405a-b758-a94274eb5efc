<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-06-15 11:18:21
 * @LastEditors: lian<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-08-11 14:22:18
 * @FilePath: \Vue学习d:\级客信安\Web\probe_v3\src\views\table\components\sessionDetails\index.vue
 * @Description: 
 * 
 * Copyright (c) 2022 by lian<PERSON><PERSON><PERSON><PERSON> <EMAIL>, All Rights Reserved. 
-->
<template>
  <div class="sessionDetails">
    <!-- <basic-info :session_id="session_id"></basic-info> -->
    <!-- TODO 需要传:session_id="session_id" -->
    <basic-info :tag_target_type="tag_target_type"></basic-info> 
  </div>
</template>

<script>
import basicInfo from "./components/basicinfo.vue";
export default {
  name:'SessionDetails',
  components: {
    basicInfo,
  },
  props: ["tag_target_type"],
  data() {
    return {};
  },
};
</script>

<style lang="scss" scoped>
.sessionDetails {
  height: 100%;
  background: url("../../../../assets/images/sessionbg.png") no-repeat;
  background-size: 100%;
  overflow: scroll;
}
</style>
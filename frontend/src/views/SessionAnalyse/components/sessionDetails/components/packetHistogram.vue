<template>
  <div class="packbox">
    <div class="packbox-top">
      <div class="title">包长</div>
      <div class="packbox-top-iptext">
        <span>
          <svg-icon icon-class="ipbule" style="margin-right: 5px" />
          {{ packethistogram.dst_ip }}</span>
        <span><svg-icon icon-class="ipviolet" style="margin-right: 5px" />{{
          packethistogram.src_ip
        }}</span>
      </div>
    </div>
    <article class="packbox-down">
      <section class="packboxmsg">
        <v-chart
          :option="pack_options"
          autoresize
          :update-options="{ notMerge: true }"
        >
        </v-chart>
      </section>
    </article>
  </div>
</template>

<script>
import { pack_options } from "../../tablechartsData";
export default {
  props: {
    session_id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      pack_options,
    };
  },
  computed: {
    packethistogram() {
      return this.$store.state.conversational.packethistogram;
    },
  },
  watch: {
    session_id: {
      deep: true,
      immediate: true,
      handler(val) {
        setTimeout(() => {
          console.log("########（包分析）#########", val);
          this.initChartData();
        }, 1000);
      },
    },
  },
  methods: {
    initChartData() {
      let that = this;
      that.pack_options.series[0].data = [];
      console.log(this.packethistogram);
      let records = this.packethistogram.histogram;
      let x_axis = [],
        dis_dst = [],
        dis_src = [];
      // let legend = [this.packethistogram.src_ip, this.packethistogram.dst_ip];
      if (records == null) return;
      for (let i = 0; i < records.length; i++) {
        let count = records[i].count;
        if (x_axis.indexOf(count) == -1) {
          x_axis.push(count);
          if (records[i].c2s) {
            dis_src.push(records[i].len);
            dis_dst.push(0);
          } else {
            dis_dst.push(-records[i].len);
            dis_src.push(0);
          }
        } else {
          if (records[i].c2s) dis_src[x_axis.indexOf(count)] += records[i].len;
          else dis_dst[x_axis.indexOf(count)] -= records[i].len;
        }
      }
      console.log(x_axis);
      console.log(dis_dst);
      console.log(dis_src);
      that.pack_options.series[0].data = dis_dst;
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  font-weight: 400;
  font-size: 12px;
  color: #2C2C35;
}
.packbox {
  position: relative;
  &-top {
    position: absolute;
    top: 2px;
    left: 0;
    width: 80%;
    display: flex;
    justify-content: space-between;
    &-iptext {
      font-weight: 400;
      font-size: 12px;
      color: #767684;
      width: 60%;
      display: flex;
      justify-content: space-between;
    }
  }
  &-down {
    .packboxmsg {
      width: 100%;
      height: 500px;
    }
  }
}
</style>
<template>
  <div class="session">
    <div class="session-head">
      <div class="session-head-line1">
        <span class="title">会话ID</span>
        <div>
          <span>{{ sessiondetail.session_id }}</span>
          <span v-show="bw_show" class="clickstyle" @click="applyBW">确认黑名单</span>
          <span v-show="!bw_show" class="clickstyle" @click="cancelBW">取消黑名单</span>
        </div>
      </div>
      <!-- <div class="session-head-line2">
        <span class="title">状态</span>
        <div>
          <img
            src="../../../../../assets/images/state1.png"
            alt=""
            style="width: 62px; height: 22px"
          />
        </div>

        <el-tag type="success">安全</el-tag>
        <el-tag type="warning">可疑</el-tag>
        <el-tag type="danger">恶意</el-tag>
      </div> -->
      <div class="session-head-line3">
        <span class="title">所属任务</span>
        <div>
          <span>{{ sessiondetail.task_name }}</span>
        </div>
      </div>
      <div class="session-head-line4">
        <span class="title">标签</span>
        <div>
          <div class="tagbox">
            <span v-for="item in sessiondetail.tags" :key="item.tag_id">
              <!-- <span>{{ item.tag_text }}</span> -->
              <el-tag
                class="tag"
                closable
                :type="item.tag_level"
                @close="closetag(item)"
              >
                {{ item.tag_text }}
              </el-tag>
              <!-- <span v-if="index < scope.row.tags.length - 1">,</span> -->
            </span>
          </div>
          <span class="clickstyle" @click="opentagbox">标签库</span>
        </div>
      </div>
      <div class="session-head-line5">
        <span class="title">备注</span>
        <div class="cert_tag">
          <el-tag
            v-for="(item, index) in sessiondetail.remark"
            :key="index"
            class="remark"
            closable
            :disable-transitions="true"
            @close="taghandleClose(item)"
          >
            {{ item.remark }}
          </el-tag>
          <div>
            <el-input
              v-if="inputVisible"
              ref="saveTagInput"
              v-model="inputValue"
              resize="horizontal"
              @keyup.enter.native="$event.target.blur"
              @blur="handleInputConfirm"
            ></el-input>
            <el-button
              class="cert_tag_button"
              size="small"
              icon="el-icon-plus"
              @click="showInput"
            >
              添加备注
            </el-button>
          </div>
        </div>
      </div>
      <div class="session-head-line6">
        <span class="title">开始时间</span>
        <div>
          <span>{{ sessiondetail.start_time }}</span>
        </div>
      </div>
      <div class="session-head-line7">
        <span class="title">结束时间</span>
        <div>
          <span>{{ sessiondetail.end_time }}</span>
        </div>
      </div>
    </div>
    <div class="session-mid">
      <div class="session-mid-l">
        <div class="midbox">
          <div class="midbox-t">客户端IP</div>
          <div>{{ sessiondetail.src_ip }}</div>
        </div>
        <div class="midbox">
          <div>源IP端口</div>
          <div>{{ sessiondetail.src_port }}</div>
        </div>
      </div>
      <div class="session-mid-m">
        <v-chart
          :option="chatOptions" 
          autoresize
          :update-options="{ notMerge: true }"
        >
        </v-chart>
        <!-- <v-chart
          :option="mOptions" 
          autoresize
          :update-options="{ notMerge: true }"
        >
        </v-chart> -->
      </div>
      <div class="session-mid-r">
        <div class="midbox">
          <div class="midbox-t">服务端IP</div>
          <div>{{ sessiondetail.dst_ip }}</div>
        </div>
        <div class="midbox">
          <div>目的IP端口</div>
          <div>{{ sessiondetail.dst_port }}</div>
        </div>
      </div>
    </div>
    <!-- <div class="indialog">
      <el-dialog
        v-dialogDrag
        :visible.sync="opentag"
        :modal="false"
        :close-on-click-modal="false"
        width="1000px"
        @opened="handleOpened"
      >
        <tagsdeta
          :display="(display = 'block')"
          :value="tagval"
          @getTagValue="getTagValue"
          @handleClose="display = 'none'"
          @cleartagdialog="cleartagdialog"
        ></tagsdeta>
      </el-dialog>
    </div> -->
    <div class="temp-info">
      <div>
        <span>IP协议</span>
        <span>{{ sessiondetail.ipproName }}</span>
      </div>
      <div>
        <span>应用</span>
        <span>{{ sessiondetail.app_name }}</span>
      </div>
    </div>
    <el-tabs v-model="activeTabName" class="tab-style">
      <el-tab-pane label="会话日志" name="sessionInfo">
        <sessionLog
          ref="sessionInfo"
          :session_id="sessiondetail.session_id"
          @updateList="updateList"
        ></sessionLog>
      </el-tab-pane>
      <el-tab-pane label="协议元数据" name="protocolInfo">
        <protocolinfo
          ref="protocolInfo"
          :session_id="sessiondetail.session_id"
        ></protocolinfo>
      </el-tab-pane>
      <!-- <el-tab-pane label="包分析" name="packetHistogram">
        <packetHistogram
          ref="packetHistogram"
          :session_id="sessiondetail.session_id"
        ></packetHistogram>
      </el-tab-pane> -->
    </el-tabs>
    <!-- 标签 -->
    <tag-view
      v-model="tagLibVisible"
      :tag_target_type="tag_target_type"
      :tag-labels="sessiondetail.tags"
      :is-show-aside="false"
      @modifyLabels="getTagValue"
    />
  </div>
</template>

<script>
import sessionLog from "./sessionLog.vue";
import protocolinfo from "./protocolinfo.vue";
import packetHistogram from "./packetHistogram.vue";
import { parseTime } from "@/utils";
import TagView from "@/components/TagView";
import tagsdeta from "@/views/SessionAnalyse/components/tag/tags_deta";
import {
  addRemark,
  deleteRemark,
  getsessiondata,
  editTargetTag
} from "@/api/sessionList/sessionlist";
import { RunJSError } from "runjs/lib/common";
export default {
  name:'BasicInfo',
  components: {
    sessionLog,
    protocolinfo,
    // packetHistogram,
    // eslint-disable-next-line vue/no-unused-components
    tagsdeta,
    TagView
  },
  props: ["tag_target_type"],
  data() {
    return {
      activeTabName: "sessionInfo",
      // 备注数据
      remarks: [],
      // 备注输入框的字段
      inputVisible: false,
      inputValue: "",
      //  =======
      // 标签弹窗字段
      opentag: false,
      tagval: "",
      bw_show: true,
      tagLibVisible:false,
      activetags: [],
      mOptions: {
        title: {},
        tooltip: {
          show: false
        },
        grid: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0
        },
        xAxis: {
          type: 'category',
          axisLine: { show: true },
          axisLabel: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        },
        yAxis: {
          axisLine: { show: false },
          axisLabel: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        },
        series: [
          {
            name: 'Cost',
            type: 'bar',
            stack: 'Total',
            label: {
              show: false
            },
            // showBackground: true,
            barWidth:6,
            itemStyle: {
              normal: {
                color: function(params) {
                  if (params.data >= 0) {
                    return '#116EF9'; // 大于等于10的柱子为红色
                  } else {
                    return '#DEE0E7'; // 小于10的柱子为蓝色
                  }
                },
                
              },

            },
            data: [0.18,0.18,0.18,0.18,0.18] // 数据数组
            
          }
        ]
      },
      chatOptions:{
        grid:{
          left:0,
          right:0,
          top:0,
          bottom:0
        },
        tooltip: {
          trigger: 'axis',
          formatter:'包长区间：{b}<br />{a0}：{c0}<br />{a1}：{c1}'
        },
        xAxis: [
          {
            type: 'category',
            axisTick: { show: false },
            name:'包长区间',
            data: ["0~128",
              "129~256",
              "257~384",
              "385~512",
              "513~640",
              "641~768",
              "769~896",
              "897~1024"]
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            type: 'bar',
            name:'源包数量',
            barWidth:'4',
            data: [],
            itemStyle: {
              color: '#116EF9', // 单一颜色
              // 或者颜色函数
            }
          },
          {
            type: 'bar',
            name:'目的包数量',
            barWidth:'4',
            data: [],
            itemStyle: {
              color: '#CECECE', // 单一颜色
              // 或者颜色函数
            }
          }
        ]
      },
      DistLen:{
        sDistLen:[],
        dDistLen:[],
      }
    };
  },
  computed: {
    // 获取到会话详情数据
    sessiondetail() {
      let sessiondetailarr = this.$store.state.conversational.sessiondetaildata;
      sessiondetailarr.start_time = parseTime(sessiondetailarr.start_time);
      sessiondetailarr.end_time = parseTime(sessiondetailarr.end_time);
      sessiondetailarr.ipproName=this.$store.state.long.Dict.protocol_type[sessiondetailarr.ippro]?.protocol_type || '-';
      if (sessiondetailarr.tags && sessiondetailarr.tags.length) {
        let tagName = sessiondetailarr.tags
          .map((item) => item.tag_text)
          .join(",");
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.tagval = tagName;
        let indexdata = tagName.indexOf("确认黑名单");
        if (indexdata != -1) {
          // eslint-disable-next-line vue/no-side-effects-in-computed-properties
          this.bw_show = false;
        } else {
          // eslint-disable-next-line vue/no-side-effects-in-computed-properties
          this.bw_show = true;
        }
        for (let i in sessiondetailarr.tags) {
          if (
            sessiondetailarr.tags[i].blackList >= 1 &&
            sessiondetailarr.tags[i].blackList <= 100 &&
            sessiondetailarr.tags[i].whiteList !== 100
          ) {
            if (sessiondetailarr.tags[i].blackList >= 80) {
              sessiondetailarr.tags[i].type = "danger";
            } else {
              sessiondetailarr.tags[i].type = "warning";
            }
          }
          if (
            sessiondetailarr.tags[i].whiteList >= 1 &&
            sessiondetailarr.tags[i].whiteList <= 100 &&
            sessiondetailarr.tags[i].blackList === 0
          ) {
            if (sessiondetailarr.tags[i].whiteList === 100) {
              sessiondetailarr.tags[i].type = "success";
            } else {
              sessiondetailarr.tags[i].type = "";
            }
          }
          if (
            sessiondetailarr.tags[i].whiteList === 0 &&
            sessiondetailarr.tags[i].blackList === 0
          ) {
            sessiondetailarr.tags[i].type = "info";
          }
        }
      }
      return sessiondetailarr;
    },
    packethistogram() {
      return this.$store.state.conversational.packethistogram;
    },
  },
  watch: {
    session_id: {
      deep: true,
      immediate: true,
      handler(val) {
        setTimeout(() => {
          this.FMT_MOPTIONS();
        }, 1000);
      },
    },
  },
  methods: {
    updateList({item,type}){
      this.DistLen[type]=item;
      this.$nextTick(()=>{
        this.chatOptions.series[0].data=this.DistLen.sDistLen;
        this.chatOptions.series[1].data=this.DistLen.dDistLen;
      });
    },
    // 格式化包分析数据
    FMT_MOPTIONS() { 
      let that = this;
      that.mOptions.series[0].data = [];
      let records = this.packethistogram.histogram;
      let x_axis = [],
        dis_dst = [],
        dis_src = [];
      // let legend = [this.packethistogram.src_ip, this.packethistogram.dst_ip];
      if (records == null) return;
      for (let i = 0; i < records.length; i++) {
        let count = records[i].count;
        if (x_axis.indexOf(count) == -1) {
          x_axis.push(count);
          if (records[i].c2s) {
            dis_src.push(records[i].len);
            dis_dst.push(0);
          } else {
            dis_dst.push(-records[i].len);
            dis_src.push(0);
          }
        } else {
          if (records[i].c2s) dis_src[x_axis.indexOf(count)] += records[i].len;
          else dis_dst[x_axis.indexOf(count)] -= records[i].len;
        }
      }
      that.mOptions.series[0].data = dis_dst;
    },
    handleOpened() {
      // 获取到id返回新的数组
      let tag_text = this.sessiondetail.tags.map((item) => {
        return item.tag_text;
      });
      this.tagval = tag_text.join(",");
    },
    taghandleClose(remark) {
      deleteRemark({ remark_id: remark.id }).then((res) => {
        this.init();
      });
    },
    // 鼠标在输入框中失去焦点的回调
    handleInputConfirm() {
      if (this.inputValue == "") return;
      let param = {
        target_key: this.sessiondetail.session_id,
        target_type: "session",
        remark: this.inputValue,
      };

      // this.$emit("modifyRemarks", this.remarks);
      addRemark(param).then((res) => {
        this.init();
      });
      this.inputVisible = false;
      this.inputValue = "";
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    opentagbox() {
      this.tagLibVisible = true;
      this.tagval = "";
    },
    getTagValue(x,val,fn) {
      if (val.length != 0) {
        let newarr = [];
        val.forEach((item) => {
          newarr.push(item.tag_id + "");
        });
        this.sessiondetail.tags.forEach((item) => {
          newarr.push(item.tag_id + "");
        });
        let param = {
          id: this.sessiondetail.id,
          es_index: this.sessiondetail.es_index,
          lables: this.unique(newarr),
        };
        editTargetTag(param).then((res) => {
          if (res.err == 0) {
            this.init();
          }
        });
      } else {
        this.sessiondetail.tags = [];
        this.tagval = "";
        let param = {
          id: this.sessiondetail.id,
          es_index: this.sessiondetail.es_index,
          lables: [],
        };
        editTargetTag(param).then((res) => {
          if (res.err == 0) {
            this.init();
          }
        });
      }
      fn(true);
    },
    // 删除
    async closetag(data) {
      let newarr = [];
      let tagarr = [];
      this.sessiondetail.tags.forEach((item) => {
        if (data.tag_id !== item.tag_id) {
          newarr.push(item.tag_id + "");
          tagarr.push(item.tag_text);
        }
      });
      let param = {
        id: this.sessiondetail.id,
        es_index: this.sessiondetail.es_index,
        lables: newarr,
      };
      this.tagval = tagarr + "";
      await editTargetTag(param).then((res) => {
        this.init();
      });
    },
    // 初始化数据
    init() {
      let param1 = {
        sub_type: "basic",
        search: this.sessiondetail.session_id,
        es_index:this.sessiondetail.es_index
      };
      // 请求基础五元组信息
      getsessiondata(param1).then((res) => {
        this.$store.commit("conversational/sessionDetailData", res.data);
      });
    },
    // 去重方法
    unique(arr) {
      const res = new Map();
      return arr.filter((arr) => !res.has(arr) && res.set(arr, 1));
    },
    // 确认黑名单
    applyBW() {
      let newarr = ["100"];
      this.sessiondetail.tags.forEach((item) => {
        newarr.push(item.tag_id + "");
      });
      let param = {
        id: this.sessiondetail.id,
        es_index: this.sessiondetail.es_index,
        lables: this.unique(newarr),
      };
      editTargetTag(param).then((res) => {
        if (res.err == 0) {
          this.init();
          this.bw_show = false;
        } else {
          console.log("加载失败");
        }
      });
    },
    // 取消黑名单
    cancelBW() {
      let newarr = [];
      this.sessiondetail.tags.forEach((item) => {
        if (item.tag_id != "100") {
          newarr.push(item.tag_id + "");
        }
      });
      let param = {
        id: this.sessiondetail.id,
        es_index: this.sessiondetail.es_index,
        lables: this.unique(newarr),
      };
      editTargetTag(param).then((res) => {
        if (res.err == 0) {
          this.init();
          this.bw_show = true;
        } else {
          console.log("加载失败");
        }
      });
    },
    // cleartagdialog() {
    //   this.opentag = false;
    // },
  },
};
</script>

<style lang="scss" scoped>
.session {
  font-weight: 400;
  font-size: 14px;
  height: 100%;
  .title {
    color: #9999a1;
  }
  .clickstyle {
    color: #116ef9;
    cursor: pointer;
  }
  &-head {
    padding: 0 24px;
    width: 100%;
    &-line1 {
      display: flex;
      justify-content: space-between;
      padding-top: 24px;
      margin-bottom: 16px;
      div {
        width: 80%;
        display: flex;
        justify-content: space-between;
      }
    }
    &-line2 {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      div {
        width: 80%;
        display: flex;
        justify-content: space-between;
      }
    }
    &-line3 {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      div {
        width: 80%;
        display: flex;
        justify-content: space-between;
      }
    }
    &-line4 {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      div {
        width: 80%;
        display: flex;
        justify-content: space-between;
        .tagbox {
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-start;
          .tag {
            margin-right: 2px;
            margin-bottom: 4px;
            height: 20px;
            font-size: 12px;
            line-height: 20px;
          }
        }
      }
    }
    &-line5 {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      .cert_tag {
        width: 80%;
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;

        ::v-deep {
          .el-input__inner {
            margin-right: 5px;
            width: 100px;
            height: 22px !important;
          }
        }
        div {
          margin-right: 2px;
          margin-bottom: 4px;
          display: flex;
          align-items: center;
        }
        .remark {
          margin-right: 2px;
          margin-bottom: 4px;
          height: 20px;
          font-size: 12px;
          line-height: 17px;
          ::v-deep {
            .el-icon-close {
              top: 0px !important;
            }
          }
        }
        .el-button {
          width: 65px;
          height: 20px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 10px;
        }
      }
    }
    &-line6 {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      div {
        width: 80%;
        display: flex;
        justify-content: space-between;
      }
    }
    &-line7 {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      div {
        width: 80%;
        display: flex;
        justify-content: space-between;
      }
    }
  }
  &-mid {
    padding: 0 24px;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    >div{
      flex:1;
    }
    img {
      width: 172px;
      height: 10px;
    }
    &-m{
    margin: 0 4px;
    height: 156px;
    }
    &-l {
      width: 180px;
      height: 156px;
      // background: #f8fbff;
      padding: 16px;
      padding-left: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background: url("../../../../../assets/images/sessiondetailbg.png")
        no-repeat 100%;

      .midbox {
        &-t {
          margin-bottom: 16px;
          padding-left: 16px;
          border-left: 3px solid #116ef9;
        }
        div:nth-child(1) {
          margin-bottom: 16px;
          padding-left: 19px;
        }
        div:nth-child(2) {
          padding-left: 19px;
        }
      }
    }
    &-r {
      width: 180px;
      height: 156px;
      background: #f8fbff;
      padding: 16px;
      padding-left: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background: url("../../../../../assets/images/sessiondetailbg.png")
        no-repeat 100%;
      // background-position: -145px -102px;
      .midbox {
        &-t {
          margin-bottom: 16px;
          padding-left: 16px;
          border-left: 3px solid #116ef9;
        }
        div:nth-child(1) {
          margin-bottom: 16px;
          padding-left: 19px;
        }
        div:nth-child(2) {
          padding-left: 19px;
        }
      }
    }
    &-arrowstop {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -150%);
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    &-arrowsdown {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, 60%);
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
  .indialog {
    ::v-deep .el-dialog {
      height: 460px;

      .el-dialog__headerbtn {
        top: 10px;
      }

      .el-dialog {
        box-shadow: 0px 6px 18px rgba(45, 47, 51, 0.14);
        border-radius: 8px;
      }
      .el-dialog__body {
        padding: 0;
      }
      .el-dialog__header {
        padding: 12px 24px;
      }
    }
  }
  .temp-info{
    width: 100%;
    height:auto;
    display: flex;
    justify-content: space-between;
    padding: 24px;
    box-sizing: border-box;
    >div{
      width: 49%;
      height:52px;
      background-color: #F7F8FA;
      padding:4px 8px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      >span:nth-of-type(1){
        color:#9999A1;
      }
      >span:nth-of-type(2){
        color:#2C2C35;
        font-weight: 600;
      }
    }
  }
  .tab-style {
    padding: 0 24px;
    margin-top: 20px;
    ::v-deep {
      .el-tabs__active-bar {
        top: 0 !important;
      }
      .el-tabs__nav-wrap::after {
        top: 0 !important;
      }
    }
  }
}
::v-deep{
  .el-tabs__nav-scroll{
      padding-left:0 !important;
    }
}
</style>
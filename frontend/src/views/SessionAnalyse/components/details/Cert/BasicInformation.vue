<template>
  <div class="formbox">
    <div class="formbox-top">
      <div class="title">
        <svg-icon icon-class="icon_certificate" style="font-size: 16px" />
        <span style="font-weight: bold">{{ basic_data.cert }}</span>
        <img src="../../../../../assets/images/unknown.png" />
      </div>
      <div class="location">
        <div>
          <span>父证书</span>
        </div>
        <span v-for="(item, index) in basic_data.fatherIdList" :key="index">{{
          item
        }}</span
        ><br />
      </div>
      <!-- <div class="task">
        <div class="task-l">
          <svg-icon icon-class="icon-task" />
          <span>任务</span>
        </div>
        <span>{{ basic_data.taskNames.join('') }}</span>
        <div class="task-r"></div>
      </div> -->
      <div class="tags">
        <div class="tags-l">
          <svg-icon icon-class="icon-label" />
          <span>标签</span>
        </div>
        <div class="tags-r">
          <div class="tags-r-tagbox" :class="showtag ? 'showmore' : ''">
            <span
              v-for="(item, index) in basic_data.certTagList"
              :key="index"
              :class="
                item.tag_level == 'danger'
                  ? 'redTag'
                  : item.tag_level == 'warning'
                  ? 'yellowTag'
                  : item.tag_level == 'info'
                  ? 'grayTag'
                  : 'blueTag'
              "
            >
              {{ item.tag_text }}
            </span>
            <el-button
              class="cert_tag_button"
              size="small"
              icon="el-icon-plus"
              @click="opentagbox"
            >
              标签
            </el-button>
          </div>
        </div>
        <div
          v-if="
            Array.isArray(basic_data.labels) && basic_data.labels.length > 7
          "
          class="tags-d"
          @click="opnetaglist"
        >
          <span>{{ showtag ? "展开" : "收起" }}</span>
          <svg-icon icon-class="menu-down" />
        </div>
      </div>
      <div class="remark">
        <div class="remark-l">
          <svg-icon icon-class="icon-remark" />
          <span>备注</span>
        </div>
        <div class="remarkbox">
          <div
            v-for="(item, index) in basic_data.remarks"
            :key="index"
            class="remarkbox-tag"
          >
            {{ item }}
            <svg-icon icon-class="icon_close" @click="deleteTag(item, index)" />
          </div>
          <div class="remarkbox-addtag">
            <el-input
              v-if="inputVisible"
              ref="saveTagInput"
              v-model="inputValue"
              resize="horizontal"
              @keyup.enter.native="$event.target.blur"
              @blur="handleInputConfirm"
            ></el-input>
            <el-button
              class="cert_tag_button"
              size="small"
              icon="el-icon-plus"
              @click="showInput"
            >
              备注
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="formbox-mid">
      <div class="formbox-mid-box">
        <span class="top">签发机构</span>
        <span class="value">{{ basic_data.issuerO }}</span>
      </div>
      <div class="formbox-mid-box">
        <span class="top">所有者</span>
        <span class="value">{{ basic_data.subjectO }}</span>
      </div>
      <div class="formbox-mid-box">
        <span class="top">签发时间</span>
        <span class="value">{{ basic_data.notBefore }}</span>
      </div>
      <div class="formbox-mid-box">
        <span class="top">有效时间</span>
        <span class="value">{{ basic_data.notAfter }}</span>
      </div>
      <div class="formbox-mid-box">
        <span class="top">服务器热度</span>
        <span class="value">{{ basic_data.serverHeat }}</span>
      </div>
      <div class="formbox-mid-box">
        <span class="top">客户端热度</span>
        <span class="value">{{ basic_data.clientHeat }}</span>
      </div>
      <div class="formbox-mid-box">
        <span class="top">首次出现时间</span>
        <span class="value">{{ $processingTime(basic_data.firstTime) }}</span>
      </div>
      <div class="formbox-mid-box">
        <span class="top">末次出现时间</span>
        <span class="value">{{ $processingTime(basic_data.lastTime) }}</span>
      </div>
    </div>
    <tag-view
      v-model="tagLibVisible"
      :tag_target_type="tag_target_type"
      :tag-labels="basic_data.certTagList"
      :is-show-aside="false"
      @modifyLabels="getTagValue"
    />
  </div>
</template>
  
<script>
import { SetRemark, SetTags } from "@/api/sessionList/sessionlist";
import TagView from "@/components/TagView";
export default {
  name: "BasicInformation",
  components: {
    TagView,
  },
  props: {
    tag_target_type: {
      type: Number,
      default: 4,
    },
    certInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      list: [
        { name: "签发机构", val: "" },
        { name: "所有者", val: "" },
        { name: "签发时间", val: "" },
        { name: "有效时间", val: "" },
        { name: "服务器热度", val: "" },
        { name: "客户端热度", val: "" },
        { name: "首次出现时间", val: "" },
        { name: "末次出现时间", val: "" },
      ],
      // 控制显示标签
      showtag: false,
      // 备注输入框的字段
      inputVisible: false,
      inputValue: "",
      // 标签弹窗字段
      opentag: false,
      tagval: "",
      direction: "rtl",
      tagLibVisible: false,
    };
  },
  computed: {
    basic_data() {
      return this.certInfo.basic_data;
    },
  },
  methods: {
    // 控制显示标签
    opnetaglist() {
      this.showtag = !this.showtag;
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    // 鼠标在输入框中失去焦点的回调
    handleInputConfirm() {
      if (this.inputValue == "") {
        this.inputVisible = false;
        return;
      }
      // 将新增的标签添加到备注列表中
      this.basic_data.remarks.push(this.inputValue);
      let param = {
        str: this.basic_data.cert,
        type: "CERT",
        remarks: this.basic_data.remarks,
      };
      SetRemark(param).then((res) => {
        if (res.err == 0) {
          this.$message.success("添加成功");
          this.inputValue == "";
        } else {
          this.$message.error("添加失败");
        }
      });
      this.inputVisible = false;
      this.inputValue = "";
    },
    // 删除备注中的标签
    deleteTag(item, index) {
      this.basic_data.remarks.splice(index, 1);
      let param = {
        str: this.basic_data.cert,
        type: "CERT",
        remarks: this.basic_data.remarks,
      };
      SetRemark(param).then((res) => {
        if (res.err == 0) {
          this.$message.success("删除成功");
        } else {
          this.$message.error("添加失败");
        }
      });
    },
    handleOpened() {
      // 获取到id返回新的数组
      let tag_text = this.basic_data.labels.map((item) => {
        return item.tag_text;
      });
      this.tagval = tag_text.join(",");
    },
    // 打开标签库
    opentagbox() {
      this.tagLibVisible = true;
      this.tagval = "";
    },
    cleartagdialog() {
      this.opentag = false;
    },
    // 获取到标签列表数组
    getTagValue(x, val, fn) {
      if (val.length != 0) {
        let newarr = [];
        val.forEach((item) => {
          newarr.push(item.tag_id + "");
        });
        this.basic_data.certTagList = val;
        let param = {
          str: this.basic_data.cert,
          type: "CERT",
          labels: newarr,
        };
        SetTags(param).then((res) => {
          if (res.err == 0) {
            this.$message.success("添加成功");
          } else {
            this.$message.error("添加失败");
          }
        });
      }
      fn(true);
    },
    // 关闭抽屉
    handleClose(done) {
      let that = this;
      that.$emit("certcloseDrawer");
    },
  },
};
</script>
  
  <style lang="scss" scoped>
.formbox {
  padding: 16px;
  &-top {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 24px;
    color: #2c2c35;
    div {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
    .title {
      margin-bottom: 12px;
      font-size: 16px;
      .svg-icon {
        margin-right: 10px;
        font-size: 22px;
      }
      span {
        margin-right: 10px;
      }
      img {
        width: 62px;
        height: 22px;
      }
    }
    .location {
      margin-bottom: 12px;
      div {
        color: #767684;
        width: 64px;
        .svg-icon {
          margin-right: 6px;
        }
      }
    }
    .task {
      margin-bottom: 12px;
      &-l {
        width: 64px;
        // margin-right: 12px;
        .svg-icon {
          margin-right: 6px;
        }
      }
    }
    .tags {
      margin-bottom: 12px;
      display: flex;
      align-items: flex-start;
      &-l {
        color: #767684;
        width: 64px;
        .svg-icon {
          margin-right: 6px;
        }
      }
      &-r {
        flex: 1;
        &-tagbox {
          display: flex;
          flex-wrap: wrap;
          align-items: flex-start;
        }
        .showmore {
          align-items: flex-start;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; /* 这里设置需要显示几行文本 */
        }
        .redTag {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #a41818;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          background: #fce7e7;
          line-height: 20px;
        }
        .blueTag {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #1b428d;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          background: #e7f0fe;
          border-radius: 2px;
        }
        .yellowTag {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #b76f1e;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          background: #f9eddf;
          border-radius: 2px;
        }
        .grayTag {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #2c2c35;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          background: #f2f3f7;
          border-radius: 2px;
        }
        .cert_tag_button {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #116ef9;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          line-height: 20px;
          background: #ffffff;
          border: 1px solid #116ef9;
        }
      }
      &-d {
        width: 46px;
        height: 22px;
        color: #116ef9;
        .svg-icon {
          font-size: 12px;
        }
        cursor: pointer;
      }
    }
    .remark {
      width: 100%;
      margin-bottom: 12px;
      display: flex;
      align-items: flex-start;
      &-l {
        color: #767684;
        width: 64px;
        // margin-right: 12px;
        .svg-icon {
          margin-right: 6px;
        }
      }
      .remarkbox {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        ::v-deep {
          .el-input__inner {
            margin-right: 5px;
            width: 100px;
            height: 22px !important;
          }
        }
        &-addtag {
          .cert_tag_button {
            color: #116ef9;
            border-radius: 2px;
            padding: 0 4px;
            background: #ffffff;
            line-height: 20px;
            border: 1px solid #116ef9;
          }
        }
        &-tag {
          padding: 0 4px;
          margin-bottom: 8px;
          background: #f2f3f7;
          border-radius: 2px;
          margin-right: 8px;
          .svg-icon {
            margin-left: 4px;
            cursor: pointer;
          }
        }
      }
    }
  }
  &-mid {
    padding-top: 12px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    &-box {
      padding-left: 11px;
      width: 50%;
      position: relative;
      display: flex;
      flex-direction: column;
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #2c2c35;
      margin-bottom: 16px;
      .top {
        color: #767684;
      }
      .value {
        font-weight: bold;
      }
    }
    &-box::before {
      position: absolute;
      top: 0;
      left: 0;
      content: "";
      height: 100%;
      border-left: 1px solid #dee0e7;
    }
  }
}
</style>
<template>
  <div>
    <el-drawer
      title="IP详情"
      :visible.sync="ipDetailDrawer"
      :direction="direction"
      :before-close="handleClose"
      destroy-on-close
      size="40%"
      @open="opneipDetail"
    >
      <div v-loading="loading" class="formbox">
        <div class="formbox-top">
          <div class="title">
            <svg-icon icon-class="icon-IPdetails" />
            <span style="font-weight: bold">{{ ipdata.ip }}</span>
            <img
              v-if="ipdata.blackList == '安全'"
              src="../../../../assets/images/ipsafety.png"
            />
            <img
              v-else-if="ipdata.blackList == '未知'"
              src="../../../../assets/images/unknown.png"
            />
            <img
              v-else-if="ipdata.blackList == '可疑'"
              src="../../../../assets/images/dubious.png"
            />
            <img v-else src="../../../../assets/images/highrisk.png" />
          </div>
          <div class="location">
            <div>
              <svg-icon icon-class="icon-location" />
              <span>位置</span>
            </div>
            <span>{{ ipdata.ipKey }}</span>
          </div>
          <div class="task">
            <div class="task-l">
              <svg-icon icon-class="icon-task" />
              <span>任务</span>
            </div>
            <span>
              {{ ipdata.taskname }}
            </span>
            <div class="task-r"></div>
          </div>
          <div class="tags">
            <div class="tags-l">
              <svg-icon icon-class="icon-label" />
              <span>标签</span>
            </div>
            <div class="tags-r">
              <div class="tags-r-tagbox" :class="showtag ? 'showmore' : ''">
                <span
                  v-for="(item, index) in ipdata.labels"
                  :key="index"
                  :class="
                    item.tag_level == 'danger'
                      ? 'redTag'
                      : item.tag_level == 'warning'
                        ? 'yellowTag'
                        : item.tag_level == 'info'
                          ? 'grayTag'
                          : 'blueTag'
                  "
                >
                  {{ item.tag_text }}
                </span>
                <!-- <span class="addtag">+ 标签</span> -->
                <el-button
                  class="cert_tag_button"
                  size="small"
                  icon="el-icon-plus"
                  @click="opentagbox"
                >
                  标签
                </el-button>
              </div>
            </div>
            <div
              v-if="Array.isArray(ipdata.labels) && ipdata.labels.length > 7"
              class="tags-d"
              @click="opnetaglist"
            >
              <span>展开</span>
              <svg-icon icon-class="menu-down" />
            </div>
          </div>
          <div class="remark">
            <div class="remark-l">
              <svg-icon icon-class="icon-remark" />
              <span>备注</span>
            </div>
            <div class="remarkbox">
              <div
                v-for="(item, index) in ipdata.remarks"
                :key="index"
                class="remarkbox-tag"
              >
                {{ item }}
                <svg-icon
                  icon-class="icon_close"
                  @click="deleteTag(item, index)"
                />
              </div>
              <div class="remarkbox-addtag">
                <el-input
                  v-if="inputVisible"
                  ref="saveTagInput"
                  v-model="inputValue"
                  resize="horizontal"
                  @keyup.enter.native="$event.target.blur"
                  @blur="handleInputConfirm"
                ></el-input>
                <el-button
                  class="cert_tag_button"
                  size="small"
                  icon="el-icon-plus"
                  @click="showInput"
                >
                  备注
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="formbox-mid">
          <div
            v-for="(item, index) in ipdata.list"
            :key="index"
            class="formbox-mid-box"
          >
            <span class="top">{{ item.name }}</span>
            <span>{{ item.value }}</span>
          </div>
        </div>
        <div v-if="$isTraffic" class="formbox-down">
          <graph v-if="ipDetailDrawer" :init-value="ip" :init-type="'IP'" />
        </div>
        <!-- <div class="indialog">
          <el-dialog v-dialogDrag :visible.sync="opentag" :modal="false" :close-on-click-modal="false" width="1000px"
                     @opened="handleOpened"
          >
            <tagsdeta ref="tagbox" :display="(display = 'block')" :value="tagval" @getTagValue="getTagValue"
                      @handleClose="display = 'none'" @cleartagdialog="cleartagdialog"
            ></tagsdeta>
          </el-dialog>
        </div> -->
      </div>
      <!-- 标签 -->
      <tag-view
        v-model="tagLibVisible"
        :tag_target_type="tag_target_type"
        :tag-labels="ipdata.labels"
        :is-show-aside="false"
        @modifyLabels="getTagValue"
      />
    </el-drawer>
  </div>
</template>

<script>
import { common } from "../listfn/Detailfn";
import graph from "../../../../components/graph/index.vue";
import { SetRemark, SetTags, GetIpInfo } from "@/api/sessionList/sessionlist";
import tagsdeta from "@/views/SessionAnalyse/components/tag/tags_deta";
import TagView from "@/components/TagView";
export default {
  components: {
    // tagsdeta,
    graph,
    TagView,
  },
  // 调用 mixin 将组件js与共用js合并 ---
  mixins: [common],
  // props: ["ipdata", "ipDetailDrawer"],
  props: ["ip", "ipDetailDrawer", "tag_target_type"],
  data() {
    return {
      loading: null,
      list: [
        { name: "开放服务数", val: "134" },
        { name: "访问服务数", val: "32" },
        { name: "所属锚域名数", val: "1412" },
        { name: "平均流量", val: "167.00 kbps" },
        { name: "发送负载量", val: "167.00 kbps" },
        { name: "接收负载量", val: "167.00 kbps" },
        { name: "首次出现时间", val: "2020-12-14  00:49:32 " },
        { name: "末次出现时间", val: "2020-12-14  00:49:32 " },
      ],
      taglist: [
        { name: "开放端口数", val: "134" },
        { name: "域名数", val: "1412" },
        { name: "域名数", val: "1412" },
        { name: "所属锚域名数111", val: "1412" },
        { name: "域名数", val: "1412" },
        { name: "所属锚域名数", val: "1412" },
        { name: "所属锚域名数", val: "1412" },
        { name: "所属锚域名数", val: "1412" },
        { name: "所属锚域名数", val: "1412" },
      ],
      // 控制显示标签
      showtag: false,
      // 备注输入框的字段
      inputVisible: false,
      inputValue: "",
      // 标签弹窗字段
      opentag: false,
      tagval: "",
      direction: "rtl",
      ipdata: {},
      tagLibVisible: false,
    };
  },
  methods: {
    // 控制显示标签
    opnetaglist() {
      this.showtag = !this.showtag;
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    // 鼠标在输入框中失去焦点的回调
    handleInputConfirm() {
      if (this.inputValue == "") {
        this.inputVisible = false;
        return;
      }
      // 将新增的标签添加到备注列表中
      this.ipdata.remarks.push(this.inputValue);
      console.log(this.ipdata.remarks);
      let param = {
        str: this.ipdata.ip,
        type: "IP",
        remarks: this.ipdata.remarks,
      };
      SetRemark(param).then((res) => {
        if (res.err == 0) {
          this.$message.success("添加成功");
        } else {
          this.$message.error("添加失败");
        }
      });
      this.inputVisible = false;
      this.inputValue = "";
    },
    // 删除备注中的标签
    deleteTag(item, index) {
      this.ipdata.remarks.splice(index, 1);
      let param = {
        str: this.ipdata.ip,
        type: "IP",
        remarks: this.ipdata.remarks,
      };
      SetRemark(param).then((res) => {
        if (res.err == 0) {
          this.$message.success("删除成功");
        } else {
          this.$message.error("添加失败");
        }
      });
    },
    handleOpened() {
      // 获取到id返回新的数组
      let tag_text = this.ipdata.labels.map((item) => {
        return item.tag_text;
      });
      console.log(tag_text);
      this.tagval = tag_text.join(",");
    },
    // 打开标签库
    opentagbox() {
      this.tagLibVisible = true;
      this.tagval = "";
    },
    cleartagdialog() {
      this.opentag = false;
    },
    // 获取到标签列表数组
    getTagValue(x,val,fn) {
      if (val.length != 0) {
        let newarr = [];
        val.forEach((item) => {
          newarr.push(item.tag_id + "");
        });
        this.ipdata.labels = val;
        let param = {
          str: this.ipdata.ip,
          type: "IP",
          labels: newarr,
        };
        SetTags(param).then((res) => {
          if (res.err == 0) {
            this.$message.success("添加成功");
          } else {
            this.$message.error("添加失败");
          }
        });
      }
      fn(true);
    },
    // 关闭ip抽屉
    handleClose(done) {
      let that = this;
      that.$emit("closeDrawer");
    },
    opneipDetail() {
      this.loading = true;
      GetIpInfo(`str=${this.ip}`).then((res) => {
        if (res.err == 0) {
          this.ipdata = this.datainfoDandle(res.data);
          console.log("IP详情数据：", this.ipdata);
          this.loading = false;
        } else {
          console.log("后台报错！！！");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.formbox {
  padding: 16px;
  &-top {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 24px;
    color: #2c2c35;

    div {
      display: flex;
      // // justify-content: flex-start;
      align-items: center;
    }
    .title {
      margin-bottom: 12px;
      font-size: 16px;
      .svg-icon {
        margin-right: 10px;
        font-size: 22px;
      }

      span {
        margin-right: 10px;
      }

      img {
        width: 62px;
        height: 22px;
      }
    }

    .location {
      margin-bottom: 12px;

      div {
        color: #767684;
        width: 64px;

        .svg-icon {
          margin-right: 6px;
        }
      }
    }

    .task {
      margin-bottom: 12px;

      &-l {
        color: #767684;
        width: 64px;

        // margin-right: 12px;
        .svg-icon {
          margin-right: 6px;
        }
      }

      // &-r {
      // }
    }

    .tags {
      margin-bottom: 12px;
      display: flex;
      align-items: flex-start;

      &-l {
        color: #767684;
        width: 64px;
        line-height: 20px;

        // margin-right: 12px;
        .svg-icon {
          margin-right: 6px;
        }
      }

      &-r {
        flex: 1;

        &-tagbox {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
        }

        .showmore {
          align-items: flex-start;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          /* 这里设置需要显示几行文本 */
        }

        .redTag {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #a41818;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          background: #fce7e7;
          line-height: 20px;
        }

        .blueTag {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #1b428d;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          background: #e7f0fe;
          border-radius: 2px;
        }

        .yellowTag {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #b76f1e;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          background: #f9eddf;
          border-radius: 2px;
        }

        .grayTag {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #2c2c35;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          background: #f2f3f7;
          border-radius: 2px;
        }

        .cert_tag_button {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #116ef9;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          line-height: 20px;
          background: #ffffff;
          border: 1px solid #116ef9;
        }

        // .addtag {
        //   cursor: pointer;
        //   margin-right: 8px;
        //   margin-bottom: 8px;
        //   color: #116ef9;
        //   border-radius: 2px;
        //   padding: 0 4px;
        //   // height: 20px;
        //   background: #ffffff;
        //   line-height: 20px;
        //   border: 1px solid #116ef9;
        // }
      }

      &-d {
        width: 46px;
        height: 22px;
        color: #116ef9;

        .svg-icon {
          font-size: 12px;
        }

        cursor: pointer;
      }
    }

    .remark {
      width: 100%;
      margin-bottom: 12px;
      display: flex;
      align-items: flex-start;

      &-l {
        color: #767684;
        width: 64px;

        // margin-right: 12px;
        .svg-icon {
          margin-right: 6px;
        }
      }

      .remarkbox {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;

        ::v-deep {
          .el-input__inner {
            margin-right: 5px;
            width: 100px;
            height: 22px !important;
          }
        }

        &-addtag {
          .cert_tag_button {
            color: #116ef9;
            border-radius: 2px;
            padding: 0 4px;
            background: #ffffff;
            line-height: 20px;
            border: 1px solid #116ef9;
          }
        }

        &-tag {
          padding: 0 4px;
          margin-bottom: 8px;
          background: #f2f3f7;
          border-radius: 2px;
          margin-right: 8px;

          .svg-icon {
            margin-left: 4px;
            cursor: pointer;
          }
        }
      }

      // .addtag {
      //   cursor: pointer;
      //   margin-right: 8px;
      //   margin-bottom: 8px;
      //   color: #116ef9;
      //   border-radius: 2px;
      //   padding: 0 4px;
      //   background: #ffffff;
      //   line-height: 20px;
      //   border: 1px solid #116ef9;
      // }
    }
  }

  &-mid {
    padding-top: 12px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    &-box {
      padding-left: 11px;
      min-width: 200px;
      position: relative;
      display: flex;
      flex-direction: column;
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #2c2c35;
      margin-bottom: 12px;

      .top {
        color: #767684;
      }
    }

    &-box::before {
      position: absolute;
      top: 10px;
      left: 0;
      content: "";
      height: 30px;
      border-left: 1px solid #dee0e7;
    }
  }

  &-down {
    width: 100%;
    height: 462px;
    background: #f7f8fa;
    border-radius: 8px;
  }

  .indialog {
    ::v-deep .el-dialog {
      height: 460px;

      .el-dialog__headerbtn {
        top: 10px;
      }

      .el-dialog {
        box-shadow: 0px 6px 18px rgba(45, 47, 51, 0.14);
        border-radius: 8px;
      }

      .el-dialog__body {
        padding: 0;
      }

      .el-dialog__header {
        padding: 12px 24px;
      }
    }
  }
}
.formbox-down {
  height: 500px;
}
</style>
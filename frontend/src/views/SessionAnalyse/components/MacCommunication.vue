<template>
  <div class="macbox">
    <div class="macbox-l">
      <el-table
        ref="table"
        :data="macTableData"
        tooltip-effect="dark"
        :default-sort="{ prop: 'begin_time', order: 'ascending' }"
        show-overflow-tooltip
        style="width: 100%"
        border
      >
        <el-table-column
          prop="num"
          label="序号"
          type="index"
          width="50"
          align="center"
        />
        <el-table-column
          prop="sMac"
          label="源MAC"
          sortable="custom"
          width="150"
        />
        <el-table-column
          prop="dMac"
          label="目的MAC"
          sortable="custom"
          width="150"
        />
        <el-table-column
          prop="total_packet_num"
          label="包数"
          width="130"
          sortable="custom"
        />
      </el-table>
    </div>
    <div class="macbox-r">
      <article class="box3">
        <div class="title">MAC通信</div>
        <section class="mac">
          <div class="mac-box">
            <v-chart
              :option="mac_options"
              autoresize
              :update-options="{ notMerge: true }"
            >
            </v-chart>
          </div>
          <!-- <aside class="mac-bg">
            <aside class="bg2">
              <aside class="op2"></aside>
              <aside class="bg3">
                <aside class="op3"></aside>
              </aside>
            </aside>
          </aside> -->
          <!-- <aside class="line" id="line1">
            <aside class="line" id="line2">
              <aside class="line" id="line3"></aside>
            </aside>
          </aside> -->
        </section>
      </article>
    </div>
  </div>
</template>

<script>
import { mac_options } from "./echartsData";
import { getMaclist } from "@/api/sessionList/sessionlist";

export default {
  name: "Mac",
  props: ["searchData", "fatherValue"],
  data() {
    return {
      // mac图配置
      mac_options,
      macTableData: [
        {
          data1: "07:16:76:00:02:86",
          data2: "00:43:56:f4:02:e3",
          data3: "17",
        },
        {
          data1: "07:16:76:00:02:86",
          data2: "00:43:56:f4:02:e3",
          data3: "17",
        },
        {
          data1: "07:16:76:00:02:86",
          data2: "00:43:56:f4:02:e3",
          data3: "17",
        },
        {
          data1: "07:16:76:00:02:86",
          data2: "00:43:56:f4:02:e3",
          data3: "17",
        },
        {
          data1: "07:16:76:00:02:86",
          data2: "00:43:56:f4:02:e3",
          data3: "17",
        },
        {
          data1: "07:16:76:00:02:86",
          data2: "00:43:56:f4:02:e3",
          data3: "17",
        },
      ],
    };
  },
  watch: {
    searchData:{
      handler(){
        this.initData();
      },
      deep:true
    }
  },
  methods: {
    initData() {
      let param = this.searchData;
      param.aggr_query = false;
      let arr = {
        and: [],
        not: [],
      };
      for (let i = 0; i < param.query.length; i++) {
        for (let j = 0; j < param.query[i].search.length; j++) {
          if (param.query[i].bool_search === "and") {
            if (param.query[i].search[j].target === "Labels") {
              arr.and.push(param.query[i].search[j].val);
            }
          }
          if (param.query[i].bool_search === "not") {
            if (param.query[i].search[j].target === "Labels") {
              arr.not.push(param.query[i].search[j].val);
            }
          }
        }
      }
      param.tag_query = arr;
      getMaclist(param).then((res) => {
        this.macTableData = res.data.communication;
        let value = res.data;
        let data = [];
        let links = [];
        if (value.mac) {
          data = value.mac;
          links = value.communication;
        } else {
          data = [];
          links = [];
        }

        let newData = [];
        for (let i of data) {
          let obj = {
            id: i.mac,
            name: i.mac,
            // symbolSize: i.recv / 1024 / 1024,
            symbolSize: 20,
            // 厂商名称
            organization_name: "--",
            // mac_inter: i.mac_inter === 'intel_ip' ? '互联网' : '内网',
            // ip: i.ip ? i.ip : '-',
            // mask: i.ip? i.ip[0].mask : '-',
            value: i.mac,
          };
          newData.push(obj);
        }
        let newLinks = [];
        for (let i of links) {
          let obj = {
            source: i.dMac,
            target: i.sMac,
          };
          newLinks.push(obj);
        }
        mac_options.series[0].data = newData;
        mac_options.series[0].links = newLinks;
        if (newData.length < 1 || newLinks.length < 1) {
          this.macShow = false;
        } else {
          this.macShow = true;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.macbox {
  display: flex;
  .title {
    margin-left: 32px;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #2c2c35;
  }
  &-l {
    width: 452px;
  }
  &-r {
    min-height: 604px;
    flex: 1;
    .box3 {
      width: 100%;
      height: 100%;
      .mac {
        width: 100%;
        min-height: 800px;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: rgb(254, 254, 255);
        .mac-box {
          width: 100%;
          height: 100%;
          z-index: 701;
          position: absolute;
          padding: 28.5px 0;
          box-sizing: border-box;
        }
        .mac-bg {
          width: 250px;
          height: 250px;
          position: absolute;
          z-index: 697;
          border: 1px solid rgb(245, 244, 244);
          border-radius: 50%;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background-color: #fff;
          box-shadow: 0px 46.1847px 36.9478px rgba(108, 73, 172, 0.1);
          box-sizing: border-box;
          .bg2 {
            width: 145px;
            height: 145px;
            position: absolute;
            z-index: 698;
            border: 1px solid rgb(245, 244, 244);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            box-shadow: 0px 46.1847px 36.9478px rgba(108, 73, 172, 0.12);
            opacity: 1;
            .op2 {
              width: 100%;
              height: 50%;
              position: absolute;
              top: -10px;
              background: linear-gradient(
                white,
                rgba(255, 255, 255, 0.9),
                rgba(255, 255, 255, 0.41)
              );
            }
            .bg3 {
              width: 93px;
              height: 93px;
              position: absolute;
              z-index: 699;
              border: 1px solid rgb(245, 244, 244);
              border-radius: 50%;
              top: 50%;
              left: 50%;
              box-sizing: border-box;
              transform: translate(-50%, -50%);
              background-color: #fff;
              box-shadow: 0px 46.1847px 36.9478px rgba(108, 73, 172, 0.15);
              background: linear-gradient(
                rgba(255, 255, 255, 0),
                rgba(255, 255, 255, 1)
              );
              .op3 {
                width: 100%;
                height: 50%;
                position: absolute;
                top: -10px;
                background: linear-gradient(
                  white,
                  rgba(255, 255, 255, 0.9),
                  rgba(255, 255, 255, 0.41)
                );
              }
            }
          }
        }
        .line {
          width: 250px;
          height: 1px;
          background-color: #f1f1f1;
          opacity: 0.8;
          transform-origin: bottom center;
          top: 50%;
          z-index: 700;
        }
        #line1 {
          transform: rotateZ(0deg) scale(1);
        }
        #line2 {
          transform: rotateZ(60deg) scale(1);
        }
        #line3 {
          transform: rotateZ(60deg) scale(1);
        }
        .largen {
          width: 30px;
          height: 30px;
          position: absolute;
          right: 11px;
          bottom: 13px;
          background-color: #fff;
          box-shadow: 0px 8.4362px 22.349px rgba(108, 73, 172, 0.3);
          border-radius: 5.2349px;
          font-size: 14px;
          color: #5a5a89;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          z-index: 702;
        }
      }
    }
  }
  // &-sankey {
  //   flex: 1;
  //   height: 350px;
  //   &-ip {
  //     height: 100%;
  //   }
  // }
}
</style>
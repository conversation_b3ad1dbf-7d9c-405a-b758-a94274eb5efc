<template>
  <div class="list">
    <div class="list-top">
      <div class="list-top-l">
        已选择<span>{{ activenum }}</span>条
      </div>
      <div class="list-top-r">
        <el-popover
          placement="bottom"
          trigger="click"
          :visible-arrow="false"
          popper-class="alldownbox"
        >
          <div slot="reference" class="qlactive">
            <div class="alldown-l">批量检索</div>
            <div class="alldown-r">
              <svg-icon icon-class="del-down2" />
            </div>
          </div>
          <div class="alldownfoot">
            <el-button
              class="alldownfoot-t"
              @click="searchforwardSearch(search_list, 'IP')"
            >
              正向检索
            </el-button>
            <el-button
              class="alldownfoot-d"
              @click="searchreverseSearch(search_list, 'IP')"
            >
              反向检索
            </el-button>
          </div>
        </el-popover>

        <div class="globaldown">
          <el-tooltip
            content="到“下载列表”的“日志导出”中下载"
            placement="top"
            effect="dark"
          >
            <el-button @click="logderive">
              <i class="el-icon--left">
                <svg-icon icon-class="globaldown" />
              </i>
              日志导出
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </div>
    <div class="list-listview">
      <el-table
        ref="table"
        :data="connTableData"
        tooltip-effect="dark"
        stripe
        :default-sort="{ prop: 'begin_time', order: 'ascending' }"
        show-overflow-tooltip
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
          fixed
          style="border-right: none !important"
        >
        </el-table-column>
        <el-table-column
          prop="num"
          label="序号"
          type="index"
          width="50"
          :index="indexMethod"
          fixed
        />
        <el-table-column
          label="IP"
          prop="IP"
          min-width="130"
          fixed
          sortable="custom"
        >
          <!-- <template slot-scope="scope">
            <span @click="openipinfo(scope.row.ip)" style="cursor: pointer">
              {{ scope.row.ip }}
            </span>
          </template> -->
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <el-tooltip
                  :content="scope.row.ip ? scope.row.ip : '--'"
                  placement="top"
                  effect="light"
                  popper-class="sessionidTooltip"
                >
                  <div
                    style="cursor: pointer; color: #116ef9"
                    @click="openipinfo(scope.row.ip,0)"
                  >
                    {{ scope.row.ip }}
                  </div>
                </el-tooltip>
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'IP')">
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'IP')">
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="labels" width="300" label="标签">
          <template slot-scope="scope">
            <div class="taglist">
              <span
                v-for="(item, index) in scope.row.labels"
                :key="item.tag_id"
                class="tagbox"
              >
                <el-popover
                  placement="bottom"
                  width="200"
                  trigger="click"
                  popper-class="sortpopover"
                >
                  <el-tag slot="reference" class="tag" :type="item.type">
                    <div style="cursor: pointer">{{ item.tag_text }}</div>
                  </el-tag>
                  <div class="sortbtn">
                    <el-button @click="forwardSearch(item, 'Labels')">
                      <svg-icon
                        icon-class="sort-up"
                        style="margin-right: 15px"
                      />正向检索
                    </el-button>
                    <el-button @click="reverseSearch(item, 'Labels')">
                      <svg-icon
                        icon-class="sort-down"
                        style="margin-right: 15px"
                      />反向检索
                    </el-button>
                  </div>
                </el-popover>
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column width="80" prop="blackList" label="威胁权重" />
        <el-table-column min-width="125" prop="country_city" label="地理位置" />
        <el-table-column
          width="110"
          prop="openServiceNum"
          label="全局开放服务"
        />
        <el-table-column
          width="110"
          prop="accessServiceNum"
          label="全局访问服务"
        />
        <el-table-column width="150" prop="domainNum" label="全局关联域名">
          <template slot="header" slot-scope="scope">
            全局关联域名
            <el-tooltip
              popper-class="atooltip"
              effect="dark"
              content="所有任务指向过该IP的域名个数"
              placement="top"
            >
              <i class="el-icon-warning-outline" style="margin-left: 6px"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column width="150" prop="certNum" label="全局关联证书">
          <template slot="header" slot-scope="scope">
            全局关联证书
            <el-tooltip
              popper-class="atooltip"
              effect="dark"
              content="被该IP使用过的证书个数"
              placement="top"
            >
              <i class="el-icon-warning-outline" style="margin-left: 6px"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column width="120" prop="alarmCount" label="关联告警">
          <template slot="header" slot-scope="scope">
            关联告警
            <el-tooltip
              popper-class="atooltip"
              effect="dark"
              content="告警对象是该ip的告警个数"
              placement="top"
            >
              <i class="el-icon-warning-outline" style="margin-left: 6px"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          width="120"
          prop="sessionCount"
          label="当前会话数量"
        />
        <el-table-column
          width="170"
          prop="startTimeMin"
          label="首次发现时间"
        />
        <el-table-column
          width="170"
          prop="startTimeMax"
          label="末次发现时间"
        />
        <!-- <el-table-column min-width="200" prop="sPayloadBytes" label="当前发送字节数" />
        <el-table-column min-width="200" prop="dPayloadBytes" label="当前接收字节数" /> -->
      </el-table>
    </div>
    <div class="list-foot">
      <div class="list-foot-top">
        <tablescroll :table-ref="$refs.table"></tablescroll>
      </div>
      <div class="list-foot-down">
        <div class="list-foot-down-l">*会话展示上限为<span>10,000</span>条</div>

        <div class="list-foot-down-r">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="page_size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <!-- <el-drawer title="IP详情" :visible.sync="drawer" :direction="direction" :before-close="handleClose" destroy-on-close>
      <ipdetails :ipdata="ipdata" />
    </el-drawer> -->
    <!-- ip详情抽屉 -->
    <!-- <div class="Drawerbox">
      <ipdetails :ipdata="ipdata" :ipDetailDrawer="ipDetailDrawer" @closeDrawer="closeDrawer" />
    </div> -->
    <div class="Drawerbox">
      <ipdetails
        :ip="detailIp"
        :tag_target_type="tag_target_type"
        :ip-detail-drawer="ipDetailDrawer"
        @closeDrawer="closeDrawer"
      />
    </div>
  </div>
</template>

<script>
import ipdetails from "../components/details/IPdetails.vue";
import tablescroll from "../../../components/TableScroll/idnex.vue";
import {
  GetIplist,
  GetIpInfo,
  alllogderive,
} from "@/api/sessionList/sessionlist";
import { common } from "./listfn/Detailfn";
// import { push } from "mock/user";
export default {
  name: "IPList",
  components: {
    ipdetails,
    tablescroll,
  },
  // 调用 mixin 将组件js与共用js合并 ---
  mixins: [common],
  props: ["searchData", "fatherValue"],
  data() {
    return {
      // 分页参数
      currentPage: 1,
      page_size: 10,
      total: 0,
      // ========
      drawer: false,
      // 处理过后的请求IP列表参数
      sessionParam: {},
      connTableData: [],
      ipdata: [],
      // 多选数组
      search_list: {},
      activenum: 0, //选中参数
      // ip详情抽屉
      ipDetailDrawer: false,
      // ipdata: [],
      ip: "",
      // 控制抽屉出现位置
      direction: "rtl",
      detailIp: "",
      tag_target_type:9999
    };
  },
  watch: {
    searchData: {
      handler(val) {
        this.currentPage = 1;
        // this.filterData = {}
        this.order = {
          order_prop: "begin_time",
          order_field: "EndTime",
          asc: true,
        };
        this.limit100 = [];
        this.pageRange = [1, 10];
        this.initData();
      },
      deep: true,
      immediate:true
    },
  },
  methods: {
    closeDrawer() {
      this.ipDetailDrawer = false;
    },
    handleClose(done) {
      done();
    },
    // 对传给后端的数据做处理
    formatParam() {
      let param = {
        current_page: this.currentPage,
        page_size: this.page_size,
        order_field: this.order.order_field,
        asc: this.order.asc,
        query: [],
      };
      if (this.searchData.query && this.searchData.query.length > 0) {
        param.query = param.query.concat(this.searchData.query);
      }
      this.searchData.task_id
        ? (param.task_id = this.searchData.task_id)
        : null;
      this.searchData.time_range
        ? (param.time_range = this.searchData.time_range)
        : null;
      // Object.assign(param, this.searchData,)
      if ("flow/fuzzy".includes(this.searchData.type)) {
        delete param.target_filter;
      }
      param.aggr_query = false;
      for (let i of param.query) {
        if (i && i.search) {
          for (let j of i.search) {
            if (j.target === "Labels") {
              param.aggr_query = true;
            }
          }
        }
      }
      let arr = {
        and: [],
        not: [],
      };
      for (let i = 0; i < param.query.length; i++) {
        if (param.query[i] && param.query[i].search) {
          for (let j = 0; j < param.query[i].search.length; j++) {
            if (param.query[i].bool_search === "and") {
              if (param.query[i].search[j].target === "Labels") {
                arr.and.push(param.query[i].search[j].val);
              }
            }
            if (param.query[i].bool_search === "not") {
              if (param.query[i].search[j].target === "Labels") {
                arr.not.push(param.query[i].search[j].val);
              }
            }
          }
        }
      }
      param.tag_query = arr;
      this.sessionParam = param;
    },
    initData() {
      this.formatParam();
      GetIplist(this.sessionParam).then((res) => {
        if (res.err === 0) {
          this.total_real = res.data.total;
          this.total = res.data.total > 10000 ? 10000 : res.data.total;
          this.$emit(
            "update:fatherValue",
            res.data.records.length > 0 ? res.data.total : 0
          );
          this.connTableData = this.listfn(res.data.records);
        }
      });
    },
    // 对列表进行数据处理
    listfn(val) {
      let tagarr = this.$store.state.conversational.taglist01;
      val.forEach((item) => {
        let newtagarr = [];
        item.startTimeMin = this.formatDate(item.startTimeMin);
        item.startTimeMax = this.formatDate(item.startTimeMax);
        if (!item.city && !item.country) {
          item.country_city = "未知";
        } else if (!item.city) {
          item.country_city = item.country;
        } else {
          // 国家城市拼接
          item.country_city = item.country + "-" + item.city;
        }
        if (item.labels.length != 0) {
          item.labels.forEach((labelitem) => {
            tagarr.forEach((tagitem) => {
              if (labelitem == tagitem.tag_id) {
                newtagarr.push(tagitem);
              }
            });
          });
        }

        item.labels = this.formatTag(newtagarr);
      });

      return val;
    },
    // 获取到ip详情
    openipinfo(srcip,type) {
      this.detailIp = srcip;
      this.ipLoading = true;
      this.ipDetailDrawer = true;
      this.tag_target_type=type;
    },

    handleSizeChange(val) {
      this.pageRange = [1, 10];
      this.page_size = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.initData();
    },
    indexMethod(index) {
      return (this.currentPage - 1) * this.page_size + index + 1;
    },

    // 正向快速检索
    forwardSearch(data, sign) {
      data.sort = false;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 反向快速检索
    reverseSearch(data, sign) {
      data.sort = true;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 批量检索方法正向
    searchforwardSearch(data, sign) {
      if (this.activenum == 0) {
        this.$message({
          message: "请至少选择一条数据检索",
          type: "warning",
        });
        return;
      }
      data.sort = false;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 批量检索方法反向
    searchreverseSearch(data, sign) {
      if (this.activenum == 0) {
        this.$message({
          message: "请至少选择一条数据检索",
          type: "warning",
        });
        return;
      }
      data.sort = true;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 多选方法
    handleSelectionChange(val) {
      this.activenum = val.length;
      let newarr = [];
      for (let i in val) {
        newarr.push(val[i].ip);
      }
      let item = {};
      // 数组转为字符串
      let str = newarr.join(",");
      item.str = str;
      this.search_list = item;
    },
    // 排序判断
    handleSortChange(val) {
      this.order.order_prop = val.prop;
      this.order.order_field = val.prop;
      if (val.prop == "IP") {
        this.order.order_field = "IP";
      }
      if (val.prop == "begin_time") {
        this.order.order_field = "StartTime";
      }
      if (val.prop == "dst_port") {
        this.order.order_field = "dPort";
      }
      if (val.prop == "src_port") {
        this.order.order_field = "sPort";
      }
      if (val.prop == "src_ip") {
        this.order.order_field = "sIp";
      }
      if (val.prop == "ippro") {
        this.order.order_field = "IPPro";
      }
      if (val.prop == "dst_ip") {
        this.order.order_field = "dIp";
      }
      if (val.prop == "end_time") {
        this.order.order_field = "EndTime";
      }
      if (val.prop == "sBytes") {
        this.order.order_field = "pkt.sPayloadBytes";
      }
      if (val.prop == "dBytes") {
        this.order.order_field = "pkt.dPayloadBytes";
      }
      if (val.order == "ascending") {
        this.order.asc = true;
      } else if (val.order == "descending") {
        this.order.asc = false;
      } else {
        this.order.order_field = "";
      }
      this.limit100 = [];
      this.pageRange = [1, 1];
      this.initData();
    },
    //日志导出
    logderive() {
      if(!this.connTableData.length){
        this.$message.error('暂无数据可导出');
        return;
      }
      let param = {
        condition: {
          current_page: this.currentPage,
          page_size: this.page_size,
          order_field: this.order.order_field,
          asc: this.order.asc,
          query: [],
          aggr_query: true,
        },
        user_id: 1,
        task_type: 6,
      };
      if (JSON.stringify(this.filterData) != "{}") {
        let arr = [];
        for (let i in this.filterData) {
          arr.push(this.filterData[i]);
        }
        param.condition.query = param.condition.query.concat(arr);
      }
      if (this.searchData.query.length > 0) {
        param.condition.query = param.condition.query.concat(
          this.searchData.query
        );
      }
      this.searchData.task_id
        ? (param.condition.task_id = this.searchData.task_id)
        : null;
      this.searchData.time_range
        ? (param.condition.time_range = this.searchData.time_range)
        : null;
      param.condition.aggr_query = false;
      for (let i of param.condition.query) {
        for (let j of i.search) {
          if (j.target === "Labels") {
            param.condition.aggr_query = true;
          }
        }
      }
      let arr = {
        and: [],
        not: []
      };
      for (let i = 0; i < param.condition.query.length; i++) {
        for (let j = 0; j < param.condition.query[i].search.length; j++) {
          if (param.condition.query[i].bool_search === 'and') {
            if (param.condition.query[i].search[j].target === 'Labels') {
              arr.and.push(param.condition.query[i].search[j].val);
            }
          }
          if (param.condition.query[i].bool_search === 'not') {
            if (param.condition.query[i].search[j].target === 'Labels') {
              arr.not.push(param.condition.query[i].search[j].val);
            }
          }
        }
      }
      param.condition.tag_query = arr;
      alllogderive(param).then((res) => {
        if (res.err == 0) {
          this.$store.commit("conversational/setdowndot", true);
          this.$message("成功加入日志导出队列");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-drawer {
    overflow: auto;
  }

  .el-drawer__header {
    margin: 0;
    padding: 16px;
    border-bottom: 1px solid #f2f3f7;

    span {
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      display: flex;
      align-items: center;
      color: #2c2c35;
    }
  }
}

.list {
  &-top {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    &-l {
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #9999a1;
      margin-right: 8px;

      span {
        color: #116ef9;
      }

      ::v-deep {
        .el-button {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 94px;
          height: 32px;
        }

        .el-checkbox-group {
          display: flex;
          flex-direction: column;
        }
      }
    }

    &-r {
      display: flex;
      align-items: center;

      ::v-deep {
        .el-button {
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .Partdown {
        .el-button {
          width: 88px;
        }

        margin-right: 8px;
      }

      .alldown {
        cursor: not-allowed;
        position: relative;
        width: 119px;
        height: 32px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        display: flex;
        align-items: center;
        color: #c0c4cc;
        margin-right: 16px;

        &-l {
          font-weight: 400;
          font-size: 14px;
          width: 88px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #dcdfe6;
        }

        &-r {
          width: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
        }
      }

      .qlactive {
        cursor: pointer;
        position: relative;
        width: 119px;
        height: 32px;
        border: 1px solid #8abcff;
        border-radius: 4px;
        display: flex;
        align-items: center;
        color: #4a97ff;
        margin-right: 8px;

        &-l {
          font-weight: 400;
          font-size: 14px;
          width: 88px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #8abcff;
        }

        &-r {
          width: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
        }
      }

      .qlactive:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }

      .globaldown {
        .el-button {
          width: 108px;
        }
      }
    }
  }

  &-listview {
    margin-top: 12px;

    .sortbox {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      // position: relative;
      .top {
        margin-right: 5px;
      }

      .down {
        width: 10px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .sorttoole {
        display: none;
        padding-bottom: 5px;
        color: #116ef9;
        // position: absolute;
        // top: -2px;
        // right: 0;
      }
    }

    .sortbox:hover {
      .sorttoole {
        display: block;
        cursor: pointer;
      }
    }

    ::v-deep {
      .el-tag {
        background: #e7f0fe;
        border-radius: 2px;
        color: #1b428d;
        margin-bottom: 4px;
      }

      .el-tag.el-tag--warning {
        background: #f9eddf;
        border-radius: 2px;
        color: #b76f1e;
      }

      .el-tag.el-tag--success {
        background: #e0f5ee;
        border-radius: 2px;
        color: #006157;
      }

      .el-tag.el-tag--danger {
        background: #fce7e7;
        border-radius: 2px;
        color: #a41818;
      }

      .el-tag.el-tag--info {
        background: #f2f3f7;
        border-radius: 2px;
        color: #2c2c35;
      }
    }

    .taglist {
      .tagbox {
        margin-right: 4px;
      }
    }

    // .tooltipitem {}
  }

  &-foot {
    position: sticky;
    bottom: 0px;
    z-index: 999;
    padding: 10px 24px;
    width: 100%;
    background: #ffffff;
    border-radius: 8px;

    &-top {
      margin-bottom: 10px;
    }

    &-down {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-bottom: 20;

      &-l {
        font-size: 12px;
        color: #9999a1;

        span {
          color: #000;
        }
      }
    }
  }
}
</style>
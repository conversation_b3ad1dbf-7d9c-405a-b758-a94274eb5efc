import Vue from 'vue';
import * as echarts from 'echarts';
Vue.prototype.$echarts = echarts;
export let taskline_options = {
  tooltip: {
    trigger: 'axis'
  },
  brush: {
    toolbox: ['rect', 'clear'],
    xAxisIndex: 0,
  },
  toolbox: {

    itemGap: 18,//icon 每项之间的间隔
    itemSize: 30,
    top: -6,
    right: 0,
    // 各工具配置项
    feature: {
      brush: {
        color: "#00D8D8",
        type: ['rect', 'clear'],
        icon: {
          clear:'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABoSURBVHgB7dYxDoAwDATBg0enoqTKp6GlQ07knKzsvMArXWEJAIDCDiVr7X40qPfr975TxZUPSJ9Q1HdyTKgCAtwIcCPAjQC3PX6h6H+yEhNyI8CNADcC3Ahw2+8Xisr+nZgQAACY8AIKshA5IUdlzAAAAABJRU5ErkJggg==',
          rect: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAGMSURBVHgB7ZhPboJAFMbfGGXtETyCvYEewZPUjbKopGPaLpCY0JN4hHoDPYJHcI2S1zdtidi0cQaYGUzeb8Pk8e998PHBAMAwDOMTobPRfP6KxXi1Wmjt44oO3DkswDcswDdXiVJOmzagk3hsId94f5HRsZ9pIf9ap3OuLnhiOpX9Xq+7oeHoUsUDXdMBGODFQrPZ2zAIejshLs0jwjbL8gcwxLkAav5RCPyglgdFDRGXSbIYp6k8giHOLKQsEwRd8jtOS+VjnuNkvY62UBEnAsJQDhC//D4slfdCnKl5eYAaWLdQGL6MqPkdlJonC71n2Xkcx/WaV1i9Ayoi6eGUpZLy+DKOoxQawoqA/yISsTNJkqc9NEjjAlREUjxurlMGtqdTPknTyDhlbtGogJ+IlDTsF7XviIwkWKIRAbYiUofaAmxGpA61YtR2ROpQ+Q64iEgdjAW4jEgdjAWor0hXEalDBQv9/oq0F5E6GM/I2kB5psaTet+06k+zwvQHAlvINyzAN3cvgGEYxi+fM8C4GIDCATYAAAAASUVORK5CYII='
        }
      },

    },
    iconStyle: {
      borderWidth: 1,

    }
  },

  legend: {
    show: false
    // data: [],
  },
  // brush:{
  //     toolbox:['rect','polygon','lineX','lineY','keep','clear'],
  //     xAxisIndex:0
  // },
  grid: {
    left: '1px',
    right: '1px',
    bottom: '1%',
    top: '70px',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['2022-05-10 12:00:00', '2022-05-11 12:00:00', '2022-05-12 12:00:00', '2022-05-13 12:00:00', '2022-05-14 12:00:00', '2022-05-15 12:00:00', '2022-05-16 12:00:00']
  },
  yAxis: {
    type: 'value',
  },
  series: [
    {
      name: ' ',
      type: 'line',
      step: 'start',
      symbol: 'circle',
      data: [50, 132, 101, 134, 90, 230, 500],
      // 设置折线上圆点大小
      symbolSize: 8,
      //自定义线条颜色，
      lineStyle: {
        normal: {
          color: '#116EF9'
        }
      },
      itemStyle: {
        normal: {
          color: '#3C8FFF', //拐点颜色        
          borderColor: '#FFFFFF',
          borderWidth: 2,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
          shadowBlur: 10,
          shadowOffsetY: 3
        }
      },
      //设置折线图线下面颜色渐变
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{//渐变颜色
            offset: 0,
            color: 'rgba(17, 110, 249, 0.1)'
          },
          {
            offset: 1,
            color: 'rgba(17, 110, 249, 0)'
          }],
          global: false,
        }
      },
    },

  ]
};

export let ip_options = {

  backgroundColor: '#FFFFFF',
  series: [{
    type: 'sankey',
    layout: 'none',
    layoutIterations: 0,
    label: {
      show: true
    },
    emphasis: {
      focus: 'adjacency'
    },
    nodeAlign: 'justify',
    // draggable:false,
    levels: [{
      depth: 0,
      itemStyle: {
        color: '#00D8D8',
        shadowColor: 'rgba(108, 73, 172, 0.1)',
        shadowBlur: 15,
        shadowOffsetY: 10
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4,
      }
    }, {
      depth: 1,
      itemStyle: {
        color: '#4A97FF'
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4,
      }
    },
    {
      depth: 2,
      itemStyle: {
        // color: {
        //   type: 'linear',
        //   x: 0,
        //   y: 1,
        //   x2: 1,
        //   y2: 0,
        //   colorStops: [{
        //     offset: 0,
        //     color: '#00D8D8' // 0% 处的颜色
        //   }, {
        //     offset: 1,
        //     color: '#4A97FF' // 100% 处的颜色
        //   }],
        //   global: false // 缺省为 false
        // }
        color: '#114EEB'
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4
      }
    },
    {
      depth: 3,
      itemStyle: {
        // color: {
        //   type: 'linear',
        //   x: 0,
        //   y: 1,
        //   x2: 1,
        //   y2: 0,
        //   colorStops: [{
        //     offset: 0,
        //     color: '#E69EFF' // 0% 处的颜色
        //   }, {
        //     offset: 1,
        //     color: '#D765FF' // 100% 处的颜色
        //   }],
        //   global: false // 缺省为 false
        // }
        color: '#561987'
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4
      }
    },

    ],
    lineStyle: {
      color: 'gradient',
      curveness: 0.5, //设置边的曲度
      opacity: 0.2 //设置边的透明度
    },
    top: '100',
    right: '10%',
    bottom: '5%',
    left: '1px',
    // 节点宽度
    // nodeWidth: 110,
    // 是否可拖动
    draggable: true,
    // 左对齐
    // eslint-disable-next-line no-dupe-keys
    nodeAlign: 'left',
    data: [],
    links: []
  }],
  tooltip: {
    trigger: 'item',
  },
  toolbox: {
    show: true,  //show为显示工具栏
    // 放工具元素
    feature: {
      saveAsImage: { type: 'jpeg', backgroundColor: '#fff' }  // saveAsImage下载图片
    },
    right: 20
  }

};

export let tag_options = {
  title: {
    text: '标签对应连接数排序',
    top: 10,
    left: 20
  },
  grid: {
    left: 155
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'shadow' }
  },
  toolbox: {
    show: true,
    feature: {
      saveAsImage: { type: 'jpeg', backgroundColor: '#fff' }  // saveAsImage下载图片
    },
    top: 10,
    right: 20
  },
  yAxis: [
    {
      type: 'category',
      //是否显示刻度值    
      axisTick: {
        show: false
      },
      data: []
    }
  ],
  xAxis: [
    {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: "dashed"
        }
      }
    }
  ],
  series: [
    {
      name: '连接数',
      type: 'bar',
      stack: '总量',
      data: [],

      itemStyle: {
        normal: {
          //颜色设置
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
            offset: 0,
            color: "rgba(17, 110, 249, 0.2) " // 0% 处的颜色
          }, {
            offset: 1,
            color: "#116EF9" // 100% 处的颜色
          }], false)
        }
      },
    }
  ]
};
export let pack_options = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    top: 80,
    bottom: 30
  },
  toolbox: {
    show: true,
    feature: {
      saveAsImage: { type: 'jpeg', backgroundColor: '#fff' }  // saveAsImage下载图片
    },
    top: 0,
    right: 20
  },
  xAxis: {
    type: 'category',
    axisLine: { show: false },
    axisLabel: { show: false },
    axisTick: { show: false },
    splitLine: { show: false },
    data: []
  },
  yAxis: {

    type: 'value',
    position: 'top',
    splitLine: {
      lineStyle: {
        type: 'dashed'
      }
    }
  },
  series: [
    {
      name: 'Cost',
      type: 'bar',
      stack: 'Total',
      barWidth: 12,
      label: {
        show: true,
        formatter: '{b}'
      },
      data: [],
      itemStyle: {
        normal: {
          //颜色设置
          color: function (params) {
            var index_color = params.value;
            if (index_color > 0) {
              return new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                offset: 0,
                color: "rgba(17, 110, 249, 0.3) " // 0% 处的颜色
              }, {
                offset: 1,
                color: "rgba(17, 110, 249, 0.8) " // 100% 处的颜色
              }], false);
            } else {
              return new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                offset: 0,
                color: "rgba(123, 97, 255, 0.8) " // 0% 处的颜色
              }, {
                offset: 1,
                color: "rgba(123, 97, 255, 0.3) " // 100% 处的颜色
              }], false);
            }
          }
        }
      },
    }
  ]
};
<template>
  <div class="fliter">
    <el-popover placement="bottom" trigger="click" :popper-append-to-body="false" :visible-arrow="false"
                popper-class="dialoguepopperbox"
    >
      <el-button slot="reference">
        <svg-icon icon-class="setlist" style="margin-right: 5px" />列设置
      </el-button>
      <div class="head">
        <el-input v-model="checkinput" placeholder="搜索需要的列设置"></el-input>
      </div>
      <div class="content">
        <el-checkbox-group v-model="checkedHeaders" @change="configStotage">
          <el-checkbox v-for="item in tableHeader1" :key="item" :label="item" style="display: block">
            {{ item }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="foot">
        <el-checkbox v-model="checkAll" style="margin-left: 16px" @change="searchButtonClick">
          全选
        </el-checkbox>
        <el-button style="margin-right: 16px" @click="resetButtonClick">
          重置
        </el-button>
      </div>
    </el-popover>
  </div>
</template>

<script>
import dictJSON from "../../../assets/dict.json";
export default {
  name:'Sessionfliter',
  data () {
    return {
      checkAll: false,
      esoptions2: "",
      checkinput: "",
      tableHeader1: [],
      tableHeader: [
        "客户端端口",
        "客户端IP位置",
        "服务端端口",
        "服务端IP位置",
        "IP协议",
        "应用",
        "开始时间",
        "结束时间",
        "标签",
        "处理线程号",
        "持续秒数",
        "客户端MAC",
        "客户端SSL指纹",
        "客户端HTTP指纹",
        "客户端发送TTL最小距离",
        "客户端发送TTL最大距离",
        "客户端发送原始TTL",
        "服务端MAC",
        "服务端SSL指纹",
        "服务端HTTP指纹",
        "服务端发送原始TTL",
        "服务端发送TTL最小距离",
        "服务端发送TTL最大距离",
      ],
      checkedHeaders: [],
      checkedHeaders2: [
        "客户端端口",
        "客户端IP位置",
        "服务端端口",
        "服务端IP位置",
        "IP协议",
        "任务名称",
        "标签",
        "应用",
        "开始时间",
        "结束时间",
      ], //
      esoptions1: [
        {
          value: "SessionId",
          label: "连接ID",
        },
        {
          value: "sIp",
          label: "源IP",
        },
        {
          value: "SIPPosition",
          label: "源IP位置",
        },
        {
          value: "sPort",
          label: "源端口",
        },
        {
          value: "SrcFirstIp",
          label: "首层源IP",
        },
        {
          value: "dIp",
          label: "目的IP",
        },
        {
          value: "DIPPosition",
          label: "目的IP位置",
        },
        {
          value: "dPort",
          label: "目的端口",
        },
        {
          value: "Proxy_IP",
          label: "代理IP",
        },
        {
          value: "Proxy_Port",
          label: "代理端口",
        },
        {
          value: "IPPro",
          label: "IP协议",
        },
        // {
        //   value: "FirstSender",
        //   label: "首发IP",
        // },
        // {
        //   value: "AppId",
        //   label: "应用ID",
        // },
        {
          value: "AppName",
          label: "应用",
        },
        {
          value: "ThreadId",
          label: "处理线程号",
        },
        {
          value: "Duration",
          label: "持续秒数",
        },
        {
          value: "pkt.sBytes",
          label: "发送负载量",
        },
        {
          value: "pkt.dBytes",
          label: "接收负载量",
        },
        {
          value: "StartTime",
          label: "开始时间",
        },
        {
          value: "EndTime",
          label: "结束时间",
        },
        {
          value: "Tag",
          label: "标签",
        },
        {
          value: "HandleBeginTime",
          label: "处理起始时间",
        },
        {
          value: "HandleEndTime",
          label: "处理结束时间",
        },
        {
          value: "RuleInfor.RuleNum",
          label: "命中规则数量",
        },
        {
          value: "RuleInfor.Level",
          label: "规则最高等级",
        },
        {
          value: "pkt.ProNum",
          label: "已知协议包数",
        },
        {
          value: "pkt.UnkProNum",
          label: "未知协议包数",
        },
        {
          value: "TotalBytes",
          label: "总字节数",
        },
        {
          value: "sMac",
          label: "源MAC",
        },
        {
          value: "sTCPFinger",
          label: "源TCP指纹",
        },
        // {
        //   value: "sTCPFingerInfor",
        //   label: "源TCP指纹详情",
        // },
        {
          value: "sSSLFinger",
          label: "源SSL指纹",
        },
        {
          value: "sHTTPFinger",
          label: "源HTTP指纹",
        },
        {
          value: "sIpCity",
          label: "源IP所在城市",
        },
        {
          value: "sIpCountry",
          label: "源IP所在国家",
        },
        {
          value: "sIpLatitude",
          label: "源IP所在纬度",
        },
        {
          value: "sIpLongitude",
          label: "源IP所在经度",
        },
        {
          value: "sIpSubdivisions",
          label: "源IP所在省份",
        },
        {
          value: "pkt.sMaxLen",
          label: "源端发送最大包长",
        },
        {
          value: "pkt.sNum",
          label: "源端发送包数",
        },
        {
          value: "pkt.sPayloadNum",
          label: "源端发送负载包数",
        },
        {
          value: "pkt.sPayloadBytes",
          label: "源端发送负载字节数",
        },
        {
          value: "pkt.sPSHNum",
          label: "源端发送PSH次数",
        },
        {
          value: "pkt.sFINNum",
          label: "源端发送FIN次数",
        },
        {
          value: "pkt.sRSTNum",
          label: "源端发送RST次数",
        },
        {
          value: "pkt.sSYNNum",
          label: "源端发送SYN次数",
        },
        {
          value: "pkt.sSYNBytes",
          label: "源端发送SYN包长",
        },
        // {
        //   value: "pkt.sTTLMax",
        //   label: "源端发送最大TTL",
        // },
        // {
        //   value: "pkt.sTTLMin",
        //   label: "源端发送最小TTL",
        // },
        {
          value: "sInitialTTL",
          label: "源端发送原始TTL",
        },
        {
          value: "sMinHopCount",
          label: "源端发送TTL最小距离",
        },
        {
          value: "sMaxHopCount",
          label: "源端发送TTL最大距离",
        },
        {
          value: "dMac",
          label: "目的MAC",
        },
        {
          value: "dTCPFinger",
          label: "目的TCP指纹",
        },
        // {
        //   value: "dTCPFingerInfor",
        //   label: "目的TCP指纹详情",
        // },
        {
          value: "dSSLFinger",
          label: "目的SSL指纹",
        },
        {
          value: "dHTTPFinger",
          label: "目的HTTP指纹",
        },
        {
          value: "dIpCity",
          label: "目的IP所在城市",
        },
        {
          value: "dIpCountry",
          label: "目的IP所在国家",
        },
        {
          value: "dIpLatitude",
          label: "目的IP所在纬度",
        },
        {
          value: "dIpLongitude",
          label: "目的IP所在经度",
        },
        {
          value: "dIpSubdivisions",
          label: "目的IP所在省份",
        },
        {
          value: "dIpLongitude",
          label: "目的IP所在经度",
        },
        {
          value: "pkt.dMaxLen",
          label: "目的端发送最大包长",
        },
        {
          value: "pkt.dNum",
          label: "目的端发送包数",
        },
        {
          value: "pkt.dPayloadNum",
          label: "目的端发送负载包数",
        },
        {
          value: "pkt.dPayloadBytes",
          label: "目的端发送负载字节数",
        },
        {
          value: "pkt.dPSHNum",
          label: "目的端发送PSH次数",
        },
        {
          value: "pkt.dFINNum",
          label: "目的端发送FIN次数",
        },
        {
          value: "pkt.dRSTNum",
          label: "目的端发送RST次数",
        },
        {
          value: "pkt.dSYNNum",
          label: "目的端发送SYN次数",
        },
        {
          value: "pkt.dPSHNum",
          label: "目的端发送PSH次数",
        },
        {
          value: "pkt.dSYNBytes",
          label: "目的端发送SYN字节数",
        },
        {
          value: "pkt.dTTLMax",
          label: "目的端发送最大TTL",
        },
        {
          value: "pkt.dTTLMin",
          label: "目的端发送最小TTL",
        },
        {
          value: "dInitialTTL",
          label: "目的端发送原始TTL",
        },
        {
          value: "dMinHopCount",
          label: "目的端发送TTL最小距离",
        },
        {
          value: "dMaxHopCount",
          label: "目的端发送TTL最大距离",
        },
      ],
      esoptions: [
        {
          value: "huihua",
          label: "会话列表",
          children: [
            {
              value: "SessionId",
              label: "连接ID",
            },
            {
              value: "sIp",
              label: "源IP",
            },
            {
              value: "SIPPosition",
              label: "源IP位置",
            },
            {
              value: "sPort",
              label: "源端口",
            },
            {
              value: "SrcFirstIp",
              label: "首层源IP",
            },
            {
              value: "dIp",
              label: "目的IP",
            },
            {
              value: "DIPPosition",
              label: "目的IP位置",
            },
            {
              value: "dPort",
              label: "目的端口",
            },
            {
              value: "Proxy_IP",
              label: "代理IP",
            },
            {
              value: "Proxy_Port",
              label: "代理端口",
            },
            {
              value: "IPPro",
              label: "IP协议",
            },
            // {
            //   value: "FirstSender",
            //   label: "首发IP",
            // },
            // {
            //   value: "AppId",
            //   label: "应用ID",
            // },
            {
              value: "AppName",
              label: "应用",
            },
            {
              value: "ThreadId",
              label: "处理线程号",
            },
            {
              value: "Duration",
              label: "持续秒数",
            },
            {
              value: "pkt.sBytes",
              label: "发送负载量",
            },
            {
              value: "pkt.dBytes",
              label: "接收负载量",
            },
            {
              value: "StartTime",
              label: "开始时间",
            },
            {
              value: "EndTime",
              label: "结束时间",
            },
            {
              value: "Tag",
              label: "标签",
            },
            {
              value: "HandleBeginTime",
              label: "处理起始时间",
            },
            {
              value: "HandleEndTime",
              label: "处理结束时间",
            },
            {
              value: "RuleInfor.RuleNum",
              label: "命中规则数量",
            },
            {
              value: "RuleInfor.Level",
              label: "规则最高等级",
            },
            {
              value: "pkt.ProNum",
              label: "已知协议包数",
            },
            {
              value: "pkt.UnkProNum",
              label: "未知协议包数",
            },
            {
              value: "TotalBytes",
              label: "总字节数",
            },
            {
              value: "sMac",
              label: "源MAC",
            },
            {
              value: "sTCPFinger",
              label: "源TCP指纹",
            },
            // {
            //   value: "sTCPFingerInfor",
            //   label: "源TCP指纹详情",
            // },
            {
              value: "sSSLFinger",
              label: "源SSL指纹",
            },
            {
              value: "sHTTPFinger",
              label: "源HTTP指纹",
            },
            {
              value: "sIpCity",
              label: "源IP所在城市",
            },
            {
              value: "sIpCountry",
              label: "源IP所在国家",
            },
            {
              value: "sIpLatitude",
              label: "源IP所在纬度",
            },
            {
              value: "sIpLongitude",
              label: "源IP所在经度",
            },
            {
              value: "sIpSubdivisions",
              label: "源IP所在省份",
            },
            {
              value: "pkt.sMaxLen",
              label: "源端发送最大包长",
            },
            {
              value: "pkt.sNum",
              label: "源端发送包数",
            },
            {
              value: "pkt.sPayloadNum",
              label: "源端发送负载包数",
            },
            {
              value: "pkt.sPayloadBytes",
              label: "源端发送负载字节数",
            },
            {
              value: "pkt.sPSHNum",
              label: "源端发送PSH次数",
            },
            {
              value: "pkt.sFINNum",
              label: "源端发送FIN次数",
            },
            {
              value: "pkt.sRSTNum",
              label: "源端发送RST次数",
            },
            {
              value: "pkt.sSYNNum",
              label: "源端发送SYN次数",
            },
            {
              value: "pkt.sSYNBytes",
              label: "源端发送SYN包长",
            },
            // {
            //   value: "pkt.sTTLMax",
            //   label: "源端发送最大TTL",
            // },
            // {
            //   value: "pkt.sTTLMin",
            //   label: "源端发送最小TTL",
            // },
            {
              value: "sInitialTTL",
              label: "源端发送原始TTL",
            },
            {
              value: "sMinHopCount",
              label: "源端发送TTL最小距离",
            },
            {
              value: "sMaxHopCount",
              label: "源端发送TTL最大距离",
            },
            {
              value: "dMac",
              label: "目的MAC",
            },
            {
              value: "dTCPFinger",
              label: "目的TCP指纹",
            },
            // {
            //   value: "dTCPFingerInfor",
            //   label: "目的TCP指纹详情",
            // },
            {
              value: "dSSLFinger",
              label: "目的SSL指纹",
            },
            {
              value: "dHTTPFinger",
              label: "目的HTTP指纹",
            },
            {
              value: "dIpCity",
              label: "目的IP所在城市",
            },
            {
              value: "dIpCountry",
              label: "目的IP所在国家",
            },
            {
              value: "dIpLatitude",
              label: "目的IP所在纬度",
            },
            {
              value: "dIpLongitude",
              label: "目的IP所在经度",
            },
            {
              value: "dIpSubdivisions",
              label: "目的IP所在省份",
            },
            {
              value: "dIpLongitude",
              label: "目的IP所在经度",
            },
            {
              value: "pkt.dMaxLen",
              label: "目的端发送最大包长",
            },
            {
              value: "pkt.dNum",
              label: "目的端发送包数",
            },
            {
              value: "pkt.dPayloadNum",
              label: "目的端发送负载包数",
            },
            {
              value: "pkt.dPayloadBytes",
              label: "目的端发送负载字节数",
            },
            {
              value: "pkt.dPSHNum",
              label: "目的端发送PSH次数",
            },
            {
              value: "pkt.dFINNum",
              label: "目的端发送FIN次数",
            },
            {
              value: "pkt.dRSTNum",
              label: "目的端发送RST次数",
            },
            {
              value: "pkt.dSYNNum",
              label: "目的端发送SYN次数",
            },
            {
              value: "pkt.dPSHNum",
              label: "目的端发送PSH次数",
            },
            {
              value: "pkt.dSYNBytes",
              label: "目的端发送SYN字节数",
            },
            {
              value: "pkt.dTTLMax",
              label: "目的端发送最大TTL",
            },
            {
              value: "pkt.dTTLMin",
              label: "目的端发送最小TTL",
            },
            {
              value: "dInitialTTL",
              label: "目的端发送原始TTL",
            },
            {
              value: "dMinHopCount",
              label: "目的端发送TTL最小距离",
            },
            {
              value: "dMaxHopCount",
              label: "目的端发送TTL最大距离",
            },
          ],
        },
        {
          value: "SSL",
          label: "元数据_SSL",
          children: [],
        },
        {
          value: "HTTP",
          label: "元数据_HTTP",
          children: [],
        },
        {
          value: "DNS",
          label: "元数据_DNS",
          children: [],
        },
      ],
    };
  },
  watch: {
    checkinput: {
      handler (val) {
        this.init(val);
      },
    },
    checkedHeaders: {
      handler (val) {
        this.$emit("checkedHeaders1", this.checkedHeaders);
      },
    },
  },
  mounted () {
    this.esoptions2 = dictJSON.data.protocol_metadata.conn;
    for (let i in this.esoptions2) {
      if (i.slice(0, 4) === "SSL.") {
        let ssl = {};
        ssl.value = this.esoptions2[i].filedName;
        ssl.label = this.esoptions2[i].Name;
        this.esoptions[1].children.push(ssl);
        this.esoptions1.push(ssl);
      }
      if (i.slice(0, 4) === "DNS.") {
        let dns = {};
        dns.value = this.esoptions2[i].filedName;
        dns.label = this.esoptions2[i].Name;
        this.esoptions[3].children.push(dns);
        this.esoptions1.push(dns);
      }
      if (i.slice(0, 5) === "HTTP.") {
        let http = {};
        http.value = this.esoptions2[i].filedName;
        http.label = this.esoptions2[i].Name;
        this.esoptions[2].children.push(http);
        this.esoptions1.push(http);
      }
    }
    this.$store.commit("conversational/esoptionsData", this.esoptions);
    this.$store.commit("conversational/esqueryData", this.esoptions1);
    this.checkedHeaders = this.checkedHeaders2;
    this.tableHeader1 = this.tableHeader;
    if (window.localStorage.showHeader) {
      this.checkedHeaders = JSON.parse(window.localStorage.showHeader);
    }
  },
  methods: {
    // 全选
    searchButtonClick (val) {
      this.checkedHeaders =val? this.tableHeader:[];
      window.localStorage.showHeader = JSON.stringify(this.checkedHeaders);
    },
    // 重置
    resetButtonClick () {
      this.checkedHeaders = this.checkedHeaders2;
      this.checkAll=false;
      window.localStorage.showHeader = JSON.stringify(this.checkedHeaders);
    },
    init (data) {
      this.tableHeader1 = [];
      var input = data;
      var items = this.tableHeader;
      var items1;
      if (input) {
        items1 = items.filter((item) => {
          return Object.keys(item).some((key1) => {
            return item.match(input);
          });
        });
        this.tableHeader1 = items1;
      } else {
        this.tableHeader1 = this.tableHeader;
      }
    },
    configStotage () {
      this.checkAll= this.checkedHeaders.length===this.tableHeader.length;
      window.localStorage.showHeader = JSON.stringify(this.checkedHeaders);
    },
  },
};
</script>

<style lang="scss" scoped>
.head {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content {
  margin: 0 16px;
  height: 220px;
  overflow: auto;

  ::v-deep .el-checkbox {
    color: #2c2c35;
    margin-bottom: 8px;
  }

  ::v-deep .is-checked+.el-checkbox__label {
    color: #2c2c35;
  }

  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
  }
}

.foot {
  height: 48px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f8f8;
  border-radius: 0px 0px 2px 2px;

  ::v-deep .el-checkbox {
    color: #2c2c35;
    // margin-bottom: 8px;
  }

  ::v-deep .is-checked+.el-checkbox__label {
    color: #2c2c35;
  }

  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
  }

  .el-button:focus,
  .el-button:hover {
    background-color: #116ef9;
    border-color: #116ef9;
    color: #ffffff;
  }

  .el-button {
    height: 24px;
    width: 58px;
    display: flex;
    justify-content: center;
    align-items: center;

  }


}

.fliter {
  ::v-deep {
    .el-button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 94px;
      height: 32px;
    }

    .el-checkbox-group {
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
<template>
  <div class="sessionList-box">
    <div class="sessionList-box-top">
      <div class="sessionList-box-top-r">
        <div class="listnum">已选择{{ activenum }}条</div>
        <div class="Partdown">
          <el-tooltip content="会话前20个包" placement="top" effect="dark">
            <el-button :disabled="downloadDisable" plain @click="batchDownload">
              快速下载
            </el-button>
          </el-tooltip>
        </div>
        <el-tooltip placement="top" effect="dark">
          <div slot="content">
            会话条数限制：超过10万条不可操作<br />
            总数据量限制：超过10GB不可操作
          </div>
          <el-popover
            placement="bottom"
            trigger="click"
            :visible-arrow="false"
            popper-class="alldownbox"
            :disabled="downloadDisable"
          >
            <div
              slot="reference"
              :class="downloadDisable ? 'alldown' : 'qlactive'"
            >
              <el-button plain>全量下载</el-button>
              <div class="alldown-r">
                <svg-icon icon-class="del-down2" fill="#116ef9" />
              </div>
            </div>
            <div class="alldownfoot">
              <el-button class="alldownfoot-t" @click="fulldoseDown">
                下载选中项
              </el-button>
              <el-button class="alldownfoot-d" @click="allDown">
                下载全部项
              </el-button>
            </div>
          </el-popover>
        </el-tooltip>
        <div class="globaldown">
          <el-tooltip
            content="到“下载列表”的“日志导出”中下载"
            placement="top"
            effect="dark"
          >
            <el-button plain @click="logderive">
              <i class="el-icon--left">
                <svg-icon icon-class="globaldown" fill="#116ef9" />
              </i>
              日志导出
            </el-button>
          </el-tooltip>
        </div>
      </div>
      <div class="sessionList-box-top-l">
        <sessionfliter
          @checkedHeaders1="checkedHeaders1"
          @change="configStotage"
        />
      </div>
    </div>
    <div class="sessionList-box-form">
      <el-table
        ref="tableList"
        border
        :data="connTableData"
        tooltip-effect="dark"
        :default-sort="{ prop: 'begin_time', order: 'ascending' }"
        show-overflow-tooltip
        :header-cell-style="headerStyle"
        :cell-style="cellStyle"
        style="width: 100%"
        stripe
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" fixed />
        <el-table-column
          width="70"
          prop="num"
          label="序号"
          type="index"
          :index="indexMethod"
          fixed
        />
        <el-table-column label="会话ID" prop="session_id" fixed min-width="200">
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <el-tooltip
                  :content="scope.row.session_id ? scope.row.session_id : '--'"
                  placement="top"
                  effect="light"
                  popper-class="sessionidTooltip"
                >
                  <div
                    style="cursor: pointer; color: #116ef9"
                    @click="opensessionDrawer(scope, 6)"
                  >
                    {{ scope.row.session_id ? scope.row.session_id : "--" }}
                  </div>
                </el-tooltip>
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'SessionId')">
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'SessionId')">
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="taskname"
          label="任务名称"
          min-width="80"
          show-overflow-tooltip
        />
        <el-table-column
          label="客户端IP"
          prop="src_ip"
          sortable="custom"
          min-width="140"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div
                class="top"
                style="cursor: pointer; color: #116ef9"
                @click="opneipDetail(scope.row.src_ip, 0)"
              >
                {{ scope.row.src_ip }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover1"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>

                    <div class="sortbtn">
                      <div class="sortbtn-top" style="position: relative">
                        <svg-icon icon-class="sort-up" class="advanceicon" />
                        <div class="title">IP正向检索</div>
                        <el-button @click="forwardsort(scope, 'dip')">
                          目的IP正向检索
                        </el-button>
                        <el-button @click="forwardsort(scope, 'sip')">
                          源IP正向检索
                        </el-button>
                      </div>
                      <div class="sortbtn-down" style="position: relative">
                        <svg-icon icon-class="sort-down" class="advanceicon" />
                        <div class="title">IP反向检索</div>
                        <el-button @click="reversesort(scope, 'dip')">
                          目的IP反向检索
                        </el-button>
                        <el-button @click="reversesort(scope, 'sip')">
                          源IP反向检索
                        </el-button>
                      </div>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeaders.includes('客户端端口')"
          width="120"
          prop="src_port"
          label="客户端端口"
          sortable="custom"
        >
          <template slot-scope="scope"> {{ scope.row.src_port }} </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeaders.includes('客户端IP位置')"
          min-width="140"
          prop="srcIPAddress"
          label="客户端IP位置"
        >
          <template slot-scope="scope"> {{ scope.row.srcIPAddress }} </template>
        </el-table-column>
        <el-table-column
          min-width="140"
          prop="dst_ip"
          label="服务端IP"
          sortable="custom"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div
                class="top"
                style="cursor: pointer; color: #116ef9"
                @click="opneipDetail(scope.row.dst_ip, 0)"
              >
                {{ scope.row.dst_ip }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover1"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <div class="sortbtn-top" style="position: relative">
                        <svg-icon class="advanceicon" icon-class="sort-up" />
                        <div class="title">IP正向检索</div>
                        <el-button @click="forwardsort(scope, 'dip')">
                          目的IP正向检索
                        </el-button>
                        <el-button @click="forwardsort(scope, 'sip')">
                          源IP正向检索
                        </el-button>
                      </div>
                      <div class="sortbtn-down" style="position: relative">
                        <svg-icon icon-class="sort-down" class="advanceicon" />
                        <div class="title">IP反向检索</div>
                        <el-button @click="reversesort(scope, 'dip')">
                          目的IP反向检索
                        </el-button>
                        <el-button @click="reversesort(scope, 'sip')">
                          源IP反向检索
                        </el-button>
                      </div>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeaders.includes('服务端端口')"
          width="120"
          label="服务端端口"
          prop="dst_port"
          sortable="custom"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                {{ scope.row.dst_port }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'dPort')">
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'dPort')">
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeaders.includes('服务端IP位置')"
          width="120"
          prop="dstIPAddress"
          label="服务端IP位置"
        >
          <template slot-scope="scope"> {{ scope.row.dstIPAddress }} </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeaders.includes('首层源IP')"
          width="120"
          prop="sFirstIP"
          label="首层源IP"
        >
          <template slot-scope="scope">
            <div>
              {{ scope.row.sFirstIP }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeaders.includes('代理IP')"
          width="100"
          prop="Proxy_IP"
          label="代理IP"
        >
          <template slot-scope="scope">
            <div>
              {{ scope.row.Proxy_IP }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeaders.includes('代理端口')"
          width="100"
          prop="Proxy_Port"
          label="代理端口"
        >
          <template slot-scope="scope">
            <div>
              {{ scope.row.Proxy_Port }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeaders.includes('IP协议')"
          prop="ProName"
          label="IP协议"
          width="90"
          sortable="custom"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                {{ scope.row.ProName || "" }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'ProName')">
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'ProName')">
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeaders.includes('首发IP')"
          width="100"
          prop="FirstSender"
          label="首发IP"
        />
        <el-table-column
          v-if="checkedHeaders.includes('应用ID')"
          width="130"
          prop="app_id"
          label="应用ID"
        />
        <el-table-column
          v-if="checkedHeaders.includes('应用')"
          min-width="120"
          label="应用"
          prop="AppName"
          sortable="custom"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                {{ scope.row.AppName }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'AppName')">
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'AppName')">
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeaders.includes('处理线程号')"
          width="150"
          label="处理线程号"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ThreadId }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeaders.includes('持续秒数')"
          width="110"
          prop="Duration"
          label="持续秒数"
          sortable="custom"
        />
        <el-table-column
          v-if="checkedHeaders.includes('发送负载量')"
          width="120"
          prop="sBytes"
          label="发送负载量"
          sortable="custom"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.sBytes }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeaders.includes('接收负载量')"
          width="120"
          prop="dBytes"
          label="接收负载量"
          sortable="custom"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.dBytes }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeaders.includes('开始时间')"
          width="180"
          prop="begin_time"
          label="开始时间"
          sortable="custom"
        >
          <template slot-scope="scope">
            <div class="sorttime">
              <div class="top">
                {{ scope.row.begin_time }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    trigger="hover"
                    popper-class="sortpopover3"
                  >
                    <span
                      slot="reference"
                      class="sorttoole-r"
                      style="line-height: 20px"
                    >...</span>
                    <div class="sortbtn">
                      <div class="sortbtn-top" style="position: relative">
                        <svg-icon class="advanceicon" icon-class="advance" />
                        <el-button @click="timesearch('adv1', scope)">
                          检索前1分钟
                        </el-button>
                        <el-button @click="timesearch('adv5', scope)">
                          检索前5分钟
                        </el-button>
                        <el-button @click="timesearch('adv10', scope)">
                          检索前10分钟
                        </el-button>
                      </div>
                      <div class="sortbtn-down" style="position: relative">
                        <svg-icon class="advanceicon" icon-class="retreat" />
                        <el-button @click="timesearch('ret1', scope)">
                          检索后1分钟
                        </el-button>
                        <el-button @click="timesearch('ret5', scope)">
                          检索后5分钟
                        </el-button>
                        <el-button @click="timesearch('ret10', scope)">
                          检索后10分钟
                        </el-button>
                      </div>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="checkedHeaders.includes('结束时间')"
          width="180"
          prop="end_time"
          label="结束时间"
          sortable="custom"
        />
        <el-table-column
          v-if="checkedHeaders.includes('处理起始时间')"
          width="180"
          prop="HandleBeginTime"
          label="处理起始时间"
        />
        <el-table-column
          v-if="checkedHeaders.includes('处理结束时间')"
          width="180"
          prop="HandleEndTime"
          label="处理结束时间"
        />

        <el-table-column
          v-if="checkedHeaders.includes('已知协议包数')"
          width="180"
          prop="pktProNum"
          label="已知协议包数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('未知协议包数')"
          width="180"
          prop="pktUnkProNum"
          label="未知协议包数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('总字节数')"
          width="100"
          prop="TotalBytes"
          label="总字节数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端MAC')"
          show-overflow-tooltip
          width="150"
          prop="sMac"
          label="客户端MAC"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端TCP指纹')"
          width="150"
          prop="sTCPFinger"
          label="客户端TCP指纹"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端TCP指纹详情')"
          width="180"
          prop="sTCPFingerInfor"
          label="客户端TCP指纹详情"
          max-height="100"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端SSL指纹')"
          width="150"
          prop="sSSLFinger"
          label="客户端SSL指纹"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端HTTP指纹')"
          width="150"
          prop="sHTTPFinger"
          label="客户端HTTP指纹"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端IP所在城市')"
          width="180"
          prop="sIpCity"
          label="客户端IP所在城市"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端IP所在国家')"
          width="200"
          prop="sIpCountry"
          label="客户端IP所在国家"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端IP所在纬度')"
          width="200"
          prop="sIpLatitude"
          label="客户端IP所在纬度"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端IP所在经度')"
          width="200"
          prop="sIpLongitude"
          label="客户端IP所在经度"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端IP所在省份')"
          width="200"
          prop="sIpSubdivisions"
          label="客户端IP所在省份"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端发送最大包长')"
          width="200"
          prop="pktsMaxLen"
          label="客户端发送最大包长"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端发送包数')"
          width="200"
          prop="pktsNum"
          label="客户端发送包数"
        />

        <el-table-column
          v-if="checkedHeaders.includes('客户端发送负载包数')"
          width="200"
          prop="pktsPayloadNum"
          label="客户端发送负载包数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端发送负载字节数')"
          width="200"
          prop="pktsPayloadBytes"
          label="客户端发送负载字节数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端发送PSH次数')"
          width="200"
          prop="pktsPSHNum"
          label="客户端发送PSH次数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端发送FIN次数')"
          width="200"
          prop="pktsFINNum"
          label="客户端发送FIN次数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端发送RST次数')"
          width="200"
          prop="pktsRSTNum"
          label="客户端发送RST次数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端发送SYN次数')"
          width="200"
          prop="pktsSYNNum"
          label="客户端发送SYN次数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端发送SYN包长')"
          width="200"
          prop="pktsSYNBytes"
          label="客户端发送SYN包长"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端发送最大TTL')"
          width="200"
          prop="pktsTTLMax"
          label="客户端发送最大TTL"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端发送最小TTL')"
          width="200"
          prop="pktsTTLMin"
          label="客户端发送最小TTL"
        />

        <el-table-column
          v-if="checkedHeaders.includes('客户端发送TTL最小距离')"
          width="200"
          prop="sMinHopCount"
          label="客户端发送TTL最小距离"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端发送TTL最大距离')"
          width="200"
          prop="sMaxHopCount"
          label="客户端发送TTL最大距离"
        />
        <el-table-column
          v-if="checkedHeaders.includes('客户端发送原始TTL')"
          width="200"
          prop="sInitialTTL"
          label="客户端发送原始TTL"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端MAC')"
          width="100"
          prop="dMac"
          label="服务端MAC"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端TCP指纹')"
          width="200"
          prop="dTCPFinger"
          label="服务端TCP指纹"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端TCP指纹详情')"
          width="200"
          prop="dTCPFingerInfor"
          label="服务端TCP指纹详情"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端SSL指纹')"
          width="200"
          prop="dSSLFinger"
          label="服务端SSL指纹"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端HTTP指纹')"
          width="200"
          prop="dHTTPFinger"
          label="服务端HTTP指纹"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端IP所在城市')"
          width="200"
          prop="dIpCity"
          label="服务端IP所在城市"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端IP所在国家')"
          width="200"
          prop="dIpCountry"
          label="服务端IP所在国家"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端IP所在纬度')"
          width="200"
          prop="dIpLatitude"
          label="服务端IP所在纬度"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端IP所在经度')"
          width="200"
          prop="dIpLongitude"
          label="服务端IP所在经度"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端IP所在省份')"
          width="200"
          prop="dIpSubdivisions"
          label="服务端IP所在省份"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端发送最大包长')"
          width="240"
          prop="pktdMaxLen"
          label="服务端发送最大包长"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端发送包数')"
          width="200"
          prop="pktdNum"
          label="服务端发送包数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端发送负载包数')"
          width="240"
          prop="pktdPayloadNum"
          label="服务端发送负载包数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端发送负载字节数')"
          width="240"
          prop="pktdPayloadBytes"
          label="服务端发送负载字节数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端发送PSH次数')"
          width="240"
          prop="pktdPSHNum"
          label="服务端发送PSH次数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端发送FIN次数')"
          width="240"
          prop="pktdFINNum"
          label="服务端发送FIN次数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端发送RST次数')"
          width="240"
          prop="pktdRSTNum"
          label="服务端发送RST次数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端发送SYN次数')"
          width="240"
          prop="pktdSYNNum"
          label="服务端发送SYN次数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端发送SYN字节数')"
          width="240"
          prop="pktdSYNBytes"
          label="服务端发送SYN字节数"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端发送最大TTL')"
          width="240"
          prop="pktdTTLMax"
          label="服务端发送最大TTL"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端发送最小TTL')"
          width="240"
          prop="pktdTTLMin"
          label="服务端发送最小TTL"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端发送原始TTL')"
          width="240"
          prop="dInitialTTL"
          label="服务端发送原始TTL"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端发送TTL最小距离')"
          width="240"
          prop="dMinHopCount"
          label="服务端发送TTL最小距离"
        />
        <el-table-column
          v-if="checkedHeaders.includes('服务端发送TTL最大距离')"
          width="240"
          prop="dMaxHopCount"
          label="服务端发送TTL最大距离"
        />
        <el-table-column
          v-if="checkedHeaders.includes('标签')"
          show-overflow-tooltip
          fixed="right"
          prop="tags"
          min-width="300"
          label="标签"
        >
          <template slot-scope="scope">
            <div class="taglist">
              <span
                v-for="(item, index) in scope.row.tags"
                :key="index"
                class="tagbox"
              >
                <el-popover
                  placement="bottom"
                  width="200"
                  trigger="click"
                  popper-class="sortpopover"
                >
                  <el-tag slot="reference" class="tag" :type="item.type">
                    <div>{{ item.tag_text }}</div>
                  </el-tag>
                  <div class="sortbtn">
                    <el-button @click="forwardSearch(item, 'Labels')">
                      <svg-icon
                        icon-class="sort-up"
                        style="margin-right: 15px"
                      />正向检索
                    </el-button>
                    <el-button @click="reverseSearch(item, 'Labels')">
                      <svg-icon
                        icon-class="sort-down"
                        style="margin-right: 15px"
                      />反向检索
                    </el-button>
                  </div>
                </el-popover>
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="80">
          <template slot="header">
            <div style="text-align: center">操作</div>
          </template>
          <template slot-scope="scope">
            <div style="text-align: center; cursor: pointer">
              <el-tooltip content="下载前20个包" placement="top" effect="dark">
                <el-button type="text" @click="addToDownloadList(scope.row)">下载</el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-show="showfoot" class="sessionList-box-foot">
      <div class="sessionList-box-foot-down">
        <div class="sessionList-box-foot-down-l">
          *会话展示上限为<span>10,000</span>条
        </div>

        <div class="sessionList-box-foot-down-r">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="page_size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <!-- 会话详情抽屉 -->
    <div class="sessionDrawerbox">
      <el-drawer
        :visible.sync="sessionDrawer"
        title="会话详情"
        modal
        size="40%"
        destroy-on-close
        @close="sessionclose"
      >
        <!-- <span>我来啦!</span> -->
        <sessionDetails
          v-loading="sessionDetailLoading"
          element-loading-text="数据请求中"
          :tag_target_type="tag_target_type"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(255, 255, 255, 0.99)"
        />
      </el-drawer>
    </div>
    <!-- ip详情抽屉 -->
    <div class="ipDrawer">
      <ipdetails
        :tag_target_type="tag_target_type"
        :ip="detailIp"
        :ip-detail-drawer="ipDetailDrawer"
        :ip-loading="ipLoading"
        @closeDrawer="closeDrawer"
      />
    </div>
  </div>
</template>

<script>
import { common } from "./listfn/Detailfn";
import sessionfliter from "./session_fliter.vue";
import sessionDetails from "../components/sessionDetails/index.vue";
import { formatNumber, parseTime, getFormatDate } from "@/utils";
import moment from "moment";
import ipdetails from "../components/details/IPdetails.vue";
import {
  getSessionList,
  downloadpartpre,
  alllogderive,
  getsessiondata,
} from "@/api/sessionList/sessionlist";
const cityOptions = ["源端口", "目的端口", "IP协议"];
export default {
  // -------------------------------
  name: "SessionList",
  components: {
    sessionDetails,
    sessionfliter,
    ipdetails,
  },
  // 调用 mixin 将组件js与共用js合并 ---
  mixins: [common],
  props: ["searchData", "fatherValue"],
  data() {
    return {
      ipLoading: false,
      showfoot: false,
      task_dic: {},
      original_data: null,
      connTableData: [],
      currentPage: 1,
      page_size: 10,
      total: 0,
      total_real: 0,
      cellStyle: {
        textAlign: "left",
        padding: 0, // 设置表格高度
      },
      headerStyle: {
        textAlign: "left",
        padding: 12, // 设置表格高度
      },
      param: {},
      sessionParam: {},
      order: {
        order_prop: "begin_time",
        order_field: "EndTime",
        asc: true,
      },
      filterData: {},
      fuzzy_match: null,
      order_changed: 0,
      reset_stamp: 0,
      activenum: 0, // 选中参数
      downloadDisable: false,
      download_ready: 0,
      download_file: "",
      download_list: [],
      download_list_sessionid: [],
      exportDisable: false,
      limit100: [],
      pageRange: [1, 10],
      tagsWidth: "110px",
      tableLoading: false,
      tableHeader: [
        "客户端端口",
        "目的端口",
        "代理端口",
        "IP协议",
        "标签",
        // "首发IP",
        // "应用ID",
        "应用",
        "处理线程号",
        "持续秒数",
        "起始时间",
        "结束时间",
        "首层源IP",
        "命中规则数量",
        "规则最高等级",
        "已知协议包数",
        "未知协议包数",
        "总字节数",
        "源MAC",
        "源TCP指纹",
        "源TCP指纹详情",
        "源SSL指纹",
        "源HTTP指纹",
        "源IP位置",
        "目的IP位置",
        "源IP所在城市",
        "源IP所在国家",
        "源IP所在纬度",
        "源IP所在经度",
        "源IP所在省份",
        "源端发送最大包长",
        "源端发送包数",
        "源端发送负载包数",
        "发送字节数",
        "源端发送负载字节数",
        "源端发送PSH次数",
        "源端发送FIN次数",
        "源端发送RST次数",
        "源端发送SYN次数",
        "源端发送SYN包长",
        "源端发送原始TTL",
        "源端发送TTL最小距离",
        "源端发送TTL最大距离",
        "目的MAC",
        "目的TCP指纹",
        "目的TCP指纹详情",
        "目的SSL指纹",
        "目的HTTP指纹",
        "目的IP所在城市",
        "目的IP所在国家",
        "目的IP所在纬度",
        "目的IP所在经度",
        "目的IP位置",
        "目的端发送最大包长",
        "目的端发送包数",
        "目的端发送负载包数",
        "接受字节数",
        "目的端发送负载字节数",
        "目的端发送PSH次数",
        "目的端发送FIN次数",
        "目的端发送RST次数",
        "目的端发送SYN次数",
        "目的端发送SYN字节数",
        "目的端发送最大TTL",
        "目的端发送原始TTL",
        "目的端发送TTL最小距离",
        "目的端发送TTL最大距离",
      ],
      display: "none", // json框
      session_id: "", // json框
      appidrest: "",
      session_list: [],
      alltable: {
        session_list: [],
      },
      // 分页相关配置
      listParam: {
        limit: 10,
        page: 1,
        order_field: "created_time",
        asc: false,
      },
      tableHeader1: [],
      // eslint-disable-next-line no-dupe-keys
      checkedHeaders: [],
      checkedHeaders2: [
        "客户端端口",
        "IP协议",
        "标签",
        "应用",
        "服务端端口",
        "开始时间",
        "发送字节数",
        "服务端IP位置",
        "接收字节数",
      ],
      // =======================
      checkedCities: ["上海", "北京"],
      cities: cityOptions,
      // 会话详情抽屉页配置项
      sessionDrawer: false,
      sessiondata: [], // 点击获取到单列表数据
      numdata: 0,
      ipDetailDrawer: false,
      direction: "rtl",
      ipdata: [],
      detailIp: "",
      sessionDetailLoading: false,
      tag_target_type: 9999,
    };
  },
  watch: {
    searchData: {
      handler(val) {
        this.currentPage = 1;
        this.order = {
          order_prop: "begin_time",
          order_field: "EndTime",
          asc: true,
        };
        this.limit100 = [];
        this.pageRange = [1, 10];
        this.initData();
      },
      deep: true,
    },
  },
  mounted() {
    this.checkedHeaders = this.checkedHeaders2;
    this.tableHeader1 = this.tableHeader;
    if (window.localStorage.showHeader1) {
      this.checkedHeaders = JSON.parse(window.localStorage.showHeader1);
    }
  },
  methods: {
    configStotage() {
      window.localStorage.showHeader = JSON.stringify(this.checkedHeaders);
    },
    checkedHeaders1(data) {
      this.checkedHeaders = data;
    },
    // 快速下载
    batchDownload() {
      if (this.download_list.length == 0) {
        this.$message({
          type: "error",
          message: "请至少选择一条记录加入下载列表",
          duration: 3 * 1000,
        });
        return;
      }
      this.downloadDisable = false;
      const params = {
        user_id: 1,
        query: this.searchData,
        type: false,
        session: this.download_list,
      };
      downloadpartpre(params).then((res) => {
        if (res.err === 0) {
          this.$store.commit("conversational/setdowndot", true);
          this.$store.commit("conversational/getdownlist", this.download_list);
          this.$message({
            message: "已加入下载列表",
            type: "success",
            duration: 2000,
          });
        }
      });
    },

    // 列表中单点快速下载
    addToDownloadList(row) {
      const item = {
        start_time: row.startTimeInt,
        end_time: row.endTimeInt,
        batch_id: row.batch_id + "",
        thread_id: row.ThreadId,
        first_proto: row.first_proto,
        session_id: row.session_id + "", // 会话id
        task_id: row.task_id,
      };
      const params = {
        user_id: 1,
        query: this.searchData,
        type: false,
        session: [item],
      };
      downloadpartpre(params).then((res) => {
        if (res.err === 0) {
          this.$store.commit("conversational/setdowndot", true);
          this.$message({
            message: "已加入下载列表",
            type: "success",
            duration: 2000,
          });
        }
      });
      this.download_list_sessionid.push(row.session_id);
      for (let i = 0; i < this.connTableData.length; i++) {
        if (row.session_id == this.connTableData[i].session_id) {
          this.connTableData[i].is_in_download = true;
        }
      }
      if (row.labels !== null && row.labels.length > 0) {
        for (let i = 0; i < row.labels.length; i++) {
          row.labels[i] += "";
        }
      }
      let dst_ip = "";
      let dst_port = "";
      if (row.Proxy_IP === "" || row.Proxy_IP === "无代理") {
        dst_ip = row.dst_ip;
        dst_port = row.dst_port;
      } else {
        dst_ip = row.Proxy_IP;
        dst_port = row.Proxy_Port;
      }
    },
    // 全量选中项下载
    fulldoseDown() {
      if (this.download_list.length == 0) {
        this.$message({
          type: "error",
          message: "请至少选择一条记录加入下载列表",
          duration: 3 * 1000,
        });
        return;
      }
      this.downloadDisable = false;
      this.$message({
        message: "服务器开始准备数据，请稍候",
        duration: 3 * 1000,
      });
      const params = {
        user_id: 1,
        query: this.searchData,
        type: true,
        session: this.download_list,
      };
      downloadpartpre(params).then((res) => {
        if (res.err === 0) {
          this.$store.commit("conversational/setdowndot", true);
          this.$store.commit("conversational/getdownlist", this.download_list);
          this.$message({
            message: "已加入下载列表",
            type: "success",
            duration: 2000,
          });
        }
      });
    },
    // 全量全部下载
    allDown() {
      if (!this.connTableData.length) {
        this.$message.error("暂无数据可下载");
        return;
      }
      this.downloadDisable = false;
      const params = {
        user_id: 1,
        query: this.searchData,
        type: true,
      };
      downloadpartpre(params).then((res) => {
        if (res.err === 0) {
          this.$store.commit("conversational/setdowndot", true);
          this.$store.commit("conversational/getdownlist", this.download_list);
          this.$message({
            message: "已加入下载列表",
            type: "success",
            duration: 2000,
          });
        }
      });
    },
    // 日志导出
    logderive() {
      if (!this.connTableData.length) {
        this.$message.error("暂无数据可导出");
        return;
      }
      const param = {
        condition: {
          current_page: this.currentPage,
          page_size: this.page_size,
          order_field: this.order.order_field,
          asc: this.order.asc,
          query: [],
        },
        user_id: 1,
        task_type: 1,
      };
      if (JSON.stringify(this.filterData) != "{}") {
        const arr = [];
        for (const i in this.filterData) {
          arr.push(this.filterData[i]);
        }
        param.condition.query = param.condition.query.concat(arr);
      }
      if (this.searchData.query.length > 0) {
        param.condition.query = param.condition.query.concat(
          this.searchData.query
        );
      }
      this.searchData.task_id
        ? (param.condition.task_id = this.searchData.task_id)
        : null;
      this.searchData.time_range
        ? (param.condition.time_range = this.searchData.time_range)
        : null;
      const arr = {
        and: [],
        not: [],
      };
      for (let i = 0; i < param.condition.query.length; i++) {
        for (let j = 0; j < param.condition.query[i].search.length; j++) {
          if (param.condition.query[i].bool_search === "and") {
            if (param.condition.query[i].search[j].target === "Labels") {
              arr.and.push(param.condition.query[i].search[j].val);
            }
          }
          if (param.condition.query[i].bool_search === "not") {
            if (param.condition.query[i].search[j].target === "Labels") {
              arr.not.push(param.condition.query[i].search[j].val);
            }
          }
        }
      }
      param.condition.tag_query = arr;
      alllogderive(param).then((res) => {
        if (res.err == 0) {
          this.$store.commit("conversational/setdowndot", true);
          this.$message("成功加入日志导出队列");
        }
      });
    },
    // 勾选变化
    handleSelectionChange(val) {
      this.activenum = val.length;
      const newarr = [];
      let dst_ip = "";
      let dst_port = "";
      for (const i in val) {
        for (let b = 0; b < this.connTableData.length; b++) {
          if (val[i].session_id == this.connTableData[b].session_id) {
            this.connTableData[b].is_in_download = true;
          }
        }
        if (val[i].labels !== null && val[i].labels.length > 0) {
          for (let a = 0; a < val[i].labels.length; a++) {
            val[i].labels[a] += "";
          }
        }
        if (val[i].Proxy_IP === "" || val[i].Proxy_IP === "无代理") {
          dst_ip = val[i].dst_ip;
          dst_port = val[i].dst_port;
        } else {
          dst_ip = val[i].Proxy_IP;
          dst_port = val[i].Proxy_Port;
        }
        const item = {};
        item.start_time = val[i].startTimeInt;
        item.end_time = val[i].endTimeInt;
        item.batch_id = val[i].batch_id + "";
        item.thread_id = val[i].ThreadId;
        item.first_proto = val[i].first_proto;
        item.session_id = val[i].session_id + ""; // 会话id
        item.task_id = val[i].task_id;
        newarr.push(item);
      }
      this.download_list = newarr;
    },
    handleSizeChange(val) {
      this.limit100 = [];
      this.pageRange = [1, 10];
      this.page_size = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.initData();
    },
    // 排序判断
    handleSortChange(val) {
      this.order.order_prop = val.prop;
      this.order.order_field = val.prop;
      if (val.prop == "begin_time") {
        this.order.order_field = "StartTime";
      }
      if (val.prop == "dst_port") {
        this.order.order_field = "dPort";
      }
      if (val.prop == "src_port") {
        this.order.order_field = "sPort";
      }
      if (val.prop == "src_ip") {
        this.order.order_field = "sIp";
      }
      if (val.prop == "ippro") {
        this.order.order_field = "IPPro";
      }
      if (val.prop == "dst_ip") {
        this.order.order_field = "dIp";
      }
      if (val.prop == "end_time") {
        this.order.order_field = "EndTime";
      }
      if (val.prop == "sBytes") {
        this.order.order_field = "pkt.sPayloadBytes";
      }
      if (val.prop == "dBytes") {
        this.order.order_field = "pkt.dPayloadBytes";
      }
      if (val.order == "ascending") {
        this.order.asc = true;
      } else if (val.order == "descending") {
        this.order.asc = false;
      } else {
        this.order.order_field = "";
      }
      this.limit100 = [];
      this.pageRange = [1, 1];
      this.initData();
    },
    formatParam() {
      const param = {
        current_page: this.currentPage,
        page_size: this.page_size,
        order_field: this.order.order_field,
        asc: this.order.asc,
        aggr_query: false,
        query: [],
      };
      if (JSON.stringify(this.filterData) != "{}") {
        const arr = [];
        for (const i in this.filterData) {
          arr.push(this.filterData[i]);
        }
        param.query = param.query.concat(arr);
      }
      if (this.searchData.query != [] || this.searchData.query.length > 0) {
        param.query = param.query.concat(this.searchData.query);
      }
      this.searchData.task_id
        ? (param.task_id = this.searchData.task_id)
        : null;
      this.searchData.time_range
        ? (param.time_range = this.searchData.time_range)
        : null;
      if ("flow/fuzzy".includes(this.searchData.type)) {
        delete param.target_filter;
      }
      // 配合后端，在前端进行有标签的情况下的参数序列化
      param.aggr_query = false;
      const arr = {
        and: [],
        not: [],
      };
      for (let i = 0; i < param.query.length; i++) {
        for (let j = 0; j < param.query[i].search.length; j++) {
          if (param.query[i].bool_search === "and") {
            if (param.query[i].search[j].target === "Labels") {
              arr.and.push(param.query[i].search[j].val);
            }
          }
          if (param.query[i].bool_search === "not") {
            if (param.query[i].search[j].target === "Labels") {
              arr.not.push(param.query[i].search[j].val);
            }
          }
        }
      }
      param.tag_query = arr;
      param.query_cn = this.searchData.query_cn;
      this.sessionParam = param;
    },
    formatTagsWidth() {
      this.tagsWidth = "110px";
      let tagsLength = 0;
      this.connTableData.forEach((item) => {
        tagsLength =
          item.tagName.length > tagsLength ? item.tagName.length : tagsLength;
      });
      if (tagsLength > 11) {
        this.tagsWidth = (tagsLength * 10 > 230 ? 230 : tagsLength * 10) + "px";
      } else {
        this.tagsWidth = "110px";
      }
    },
    async initData() {
      this.formatParam();
      await getSessionList(this.sessionParam).then((res) => {
        this.tableLoading = false;
        if (res.err === 0) {
          this.total_real = res.data.total;
          this.total = res.data.total > 10000 ? 10000 : res.data.total;

          this.$emit(
            "update:fatherValue",
            res.data.records.length > 0 ? res.data.total : 0
          );
          const SessionId = [];
          const data = res.data.records || [];
          if (data.length) {
            this.showfoot = true;
            this.alltable.session_list = [];
            this.limit100 = this.formatList(data);
            this.pageRange = [
              Math.floor((this.currentPage - 1) / 10) * 10 + 1,
              Math.floor((this.currentPage - 1) / 10) * 10 +
                Math.ceil(data.length / this.page_size),
            ];
            this.connTableData = this.limit100.slice(0, this.page_size);
            this.connTableData.forEach((val) => {
              const item = {};
              item.session_id = val.session_id;
              item.batch_id = String(val.batch_id);
              (item.start_time = Date.parse(val.HandleBeginTime) / 1000),
              (item.end_time = Date.parse(val.HandleEndTime) / 1000),
              (item.thread_id = val.ThreadId + ""),
              (item.first_proto = val.FirstProto);
              this.alltable.session_list.push(item);
            });
            this.formatTagsWidth();
          } else {
            this.limit100 = [];
            this.pageRange = [this.currentPage, this.currentPage];
            this.connTableData = [];
            return;
          }
          // }
        } else {
          this.$message.error(res.msg);
          this.limit100 = [];
          this.pageRange = [this.currentPage, this.currentPage];
          this.connTableData = [];
          this.total = 0;
          this.$emit("update:fatherValue", 0);
        }
      });
    },
    formatList(arr) {
      if (!(arr instanceof Array)) {
        arr = [];
      }
      const newArr = arr.map((v) => {
        let sIPLocation = [];
        if (v.sIpCountry && v.sIpCountry != "Unknown") {
          sIPLocation.push(v.sIpCountry);
        }
        if (v.sIpCity && v.sIpCity != "Unknown") {
          sIPLocation.push(v.sIpCity);
        }
        if (sIPLocation) {
          sIPLocation = sIPLocation.join("-");
        } else {
          sIPLocation = "";
        }
        let dIPLocation = [];
        if (v.dIpCountry && v.dIpCountry != "Unknown") {
          dIPLocation.push(v.dIpCountry);
        }
        if (v.dIPCity && v.dIPCity != "Unknown") {
          dIPLocation.push(v.dIPCity);
        }
        if (dIPLocation) {
          dIPLocation = dIPLocation.join("-");
        } else {
          dIPLocation = "";
        }
        let ipProtocol = "";
        if (v.IPPro || v.IPPro == 0) {
          if (v.IPPro >= 134 && v.IPPro <= 254) {
            ipProtocol = "";
          } else {
            if (this.$store.state.long.Dict.protocol_type) {
              ipProtocol =
                this.$store.state.long.Dict.protocol_type[v.IPPro]
                  .protocol_type;
            } else {
              ipProtocol = "空";
            }
          }
        } else {
          ipProtocol = "";
        }
        let appProtocol = "";
        if (v.AppId || v.AppId == 0) {
          appProtocol = this.$store.getters.dictionaries.app_value[v.AppId];
        } else {
          appProtocol = "";
        }
        let is_in_d = false;
        for (let i = 0; i < this.download_list_sessionid.length; i++) {
          if (v.SessionId == this.download_list_sessionid[i]) {
            is_in_d = true;
          }
        }
        let Proxy_IP = "无代理";
        // eslint-disable-next-line no-prototype-builtins
        if (v.hasOwnProperty("ProxyIP")) {
          if (v.ProxyIP) {
            Proxy_IP = v.ProxyIP;
          }
        }
        const tagName = "";
        if (v.tagText && v.tagText.length) {
          v.tagText = v.tagText.filter((n) => n);
          for (const i in v.tagText) {
            if (
              v.tagText[i].black_list >= 1 &&
              v.tagText[i].black_list <= 100 &&
              v.tagText[i].white_list !== 100
            ) {
              if (v.tagText[i].black_list >= 80) {
                v.tagText[i].type = "danger";
              } else {
                v.tagText[i].type = "warning";
              }
            }
            if (
              v.tagText[i].white_list >= 1 &&
              v.tagText[i].white_list <= 100 &&
              v.tagText[i].black_list === 0
            ) {
              if (v.tagText[i].white_list === 100) {
                v.tagText[i].type = "success";
              } else {
                v.tagText[i].type = "";
              }
            }
            if (
              v.tagText[i].white_list === 0 &&
              v.tagText[i].black_list === 0
            ) {
              v.tagText[i].type = "info";
            }
          }
          v.tagText.sort((a, b) => {
            if (a.black_list > b.black_list) {
              return b.black_list - a.black_list;
            } else if (a.black_list == 0 && b.black_list == 0) {
              return b.white_list - a.white_list;
            }
          });
        }
        return {
          task_id: v.task_id,
          taskID: v.taskName.task_id,
          es_index: v.es_index,
          AppName: v.AppName,
          downloaded: v.downloaded, // 判断加入全量列表是否 置灰
          FirstProto: v.FirstProto, // 首层协议ID
          batch_id: v.es_key,
          taskname: v.taskName,
          session_id: v.SessionId,
          CreateTime: v.CreateTime,
          Proxy_IP,
          Proxy_Port: v.ProxyPort,
          src_ip: v.sIp,
          srcIPAddress: sIPLocation,
          src_port: parseInt(v.sPort),
          dst_ip: v.dIp,
          dstIPAddress: dIPLocation,
          dst_port: parseInt(v.dPort),
          ippro: parseInt(v.IPPro),
          ipProtocol: ipProtocol,
          ProName: v.ProName,
          app_id: v.AppId,
          appProtocol: appProtocol,
          sBytes: formatNumber(v.pkt ? v.pkt.sPayloadBytes || 0 : 0),
          dBytes: formatNumber(v.pkt ? v.pkt.dPayloadBytes || 0 : 0),
          csvsbytes: v.pkt.sPayloadBytes + "B",
          csvdbytes: v.pkt.dPayloadBytes + "B",
          startTimeInt: v.StartTime,
          begin_time: v.StartTime ? parseTime(v.StartTime) : "",
          endTimeInt: v.EndTime,
          end_time: v.EndTime ? parseTime(v.EndTime) : "",
          Duration: getFormatDate(v.Duration || 0),
          Duration1: v.Duration,
          ThreadId: v.ThreadId,
          labels: v.Labels ? v.Labels : [],
          tags: v.tagText,
          tagName,
          device_id: v.DeviceID || null,
          is_in_download: is_in_d,
          first_proto: v.FirstProto || 0,
          dMaxHopCount: v.dMaxHopCount,
          sFirstIP: v.sIp,
          FirstSender: v.FirstSender,
          RuleInfor_RuleNum: v.RuleInfor ? v.RuleInfor.RuleNum : "--", // 命中规则数量
          RuleInfor_Level: v.RuleInfor ? v.RuleInfor.Level : "--", //  规则最高等级
          pktProNum: v.pkt ? (v.pkt.ProNum ? v.pkt.ProNum : "--") : "--", // 已知协议包数
          pktUnkProNum: v.pkt
            ? v.pkt.UnkProNum
              ? v.pkt.UnkProNum
              : "--"
            : "--", // 未知包数
          TotalBytes: v.TotalBytes, // 总字节
          sMac: v.sMac, // 源mac
          sTCPFinger: v.sTCPFinger ? v.sTCPFinger : "--", // 源tcp指纹
          sTCPFingerInfor: v.sTCPFingerInfor, // 指纹详情
          sSSLFinger: v.sSSLFinger ? v.sSSLFinger : "--", //   SSl 指纹
          sHTTPFinger: v.sHTTPFinger ? v.sHTTPFinger : "--", // 源 http指纹
          sIpCity: v.sIpCity ? v.sIpCity : "--", // 源ip所在城市
          sIpCountry: v.sIpCountry ? v.sIpCountry : "--", // 源IP所在国家
          sIpLatitude: v.sIpLatitude ? v.sIpLatitude : "--", //   所在维度
          sIpLongitude: v.sIpLongitude ? v.sIpLongitude : "--", // 所在经度
          sIpSubdivisions: v.sIpSubdivisions ? v.sIpSubdivisions : "--", // 所在省份
          pktsMaxLen: v.pkt ? (v.pkt.sMaxLen ? v.pkt.sMaxLen : "--") : "--",
          pktsNum: v.pkt ? (v.pkt.sNum ? v.pkt.sNum : "--") : "--",
          pktsPayloadNum: v.pkt
            ? v.pkt.sPayloadNum
              ? v.pkt.sPayloadNum
              : "--"
            : "--",
          pktsPayloadBytes: v.pkt
            ? v.pkt.sPayloadBytes
              ? v.pkt.sPayloadBytes
              : "--"
            : "--",
          pktsPSHNum: v.pkt ? (v.pkt.sPSHNum ? v.pkt.sPSHNum : "--") : "--",
          pktsFINNum: v.pkt ? (v.pkt.sFINNum ? v.pkt.sFINNum : "--") : "--",
          pktsRSTNum: v.pkt ? (v.pkt.sRSTNum ? v.pkt.sRSTNum : "--") : "--",
          pktsSYNNum: v.pkt ? (v.pkt.sSYNNum ? v.pkt.sSYNNum : "--") : "--",
          pktsSYNBytes: v.pkt
            ? v.pkt.sSYNBytes
              ? v.pkt.sSYNBytes
              : "--"
            : "--",
          sInitialTTL: v.sInitialTTL, //  目的 mac
          sMinHopCount: v.sMinHopCount ? v.sMinHopCount : "--", // 目的 TCP指纹
          sMaxHopCount: v.sMaxHopCount,
          dMac: v.dMac,
          dTCPFinger: v.dTCPFinger ? v.dTCPFinger : "--",
          dTCPFingerInfor: v.dTCPFingerInfor, // /  对象
          dSSLFinger: v.dSSLFinger ? v.dSSLFinger : "--",
          dHTTPFinger: v.dHTTPFinger ? v.dHTTPFinger : "--",
          dIpCity: v.dIpCity ? v.dIpCity : "--",
          dIpCountry: v.dIpCountry ? v.dIpCountry : "--",
          dIpLatitude: v.dIpLatitude ? v.dIpLatitude : "--",
          dIpLongitude: v.dIpLongitude ? v.dIpLongitude : "--",
          dIpSubdivisions: v.dIpSubdivisions ? v.dIpSubdivisions : "--",
          pktdMaxLen: v.pkt ? (v.pkt.dMaxLen ? v.pkt.dMaxLen : "--") : "--",
          pktdNum: v.pkt ? (v.pkt.dNum ? v.pkt.dNum : "--") : "--",
          pktdPayloadNum: v.pkt
            ? v.pkt.dPayloadNum
              ? v.pkt.dPayloadNum
              : "--"
            : "--",
          pktdPayloadBytes: v.pkt
            ? v.pkt.dPayloadBytes
              ? v.pkt.dPayloadBytes
              : "--"
            : "--",
          pktdPSHNum: v.pkt ? (v.pkt.dPSHNum ? v.pkt.dPSHNum : "--") : "--",
          pktdFINNum: v.pkt ? (v.pkt.dFINNum ? v.pkt.dFINNum : "--") : "--",
          pktdRSTNum: v.pkt ? (v.pkt.dRSTNum ? v.pkt.dRSTNum : "--") : "--",
          pktdSYNNum: v.pkt ? (v.pkt.dSYNNum ? v.pkt.dSYNNum : "--") : "--",
          pktdSYNBytes: v.pkt
            ? v.pkt.dSYNBytes
              ? v.pkt.dSYNBytes
              : "--"
            : "--",
          pktdTTLMax: v.pkt ? (v.pkt.dTTLMax ? v.pkt.dTTLMax : "--") : "--",
          pktdTTLMin: v.pkt ? (v.pkt.dTTLMin ? v.pkt.dTTLMin : "--") : "--",
          dInitialTTL: v.dInitialTTL,
          dMinHopCount: v.dMinHopCount,
          HandleEndTime: v.HandleEndTime ? parseTime(v.HandleEndTime) : "--", // 处理起始时间
          HandleBeginTime: v.HandleBeginTime
            ? parseTime(v.HandleBeginTime)
            : "--", // 处理结束时间
          Hkey: v.Hkey,
        };
      });
      return newArr;
    },
    indexMethod(index) {
      return (this.currentPage - 1) * this.page_size + index + 1;
    },
    // 打开会话详情抽屉页面
    async opensessionDrawer(data, type) {
      this.tag_target_type = type;
      this.sessionDrawer = true;
      this.sessionDetailLoading = true;
      this.sessiondata = data.row;
      const param1 = {
        sub_type: "basic",
        search: data.row.session_id,
        es_index: data.row.es_index,
      };
      const param2 = {
        sub_type: "session_log",
        search: data.row.session_id,
        Hkey: data.row.Hkey,
        es_index: data.row.es_index,
      };
      const param3 = {
        sub_type: "session_meta_data",
        search: data.row.session_id,
        Hkey: data.row.Hkey,
        limit: 50,
        app_name: data.row.AppName,
        es_index: data.row.es_index,
      };
      const param4 = {
        sub_type: "packet_histogram",
        search: data.row.session_id,
        Hkey: data.row.Hkey,
        limit: 50,
      };
      // 请求基础五元组信息
      await getsessiondata(param1).then((res) => {
        if (res.err == 0) {
          this.$store.commit("conversational/sessionDetailData", res.data);
          this.numdata++;
          this.addnum();
        }
      });
      // 请求会话日志
      await getsessiondata(param2).then((res) => {
        if (res.err == 0) {
          if (res.data == "查询会话详情信息为空") {
            this.$store.commit("conversational/sessionDetailLog", {});
          } else {
            this.$store.commit("conversational/sessionDetailLog", res.data);
          }
          this.numdata++;
          this.addnum();
        }
      });
      // 请求协议元数据
      await getsessiondata(param3).then((res) => {
        if (res.err == 0) {
          this.$store.commit("conversational/sessionDetailAgreement", res.data);
          this.numdata++;
          this.addnum();
        }
      });
      // 请求包分析数据
      await getsessiondata(param4).then((res) => {
        if (res.err == 0) {
          this.$store.commit("conversational/sessionDetailHistogram", res.data);
          this.numdata++;
          this.addnum();
        }
      });
    },
    // 打开ip详情
    opneipDetail(srcip, type) {
      this.detailIp = srcip;
      this.ipLoading = true;
      this.ipDetailDrawer = true;
      this.tag_target_type = type;
    },

    closeDrawer() {
      this.ipDetailDrawer = false;
    },

    addnum() {
      if (this.numdata == 4) {
        this.sessionDetailLoading = false;
        this.numdata = 0;
      }
    },
    // 关闭会话详情抽屉页面
    sessionclose() {
      this.$store.commit("conversational/sessionDetailData", {});
      this.$store.commit("conversational/sessionDetailLog", {});
      this.$store.commit("conversational/sessionDetailAgreement", {});
      this.$store.commit("conversational/sessionDetailHistogram", {});
    },
    // 客户端IP正向检索
    forwardsort(data, sign) {
      data.row.value = "connectinfo";
      if (sign == "ip") {
        data.row.sort = false;
        data.row.sortname = sign;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "sip") {
        data.row.sort = false;
        data.row.sortname = sign;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "dip") {
        data.row.sort = false;
        data.row.sortname = sign;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
    },
    // 客户端IP反向检索
    reversesort(data, sign) {
      data.row.value = "connectinfo";
      if (sign == "ip") {
        data.row.sort = true;
        data.row.sortname = sign;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "sip") {
        data.row.sort = true;
        data.row.sortname = sign;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
      if (sign == "dip") {
        data.row.sort = true;
        data.row.sortname = sign;
        this.$store.commit("conversational/getSessionQuickSearch", data.row);
      }
    },
    // 时间快速检索
    timesearch(sign, data) {
      // 前1分钟
      if (sign == "adv1") {
        const time = moment(data.row.begin_time)
          .subtract(1, "minutes")
          .format("YYYY-MM-DD HH:mm:ss");
        const timedata = [
          moment(time).valueOf(),
          moment(data.row.begin_time).valueOf(),
        ];
        this.$store.commit("conversational/getTimeQuickSearch", timedata);
      }
      // 前5分钟
      if (sign == "adv5") {
        const time = moment(data.row.begin_time)
          .subtract(5, "minutes")
          .format("YYYY-MM-DD HH:mm:ss");
        const timedata = [
          moment(time).valueOf(),
          moment(data.row.begin_time).valueOf(),
        ];
        this.$store.commit("conversational/getTimeQuickSearch", timedata);
      }
      // 前10分钟
      if (sign == "adv10") {
        const time = moment(data.row.begin_time)
          .subtract(10, "minutes")
          .format("YYYY-MM-DD HH:mm:ss");
        const timedata = [
          moment(time).valueOf(),
          moment(data.row.begin_time).valueOf(),
        ];
        this.$store.commit("conversational/getTimeQuickSearch", timedata);
      }
      // 后1分钟
      if (sign == "ret1") {
        const time = moment(data.row.begin_time)
          .add(1, "minutes")
          .format("YYYY-MM-DD HH:mm:ss");
        const timedata = [
          moment(data.row.begin_time).valueOf(),
          moment(time).valueOf(),
        ];
        this.$store.commit("conversational/getTimeQuickSearch", timedata);
      }
      // 后5分钟
      if (sign == "ret5") {
        const time = moment(data.row.begin_time)
          .add(5, "minutes")
          .format("YYYY-MM-DD HH:mm:ss");
        const timedata = [
          moment(data.row.begin_time).valueOf(),
          moment(time).valueOf(),
        ];
        this.$store.commit("conversational/getTimeQuickSearch", timedata);
      }
      // 后10分钟
      if (sign == "ret10") {
        const time = moment(data.row.begin_time)
          .add(10, "minutes")
          .format("YYYY-MM-DD HH:mm:ss");
        const timedata = [
          moment(data.row.begin_time).valueOf(),
          moment(time).valueOf(),
        ];
        this.$store.commit("conversational/getTimeQuickSearch", timedata);
      }
    },
    // 正向快速检索
    forwardSearch(data, sign) {
      data.value = "connectinfo";
      data.sort = false;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 反向快速检索
    reverseSearch(data, sign) {
      data.value = "connectinfo";
      data.sort = true;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
  },
};
</script>

<style lang="scss" scoped>
.sessionList-box {
  &-top {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-l {
      ::v-deep {
        .el-button {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 94px;
          height: 32px;
        }

        .el-checkbox-group {
          display: flex;
          flex-direction: column;
        }
      }
    }

    &-r {
      display: flex;
      align-items: center;

      ::v-deep {
        .el-button {
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .listnum {
        font-weight: 400;
        font-size: 14px;
        color: #767684;
        margin-right: 8px;
      }

      .Partdown {
        .el-button {
          width: 88px;
        }

        margin-right: 8px;
      }

      .alldown {
        cursor: not-allowed;
        position: relative;
        width: 119px;
        height: 32px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        display: flex;
        align-items: center;
        color: #c0c4cc;
        margin-right: 16px;
        &-r {
          width: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
        }
      }

      .qlactive {
        cursor: pointer;
        position: relative;
        width: 119px;
        height: 32px;
        border: 1px solid #8abcff;
        border-radius: 4px;
        display: flex;
        align-items: center;
        color: #4a97ff;
        margin-right: 8px;

        &-l {
          font-weight: 400;
          font-size: 14px;
          width: 88px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #8abcff;
        }

        &-r {
          width: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
        }
      }

      .qlactive:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }
      .globaldown {
        .el-button {
          width: 108px;
        }
      }
    }
  }

  &-form {
    margin-top: 16px;

    .taglist {
      display: flex;
      flex-wrap: wrap;
      margin: 5px 0;

      .tagbox {
        margin-right: 4px;

        .tag {
          height: 20px;
          font-size: 12px;
          line-height: 20px;
          cursor: pointer;
        }
      }
    }

    .sortbox {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .top {
        margin-right: 5px;
      }
      .down {
        width: 10px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .sorttoole {
        display: none;
        padding-bottom: 5px;
        color: #116ef9;
      }
    }

    .sortbox:hover {
      .sorttoole {
        display: block;
        cursor: pointer;
      }
    }

    .sorttime {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .top {
        margin-right: 5px;
      }

      .down {
        width: 10px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .sorttoole {
        display: none;
        padding-bottom: 5px;
        color: #116ef9;
      }
    }

    .sorttime:hover {
      .sorttoole {
        display: block;
        cursor: pointer;
      }
    }

    ::v-deep {
      .el-tag {
        background: #e7f0fe;
        border-radius: 2px;
        color: #1b428d;
      }

      .el-tag.el-tag--warning {
        background: #f9eddf;
        border-radius: 2px;
        color: #b76f1e;
      }

      .el-tag.el-tag--success {
        background: #e0f5ee;
        border-radius: 2px;
        color: #006157;
      }

      .el-tag.el-tag--danger {
        background: #fce7e7;
        border-radius: 2px;
        color: #a41818;
      }

      .el-tag.el-tag--info {
        background: #f2f3f7;
        border-radius: 2px;
        color: #2c2c35;
      }
    }
  }

  &-foot {
    position: sticky;
    bottom: 0px;
    z-index: 999;
    padding: 10px 0;
    width: 100%;
    background: #ffffff;
    border-radius: 8px;

    &-top {
      margin-bottom: 10px;
    }

    &-down {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-l {
        font-size: 14px;
        color: #767684;

        span {
          color: #000;
        }
      }
    }
  }

  .sessionDrawerbox {
    ::v-deep {
      .el-drawer {
        overflow: scroll;
      }

      .el-drawer__header {
        font-weight: 500;
        font-size: 14px;
        color: #2c2c35;
        margin: 0;
        padding: 10px 16px;
      }
    }
  }
}
</style>

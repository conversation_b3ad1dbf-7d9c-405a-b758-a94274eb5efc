import protocolInforlogin from "./protocolInforlogin";
import protocolInfotelnet from "./protocolInfotelnet";
import protocolInfossh from "./protocolInfossh";
import protocolInfordp from "./protocolInfordp";
import protocolInfovnc from "./protocolInfovnc";
import protocolInfoxdmcp from "./protocolInfoxdmcp";
export {
  protocolInforlogin,
  protocolInfotelnet,
  protocolInfossh,
  protocolInfordp,
  protocolInfovnc,
  protocolInfoxdmcp,
};


<template>
  <div class="list">
    <div class="list-top">
      <div class="list-top-l">已选择<span>0</span>条</div>
      <div class="list-top-r">
        <el-popover
          placement="bottom"
          trigger="click"
          :visible-arrow="false"
          popper-class="alldownbox"
        >
          <div slot="reference" class="qlactive">
            <div class="alldown-l">批量检索</div>
            <div class="alldown-r">
              <svg-icon icon-class="del-down2" />
            </div>
          </div>
          <div class="alldownfoot">
            <el-button
              class="alldownfoot-t"
              @click="searchforwardSearch(search_list, 'AllDomain')"
            >
              正向检索
            </el-button>
            <el-button
              class="alldownfoot-d"
              @click="searchreverseSearch(search_list, 'AllDomain')"
            >
              反向检索
            </el-button>
          </div>
        </el-popover>

        <div class="globaldown">
          <el-tooltip
            content="到“下载列表”的“日志导出”中下载"
            placement="top"
            effect="dark"
          >
            <el-button @click="logderive">
              <i class="el-icon--left">
                <svg-icon icon-class="globaldown" />
              </i>
              日志导出
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </div>
    <div class="list-listview">
      <el-table
        ref="domaintable"
        :data="connTableData"
        tooltip-effect="dark"
        stripe
        :default-sort="{ prop: 'begin_time', order: 'ascending' }"
        show-overflow-tooltip
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="50" fixed></el-table-column>
        <el-table-column
          :index="indexMethod"
          prop="num"
          label="序号"
          type="index"
          width="50"
          fixed
        />
        <el-table-column
          label="域名"
          prop="DOMAIN"
          width="120"
          fixed
          sortable="custom"
        >
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <el-tooltip
                  :disabled="!scope.row.domain"
                  class="item"
                  effect="light"
                  placement="top"
                  popper-class="sessionidTooltip"
                >
                  <div slot="content">
                    {{ scope.row.domain }}
                  </div>
                  <span
                    style="color: #116ef9"
                    :style="
                      ipverify_dns(scope.row.domain).sign
                        ? 'cursor: not-allowed'
                        : 'cursor: pointer'
                    "
                    @click="
                      ipverify_dns(scope.row.domain).sign
                        ? ''
                        : openDomaininfo(scope.row.domain, 3)
                    "
                  >
                    {{
                      ipverify_dns(scope.row.domain).host
                        ? ipverify_dns(scope.row.domain).host.slice(0, 6) +
                          "..."
                        : "--"
                    }}
                  </span>
                </el-tooltip>
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'AllDomain')">
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'AllDomain')">
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="labels" width="300" label="标签">
          <template slot-scope="scope">
            <div class="taglist">
              <span
                v-for="item in scope.row.labels"
                :key="item.tag_id"
                class="tagbox"
              >
                <el-popover
                  placement="bottom"
                  width="200"
                  trigger="click"
                  popper-class="sortpopover"
                >
                  <el-tag slot="reference" class="tag" :type="item.type">
                    <div style="cursor: pointer">{{ item.tag_text }}</div>
                  </el-tag>
                  <div class="sortbtn">
                    <el-button @click="forwardSearch(item, 'Labels')">
                      <svg-icon
                        icon-class="sort-up"
                        style="margin-right: 15px"
                      />正向检索
                    </el-button>
                    <el-button @click="reverseSearch(item, 'Labels')">
                      <svg-icon
                        icon-class="sort-down"
                        style="margin-right: 15px"
                      />反向检索
                    </el-button>
                  </div>
                </el-popover>
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column width="100" prop="blackList" label="威胁权重" />
        <el-table-column
          width="150"
          show-overflow-tooltip
          prop="fdomain"
          label="锚域名"
        />
        <el-table-column
          width="120"
          show-overflow-tooltip
          prop="whoIs"
          label="WhoIS"
        />
        <el-table-column
          width="120"
          show-overflow-tooltip
          prop="ipHeart"
          label="查询热度"
        >
          <template slot="header">
            查询热度
            <el-tooltip
              popper-class="atooltip"
              effect="dark"
              content="访问该域名的IP的热度，热度越高，访问该域名的IP越多"
              placement="top"
            >
              <i class="el-icon-warning-outline" style="margin-left: 6px"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          width="200"
          show-overflow-tooltip
          prop="relatedIps"
          label="反查IP"
        >
          <template slot="header">
            反查IP
            <el-tooltip
              popper-class="atooltip"
              effect="dark"
              content="域名指向的IP （服务端）"
              placement="top"
            >
              <i class="el-icon-warning-outline" style="margin-left: 6px"></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span
              v-if="scope.row.relatedIps"
              class="relatedip-style"
              @click="SHOW_RELATEDIPS(scope.row.relatedIps)"
            >
              {{ scope.row.relatedIps }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          width="180"
          prop="cnameToMe"
          label="CName查询该域名数量"
        />
        <el-table-column
          width="180"
          prop="cnameToOther"
          label="CName指向该域名数量"
        />
        <el-table-column width="120" prop="respondTypes" label="回应类型" />
        <el-table-column width="120" label="出现位置" align="center">
          <el-table-column prop="location.dns" label="DNS查询" width="120">
          </el-table-column>
          <el-table-column prop="location.http" label="HTTP" width="120">
          </el-table-column>
          <el-table-column prop="location.ssl" label="SSL" width="120">
          </el-table-column>
          <el-table-column prop="location.cert" label="证书" width="120">
          </el-table-column>
        </el-table-column>
        <el-table-column width="170" prop="data11" label="关联告警">
          <template slot="header">
            关联告警
            <el-tooltip
              popper-class="atooltip"
              effect="dark"
              content="告警对象是该域名的告警个数"
              placement="top"
            >
              <i class="el-icon-warning-outline" style="margin-left: 6px"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column width="200" prop="brotherNum" label="兄弟域名数量">
          <template slot="header">
            兄弟域名数量
            <el-tooltip
              popper-class="atooltip"
              effect="dark"
              content="父域名相同的子域名个数"
              placement="top"
            >
              <i class="el-icon-warning-outline" style="margin-left: 6px"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column width="200" prop="count" label="会话数量" />
        <el-table-column width="200" prop="firstTime" label="首次发现时间" />
        <el-table-column width="200" prop="lastTime" label="末次发现时间" />
      </el-table>
    </div>
    <div class="list-foot">
      <div class="list-foot-top">
        <tablescroll :table-ref="$refs.domaintable"></tablescroll>
      </div>
      <div class="list-foot-down">
        <div class="list-foot-down-l">*会话展示上限为<span>10,000</span>条</div>

        <div class="list-foot-down-r">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="page_size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <div class="Drawerbox">
      <Domaindetails
        :domain-list="domainList"
        :tag_target_type="tag_target_type"
        :domain-detail-drawer="DomainDetailDrawer"
        @DomaincloseDrawer="DomaincloseDrawer"
      />
    </div>
    <el-drawer
      title="反查IP"
      :visible.sync="idsDrawer"
      direction="rtl"
      class="idsDrawer"
    >
      <el-table
        :data="relatedIps"
        class="idsDrawer-table"
        style="width: 100%"
        stripe
        border
      >
        <el-table-column prop="IP" label="IP">
          <template #default="{ row }">
            {{ row }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button type="text" @click="handleCopy(row)">复制</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>
  </div>
</template>

<script>
import Domaindetails from "../components/details/Domaindetails.vue";
import tablescroll from "../../../components/TableScroll/idnex.vue";
import {
  GetDomianlist,
  GetDomainInfo,
  alllogderive,
} from "@/api/sessionList/sessionlist";
export default {
  name: "IPList",
  components: {
    Domaindetails,
    tablescroll,
  },
  props: ["searchData", "fatherValue"],
  data() {
    return {
      // 分页参数
      currentPage: 1,
      page_size: 10,
      total: 0,
      // 处理过后的请求IP列表参数
      sessionParam: {},
      order: {
        order_prop: "begin_time",
        order_field: "EndTime",
        asc: true,
      },
      drawer: false,
      direction: "rtl",
      connTableData: [],
      // 获取域名列表
      domainList: {},
      search_list: {},
      activenum: 0,
      // Domain详情抽屉
      DomainDetailDrawer: false,
      idsDrawer: false,
      relatedIps: [],
      tag_target_type: 9999,
    };
  },
  watch: {
    searchData: {
      handler(val) {
        if(Object.keys(val).length){
          this.currentPage = 1;
          this.initData();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 反查ip复制功能
    handleCopy(text) {
      navigator.clipboard.writeText(text);
      this.$message.success(`${text}已复制到剪切板`);
    },
    DomaincloseDrawer() {
      this.DomainDetailDrawer = false;
    },
    handleClose(done) {
      done();
    },
    // 排序判断
    handleSortChange(val) {
      console.log(val);
      this.order.order_prop = val.prop;
      this.order.order_field = val.prop;
      if (val.prop === "DOMAIN") {
        this.order.order_field = "DOMAIN";
      }
      if (val.prop == "begin_time") {
        this.order.order_field = "StartTime";
      }
      if (val.prop == "dst_port") {
        this.order.order_field = "dPort";
      }
      if (val.prop == "src_port") {
        this.order.order_field = "sPort";
      }
      if (val.prop == "src_ip") {
        this.order.order_field = "sIp";
      }
      if (val.prop == "ippro") {
        this.order.order_field = "IPPro";
      }
      if (val.prop == "dst_ip") {
        this.order.order_field = "dIp";
      }
      if (val.prop == "end_time") {
        this.order.order_field = "EndTime";
      }
      if (val.prop == "sBytes") {
        this.order.order_field = "pkt.sPayloadBytes";
      }
      if (val.prop == "dBytes") {
        this.order.order_field = "pkt.dPayloadBytes";
      }
      if (val.order == "ascending") {
        this.order.asc = true;
      } else if (val.order == "descending") {
        this.order.asc = false;
      } else {
        this.order.order_field = "";
      }
      this.limit100 = [];
      this.pageRange = [1, 1];
      this.initData();
    },
    // 处理请求域名列表参数
    formatParam() {
      let param = {
        current_page: this.currentPage,
        page_size: this.page_size,
        order_field: this.order.order_field,
        asc: this.order.asc,
        query: [],
      };
      if (this.searchData.query != [] || this.searchData.query.length > 0) {
        param.query = param.query.concat(this.searchData.query);
      }
      this.searchData.task_id
        ? (param.task_id = this.searchData.task_id)
        : null;
      this.searchData.time_range
        ? (param.time_range = this.searchData.time_range)
        : null;
      if ("flow/fuzzy".includes(this.searchData.type)) {
        delete param.target_filter;
      }
      param.aggr_query = false;
      for (let i of param?.query) {
        for (let j of i?.search) {
          if (j.target === "Labels") {
            param.aggr_query = true;
          }
        }
      }
      let arr = {
        and: [],
        not: [],
      };
      for (let i = 0; i < param.query.length; i++) {
        for (let j = 0; j < param.query[i].search.length; j++) {
          if (param.query[i].bool_search === "and") {
            if (param.query[i].search[j].target === "Labels") {
              arr.and.push(param.query[i].search[j].val);
            }
          }
          if (param.query[i].bool_search === "not") {
            if (param.query[i].search[j].target === "Labels") {
              arr.not.push(param.query[i].search[j].val);
            }
          }
        }
      }
      param.tag_query = arr;
      this.sessionParam = param;
    },
    initData() {
      this.formatParam();
      GetDomianlist(this.sessionParam).then((res) => {
        if (res.err === 0) {
          this.total_real = res.data.total;
          this.total = res.data.total > 10000 ? 10000 : res.data.total;
          this.$emit(
            "update:fatherValue",
            res.data.records.length > 0 ? res.data.total : 0
          );
          this.connTableData = this.listfn(res.data.records);
        }
      });
    },
    openDomaininfo(domainID, type) {
      this.domainList = {};
      GetDomainInfo(`str=${domainID}`).then((res) => {
        if (res.err === 0) {
          this.domainList = this.datainfoDandle(res.data);
          this.DomainDetailDrawer = true;
          this.tag_target_type = type;
        }
      });
    },
    // 对列表进行数据处理
    listfn(val) {
      let tagarr = this.$store.state.conversational.taglist01;
      val.forEach((item) => {
        let newtagarr = [];
        item.firstTime = this.formatDate(item.firstTime);
        item.lastTime = this.formatDate(item.lastTime);
        // 回应类型数组转字符串
        item.respondTypes = item.respondTypes.join("--");
        //出现位置转字符串
        item.location.dns = `${item.location.dns ? "是" : "否"}`;
        item.location.http = `${item.location.http ? "是" : "否"}`;
        item.location.ssl = `${item.location.ssl ? "是" : "否"}`;
        item.location.cert = `${item.location.cert ? "是" : "否"}`;
        if (item.labels != null && item.labels != []) {
          item.labels.forEach((labelitem) => {
            tagarr.forEach((tagitem) => {
              if (labelitem == tagitem.tag_id) {
                newtagarr.push(tagitem);
              }
            });
          });
        }
        let concatArr = newtagarr.concat(item.fLabels);
        item.labels = this.formatTag(concatArr);
        item.relatedIps = item.relatedIps.join("，");
      });
      return val;
    },
    //获取到域名数据进行处理
    datainfoDandle(val) {
      let data = {};
      data.domain = val.domain;
      data.alexa_rank = val.alexaRank;
      data.taskname = val.taskNames[0];

      if (val.blackList == 0) {
        console.log("安全安全安全安全");
        data.blackList = val.blackList = "安全";
      } else if (val.blackList >= 1 && val.blackList < 60) {
        data.blackList = val.blackList = "未知";
      } else if (val.blackList >= 60 && val.blackList < 90) {
        data.blackList = val.blackList = "可疑";
      } else if (val.blackList >= 90 && val.blackList <= 100) {
        data.blackList = val.blackList = "恶意";
      }
      data.labels = [];
      let tag_id_list = this.$store.state.conversational.taglist01;
      console.log(tag_id_list);
      // 获取到标签id列表，对id在标签库进行循环获取完整标签信息
      if (val.labels != null) {
        for (let i = 0; i < tag_id_list.length; i++) {
          for (let j = 0; j < val.labels.length; j++) {
            if (tag_id_list[i].tag_id == val.labels[j]) {
              data.labels.push(tag_id_list[i]);
            }
          }
        }
        data.labels = this.formatTag(data.labels);
      } else {
        data.labels = [];
      }
      data.whoIs = val.whoIs || "--";
      data.brotherNum = val.brotherNum;
      data.clientHeat = val.clientHeat;
      data.fdomain = val.fdomain != null ? val.fdomain : "--";
      data.remarks = val.remarks;
      data.toIpNum = val.toIpNum;
      data.whiteList = val.whiteList;
      let list = [
        {
          name: "Alex排名",
          value: val.alexaRank,
        },
        {
          name: "兄弟域名数量",
          value: val.brotherNum,
        },
        {
          name: "指向IP数量",
          value: val.toIpNum,
        },
        {
          name: "WhoIS",
          value: val.whoIs,
        },
        {
          name: "客户端热度",
          value: val.clientHeat,
        },
        {
          name: "关联证书数量",
          value: val.certNum,
        },
        {
          name: "最早出现时间",
          value: this.formatDate(val.firstTime),
        },
        {
          name: "最后出现时间",
          value: this.formatDate(val.lastTime),
        },
      ];
      data.list = list;
      return data;
    },
    // 时间戳转时间单位秒
    formatDate(val) {
      if (!val) {
        return "";
      }
      let date = new Date(val * 1000);
      let Y = date.getFullYear() + "-";
      let M =
        (date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1) + "-";
      let D =
        (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
      let h =
        (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
      let m =
        (date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) +
        ":";
      let s =
        date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      return Y + M + D + h + m + s;
    },
    handleSizeChange(val) {
      this.limit100 = [];
      this.pageRange = [1, 10];
      this.page_size = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.initData();
    },
    indexMethod(index) {
      return (this.currentPage - 1) * this.page_size + index + 1;
    },
    // 对标签列表进行数据处理
    formatTag(arr) {
      arr.sort((a, b) => {
        if (a.black_list > b.black_list) {
          return b.black_list - a.black_list;
        } else if (a.black_list == 0 && b.black_list == 0) {
          return b.white_list - a.white_list;
        }
      });

      let tags = [];

      tags = arr.map((val) => {
        // console.log(val);
        let type = "";
        if (
          val.black_list >= 1 &&
          val.black_list <= 100 &&
          val.white_list !== 100
        ) {
          if (val.black_list >= 80) {
            type = "danger";
          } else {
            type = "warning";
          }
        }
        if (
          val.white_list >= 1 &&
          val.white_list <= 100 &&
          val.black_list === 0
        ) {
          if (val.white_list === 100) {
            type = "success";
          } else {
            type = "";
          }
        }
        if (val.white_list === 0 && val.black_list === 0) {
          type = "info";
        }
        return {
          tag_text: val.tag_text,
          tagText: val.tag_text,
          type,
          targetType: val.type,
          tag_id: val.tag_id,
          tagID: val.tag_id,
          tag_explain: val.tag_explain,
          attribute_name: val.attribute_name,
          exist: val.exist,
        };
      });
      // 标签去重
      let fjLists = [];
      const res = new Map();
      fjLists = tags.filter(
        (item) => !res.has(item.tagText) && res.set(item.tagText, 1)
      );
      return fjLists;
    },
    // 正向快速检索
    forwardSearch(data, sign) {
      data.sort = false;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 反向快速检索
    reverseSearch(data, sign) {
      data.sort = true;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 批量检索方法正向
    searchforwardSearch(data, sign) {
      if (this.activenum == 0) {
        this.$message({
          message: "请至少选择一条数据检索",
          type: "warning",
        });
        return;
      }
      data.sort = false;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 批量检索方法反向
    searchreverseSearch(data, sign) {
      if (this.activenum == 0) {
        this.$message({
          message: "请至少选择一条数据检索",
          type: "warning",
        });
        return;
      }
      data.sort = true;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 多选方法
    handleSelectionChange(val) {
      this.activenum = val.length;
      console.log(val, "多选结果");
      // console.log(val);
      let newarr = [];
      for (let i in val) {
        newarr.push(val[i].domain);
      }
      let item = {};
      // 数组转为字符串
      let str = newarr.join(",");
      item.str = str;

      console.log(item);
      this.search_list = item;
    },
    // 域名过滤方法
    ipverify_dns(v) {
      let item = {
        host: v,
        sign: false,
      };
      const reg = new RegExp(
        /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
      );
      const pattIp = new RegExp(
        /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/
      );
      const pattIp1 =
        /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/;
      const pattIp2 =
        /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/;
      const DomainIP =
        /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;
      if (pattIp.test(v) || reg.test(v) || pattIp1.test(v) || pattIp2.test(v)) {
        console.log("有一个为真，我就高兴了！！！");
        item.sign = true;
      }
      if (!DomainIP.test(v)) {
        console.log("芜湖！出来结果了");
        item.sign = true;
      } else {
        if (v.match(/\.arpa(.*)/) != null) {
          item.sign = true;
        }
      }
      return item;
    },
    // 反查IP展示
    SHOW_RELATEDIPS(val) {
      this.idsDrawer = true;
      this.relatedIps = val.split("，");
    },
    //日志导出
    logderive() {
      if(!this.connTableData.length){
        this.$message.error('暂无数据可导出');
        return;
      }
      let param = {
        condition: {
          current_page: this.currentPage,
          page_size: this.page_size,
          order_field: this.order.order_field,
          asc: this.order.asc,
          query: [],
          aggr_query: true,
        },
        user_id: 1,
        task_type: 7,
      };
      if (JSON.stringify(this.filterData) != "{}") {
        let arr = [];
        for (let i in this.filterData) {
          arr.push(this.filterData[i]);
        }
        param.condition.query = param.condition.query.concat(arr);
      }
      if (this.searchData.query.length > 0) {
        param.condition.query = param.condition.query.concat(
          this.searchData.query
        );
      }
      this.searchData.task_id
        ? (param.condition.task_id = this.searchData.task_id)
        : null;
      this.searchData.time_range
        ? (param.condition.time_range = this.searchData.time_range)
        : null;

      param.condition.aggr_query = false;
      for (let i of param.condition.query) {
        for (let j of i.search) {
          if (j.target === "Labels") {
            param.condition.aggr_query = true;
          }
        }
      }
      let arr = {
        and: [],
        not: [],
      };
      for (let i = 0; i < param.condition.query.length; i++) {
        for (let j = 0; j < param.condition.query[i].search.length; j++) {
          if (param.condition.query[i].bool_search === "and") {
            if (param.condition.query[i].search[j].target === "Labels") {
              arr.and.push(param.condition.query[i].search[j].val);
            }
          }
          if (param.condition.query[i].bool_search === "not") {
            if (param.condition.query[i].search[j].target === "Labels") {
              arr.not.push(param.condition.query[i].search[j].val);
            }
          }
        }
      }
      param.condition.tag_query = arr;
      alllogderive(param).then((res) => {
        console.log(res);
        if (res.err == 0) {
          this.$store.commit("conversational/setdowndot", true);
          this.$message("成功加入日志导出队列");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-drawer {
    overflow: auto;
  }

  .el-drawer__header {
    margin: 0;
    padding: 16px;
    border-bottom: 1px solid #f2f3f7;

    span {
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      display: flex;
      align-items: center;
      color: #2c2c35;
    }
  }
}

.list {
  &-top {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    &-l {
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #9999a1;
      margin-right: 8px;

      span {
        color: #116ef9;
      }

      ::v-deep {
        .el-button {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 94px;
          height: 32px;
        }

        .el-checkbox-group {
          display: flex;
          flex-direction: column;
        }
      }
    }

    &-r {
      display: flex;
      align-items: center;

      ::v-deep {
        .el-button {
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .Partdown {
        .el-button {
          width: 88px;
        }

        margin-right: 8px;
      }

      .alldown {
        cursor: not-allowed;
        position: relative;
        width: 119px;
        height: 32px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        display: flex;
        align-items: center;
        color: #c0c4cc;
        margin-right: 16px;

        &-l {
          font-weight: 400;
          font-size: 14px;
          width: 88px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #dcdfe6;
        }

        &-r {
          width: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
        }
      }

      .qlactive {
        cursor: pointer;
        position: relative;
        width: 119px;
        height: 32px;
        border: 1px solid #8abcff;
        border-radius: 4px;
        display: flex;
        align-items: center;
        color: #4a97ff;
        margin-right: 8px;

        &-l {
          font-weight: 400;
          font-size: 14px;
          width: 88px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #8abcff;
        }

        &-r {
          width: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
        }
      }

      .qlactive:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }

      .globaldown {
        .el-button {
          width: 108px;
        }
      }
    }
  }

  &-listview {
    margin-top: 12px;

    .sortbox {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      // position: relative;
      .top {
        margin-right: 5px;
      }

      .down {
        width: 10px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .sorttoole {
        display: none;
        padding-bottom: 5px;
        color: #116ef9;
        // position: absolute;
        // top: -2px;
        // right: 0;
      }
    }

    .sortbox:hover {
      .sorttoole {
        display: block;
        cursor: pointer;
      }
    }

    ::v-deep {
      .el-tag {
        background: #e7f0fe;
        border-radius: 2px;
        color: #1b428d;
        margin-bottom: 4px;
      }

      .el-tag.el-tag--warning {
        background: #f9eddf;
        border-radius: 2px;
        color: #b76f1e;
      }

      .el-tag.el-tag--success {
        background: #e0f5ee;
        border-radius: 2px;
        color: #006157;
      }

      .el-tag.el-tag--danger {
        background: #fce7e7;
        border-radius: 2px;
        color: #a41818;
      }

      .el-tag.el-tag--info {
        background: #f2f3f7;
        border-radius: 2px;
        color: #2c2c35;
      }
    }

    .taglist {
      .tagbox {
        margin-right: 4px;
      }
    }
  }

  &-foot {
    position: sticky;
    bottom: 0px;
    z-index: 999;
    padding: 10px 24px;
    width: 100%;
    background: #ffffff;
    border-radius: 8px;

    &-top {
      margin-bottom: 10px;
    }

    &-down {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-bottom: 20;

      &-l {
        font-size: 12px;
        color: #9999a1;

        span {
          color: #000;
        }
      }
    }
  }
}

.relatedIps-row {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 3; //行数
  -webkit-box-orient: vertical;
}

.relatedIps-row:hover {
  color: #409eff;
  text-decoration: underline;
  cursor: pointer;
}
.relatedip-style {
  color: #116ef9;
  &:hover {
    cursor: pointer;
  }
}
.idsDrawer {
  .el-table::before{
    height: 0;
  }
  .idsDrawer-table {
    padding: 16px;

    ::v-deep {
      .el-table__cell {
        padding: 0;
      }
    }
  }
}
</style>
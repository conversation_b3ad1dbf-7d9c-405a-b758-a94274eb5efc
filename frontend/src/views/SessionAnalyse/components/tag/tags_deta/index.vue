<template>
  <!-- 全部标签 -->
  <!-- <div class="tag-box box animate__animated animate__fadeInDown" :style="{ display: display }"> -->
  <div class="tag-box box animate__animated animate__fadeInDown">
    <!-- <div @click="handleClose">
      <svg-icon icon-class="close" class="icon-close" />
    </div> -->
    <div class="tag-box-l">
      <el-tabs v-model="activeName" tab-position="left" type="card" @tab-click="opentagcontent">
        <el-tab-pane name="6" :disabled="tagList.length != 0 ? false : true">
          <span slot="label">会话
            <loading :num="tagList.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane name="0" :disabled="tagList1.length != 0 ? false : true">
          <span slot="label">IP
            <loading :num="tagList1.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList1" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane name="3" :disabled="tagList2.length != 0 ? false : true">
          <span slot="label">域名
            <loading :num="tagList2.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList2" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane name="7" :disabled="tagList3.length != 0 ? false : true">
          <span slot="label">指纹
            <loading :num="tagList3.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList3" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane name="4" :disabled="tagList4.length != 0 ? false : true">
          <span slot="label">证书
            <loading :num="tagList4.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList4" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane name="1" :disabled="tagList5.length != 0 ? false : true">
          <span slot="label">端口
            <loading :num="tagList5.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList5" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane name="2" :disabled="tagList6.length != 0 ? false : true">
          <span slot="label">应用
            <loading :num="tagList6.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList6" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="MAC" name="5" :disabled="tagList7.length != 0 ? false : true">
          <span slot="label">MAC
            <loading :num="tagList7.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList7" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="通用" name="10" :disabled="tagList8.length != 0 ? false : true">
          <span slot="label">通用
            <loading :num="tagList8.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList8" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="可信任" name="11" :disabled="tagList9.length != 0 ? false : true">
          <span slot="label">可信任
            <loading :num="tagList9.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList9" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="基础属性" name="12" :disabled="tagList10.length != 0 ? false : true">
          <span slot="label">基础属性
            <loading :num="tagList10.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList10" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="安全事件" name="13" :disabled="tagList11.length != 0 ? false : true">
          <span slot="label">安全事件
            <loading :num="tagList11.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList11" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="AI模型" name="14" :disabled="tagList12.length != 0 ? false : true">
          <span slot="label">AI模型
            <loading :num="tagList12.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList12" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="威胁" name="15" :disabled="tagList13.length != 0 ? false : true">
          <span slot="label">威胁
            <loading :num="tagList13.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList13" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="加密流量检测" name="16" :disabled="tagList14.length != 0 ? false : true">
          <span slot="label" style="width: 130px">加密流量检测
            <loading :num="tagList14.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList14" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="行为检测" name="17" :disabled="tagList15.length != 0 ? false : true">
          <span slot="label">行为检测
            <loading :num="tagList15.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList15" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="高维标签" name="18" :disabled="tagList16.length != 0 ? false : true">
          <span slot="label">高维标签
            <loading :num="tagList16.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList16" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="功能描述" name="19" :disabled="tagList17.length != 0 ? false : true">
          <span slot="label">功能描述
            <loading :num="tagList17.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList17" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="知识库" name="20" :disabled="tagList18.length != 0 ? false : true">
          <span slot="label">知识库
            <loading :num="tagList18.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList18" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="合法性" name="21" :disabled="tagList19.length != 0 ? false : true">
          <span slot="label">合法性
            <loading :num="tagList19.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList19" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="知识库加密流量检测" name="22" :disabled="tagList20.length != 0 ? false : true">
          <span slot="label">知识库加密流量检测
            <loading :num="tagList20.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList20" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="行为检测模块" name="23" :disabled="tagList21.length != 0 ? false : true">
          <span slot="label">行为检测模块
            <loading :num="tagList21.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList21" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="攻击入侵" name="24" :disabled="tagList22.length != 0 ? false : true">
          <span slot="label">攻击入侵
            <loading :num="tagList22.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList22" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="行为描述" name="25" :disabled="tagList23.length != 0 ? false : true">
          <span slot="label">行为描述
            <loading :num="tagList23.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList23" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="命令与控制" name="26" :disabled="tagList24.length != 0 ? false : true">
          <span slot="label">命令与控制
            <loading :num="tagList24.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList24" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="身份欺骗" name="27" :disabled="tagList25.length != 0 ? false : true">
          <span slot="label">身份欺骗
            <loading :num="tagList25.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList25" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="翻墙上网" name="28" :disabled="tagList26.length != 0 ? false : true">
          <span slot="label">翻墙上网
            <loading :num="tagList26.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList26" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="中间人" name="29" :disabled="tagList27.length != 0 ? false : true">
          <span slot="label">中间人
            <loading :num="tagList27.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList27" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="APT" name="30" :disabled="tagList28.length != 0 ? false : true">
          <span slot="label">APT
            <loading :num="tagList28.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList28" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="私有检测" name="31" :disabled="tagList29.length != 0 ? false : true">
          <span slot="label">私有检测
            <loading :num="tagList29.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList29" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
        <el-tab-pane label="高维度标签" name="32" :disabled="tagList30.length != 0 ? false : true">
          <span slot="label">高维度标签
            <loading :num="tagList30.length" />
          </span>
          <!-- <tags-target :searchTagList="tagList30" v-bind="$attrs" v-on="$listeners"></tags-target> -->
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="tag-box-r">
      <div class="tag-content">
        <div class="search-box">
          <el-input v-model="searchTagName" style="width: 208px; margin-bottom: 10px" clearable placeholder="搜索标签名">
          </el-input>
        </div>
        <div class="main">
          <tags-target v-show="tagsign == '6'" :search-tag-list="tagList" v-bind="$attrs" v-on="$listeners"></tags-target>
          <tags-target v-show="tagsign == '0'" :search-tag-list="tagList1" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '3'" :search-tag-list="tagList2" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '7'" :search-tag-list="tagList3" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '4'" :search-tag-list="tagList4" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '1'" :search-tag-list="tagList5" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '2'" :search-tag-list="tagList6" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '5'" :search-tag-list="tagList7" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '10'" :search-tag-list="tagList8" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '11'" :search-tag-list="tagList9" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '12'" :search-tag-list="tagList10" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '13'" :search-tag-list="tagList11" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '14'" :search-tag-list="tagList12" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '15'" :search-tag-list="tagList13" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '16'" :search-tag-list="tagList14" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '17'" :search-tag-list="tagList15" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '18'" :search-tag-list="tagList16" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '19'" :search-tag-list="tagList17" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '20'" :search-tag-list="tagList18" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '21'" :search-tag-list="tagList19" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '22'" :search-tag-list="tagList20" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '23'" :search-tag-list="tagList21" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '24'" :search-tag-list="tagList22" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '25'" :search-tag-list="tagList23" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '26'" :search-tag-list="tagList24" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '27'" :search-tag-list="tagList25" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '28'" :search-tag-list="tagList26" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '29'" :search-tag-list="tagList27" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '30'" :search-tag-list="tagList28" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '31'" :search-tag-list="tagList29" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <tags-target v-show="tagsign == '32'" :search-tag-list="tagList30" v-bind="$attrs" v-on="$listeners">
          </tags-target>
          <div style="width: 100%; height: 64px"></div>
        </div>
      </div>
      <div class="tag-btn">
        <el-button @click="resetTag">重置</el-button>
        <div>
          <el-button @click="cleartagdialog">关闭</el-button>
          <el-button type="primary" style="color:#fff" @click="cleartagdialog">确认</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import { getTags, gettaglist } from "@/api/Conversational/conversational";
import dict from "@/assets/dict.json";
import tagsTarget from "./components/tags-target.vue";
import loading from "../../loading/index.vue";
export default {
  components: { tagsTarget, loading },
  props: {
    display: {
      type: String,
      define: "none",
    },
    value: {
      type: String,
    },
    type: {
      type: String,
      define: "analysis",
    },
  },
  data() {
    return {
      tag_list: [],
      tagsign: "6",
      searchTagList: [],
      searchTagListNum: [], //各类型目标的标签数量
      searchTagName: "",
      notShow: [
        211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224,
        225, 226, 230,
      ],
      activeName: "6",
      tagList: [],
      tagList1: [],
      tagList2: [],
      tagList3: [],
      tagList4: [],
      tagList5: [],
      tagList6: [],
      tagList7: [],
      tagList8: [],
      tagList9: [],
      tagList10: [],
      tagList11: [],
      tagList12: [],
      tagList13: [],
      tagList14: [],
      tagList15: [],
      tagList16: [],
      tagList17: [],
      tagList18: [],
      tagList19: [],
      tagList20: [],
      tagList21: [],
      tagList22: [],
      tagList23: [],
      tagList24: [],
      tagList25: [],
      tagList26: [],
      tagList27: [],
      tagList28: [],
      tagList29: [],
      tagList30: [],
      tagList31: [],
      searchList: [],
      searchList1: [],
      searchList2: [],
      searchList3: [],
      searchList4: [],
      searchList5: [],
      searchList6: [],
      searchList7: [],
      searchList8: [],
      searchList9: [],
      searchList10: [],
      searchList11: [],
      searchList12: [],
      searchList13: [],
      searchList14: [],
      searchList15: [],
      searchList16: [],
      searchList17: [],
      searchList18: [],
      searchList19: [],
      searchList20: [],
      searchList21: [],
      searchList22: [],
      searchList23: [],
      searchList24: [],
      searchList25: [],
      searchList26: [],
      searchList27: [],
      searchList28: [],
      searchList29: [],
      searchList30: [],
    };
  },
  watch: {
    // 搜索框的方法 不管外面
    searchTagName: {
      handler(val) {
        let searchList = JSON.parse(JSON.stringify(this.searchList));
        let searchList1 = JSON.parse(JSON.stringify(this.searchList1));
        let searchList2 = JSON.parse(JSON.stringify(this.searchList2));
        let searchList3 = JSON.parse(JSON.stringify(this.searchList3));
        let searchList4 = JSON.parse(JSON.stringify(this.searchList4));
        let searchList5 = JSON.parse(JSON.stringify(this.searchList5));
        let searchList6 = JSON.parse(JSON.stringify(this.searchList6));
        let searchList7 = JSON.parse(JSON.stringify(this.searchList7));
        let searchList8 = JSON.parse(JSON.stringify(this.searchList8));
        let searchList9 = JSON.parse(JSON.stringify(this.searchList9));
        let searchList10 = JSON.parse(JSON.stringify(this.searchList10));
        let searchList11 = JSON.parse(JSON.stringify(this.searchList11));
        let searchList12 = JSON.parse(JSON.stringify(this.searchList12));
        let searchList13 = JSON.parse(JSON.stringify(this.searchList13));
        let searchList14 = JSON.parse(JSON.stringify(this.searchList14));
        let searchList15 = JSON.parse(JSON.stringify(this.searchList15));
        let searchList16 = JSON.parse(JSON.stringify(this.searchList16));
        let searchList17 = JSON.parse(JSON.stringify(this.searchList17));
        let searchList18 = JSON.parse(JSON.stringify(this.searchList18));
        let searchList19 = JSON.parse(JSON.stringify(this.searchList19));
        let searchList20 = JSON.parse(JSON.stringify(this.searchList20));
        let searchList21 = JSON.parse(JSON.stringify(this.searchList21));
        let searchList22 = JSON.parse(JSON.stringify(this.searchList22));
        let searchList23 = JSON.parse(JSON.stringify(this.searchList23));
        let searchList24 = JSON.parse(JSON.stringify(this.searchList24));
        let searchList25 = JSON.parse(JSON.stringify(this.searchList25));
        let searchList26 = JSON.parse(JSON.stringify(this.searchList26));
        let searchList27 = JSON.parse(JSON.stringify(this.searchList27));
        let searchList28 = JSON.parse(JSON.stringify(this.searchList28));
        let searchList29 = JSON.parse(JSON.stringify(this.searchList29));
        let searchList30 = JSON.parse(JSON.stringify(this.searchList30));

        if (val) {
          this.tagList = searchList.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList1 = searchList1.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList2 = searchList2.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList3 = searchList3.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList4 = searchList4.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList5 = searchList5.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList6 = searchList6.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList7 = searchList7.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList8 = searchList8.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList9 = searchList9.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList10 = searchList10.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList11 = searchList11.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList12 = searchList12.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList13 = searchList13.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList14 = searchList14.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList15 = searchList15.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList16 = searchList16.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList17 = searchList17.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList18 = searchList18.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList19 = searchList19.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList20 = searchList20.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList21 = searchList21.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList22 = searchList22.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList23 = searchList23.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList24 = searchList24.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList25 = searchList25.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList26 = searchList26.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList27 = searchList27.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList28 = searchList28.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList29 = searchList29.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.tagList30 = searchList30.filter((item) => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
          this.esist(this.value);
        } else {
          this.tagList = JSON.parse(JSON.stringify(this.searchList));
          this.tagList1 = JSON.parse(JSON.stringify(this.searchList1));
          this.tagList2 = JSON.parse(JSON.stringify(this.searchList2));
          this.tagList3 = JSON.parse(JSON.stringify(this.searchList3));
          this.tagList4 = JSON.parse(JSON.stringify(this.searchList4));
          this.tagList5 = JSON.parse(JSON.stringify(this.searchList5));
          this.tagList6 = JSON.parse(JSON.stringify(this.searchList6));
          this.tagList7 = JSON.parse(JSON.stringify(this.searchList7));
          this.tagList8 = JSON.parse(JSON.stringify(this.searchList8));
          this.tagList9 = JSON.parse(JSON.stringify(this.searchList9));
          this.tagList10 = JSON.parse(JSON.stringify(this.searchList10));
          this.tagList11 = JSON.parse(JSON.stringify(this.searchList11));
          this.tagList12 = JSON.parse(JSON.stringify(this.searchList12));
          this.tagList13 = JSON.parse(JSON.stringify(this.searchList13));
          this.tagList14 = JSON.parse(JSON.stringify(this.searchList14));
          this.tagList15 = JSON.parse(JSON.stringify(this.searchList15));
          this.tagList16 = JSON.parse(JSON.stringify(this.searchList16));
          this.tagList17 = JSON.parse(JSON.stringify(this.searchList17));
          this.tagList18 = JSON.parse(JSON.stringify(this.searchList18));
          this.tagList19 = JSON.parse(JSON.stringify(this.searchList19));
          this.tagList20 = JSON.parse(JSON.stringify(this.searchList20));
          this.tagList21 = JSON.parse(JSON.stringify(this.searchList21));
          this.tagList22 = JSON.parse(JSON.stringify(this.searchList22));
          this.tagList23 = JSON.parse(JSON.stringify(this.searchList23));
          this.tagList24 = JSON.parse(JSON.stringify(this.searchList24));
          this.tagList25 = JSON.parse(JSON.stringify(this.searchList25));
          this.tagList26 = JSON.parse(JSON.stringify(this.searchList26));
          this.tagList27 = JSON.parse(JSON.stringify(this.searchList27));
          this.tagList28 = JSON.parse(JSON.stringify(this.searchList28));
          this.tagList29 = JSON.parse(JSON.stringify(this.searchList29));
          this.tagList30 = JSON.parse(JSON.stringify(this.searchList30));
          this.esist(this.value);
        }
      },
    },
    value: {
      handler(val) {
        console.log(val);
        this.esist(val);
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.gettags();
    // this.tag_list = ;
    // gettaglist().then((res) => {
    //   console.log(res);
    // });
    // tagList = this.formatTag(tagList);
    // console.log('tagList',tagList)
    // this.tagList = tagList.map((item) => {
    //   item.exist = false;
    //   return item;
    // });
    // this.searchTagList = JSON.parse(JSON.stringify(this.tagList));
    // console.log("this.searchTagList", this.searchTagList);
    // this.getArrNumber(this.searchTagList, this.searchTagListNum);
  },
  methods: {
    gettags() {
      let tagDict = this.$store.state.conversational.taglist01;
      let valueList = [];
      for (let key in tagDict) {
        // tagList.push({
        //   black_list: tagDict[key].black_list,
        //   tag_text: tagDict[key].value,
        //   white_list: tagDict[key].white_list,
        //   type: tagDict[key].type,
        //   tagid:tagDict[key].id,
        //    tag_explain:tagDict[key].tag_explain,
        // attribute_name: tagDict[key].attribute_name;
        // });
        // 会话
        if (tagDict[key].tag_target_type === 6) {
          this.tagList.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 6,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
            exist: false,
          });
        }
        // if (tagDict[key].attribute_name === "会话") {
        //   this.tagList.push({
        //     black_list: tagDict[key].black_list,
        //     white_list: tagDict[key].white_list,
        //     tag_text: tagDict[key].tag_text,
        //     type: 6,
        //     tagid: tagDict[key].tag_id,
        //     tag_explain: tagDict[key].tag_explain,
        //     attribute_name: tagDict[key].attribute_name,
        //     exist: false,
        //   });
        // }
        // IP
        if (tagDict[key].tag_target_type === 0) {
          this.tagList1.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 0,
            exist: false,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        // if (tagDict[key].attribute_name === "IP") {
        //   this.tagList1.push({
        //     black_list: tagDict[key].black_list,
        //     white_list: tagDict[key].white_list,
        //     tag_text: tagDict[key].tag_text,
        //     type: 0,
        //     exist: false,
        //     tagid: tagDict[key].tag_id,
        //     tag_explain: tagDict[key].tag_explain,
        //     attribute_name: tagDict[key].attribute_name,
        //   });
        // }
        // 域名
        if (tagDict[key].tag_target_type === 3) {
          this.tagList2.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 3,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
            exist: false,
          });
        }
        // if (tagDict[key].attribute_name === "域名") {
        //   this.tagList2.push({
        //     black_list: tagDict[key].black_list,
        //     white_list: tagDict[key].white_list,
        //     tag_text: tagDict[key].tag_text,
        //     type: 3,
        //     tagid: tagDict[key].tag_id,
        //     tag_explain: tagDict[key].tag_explain,
        //     attribute_name: tagDict[key].attribute_name,
        //     exist: false,
        //   });
        // }
        // 指纹
        if (tagDict[key].tag_target_type === 7) {
          this.tagList3.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            exist: false,
            type: 7,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        // if (tagDict[key].attribute_name === "指纹") {
        //   this.tagList3.push({
        //     black_list: tagDict[key].black_list,
        //     white_list: tagDict[key].white_list,
        //     tag_text: tagDict[key].tag_text,
        //     exist: false,
        //     type: 7,
        //     tagid: tagDict[key].tag_id,
        //     tag_explain: tagDict[key].tag_explain,
        //     attribute_name: tagDict[key].attribute_name,
        //   });
        // }
        // 证书
        if (tagDict[key].tag_target_type === 4) {
          this.tagList4.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            exist: false,
            type: 4,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        // if (tagDict[key].attribute_name === "证书") {
        //   this.tagList4.push({
        //     black_list: tagDict[key].black_list,
        //     white_list: tagDict[key].white_list,
        //     tag_text: tagDict[key].tag_text,
        //     exist: false,
        //     type: 4,
        //     tagid: tagDict[key].tag_id,
        //     tag_explain: tagDict[key].tag_explain,
        //     attribute_name: tagDict[key].attribute_name,
        //   });
        // }
        // 端口
        if (tagDict[key].tag_target_type === 1) {
          this.tagList5.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            exist: false,
            type: 1,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }

        // if (tagDict[key].attribute_name === "端口") {
        //   this.tagList5.push({
        //     black_list: tagDict[key].black_list,
        //     white_list: tagDict[key].white_list,
        //     tag_text: tagDict[key].tag_text,
        //     exist: false,
        //     type: 1,
        //     tagid: tagDict[key].tag_id,
        //     tag_explain: tagDict[key].tag_explain,
        //     attribute_name: tagDict[key].attribute_name,
        //   });
        // }
        // 应用
        if (tagDict[key].tag_target_type === 2) {
          this.tagList6.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            exist: false,
            type: 2,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        // if (tagDict[key].attribute_name === "应用") {
        //   this.tagList6.push({
        //     black_list: tagDict[key].black_list,
        //     white_list: tagDict[key].white_list,
        //     tag_text: tagDict[key].tag_text,
        //     exist: false,
        //     type: 2,
        //     tagid: tagDict[key].tag_id,
        //     tag_explain: tagDict[key].tag_explain,
        //     attribute_name: tagDict[key].attribute_name,
        //   });
        // }
        // MAC
        if (tagDict[key].tag_target_type === 5) {
          this.tagList7.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 5,
            exist: false,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        // if (tagDict[key].attribute_name === "MAC") {
        //   this.tagList7.push({
        //     black_list: tagDict[key].black_list,
        //     white_list: tagDict[key].white_list,
        //     tag_text: tagDict[key].tag_text,
        //     type: 5,
        //     exist: false,
        //     tagid: tagDict[key].tag_id,
        //     tag_explain: tagDict[key].tag_explain,
        //     attribute_name: tagDict[key].attribute_name,
        //   });
        // }

        if (tagDict[key].attribute_name === "通用") {
          this.tagList8.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 10,
            tagid: tagDict[key].tag_id,
            exist: false,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "可信任") {
          this.tagList9.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            exist: false,
            type: 11,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "基础属性") {
          this.tagList10.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            exist: false,
            tag_text: tagDict[key].tag_text,
            type: 11,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "安全事件") {
          this.tagList11.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            exist: false,
            tag_text: tagDict[key].tag_text,
            type: 13,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "AI模型") {
          this.tagList12.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            exist: false,
            type: 14,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "威胁") {
          this.tagList13.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            exist: false,
            type: 15,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "加密流量检测") {
          this.tagList14.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            exist: false,
            type: 16,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "行为检测") {
          this.tagList15.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            exist: false,
            type: 17,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "高维标签") {
          this.tagList16.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            exist: false,
            tag_text: tagDict[key].tag_text,
            type: 18,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "功能描述") {
          this.tagList17.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            exist: false,
            type: 19,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "知识库") {
          this.tagList18.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            exist: false,
            tag_text: tagDict[key].tag_text,
            type: 20,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "合法性") {
          this.tagList19.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            exist: false,
            type: 21,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "知识库加密流量检测") {
          this.tagList20.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            exist: false,
            type: 22,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "行为检测模块") {
          this.tagList21.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 23,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            exist: false,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "攻击入侵") {
          this.tagList22.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 24,
            exist: false,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "行为描述") {
          this.tagList23.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 25,
            exist: false,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "命令与控制") {
          this.tagList24.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 26,
            exist: false,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "身份欺骗") {
          this.tagList25.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 27,
            exist: false,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "翻墙上网") {
          this.tagList26.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 28,
            exist: false,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "中间人") {
          this.tagList27.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 29,
            exist: false,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "APT") {
          this.tagList28.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 30,
            exist: false,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "私有检测") {
          this.tagList29.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 31,
            exist: false,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        if (tagDict[key].attribute_name === "高维度标签") {
          this.tagList30.push({
            black_list: tagDict[key].black_list,
            white_list: tagDict[key].white_list,
            tag_text: tagDict[key].tag_text,
            type: 32,
            exist: false,
            tagid: tagDict[key].tag_id,
            tag_explain: tagDict[key].tag_explain,
            attribute_name: tagDict[key].attribute_name,
          });
        }
        //   标签增加的话依次
        // if (!valueList.includes(tagDict[key].value)) {
        // valueList.push(tagDict[key].value);
        this.tagList31.push({
          tag_text: tagDict[key].tag_text,
          black_list: tagDict[key].black_list,
          white_list: tagDict[key].white_list,
          type: tagDict[key].type,
          tagid: tagDict[key].tag_id,
          exist: false,
          tag_explain: tagDict[key].tag_explain,
          tagtype: tagDict[key].attribute_name,
        });
        // }
      }
      this.$store.commit(
        "conversational/taglisttrueData",
        this.formatTag(this.tagList31)
      );
      this.tagList = this.formatTag(this.tagList);
      this.tagList1 = this.formatTag(this.tagList1);
      this.tagList2 = this.formatTag(this.tagList2);
      this.tagList3 = this.formatTag(this.tagList3);
      this.tagList4 = this.formatTag(this.tagList4);
      this.tagList5 = this.formatTag(this.tagList5);
      this.tagList6 = this.formatTag(this.tagList6);
      this.tagList7 = this.formatTag(this.tagList7);
      this.tagList8 = this.formatTag(this.tagList8);
      this.tagList9 = this.formatTag(this.tagList9);
      this.tagList10 = this.formatTag(this.tagList10);
      this.tagList11 = this.formatTag(this.tagList11);
      this.tagList12 = this.formatTag(this.tagList12);
      this.tagList13 = this.formatTag(this.tagList13);
      this.tagList14 = this.formatTag(this.tagList14);
      this.tagList15 = this.formatTag(this.tagList15);
      this.tagList16 = this.formatTag(this.tagList16);
      this.tagList17 = this.formatTag(this.tagList17);
      this.tagList18 = this.formatTag(this.tagList18);
      this.tagList19 = this.formatTag(this.tagList19);
      this.tagList20 = this.formatTag(this.tagList20);
      this.tagList21 = this.formatTag(this.tagList21);
      this.tagList22 = this.formatTag(this.tagList22);
      this.tagList23 = this.formatTag(this.tagList23);
      this.tagList24 = this.formatTag(this.tagList24);
      this.tagList25 = this.formatTag(this.tagList25);
      this.tagList26 = this.formatTag(this.tagList26);
      this.tagList27 = this.formatTag(this.tagList27);
      this.tagList28 = this.formatTag(this.tagList28);
      this.tagList29 = this.formatTag(this.tagList29);
      this.tagList30 = this.formatTag(this.tagList30);
      this.searchList = this.tagList;
      this.searchList1 = this.tagList1;
      this.searchList2 = this.tagList2;
      this.searchList3 = this.tagList3;
      this.searchList4 = this.tagList4;
      this.searchList5 = this.tagList5;
      this.searchList6 = this.tagList6;
      this.searchList7 = this.tagList7;
      this.searchList8 = this.tagList8;
      this.searchList9 = this.tagList9;
      this.searchList10 = this.tagList10;
      this.searchList11 = this.tagList11;
      this.searchList12 = this.tagList12;
      this.searchList13 = this.tagList13;
      this.searchList14 = this.tagList14;
      this.searchList15 = this.tagList15;
      this.searchList16 = this.tagList16;
      this.searchList17 = this.tagList17;
      this.searchList18 = this.tagList18;
      this.searchList19 = this.tagList19;
      this.searchList20 = this.tagList20;
      this.searchList21 = this.tagList21;
      this.searchList22 = this.tagList22;
      this.searchList23 = this.tagList23;
      this.searchList24 = this.tagList24;
      this.searchList25 = this.tagList25;
      this.searchList26 = this.tagList26;
      this.searchList27 = this.tagList27;
      this.searchList28 = this.tagList28;
      this.searchList29 = this.tagList29;
      this.searchList30 = this.tagList30;
    },
    // 打开对应标签
    opentagcontent(e) {
      console.log('kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk',e);
      this.tagsign = e.name;
    },
    esist(val) {
      console.log(val, "传回来的标签名字");
      let existList = val.split(",");
      console.log(existList, 111111111111);
      console.log(this.tagList, "标签列表");
      this.tagList.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.searchTagList.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.$store.state.conversational.taglisttrue.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList1.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList2.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList3.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList4.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList5.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList6.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList7.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList8.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList9.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList10.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList11.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList12.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList13.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList14.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList15.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList16.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList17.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList18.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList19.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList20.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList21.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList22.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList23.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList24.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList25.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList26.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList27.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList28.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList29.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
      this.tagList30.forEach((item) => {
        item.exist = existList.includes(item.tagText) ? true : false;
      });
    },
    //获取不同目标标签的数量
    getArrNumber(arr, newArr) {
      for (var i = 0; i < arr.length; i++) {
        var temp = arr[i].targetType;
        var count = 0;
        for (var j = 0; j < arr.length; j++) {
          if (arr[j].targetType == temp) {
            count++;
            // arr[j].targetType = -1;
          }
        }
        if (temp != -1) {
          newArr.push({
            targetType: temp,
            count: count,
          });
        }
      }
      return newArr;
    },
    getTargetNum(type) {
      let arr = this.searchTagListNum;
      for (var i = 0; i < arr.length; i++) {
        if (arr[i].targetType == type) {
          return arr[i].count ? arr[i].count : 0;
        }
      }
      return 0;
    },
    formatTag(arr) {
      arr.sort((a, b) => {
        if (a.black_list > b.black_list) {
          return b.black_list - a.black_list;
        } else if (a.black_list == 0 && b.black_list == 0) {
          return b.white_list - a.white_list;
        }
      });

      let tags = [];

      tags = arr.map((val) => {
        // console.log(val);
        let type = "";
        if (
          val.black_list >= 1 &&
          val.black_list <= 100 &&
          val.white_list !== 100
        ) {
          if (val.black_list >= 80) {
            type = "danger";
          } else {
            type = "warning";
          }
        }
        if (
          val.white_list >= 1 &&
          val.white_list <= 100 &&
          val.black_list === 0
        ) {
          if (val.white_list === 100) {
            type = "success";
          } else {
            type = "";
          }
        }
        if (val.white_list === 0 && val.black_list === 0) {
          type = "info";
        }
        let tag_info = [val.tag_text, type, val.tag_id || 0, val.type];
        // tags.push(tag_info)

        return {
          id: val.tag_id || 0,
          tagText: val.tag_text,
          type,
          targetType: val.type,
          tagID: val.tagid,
          tag_explain: val.tag_explain,
          attribute_name: val.attribute_name,
          exist: val.exist,
        };
      });
      // 标签去重
      let fjLists = [];

      const res = new Map();

      fjLists = tags.filter(
        (item) => !res.has(item.tagText) && res.set(item.tagText, 1)
      );
      return fjLists;
    },
    cleartagdialog() {
      console.log("关闭弹窗");
      this.$emit("cleartagdialog");
    },
    // 重置标签
    resetTag() {
      this.esist("");
      this.$emit("getTagValue", []);
    },
  },
};
</script>

<style lang="scss" scoped>
.tag-box {
  display: none;
  display: flex;

  &-l {
    width: 160px;
    height: 430px;
    background: #ffffff;
    overflow: auto;
    border-radius:8px ;
    z ::v-deep {
      .el-tabs__header {
        border: 0;
        width: 100%;
      }
    }
  }

  &-r {
    position: relative;
    flex: 1;

    .tag-content {
      // margin: 15px 0 5px;
      // max-height: 550px;
      background: #ffffff;

      .main {
        width: 100%;
        height: 366px;
        overflow: auto;

      }

      .tag-item {
        position: relative;
        margin: 0 10px;

        .tag.el-tag {
          margin: 3px 0;
          cursor: pointer;
        }
      }

      .icon-seleted {
        display: none;
        position: absolute;
        width: 30px;
        height: 35px;
        right: -5px;
        top: -2px;
        cursor: pointer;
        // color: #666;
      }

      .exist {
        .tag.el-tag {
          opacity: 0.5;
        }

        .icon-seleted {
          display: block;
        }
      }

      .search-box {
        margin-left: 10px;
        width: 208px;

        ::v-deep .el-input__inner {
          height: 32px;
          font-weight: 400;
          font-size: 14px;
          align-items: center;
          color: #cecece;
        }
      }
    }

    .tag-btn {
      padding: 0 16px;
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 64px;
      background: #ffffff;
      border-top: 1px solid #f2f3f7 !important;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius:8px ;
      .el-button {
        width: 60px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #CECECE;
        color: #2C2C35;
      }

      div {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  ::v-deep .el-tabs__item.is-disabled {
    color: #c0c4cc;
    cursor: default;
  }

  ::v-deep .el-tabs__header .el-tabs__item {
    width: 100%;
    border: 0;
    margin: 0 !important;
    padding: 0;
    padding-left: 16px !important;
    display: flex;
    justify-content: flex-start;
    height: 32px;
    line-height: 32px;
  }

  ::v-deep .el-tabs__header .el-tabs__item.is-active {
    border: 0;
    background: #e7f0fe;
    color: #116ef9 !important;
    width: 100%;
  }

  ::v-deep .el-tabs__nav-scroll {
    padding-left: 0px !important;
  }

  ::v-deep {
    .el-tabs--left.el-tabs--card .el-tabs__item.is-left:first-child {
      border: 0;
    }

    .el-tabs--card>.el-tabs__header .el-tabs__nav {
      border: 0;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }
  }

  ::v-deep .el-tabs__content {
    border-top: 0;
  }

  ::v-deep.el-tabs__header {
    margin: 0;
  }

  .icon-close {
    float: right;
    width: 15px;
    height: 15px;
    cursor: pointer;
  }
}
</style>

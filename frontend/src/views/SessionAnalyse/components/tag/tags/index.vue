<template>
  <!-- 全部标签 -->
  <div class="tag-box box animate__animated animate__fadeInDown" :style="{display: display}">
    <div @click="handleClose">
      <svg-icon icon-class="close" class="icon-close" />
    </div>
    <div class="tag-content">
      <div class="search-box">
        <el-input
          v-model="searchTagName"
          style="width: 400px;margin-bottom: 10px;"
          clearable
          placeholder="请输入标签名"
        >
        </el-input>
      </div>
      <span v-for="(item, index) in searchTagList" :key="item.tagText" class="tag-item" :class="{ 'exist': item.exist }">
        <div style="display: inline-block;" @click="addTag(item.tagText, index)">
          <svg-icon icon-class="seleted" class="icon-seleted" />
          <el-tag
            class="tag"
            :type="item.type"
          >
            <div>{{ item.tagText }}</div>
          </el-tag>
        </div>
      </span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    "display": {
      "type": String,
      "define": 'none'
    },
    "value": {
      "type": Object
    },
    "type": {
      "type": String,
      "define": 'analysis'
    }
  },
  data() {
    return {
      tagList: [],
      searchTagList: [],
      searchTagName: '',
      notShow: [211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 230]
    };
  },
  watch: {
    value: {
      handler(val) {
        console.log(val);
        let existList = val.split(',');
        this.tagList.forEach(item => {
          item.exist = existList.includes(item.tagText) ? true : false;
        });
        this.searchTagList.forEach(item => {
          item.exist = existList.includes(item.tagText) ? true : false;
        });
      }
    },
    searchTagName: {
      handler(val) {
        let searchList = JSON.parse(JSON.stringify(this.tagList));
        if (val) {
          this.searchTagList = searchList.filter(item => {
            if (item.tagText.includes(val)) {
              return item;
            }
          });
        } else {
          this.searchTagList = JSON.parse(JSON.stringify(this.tagList));
        }
      }
    }
  },
  mounted() {
    let tagDict = this.$store.getters.dictionaries.analysis_sign;
    if (this.type === 'cert') {
      let cert_sign = {};
      for (let i in this.$store.getters.dictionaries.cert_sign) {
        if (!this.notShow.includes(this.$store.getters.dictionaries.cert_sign[i].id)) {
          cert_sign[i] = this.$store.getters.dictionaries.cert_sign[i];
        }
      }
      tagDict = cert_sign;
    }
    let tagList = [];
    let valueList = [];
    for(let key in tagDict) {
      if (!valueList.includes(tagDict[key].value)) {
        valueList.push(tagDict[key].value);
        tagList.push({
          black_list: tagDict[key].black_list,
          tag_text: tagDict[key].value,
          white_list: tagDict[key].white_list
        });
      }
    }
    tagList = this.formatTag(tagList);
    this.tagList = tagList.map(item => {
      item.exist = false;
      return item;
    });
    this.searchTagList = JSON.parse(JSON.stringify(this.tagList));
  },
  methods: {
    addTag(v, i) {
      let value = [];
      if (this.type === 'cert' && this.value.split(',').length === 6 && !this.searchTagList[i].exist) return;
      this.searchTagList[i].exist = !this.searchTagList[i].exist ? true : false;
      this.tagList.forEach(item => {
        if (item.tagText === v) {
          item.exist = !item.exist ? true : false;
        }
        if (item.exist) {
          value.push(item.tagText);
        }
      });
      console.log(value,"))))))))))))))))))))))))))))))))))))");
      this.$emit('getTagValue', value.join(','));
    },
    handleClose() {
      this.$emit('handleClose');
    },
    formatTag(arr) {
      arr.sort((a, b) => {
        if(a.black_list > b.black_list) {
          return b.black_list - a.black_list;
        } else if( a.black_list == 0 && b.black_list == 0) {
          return b.white_list - a.white_list;
        }
      });
      let tags = [];
      tags = arr.map(val => {
        let type = '';
        if(val.black_list>=1 && val.black_list<=100 && val.white_list !== 100){
          if(val.black_list >= 80){
            type = 'danger';
          } else {
            type = 'warning';
          }
        }
        if(val.white_list>=1 && val.white_list<=100 && val.black_list === 0){
          if(val.white_list === 100){
            type = 'success';
          } else {
            type = '';
          }
        }
        if( val.white_list === 0 && val.black_list === 0){
          type = 'info';
        }
        let tag_info = [val.tag_text, type, val.tag_id || 0];
        // tags.push(tag_info)
        return {
          "id": val.tag_id || 0,
          "tagText": val.tag_text,
          type
        };
      });
      return tags;
    }
  }
};
</script>

<style lang="scss" scoped>
  .tag-box {
    display: none;
    position: fixed;
    top: 320px;
    left: 0;
    right: 0;
    margin: auto;
    padding: 10px;
    width: 900px;
    max-height: 600px;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    z-index: 999;
    .icon-close {
      float: right;
      width:15px;
      height:15px;
      cursor: pointer;
    }
    .tag-content {
      margin: 24px 0 10px;
      max-height: 550px;
      overflow: auto;
      .tag-item {
        position: relative;
        margin: 0 10px;
        .tag.el-tag {
          margin: 3px 0;
          cursor: pointer;
        }
      }
      .icon-seleted {
        display: none;
        position: absolute;
        width: 30px;
        height: 35px;
        right: -5px;
        top: -2px;
        cursor: pointer;
        // color: #666;
      }
      .exist {
        .tag.el-tag {
          opacity: 0.5;
        }
        .icon-seleted {
          display: block;
        }
      }
      .search-box {
        margin-left: 10px;
        width: 400px;
      }
    }
  }
</style>

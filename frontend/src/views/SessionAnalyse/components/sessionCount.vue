<template>
  <div class="sessionStat">
    <div class="title">24小时分布图</div>
    <div class="echartsbox">
      <div class="listTitle">会话数</div>
      <v-chart
        :option="stat_options"
        autoresize
        :update-options="{ notMerge: true }"
      >
      </v-chart>
    </div>
  </div>
</template>

<script>
import { stat_options } from "./echartsData";
export default {
  name: "sessionStat",
  data() {
    return {
      stat_options,
    };
  },
};
</script>

<style lang="scss" scoped>
.sessionStat {
  .title {
    font-family: "Alibaba PuHuiTi";
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #2c2c35;
  }
  .echartsbox {
    position: relative;
    width: 100%;
    height: 530px;
    .listTitle {
      position: absolute;
      top: 10px;
      left: 0;
      color: #9999a1;
    }
  }
}
</style>
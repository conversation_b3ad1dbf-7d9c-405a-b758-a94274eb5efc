<template>
  <div class="container">
    <div class="Session">
      <div class="Session-top">
        <trafficRetrieval
          @search="search"
          @triggerActiveName="triggerActiveName"
          @tagName="tagName"
          @tabList="tabList"
        />
        <div class="Session-list">
          <el-tabs
            v-model="activeName"
            class="tab-style"
            @tab-click="handleClick"
          >
            <!-- 会话 -->
            <el-tab-pane key="sessionList" name="sessionList">
              <span slot="label">会话列表
                <loading :num="tab_num.sessionList_num" />
              </span>
              <session-list
                ref="sessionList"
                style="padding: 0 16px"
                :search-data="searchData"
                :father-value.sync="tab_num.sessionList_num"
              >
              </session-list>
            </el-tab-pane>
            <!-- 通信信息 -->
            <el-tab-pane
              v-if="checkedHeaders.includes('通信信息')"
              key="通信信息"
              name="communication"
            >
              <span slot="label">通信信息 </span>
              <communicationList :search-data="searchData" />
            </el-tab-pane>
            <!-- IP -->
            <el-tab-pane
              v-if="checkedHeaders.includes('IP列表')"
              key="IPList"
              name="IPList"
            >
              <span slot="label">IP列表
                <loading :num="tab_num.ipList_num" />
              </span>
              <IPlist
                ref="IPList"
                style="padding: 0 16px"
                :search-data="searchData"
                :father-value.sync="tab_num.ipList_num"
              />
            </el-tab-pane>
            <!-- 域名 -->
            <el-tab-pane
              v-if="checkedHeaders.includes('域名列表')"
              key="DomainList"
              name="DomainList"
            >
              <span slot="label">域名列表
                <loading :num="tab_num.domainList_num" />
              </span>
              <Domainlist
                ref="DomainList"
                style="padding: 0 16px"
                :search-data="searchData"
                :father-value.sync="tab_num.domainList_num"
              />
            </el-tab-pane>
            <!-- 证书 -->
            <el-tab-pane
              v-if="checkedHeaders.includes('证书列表')"
              key="CcieList"
              name="CcieList"
            >
              <span slot="label">证书列表
                <loading :num="tab_num.certList_num" />
              </span>
              <CcieList
                ref="CcieList"
                style="padding: 0 16px"
                :search-data="searchData"
                :father-value.sync="tab_num.certList_num"
              />
            </el-tab-pane>
            <!-- 指纹 -->
            <el-tab-pane
              v-if="checkedHeaders.includes('指纹列表')"
              key="FingerList"
              name="FingerList"
            >
              <span slot="label">指纹列表
                <loading :num="tab_num.FingerList_num" />
              </span>
              <FingerList
                style="padding: 0 16px"
                :search-data="searchData"
                :father-value.sync="tab_num.FingerList_num"
              />
            </el-tab-pane>
            <!-- 元数据_SSL -->
            <el-tab-pane
              v-if="checkedHeaders.includes('元数据_SSL')"
              key="protocolInfossl"
              name="protocolInfossl"
            >
              <span slot="label">元数据_SSL
                <loading :num="tab_num.protocolInfossl_num" />
              </span>
              <protocolInfossl
                ref="protocolInfossl"
                style="padding: 0 16px"
                :search-data="searchData"
                :father-value.sync="tab_num.protocolInfossl_num"
              >
              </protocolInfossl>
            </el-tab-pane>
            <!-- 元数据_HTTP -->
            <el-tab-pane
              v-if="checkedHeaders.includes('元数据_HTTP')"
              key="protocolInfohttp"
              name="protocolInfohttp"
            >
              <span slot="label">元数据_HTTP
                <loading :num="tab_num.protocolInfohttp_num" />
              </span>
              <protocolInfohttp
                ref="protocolInfohttp"
                style="padding: 0 16px"
                :search-data="searchData"
                :father-value.sync="tab_num.protocolInfohttp_num"
              >
              </protocolInfohttp>
            </el-tab-pane>
            <!-- 元数据_DNS -->
            <el-tab-pane
              v-if="checkedHeaders.includes('元数据_DNS')"
              key="protocolInfodns"
              name="protocolInfodns"
            >
              <span slot="label">元数据_DNS
                <loading :num="tab_num.protocolInfodns_num" />
              </span>
              <protocolInfodns
                ref="protocolInfodns"
                style="padding: 0 16px"
                :search-data="searchData"
                :father-value.sync="tab_num.protocolInfodns_num"
              >
              </protocolInfodns>
            </el-tab-pane>
            <!-- 元数据_RLogin -->
            <!-- <el-tab-pane
            v-if="checkedHeaders.includes('元数据_RLogin')"
            key="protocolInforlogin"
            name="protocolInforlogin"
          >
            <span slot="label">元数据_RLogin
              <loading :num="tab_num.protocolInforlogin_num" />
            </span>
            <protocolInforlogin
              ref="protocolInforlogin"
              style="padding: 0 16px"
              :search-data="searchData"
              :father-value.sync="tab_num.protocolInforlogin_num"
            />
          </el-tab-pane> -->
            <!-- 元数据_TELNET -->
            <!-- <el-tab-pane
            v-if="checkedHeaders.includes('元数据_TELNET')"
            key="protocolInfotelnet"
            name="protocolInfotelnet"
          >
            <span slot="label">元数据_TELNET
              <loading :num="tab_num.protocolInfotelnet_num" />
            </span>
            <protocolInfotelnet
              ref="protocolInfotelnet"
              style="padding: 0 16px"
              :search-data="searchData"
              :father-value.sync="tab_num.protocolInfotelnet_num"
            />
          </el-tab-pane> -->
            <!-- 元数据_SSH -->
            <!-- <el-tab-pane
            v-if="checkedHeaders.includes('元数据_SSH')"
            key="protocolInfossh"
            name="protocolInfossh"
          >
            <span slot="label">元数据_SSH
              <loading :num="tab_num.protocolInfossh_num" />
            </span>
            <protocolInfossh
              ref="protocolInfossh"
              style="padding: 0 16px"
              :search-data="searchData"
              :father-value.sync="tab_num.protocolInfossh_num"
            />
          </el-tab-pane> -->
            <!-- 元数据_RDP -->
            <!-- <el-tab-pane
            v-if="checkedHeaders.includes('元数据_RDP')"
            key="protocolInfordp"
            name="protocolInfordp"
          >
            <span slot="label">元数据_RDP
              <loading :num="tab_num.protocolInfordp_num" />
            </span>
            <protocolInfordp
              ref="protocolInfordp"
              style="padding: 0 16px"
              :search-data="searchData"
              :father-value.sync="tab_num.protocolInfordp_num"
            />
          </el-tab-pane> -->
            <!-- 元数据_VNC -->
            <!-- <el-tab-pane
            v-if="checkedHeaders.includes('元数据_VNC')"
            key="protocolInfovnc"
            name="protocolInfovnc"
          >
            <span slot="label">元数据_VNC
              <loading :num="tab_num.protocolInfovnc_num" />
            </span>
            <protocolInfovnc
              ref="protocolInfovnc"
              style="padding: 0 16px"
              :search-data="searchData"
              :father-value.sync="tab_num.protocolInfovnc_num"
            />
          </el-tab-pane> -->
            <!-- 元数据_XDMCP -->
            <!-- <el-tab-pane
            v-if="checkedHeaders.includes('元数据_XDMCP')"
            key="protocolInfoxdmcp"
            name="protocolInfoxdmcp"
          >
            <span slot="label">元数据_XDMCP
              <loading :num="tab_num.protocolInfoxdmcp_num" />
            </span>
            <protocolInfoxdmcp
              ref="protocolInfoxdmcp"
              style="padding: 0 16px"
              :search-data="searchData"
              :father-value.sync="tab_num.protocolInfoxdmcp_num"
            />
          </el-tab-pane> -->
            <!-- Mac通信 -->
            <el-tab-pane
              v-if="checkedHeaders.includes('Mac通信')"
              key="MacCommunication"
              name="MacCommunication"
            >
              <span slot="label">Mac通信
              <!-- <loading :num="tab_loading.commuList" /> -->
              </span>
              <MacCommunication
                ref="MacCommunication"
                style="padding: 0 16px"
                :search-data="searchData"
              />
            </el-tab-pane>
            <!-- <el-tab-pane
            v-if="checkedHeaders.includes('聚合展示')"
            key="aggregationList"
            name="aggregationList"
            lazy
          >
            <span slot="label">聚合展示
            </span>

            <aggregationList :search-data="searchData"></aggregationList>
          </el-tab-pane> -->
            <!-- 标签 -->
            <el-tab-pane
              v-if="checkedHeaders.includes('标签')"
              key="tag"
              ::disabled="tab_num.tag_num ? false : true"
              name="tag"
            >
              <span slot="label">标签
                <loading :num="tab_num.tag_num" />
              </span>
              <tag
                ref="tag"
                :search-data="searchData"
                :father-value.sync="tab_num.tag_num"
              ></tag>
            </el-tab-pane>
          </el-tabs>
          <!-- 更多 -->
          <div class="more">
            <span slot="label">
              <div>
                <div class="tab-style1">
                  <el-popover
                    placement="bottom"
                    trigger="click"
                    popper-class="moretag"
                  >
                    <div>
                      <div class="main">
                        <el-checkbox-group
                          v-model="checkedHeaders"
                          @change="configStotage"
                        >
                          <el-checkbox
                            v-for="item in tableHeader"
                            :key="item"
                            :label="item"
                            style="display: block"
                            :disabled="item === '会话列表'"
                          >{{ item }}</el-checkbox>
                        </el-checkbox-group>
                      </div>
                      <div class="foot">
                        <el-checkbox
                          v-model="checkAll"
                          :indeterminate="isIndeterminate"
                          style="margin-left: 16px"
                          @change="handleCheckAllChange"
                        >
                          全选
                        </el-checkbox>
                        <el-button style="margin-right: 16px" @click="resetTable">
                          重置
                        </el-button>
                      </div>
                    </div>
                    <span slot="reference" class="custom-table-header">
                      <div class="abc">
                        <el-button
                          slot="reference"
                          type="text"
                          class="abc"
                          icon="el-icon-circle-plus-outline"
                        >更多</el-button>
                      </div>
                    </span>
                  </el-popover>
                </div>
              </div>
            </span>
          </div>
        </div>
      </div>
      <div class="Session-foot"></div>
    </div>
  </div>
</template>

<script>
// import aggregationList from "./components/aggregationList";
import sessionList from "./components/sessionList";
import communicationList from "./components/communicationList.vue";
import trafficRetrieval from "./components/trafficRetrieval";
import IPlist from "./components/IPList.vue";
import Domainlist from "./components/DomainList.vue";
import CcieList from "./components/CcieList.vue";
import FingerList from "./components/FingerList.vue";
import MacCommunication from "./components/MacCommunication.vue";
import sessionStat from "./components/sessionCount.vue";
import loading from "./components/loading";
import protocolInfossl from "./components/protocolInfossl";
import protocolInfohttp from "./components/protocolInfohttp";
import protocolInfodns from "./components/protocolInfodns";
import {
  protocolInforlogin,
  protocolInfotelnet,
  protocolInfossh,
  protocolInfordp,
  protocolInfovnc,
  protocolInfoxdmcp,
} from "./components/metadata/index.js";
import tag from "./components/tag";
const trafficOptions = [
  "通信信息",
  "IP列表",
  "域名列表",
  "证书列表",
  "指纹列表",
  "元数据_SSL",
  "元数据_HTTP",
  "元数据_DNS",
  // "元数据_RLogin",
  // "元数据_TELNET",
  // "元数据_SSH",
  // "元数据_RDP",
  // "元数据_VNC",
  // "元数据_XDMCP",
  "Mac通信",
  // '聚合展示',
  "标签",
];
const auditOptions = [
  "通信信息",
  "元数据_SSL",
  "元数据_HTTP",
  "元数据_DNS",
  "Mac通信",
  // '聚合展示',
  "标签",
];
export default {
  name: "SessionAnalyse",
  components: {
    sessionList,
    trafficRetrieval,
    IPlist,
    Domainlist,
    CcieList,
    FingerList,
    MacCommunication,
    // sessionStat,
    loading,
    protocolInfossl,
    protocolInfohttp,
    protocolInfodns,
    // aggregationList,
    tag,
    communicationList,
    // protocolInforlogin,
    // protocolInfotelnet,
    // protocolInfossh,
    // protocolInfordp,
    // protocolInfovnc,
    // protocolInfoxdmcp,
  },
  data() {
    return {
      tableHeader: this.$isTraffic ? trafficOptions : auditOptions,
      isIndeterminate: true,
      checkedHeaders: [],
      checkAll: false,
      searchData: {},
      activeName: "sessionList",
      activeNameList: {
        ip: "ipList",
        finger: "printList",
        domain: "domainList",
        cert: "certList",
        port: "portList",
        tag: "tag",
        app_id: "appList",
        session: "sessionList",
      },
      activeNameCn:{
        '会话列表':'sessionList',
        '通信信息':'communication',
        'IP列表':'IPList',
        '域名列表':'DomainList',
        '证书列表':'CcieList',
        '指纹列表':'FingerList',
        '元数据_SSL':'protocolInfossl',
        '元数据_HTTP':'protocolInfohttp',
        '元数据_DNS':'protocolInfodns',
        'Mac通信':'MacCommunication',
        '标签':'tag',
      },
      tab_num: {
        ipList_num: null,
        domainList_num: null,
        certList_num: null,
        FingerList_num: null,
        printList_num: null,
        appList_num: null,
        portList_num: null,
        sessionList_num: null,
        serveInfo_num: null,
        abnormalSession_num: null,
        macCommunication_num: null,
        ipCommunication_num: null,
        tag_num: null,
        ipRelevanceFinger_num: null,
        domainAnalysis_num: null,
        certRelevanceIP_num: null,
        ipRelevanceCert_num: null,
        ipRelevanceDomain_num: null,
        protocolInfodns_num: null,
        protocolInfossl_num: null,
        protocolInfohttp_num: null,
        protocolInforlogin_num: null,
        protocolInfotelnet_num: null,
        protocolInfossh_num: null,
        protocolInfordp_num: null,
        protocolInfovnc_num: null,
        protocolInfoxdmcp_num: null,
      },
    };
  },
  watch: {
    activeName: {
      handler(val) {
        if (this.actname1 == "target") {
          if (!this.hasTrigger?.includes(val)) {
            this.hasTrigger?.push(val);
            // this.$refs[val].initData()
          }
        }
        if (val) {
          this.$refs[val]?.initData();
        }
      },
    },
    searchData: {
      handler(val) {
        if (val.query?.length > 0) {
          if (val.query[0].search.length === 1) {
            this.ip_rele = true;
          } else {
            this.ip_rele = false;
          }
        }
      },
    },
  },
  created() {
    if (window.localStorage.checkedCount) {
      this.checkedHeaders = JSON.parse(window.localStorage.checkedCount);
    }
  },
  methods: {
    search(val) {
      this.tab_num = {
        ipList_num: null,
        domainList_num: null,
        certList_num: null,
        FingerList_num: null,
        printList_num: null,
        appList_num: null,
        portList_num: null,
        sessionList_num: null,
        serveInfo_num: null,
        abnormalSession_num: null,
        macCommunication_num: null,
        ipCommunication_num: null,
        tag_num: null,
        ipRelevanceFinger_num: null,
        domainAnalysis_num: null,
        certRelevanceIP_num: null,
        ipRelevanceCert_num: null,
        ipRelevanceDomain_num: null,
        protocolInfodns_num: null,
        protocolInfossl_num: null,
        protocolInfohttp_num: null,
      };
      this.tab_loading = {
        commuList: false,
      };
      // eslint-disable-next-line no-prototype-builtins
      if (val.hasOwnProperty("target_filter")) {
        if (this.targetType === "tag") this.activeName = "IPList";
      }
      this.searchData = val;
    },
    triggerActiveName(val) {
      if (val === "tag") {
        val = "session";
      }
      this.activeName = this.activeNameList[val];
    },
    tagName(val) {
      this.actname1 = val;
      this.activeName = "IPList";
    },
    tabList(val) {
      this.tabNameList = val;
    },
    handleClick(tab) {
      let that = this;
      this.activeName = tab.name;
      this.$store.commit("conversational/protocolInfofliterData", "");
      if (tab.name == "protocolInfossl") {
        this.$store.commit(
          "conversational/protocolInfofliterData",
          "protocolInfossl"
        );
      } else if (tab.name == "protocolInfohttp") {
        this.$store.commit(
          "conversational/protocolInfofliterData",
          "protocolInfohttp"
        );
      } else if (tab.name == "protocolInfodns") {
        this.$store.commit(
          "conversational/protocolInfofliterData",
          "protocolInfodns"
        );
      } else if (tab.name == "protocolInforlogin") {
        this.$store.commit(
          "conversational/protocolInfofliterData",
          "protocolInforlogin"
        );
      } else if (tab.name == "protocolInfotelnet") {
        this.$store.commit(
          "conversational/protocolInfofliterData",
          "protocolInfotelnet"
        );
      } else if (tab.name == "protocolInfossh") {
        this.$store.commit(
          "conversational/protocolInfofliterData",
          "protocolInfossh"
        );
      } else if (tab.name == "protocolInfordp") {
        this.$store.commit(
          "conversational/protocolInfofliterData",
          "protocolInfordp"
        );
      } else if (tab.name == "protocolInfovnc") {
        this.$store.commit(
          "conversational/protocolInfofliterData",
          "protocolInfovnc"
        );
      } else if (tab.name == "protocolInfoxdmcp") {
        this.$store.commit(
          "conversational/protocolInfofliterData",
          "protocolInfoxdmcp"
        );
      }
      this.$refs.sessionList.$refs.tableList.doLayout();
    },
    resetTable() {
      let resttable = [
        "IP列表",
        "域名列表",
        "证书列表",
        "通信信息",
        "指纹列表",
        "元数据_SSL",
        "元数据_HTTP",
        "元数据_DNS",
        "元数据_RLogin",
        "元数据_TELNET",
        "元数据_SSH",
        "元数据_RDP",
        "元数据_VNC",
        "元数据_XDMCP",
        "Mac通信",
        "聚合展示",
        "标签",
      ];
      this.checkedHeaders = resttable;
      this.isIndeterminate=false;
      this.checkAll=true;
    },
    handleCheckAllChange(val) {
      let checkedCount = val.length;
      this.checkedHeaders = val
        ? this.$isTraffic
          ? trafficOptions
          : auditOptions
        : [];
      if(!this.checkedHeaders.length) this.activeName="sessionList";
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.tableHeader.length;
      window.localStorage.setItem(
        "checkedCount",
        JSON.stringify(this.checkedHeaders)
      );
    },
    configStotage(value) {
      let checkedHeader=JSON.parse(window.localStorage.checkedCount);
      let checkedCount = value.length;
      if(checkedCount<checkedHeader.length){
        this.cancelTabResetName(value,checkedHeader);
      }
      this.checkAll = checkedCount === this.tableHeader.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.tableHeader.length;
      window.localStorage.setItem(
        "checkedCount",
        JSON.stringify(this.checkedHeaders)
      );
 
    },
    // 取消时，如果当前activename是取消的项，就应该初始化到sessionList
    cancelTabResetName(item1,item2){
      // 找到item2中item1中不存在的数据，则认为是删除的数据
      let diff=item2.filter(item=>!item1.includes(item));
      if(this.activeNameCn[diff[0]]===this.activeName){
        this.activeName="sessionList";
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.Session {
  ::v-deep .el-tabs__nav-scroll{
    padding-left: 16px;
  }
  .tab-style {
    ::v-deep {
      .el-tabs__content {
        overflow: visible !important;
      }
    }
  }

  .title {
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #2C2C35;
    margin-bottom: 16px;
  }

  &-list {
    position: relative;
    // padding: 16px 24px;
    background: #ffffff;
    border: 1px solid #f2f3f7;
    border-radius: 8px;

    .more {
      position: absolute;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      top: 8px;
      right: 25px;
      .abc {
        .el-button {
          border: 0;
        }
      }
    }
    ::v-deep .el-tabs__header {
      padding-right: 100px;
    }
    ::v-deep .el-tabs__content {
      overflow: visible;
    }

    ::v-deep.el-tabs__item.is-active {
      color: #116ef9;
    }

    .tabs-num {
      color: #9999a1;
    }

    ::v-deep .el-tabs__item {
      color: #2c2c35;
    }

    ::v-deep .el-tabs__nav-wrap::after {
      height: 1px;
    }

    ::v-deep .el-tabs__nav {
      height: 54px;
      display: flex;
      align-items: center;
    }
  }
}
.main {
  width: 240px;
  height: 280px;
  overflow: auto;
  margin: 10px 16px;
}
.foot {
  height: 48px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f8f8;
  border-radius: 0px 0px 2px 2px;

  .el-button {
    height: 24px;
    width: 58px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
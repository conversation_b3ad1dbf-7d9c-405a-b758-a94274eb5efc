/* eslint-disable indent */
import $store from '../../store';
export function formatGraphData(data) {
  // 节点
  let nodes = data.vertex;
  if (data.label_vertex) {
    nodes = nodes.concat(data.label_vertex);
  }
  // 线条
  let links = data.edge;
  let contains = data.contains || [];
  // 节点数据匹配
  let tempNodes = [];
  nodes.forEach(i => {
    let label = i.label || 'N/A';
    let labels = i.labels || '';
    let vid = i.vid || '';
    let img;
    let disableDefaultClickEffect = false;
    if (i.type === 'Folder') {
      label = folder_fmt(i);
      img = folder_img_fmt(i.label).img;
      disableDefaultClickEffect = true;
    } else if (i.type === 'LABELS') {
      label = ' ';
      disableDefaultClickEffect = true;
    } else {
      if (i.identity == 'attacker') {
        img = require('../../assets/graph/icon_attacker_wihte.svg');
      } else if (i.identity == 'victim') {
        img = require('../../assets/graph/icon_sufferer_wihte.svg');
      } else {
        img = node_img_fmt(i.type).icon.img;
      }
      disableDefaultClickEffect = false;
    }
    let temp = {
      id: i.id,
      text: i.label,
      styleClass: 'raw-node',
      borderWidth: '0',
      disableDefaultClickEffect: disableDefaultClickEffect,
      data: {
        id: i.id,
        label: label,
        hideLabel: label,
        type: i.type,
        img: img,
        labels: labels,
        vid: vid,
        identity: i.identity || ''
      }
      //   html: `
      //   <div  style="display:${i.type !== 'Folder'?'':'none'}" >
      //         <img src="${img}"/>
      //   </div>
      //   <div class="folder-node" style="display:${i.type === 'Folder'?'':'none'}" >
      //         <img  src="${img}"/>
      //   </div>
      //   <div class="node-label" >${label || 'N/A'}</div>
      //   `
    };
    tempNodes.push({
      ...temp
    });
  });
  // 线条数据匹配
  let tempLinks = [];
  links.forEach(k => {
    let label = k.label || '';
    if (label !== '') label = folder_fmt(k);
    let temp = {
      id: k.id || '',
      from: k.from,
      to: k.to,
      text: label,
      color: '#2C2C35',
      fontColor: '#2C2C35',
      data: k,
      x: 3
    };
    tempLinks.push({
      ...temp
    });
  });
  contains.forEach(k => {
    let temp = {
      from: k.from,
      to: k.to,
      isHide: true
    };
    tempLinks.push({
      ...temp
    });
  });
  let send = {
    rootId: 'a',
    nodes: tempNodes,
    links: tempLinks
  };
  return send;
}

function folder_fmt(node) {
  let label = node.label || 'N/A';
  let text = node.label || 'N/A';
  let reg = new RegExp('s_');
  let reg2 = new RegExp('d_');
  let reg3 = new RegExp('r_');
  // eslint-disable-next-line eqeqeq
  if (label.substr(0, 4) == 'APP_') {
    return label;
  }
  let cache = $store.state.long.Dict.nebula_type || $store.state.diction.dicList.nebula_type;
  if (node.label && cache[node.label.replace(reg, '')]) {
    text = cache[node.label.replace(reg, '')].desc || node.label || 'N/A';
  }
  if (node.label && cache[node.label.replace(reg2, '')]) {
    text = cache[node.label.replace(reg2, '')].desc || node.label || 'N/A';
  }
  if (node.label && cache[node.label.replace(reg3, '')]) {
    text = cache[node.label.replace(reg3, '')].desc || node.label || 'N/A';
  }
  label = node.label.replace(reg, '').replace(reg2, '').replace(reg3, '');
  return text;
}

function folder_img_fmt(label) {
  let reg = new RegExp('s_');
  let reg2 = new RegExp('d_');
  let reg3 = new RegExp('r_');
  label = label.replace(reg, '').replace(reg2, '').replace(reg3, '');
  if (label === "IP") {
    return {
      img: require('../../assets/graph/ip_默认.svg')
    };
  } else if (label === 'MAC') {
    return {
      img: require('../../assets/graph/mac_默认.svg')
    };
  } else if (label === 'UA') {
    return {
      img: require('../../assets/graph/UA_默认.svg')
    };
  } else if (label === 'DOMAIN') {
    return {
      img: require('../../assets/graph/域名_默认.svg')
    };
  } else if (label === 'APPSERVICE') {
    return {
      img: require('../../assets/graph/应用服务_默认.svg')
    };
  } else if (label === 'FDOMAIN') {
    return {
      img: require('../../assets/graph/锚域名_默认.svg')
    };
  } else if (label === 'CERT') {
    return {
      img: require('../../assets/graph/证书_默认.svg')
    };
  } else if (label === 'SSLFINGER') {
    return {
      img: require('../../assets/graph/指纹_默认.svg')
    };
  } else if (label === 'URL') {
    return {
      img: require('../../assets/graph/URL文件夹.svg')
    };
  }
}

function node_img_fmt(type) {
  switch (type) {
    // IP
    case 'IP':
      return {
        icon: {
          img: require('../../assets/graph/Node_IP_Blue.svg')
        }
      };
    // 域名
    case 'DOMAIN':
      return {
        icon: {
          img: require('../../assets/graph/Node_web_Blue.svg')
        }
      };
    // MAC
    case 'MAC':
      return {
        icon: {
          img: require('../../assets/graph/Node_MAC_Blue.svg')
        }
      };
    // 应用服务
    case 'APPSERVICE':
      return {
        icon: {
          img: require('../../assets/graph/Node_APP_Blue.svg')
        }
      };
    // 锚域名
    case 'FDOMAIN':
      return {
        icon: {
          img: require('../../assets/graph/Node_anchor_Blue.svg')
        }
      };
    // 证书CERT
    case 'CERT':
      return {
        icon: {
          img: require('../../assets/graph/Node_certificate_Blue.svg')
        }
      };
    // 企业
    case 'ORG':
      return {
        icon: {
          img: require('../../assets/graph/Node_enterprise_Blue.svg')
        }
      };
    // 指纹
    case 'SSLFINGER':
      return {
        icon: {
          img: require('../../assets/graph/Node_fingerprint_Blue.svg')
        }
      };
    case 'FINGER':
      return {
        icon: {
          img: require('../../assets/graph/Node_fingerprint_Blue.svg')
        }
      };
    // UA
    case 'UA':
      return {
        icon: {
          img: require('../../assets/graph/Node_UA_Blue.svg')
        }
      };
    // 硬件类型
    case 'DEVICE':
      return {
        icon: {
          img: require('../../assets/graph/Node_hardware_Blue.svg')
        }
      };
    // 系统类型
    case 'OS':
      return {
        icon: {
          img: require('../../assets/graph/Node_system_Blue.svg')
        }
      };
    // 应用类型
    case 'APPTYPE':
      return {
        icon: {
          img: require('../../assets/graph/Node_Apptype_Blue.svg')
        }
      };
    // 资产测绘
    case 'SURVEY_DATA':
      return {
        icon: {
          img: require('../../assets/graph/icon_资产测绘_默认.svg')
        }
      };
    // URL
    case 'URL':
      return {
        icon: {
          img: require('../../assets/graph/URL_默认.svg')
        }
      };
    // 区块链
    case 'BLOCKCHAIN':
      return {
        icon: {
          img: require('../../assets/graph/icon_blockchain.svg')
        }
      };
    default:
      return {
        icon: {
          img: require('../../assets/graph/URL_默认.svg')
        }
      };

  }
}



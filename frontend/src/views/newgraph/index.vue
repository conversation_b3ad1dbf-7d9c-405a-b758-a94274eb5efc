<template>
  <div
    v-loading="loading"
    class="newgraph"
    element-loading-text="数据请求中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(255, 255, 255, 0.8)"
    @click="closeNodeMenuPanel"
  >
    <div id="header" class="header">
      <div class="tool">
        <el-button icon="el-icon-setting" @click="TOOL_MENU">设置</el-button>
        <!-- <el-date-picker
          v-model="date"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          type="date"
          placeholder="选择日期"
          @change="changeDate"
        >
        </el-date-picker> -->
      </div>
      <div class="search">
        <div class="search-sign">
          <el-button type="primary" @click="RESET_SIGN">清除标记</el-button>
        </div>
        <el-button plain @click="defaultFlag = false"> 清空 </el-button>
        <el-button
          plain
          icon="el-icon-time"
          @click="searchHistoryVisible = true"
        >
          搜索历史
        </el-button>
        <el-dropdown type="primary" @command="handleCommand">
          <el-button type="primary">
            开始检索<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="TargetDialogVisible">
              目标ID检索
            </el-dropdown-item>
            <el-dropdown-item command="KeywordDialogVisible">
              关键词检索
            </el-dropdown-item>
            <el-dropdown-item command="SubgraphDialogVisible">
              子图遍历
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <template v-if="defaultFlag">
      <el-empty v-if="isEmpty" :image-size="350"></el-empty>
      <template v-else>
        <aside class="type-box">
          <div
            v-for="(item, index) of asideList"
            :key="index"
            class="type-box-cell"
          >
            <div>
              <img :src="item.src" alt="" />
            </div>
            <div>{{ item.name }}</div>
          </div>
        </aside>
        <div
          ref="mainbox"
          class="graph-box"
          @click="isShowNodeMenuPanel = false"
        >
          <SeeksRelationGraph
            ref="seeksRelationGraph"
            :options="G"
            :on-node-click="onNodeClick"
          >
            <div slot="node" slot-scope="{ node }">
              <div
                v-if="
                  node.data.type !== 'Folder' && node.data.type !== 'LABELS'
                "
                id="node"
                :class="NODE_CLASS(node.data.identity)"
                @contextmenu.prevent.stop="showNodeMenus(node, $event)"
              >
                <img :src="node.data.img" draggable="false" />
                <aside v-if="node.data.sign === true" class="sign">
                  <div>
                    <img src="@/assets/images/20230417-174410.png" alt="" />
                  </div>
                </aside>
              </div>
              <div
                v-if="node.data.type === 'Folder'"
                class="folder-node"
                @contextmenu.prevent.stop="showNodeMenus(node, $event)"
              >
                <img :src="node.data.img" draggable="false" />
              </div>
              <div v-if="node.data.type === 'LABELS'" class="labels-node">
                <section
                  v-for="(item, index) of node.data.labels"
                  :key="index"
                  :class="LABEL_LV(item)"
                >
                  {{ item.tagText }}
                </section>
                <aside class="del" @click="DEL_LABELS(node, $event)">
                  <i class="el-icon-close"></i>
                </aside>
              </div>
              <div v-if="node.data.type !== 'LABELS'" class="node-label">
                {{ node.data.label }}
              </div>
            </div>
          </SeeksRelationGraph>
        </div>
        <div
          v-show="isShowNodeMenuPanel"
          :style="{
            left: nodeMenuPanelPosition.x + 'px',
            top: nodeMenuPanelPosition.y + 'px',
          }"
          class="clickmenu"
        >
          <section v-if="currentNode.lot.childs.length" @click="onNodeExpand">
            {{ currentNode.expanded ? "收起" : "全部展开" }}
          </section>
          <section
            v-if="
              currentNode.lot.childs.length && currentNode.expanded == false
            "
            @click="expandedPart"
          >
            部分展开
          </section>
          <section @click="toggle">
            {{ currentNode.data.label ? "隐藏" : "显示" }}
          </section>
          <section @click="REAL">关联</section>
          <section v-if="currentNode.data.sign" @click="CANCEL_SIGN">
            取消标记
          </section>
          <section v-else @click="SIGN">标记</section>
          <section
            v-if="
              ['CERT', 'IP', 'DOMAIN', 'SSLFINGER', 'APP'].includes(
                currentNode.data.type
              )
            "
            @click="VIEW_LABELS"
          >
            查看标签
          </section>
          <section @click="DEL_NODE">删除</section>
        </div>
      </template>
    </template>
    <div v-else class="default">
      <img src="@/assets/images/Frame3298.svg" alt="" />
      <div class="a2">图探索</div>
      <div class="a3">
        图探索主要用来构建图联通关系，并实现在长时空维度下的图空间检索。在构建知识图谱的基础上，通过图关系的约束，实现聚维图谱分析。
      </div>
    </div>
    <el-dialog
      :title="relaTitle"
      :visible.sync="relaShow"
      width="600px"
      class="rela-dialog"
      destroy-on-close
      :show-close="!relaLoading"
      :close-on-press-escape="!relaLoading"
      :close-on-click-modal="!relaLoading"
    >
      <div
        v-loading="relaLoading"
        element-loading-text="数据请求中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 1)"
      >
        <el-checkbox
          v-model="checkAll"
          :indeterminate="isIndeterminate"
          @change="handleCheckAllChange"
        >
          全部关联
        </el-checkbox>
        <div style="margin: 15px 0"></div>
        <el-checkbox-group
          v-model="relaArr"
          @change="handleCheckedCitiesChange"
        >
          <div
            v-for="(item, index) in relaOptions"
            :key="index"
            class="check-cell"
          >
            <el-checkbox :key="index" :label="item.name">
              {{ item.label }}
            </el-checkbox>
            <el-select
              v-model="item.black"
              clearable
              size="mini"
              style="margin-left: auto; margin-right: 5px"
            >
              <el-option
                v-for="(item2, index2) in balckOptions"
                :key="index2"
                :label="item2.label"
                :value="item2.black"
              >
              </el-option>
            </el-select>
            <el-input-number
              v-model="item.num"
              :min="1"
              :max="10"
              size="mini"
            ></el-input-number>
          </div>
        </el-checkbox-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="relaLoading" @click="relaShow = false">取 消</el-button>
        <el-button
          type="primary"
          :disabled="handleSubmit || relaLoading"
          @click="CLICK_REAL"
        >确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="设置"
      width="480px"
      :visible.sync="settingShow"
      custom-class="setting"
    >
      <el-form label-position="top" label-width="80px" :model="settingForm">
        <el-form-item label="方向">
          <el-radio v-model="settingForm.position" label="all">双向</el-radio>
          <el-radio v-model="settingForm.position" label="out">流出</el-radio>
          <el-radio v-model="settingForm.position" label="in">流入</el-radio>
        </el-form-item>
        <el-form-item label="全局节点名称">
          <el-radio v-model="settingForm.name" label="1">显示</el-radio>
          <el-radio v-model="settingForm.name" label="0">隐藏</el-radio>
        </el-form-item>
        <el-form-item label="默认最大部分展开节点数">
          <el-input v-model="settingForm.num"></el-input>
        </el-form-item>
        <el-form-item label="节点是否跟随移动">
          <el-radio v-model="G.isMoveByParentNode" :label="1">是</el-radio>
          <el-radio v-model="G.isMoveByParentNode" :label="0">否</el-radio>
        </el-form-item>
      </el-form>
    </el-dialog>
    <Detail
      v-if="visibleShow"
      :visible-show="visibleShow"
      :detail-value="detailValue"
      :detail-type="detailType"
      :detail-label="detailLabel"
      @DETAIL_CLOSE="DETAIL_CLOSE"
    />
    <SearchHistory
      v-model="searchHistoryVisible"
      @searchResult="searchResult"
    ></SearchHistory>
    <TargetDialog
      v-model="TargetDialogVisible"
      header-title="目标ID检索"
      @targetDialog="INIT_GRAPH"
    />
    <KeywordDialog
      v-model="KeywordDialogVisible"
      header-title="关键词检索"
      @keywordDialog="getList"
    />
    <SubgraphDialog
      v-model="SubgraphDialogVisible"
      header-title="子图遍历 "
      @SUBMIT_NODE_SEARCH="SUBMIT_NODE_SEARCH"
    />
  </div>
</template>

<script>
import SearchHistory from "./components/SearchHistory.vue";
import SeeksRelationGraph from "relation-graph";
import {
  get_next,
  get_edge_json,
  search_node,
  tagLabels,
  search_properties,
  visibleRelation,
  subSearch,
  focusTag,
  get_data
} from "@/api/graph";
import { formatGraphData } from "./fmtData";
import Detail from "./detail/index.vue";
import TargetDialog from "./components/TargetDialog";
import SubgraphDialog from "./components/SubgraphDialog";
import KeywordDialog from "./components/KeywordDialog";


export default {
  name: "Newgraph",
  components: {
    SeeksRelationGraph,
    Detail,
    SearchHistory,
    TargetDialog,
    SubgraphDialog,
    KeywordDialog,
  },
  data() {
    return {
      /* 标签 */
      initValue:'',
      initType:'',
      TargetDialogVisible: false,
      SubgraphDialogVisible: false,
      KeywordDialogVisible: false,
      searchHistoryVisible: false,
      ExpandDialogVisible:false,
      loading: false,
      isShowNodeMenuPanel: false,
      nodeMenuPanelPosition: { x: 0, y: 0 },
      currentNode: {
        data: {
          type: "",
          sign:false
        },
        lot: {
          childs: [],
        },
      },
      G: {
        /* 线y轴偏移量 */
        // defaultLineTextOffset_y:16,
        showDebugPanel: false,
        g_loading: true,
        demoname: "---",
        defaultNodeBorderWidth: 0,
        defaultNodeColor: "rgba(238, 178, 94, 1)",
        defaultLineColor: "#000",
        allowSwitchLineShape: true,
        defaultLineShape: 1,
        defaultJunctionPoint: "border",
        // defaultLineMarker:{
        //   "markerWidth": "16",
        //   "markerHeight": "16",
        //   "refX": 6,
        //   "refY": 6,
        //   "data": "M2,2 L10,6 L2,10 L6,6 L2,2"
        // },
        layouts: [
          {
            label: "力导",
            layoutName: "force",
            "force_node_repulsion": 0.8,
            "force_line_elastic": 1
          },
        ],
        // defaultLineMarker: {
        //   markerWidth: 20,
        //   markerHeight: 20,
        //   refX: 3,
        //   refY: 3,
        //   data: "M 0 0, V 6, L 4 3, Z",
        // },
        // 节点跟随
        isMoveByParentNode: 0
      },
      // 关联弹出框
      handleList: [],
      relaShow: false,
      relaTitle: "",
      checkAll: false,
      allOptions: [],
      relaArr: [],
      relaOptions: [],
      isIndeterminate: false,
      handleSubmit: true,
      relaLoading: false,
      // 设置弹出框
      settingShow: false,
      settingForm: {
        position: "all",
        name: "1",
        num: 5,
        nodeExpandNum:5
      },
      // 搜索的值
      searchValue: "",
      // 详情组件相关
      visibleShow: false,
      detailValue: "",
      detailType: "",
      detailLabel: "",
      // 左侧图例
      asideListOLD: [
        {
          name: "签发机构",
          src: require("../../assets/graph/icon_org.svg"),
        },
        {
          name: "所有者",
          src: require("../../assets/graph/icon_owner.svg"),
        },
        {
          name: "应用服务",
          src: require("../../assets/graph/icon_APP.svg"),
        },
        {
          name: "APP",
          src: require("../../assets/graph/icon_appservice.svg"),
        },
        {
          name: "域名",
          src: require("../../assets/graph/icon_web.svg"),
        },
        {
          name: "锚域名",
          src: require("../../assets/graph/icon_anchor.svg"),
        },
        {
          name: "关联证书",
          src: require("../../assets/graph/icon_certificate.svg"),
        },
        {
          name: "企业名称",
          src: require("../../assets/graph/icon_enterprise.svg"),
        },
        {
          name: "SSL指纹",
          src: require("../../assets/graph/icon_fingerprint.svg"),
        },
      ],
      // 侧边栏图例
      asideList:[{
        name:'IP',
        src:require('../../assets/graph/IP.svg')
      },{
        name:'MAC',
        src:require('../../assets/graph/MAC.svg')
      },{
        name:'应用服务',
        src:require('../../assets/graph/icon_APP_Service.svg')
      },{
        name:'域名',
        src:require('../../assets/graph/icon_web.svg')
      },{
        name:'锚域名',
        src:require('../../assets/graph/icon_anchor.svg')
      },{
        name:'证书',
        src:require('../../assets/graph/icon_certificate.svg')
      },{
        name:'签发机构',
        src:require("../../assets/graph/icon_org.svg")
      },{
        name:'所有者',
        src:require("../../assets/graph/icon_owner.svg")
      },{
        name:'组织',
        src:require("../../assets/graph/icon_enterprise.svg")
      },{
        name:'SSL指纹',
        src:require('../../assets/graph/icon_fingerprint.svg')
      },{
        name:'UA',
        src:require('../../assets/graph/icon_UA.svg')
      },{
        name:'硬件类型',
        src:require('../../assets/graph/icon_hardware.svg')
      },{
        name:'操作系统',
        src:require('../../assets/graph/icon_system.svg')
      },{
        name:'URL',
        src:require('../../assets/graph/URL_icon.svg')
      },{
        name:'应用',
        src:require("../../assets/graph/icon_APP.svg")
      }],
      // 黑名单权重下拉
      balckOptions: [
        {
          label: "默认(所有等级)",
          black: "",
        },
        {
          label: "高危",
          black: [91, 100],
        },
        {
          label: "可疑",
          black: [61, 90],
        },
        {
          label: "未知",
          black: [1, 60],
        },
        {
          label: "安全",
          black: [0, 0],
        },
      ],
      balckValue: "",
      initId: "",
      initIndex: "",
      defaultFlag: false,
      historyResult: {
        1: "INIT_GRAPH",
        2: "getList",
        3: "SUBMIT_NODE_SEARCH",
      },
      isEmpty: true,
      date: "",
      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() > Date.now() ||
            time.getTime() < Date.now() - 30 * 24 * 3600 * 1000
          );
        },
      },
    };
  },
  watch: {
    "settingForm.position": {
      handler(val) {
        localStorage.setItem("detailLablePosition", val);
      },
      immediate: true,
      deep: true, 
    },
    "settingForm.num": function (val, old) {
      localStorage.setItem("nextNum", val);
    },
    "settingForm.name": function (val, old) {
      this.settingForm.name = val;
      this.$nextTick(()=>{
        const graphInstance = this.$refs.seeksRelationGraph;
        if(!graphInstance)return;
        const nodes = graphInstance.getNodes();
        if(this.settingForm.name === "0"){
        /* 隐藏的节点名称 */
          nodes.forEach(node=>{
            node.data.label = '';
          });
        }else{
          nodes.forEach(node=>{
            node.data.label = node.data.hideLabel;
          });
        }
      });

  
      localStorage.setItem("nameShow", val);
    },
    "G.isMoveByParentNode": function (val, old) {
      localStorage.setItem("isMoveByParentNode", val);
      this.$refs.seeksRelationGraph.setOptions({
        isMoveByParentNode: val,
      });
    },
  },
  created(){
    /* 初始化的时候都 */
    localStorage.setItem('nextNum', 5);
    if (this.$route.query.initValue && this.$route.query.initType) {
      this.initValue = this.$route.query.initValue;
      this.initType = this.$route.query.initType;
      this.get_data();
    }
  },
  methods: {
    /* 通过事件冒泡的形式获取 */
    closeNodeMenuPanel(event) {
    // 检查点击事件是否发生在弹窗内部
      const isClickInsideMenu = event.target.closest('.clickmenu');
      if (!isClickInsideMenu) {
        this.isShowNodeMenuPanel = false;
      }
    },
    // 清除标记
    RESET_SIGN() { 
      const allNodes = this.$refs.seeksRelationGraph.getInstance().getNodes();
      const allLinks = this.$refs.seeksRelationGraph.getInstance().getLinks();
      allNodes.forEach(item => {           
        item.opacity = 1;
        item.data.sign = false;
      });
      allLinks.forEach(item => {           
        item.isHide = false;
        item.relations.forEach(ri => { 
          ri.color = '#2C2C35';
          ri.fontColor = '#2C2C35';
        });
      });
      this.$refs.seeksRelationGraph.getInstance().dataUpdated();
    },
    changeDate(val) {
      this.defaultFlag = false;
    },
    searchResult(row) {
      const { atlas_type, atlas_condition } = row;
      const val = JSON.parse(atlas_condition);
      this.date = "";
      let fn = this.historyResult[atlas_type]; /* string */
      this[fn](val); /* 动态调用方法 */
    },
    handleCommand(val) {
      this[val] = true;
    },
    getList(params, callback) {
      this.loading = true;
      search_properties(params)
        .then((res) => {
          if ((res.err === 0||res.code===200) && res.data.vertex.length > 0) {
            this.defaultFlag = true;
            this.isEmpty = false;
            this.loading = false;
            this.$nextTick(() => {
              this.$refs.seeksRelationGraph.setJsonData(
                formatGraphData(res.data),
                () => {
                  // 关系图渲染完成时会调用
                  this.$message.success("图谱渲染完成");
                  callback && callback();
                  for (let i of this.$refs.seeksRelationGraph.getGraphJsonData()
                    .nodes) {
                    i.fixed = true;
                    i.x = 100;
                    i.y = 500;
                  }
                  this.$nextTick(() => {
                    this.$refs.seeksRelationGraph.refresh();
                  });
                }
              );
            });
          } else {
            this.defaultFlag = true;
            this.loading = false;
            this.isEmpty = true;
            callback && callback();
          }
        })
        .catch((err) => {
          this.loading = false;
          this.defaultFlag = true;
          this.isEmpty = true;
        });
    },
    // 显示隐藏名称
    toggle() {
      const targetNodeId = this.currentNode.id;
      // 获取relation-graph 实例
      const graphInstance = this.$refs.seeksRelationGraph.getInstance();
      // 调用实例方法获取节点对象
      const targetNode = graphInstance.getNodeById(targetNodeId);
      if (targetNode.data.label) {
        targetNode.data.label = "";
      } else {
        targetNode.data.label = targetNode.data.hideLabel;
      }
    },
    // 展开和收起
    async onNodeExpand() {
      const targetNodeId = this.currentNode.id;
      // 获取relation-graph 实例
      const graphInstance = this.$refs.seeksRelationGraph.getInstance();
      // 调用实例方法获取节点对象
      let targetNode = graphInstance.getNodeById(targetNodeId);
      // 根据节点对象属性判断节点是否已经展开
      if (targetNode.expanded === true) {
        // 调用实例方法收缩节点
        await graphInstance.collapseNode(targetNode); // 这个e可以不传，除非需要触发<relation-graph />组件的on-node-click事件且该事件中需要使用到event对象
      } else {
        this.isShowNodeMenuPanel = false;
        const graphInstance = this.$refs.seeksRelationGraph.getInstance();
        const targetNodeId = this.currentNode.id;
        let targetNode = graphInstance.getNodeById(targetNodeId);
        await graphInstance.expandNode(targetNode);
      }
    },
    async expandedPart(){
      // 获取relation-graph 实例
      const graphInstance = this.$refs.seeksRelationGraph.getInstance();
      const targetNodeId = this.currentNode.id;
      let targetNode = graphInstance.getNodeById(targetNodeId);
      /* 获取设置的部分展开数 */
      let maxExpandNum = Math.abs(localStorage.getItem('nextNum')); 
      // 获取relation-graph 实例
      const openByLevel = (level)=> {
        /* 重置数据 */
        graphInstance.getNodes().forEach(node => {
          node.expanded = true;
        });
        const targetLevel = Math.abs(targetNode.lot.level);
        graphInstance.getNodes().forEach(node=>{
          let currentLevel = targetLevel + level;
          if(Math.abs(node.lot.level)=== currentLevel){
            node.expanded = false;
          }
        });
      };
      openByLevel(maxExpandNum);
    },
    async  expandNodeOption(type){
      // 获取relation-graph 实例
      const graphInstance = this.$refs.seeksRelationGraph.getInstance();
      const targetNodeId = this.currentNode.id;
      let targetNode = graphInstance.getNodeById(targetNodeId);
      const levelNodes = graphInstance.getNodeById(targetNodeId).lot.childs;
      // 调用实例方法获取节点对象
      if(type === 'full'){/* 全部展开 */
        await graphInstance.expandNode(targetNode);
      }else{
        /* 获取设置的部分展开数 */
        let maxExpandNum = Math.abs(localStorage.getItem('nextNum')); 
        // 获取relation-graph 实例
        const graphInstance = this.$refs.seeksRelationGraph.getInstance();
        const openByLevel = (level)=> {
          /* 重置数据 */
          graphInstance.getNodes().forEach(node => {
            node.expanded = true;
          });
          const targetLevel = Math.abs(targetNode.lot.level);
          graphInstance.getNodes().forEach(node=>{
            let currentLevel = targetLevel + level;
            if(Math.abs(node.lot.level)=== currentLevel){
              node.expanded = false;
            }
          });
        };
        openByLevel(maxExpandNum);
      }
      this.$nextTick(async()=>{
        await graphInstance.doLayout();
      });
    },
    deepCopyWithCircular(obj, hash = new WeakMap()) {
      if (Object(obj) !== obj) return obj; // primitives
      if (hash.has(obj)) return hash.get(obj); // circular reference
  
      let result;
      if (obj instanceof Set) {
        result = new Set();
        hash.set(obj, result);
        obj.forEach(value => result.add(this.deepCopyWithCircular(value, hash)));
      } else if (obj instanceof Map) {
        result = new Map();
        hash.set(obj, result);
        obj.forEach((value, key) => result.set(this.deepCopyWithCircular(key, hash), this.deepCopyWithCircular(value, hash)));
      } else if (Array.isArray(obj)) {
        result = [];
        hash.set(obj, result);
        obj.forEach((value, index) => result[index] = this.deepCopyWithCircular(value, hash));
      } else if (obj instanceof Object) {
        result = {};
        hash.set(obj, result);
        Object.keys(obj).forEach(key => result[key] = this.deepCopyWithCircular(obj[key], hash));
      }
  
      return result;
    },
    // 获取边的转折点关系
    GET_JSON() {
      get_edge_json().then((res) => {
        this.handleList = res.data;
      });
    },
    INIT_GRAPH(vid_list, callback) {
      /* 初始化显示状态 */
      this.settingForm.name = '1';
      this.loading = true;
      search_node(vid_list)
        .then((res) => {
          if ((res.err === 0||res.code===200) && res.data.vertex.length > 0) {
            this.defaultFlag = true;
            this.loading = false;
            this.isEmpty = false;
            this.$nextTick(() => {
              this.$refs.seeksRelationGraph.setJsonData(
                formatGraphData(res.data),
                (seeksRGGraph) => {
                  // 关系图渲染完成时会调用
                  this.$message.success("图谱渲染完成");
                  callback && callback();
                  for (let i of this.$refs.seeksRelationGraph.getGraphJsonData()
                    .nodes) {
                    i.fixed = false;
                    i.x = 100;
                    i.y = 500;
                  }
                  this.$nextTick(() => {
                    this.$refs.seeksRelationGraph.refresh();
                  });
                }
              );
            });
          } else {
            this.defaultFlag = true;
            this.loading = false;
            this.isEmpty = true;
            callback && callback();
          }
        })
        .catch((err) => {
          this.loading = false;
          this.defaultFlag = true;
          this.isEmpty = true;
        });
    },
    // 图数据
    get_data(){
      // 图数据
      get_data({
        str: this.initValue,
        type: this.initType
      }).then(res => {
        if (res.err === 0 && res.data.vertex.length > 0) {
          this.defaultFlag = true;
          this.loading = false;
          this.isEmpty = false;
          this.$nextTick(() => {
            this.$refs.seeksRelationGraph.setJsonData(
              formatGraphData(res.data),
              (seeksRGGraph) => {
                // 关系图渲染完成时会调用
                this.$message.success("图谱渲染完成");
                for (let i of this.$refs.seeksRelationGraph.getGraphJsonData()
                  .nodes) {
                  i.fixed = false;
                  i.x = 100;
                  i.y = 500;
                }
                this.$nextTick(() => {
                  this.$refs.seeksRelationGraph.refresh();
                });
              }
            );
          });
        }else {
          this.defaultFlag = true;
          this.loading = false;
          this.isEmpty = true;
        }
        this.loading = false;
      }).catch(err => {
        this.loading = false;
      });
    },  
    onNodeClick(node, $event) {
      if (
        node.data.type === "DOMAIN" ||
        node.data.type === "IP" ||
        node.data.type === "CERT" ||
        node.data.type === "ORG" ||
        node.data.type === "BLOCKCHAIN" ||
        node.data.type === "SSLFINGER" ||
        node.data.type === "APPSERVICE" ||
        node.data.type === "APP"
      ) {
        this.detailType = node.data.type;
        this.detailValue = node.data.id;
        this.detailLabel = node.data.label;
        this.visibleShow = true;
      }
    },
    showNodeMenus(nodeObject, $event) {
      this.currentNode = nodeObject;
      let _base_position = this.$refs.mainbox.getBoundingClientRect();
      this.isShowNodeMenuPanel = true;
      this.nodeMenuPanelPosition.x = $event.clientX - _base_position.x;
      this.nodeMenuPanelPosition.y = $event.clientY - _base_position.y;
    },
    // 关联单选多选
    handleCheckAllChange(val) {
      this.relaArr = val ? this.allOptions : [];
      this.isIndeterminate = false;
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.relaOptions.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.relaOptions.length;
    },
    // 点击关联
    REAL() {
      this.isShowNodeMenuPanel = false;
      const model = this.currentNode.data;
      let direct = "";
      let directName = '';
      if (localStorage.getItem("detailLablePosition") === "all") {
        direct = "bothway";
        directName = '双向';
      } else if (localStorage.getItem("detailLablePosition") === "in") {
        direct = "reverse";
        directName = '流入';
      } else if (localStorage.getItem("detailLablePosition") === "out") {
        direct = "forward";
        directName = '流出';
      } else {
        direct = "bothway";
        directName = '双向';
      }
      let newArr = [];
      this.handleSubmit = true;
      let str = model.id;
      let params = {
        str: str,
        type: model.type,
        direct,
      };
      visibleRelation(params).then((res) => {
        const {msg} = res;
        if (res.err === 0||res.code===200) {
          this.handleSubmit = false;
          this.allOptions = res.data;
          let arr = res.data;
          if(arr.length>0){
            for (let i = 0; i < arr.length; i++) {
              newArr.push({
                label:
                this.$store.state.long.Dict.nebula_type[arr[i]]?.desc ||
                "未知（出错）",
                name: arr[i],
                num: this.settingForm.nodeExpandNum,
                black: "",
              });
            }
            this.relaShow = true;
          }else{
            this.$message({
              message:`未查询到方向为${directName}的关联数据`,
              type:'warning'
            });
          }

        }else{
          this.relaShow = false;
          this.$message({
            message:msg,
            type:'error'
          });

        }
      });
      this.relaOptions = newArr;
      this.relaTitle = model.label;
      this.isIndeterminate = false;
      this.checkAll = false;
      this.relaArr = [];
      
    },
    // 点击关联框中的确认
    CLICK_REAL() {
      this.relaLoading = true;
      let arr = [];
      for (let i = 0; i < this.relaOptions.length; i++) {
        for (let j = 0; j < this.relaArr.length; j++) {
          if (this.relaArr[j] === this.relaOptions[i].name) {
            // 剔除需要传到后端的前缀带r_的字符串
            if (
              this.relaOptions[i].name.substr(0, 2) === "r_" &&
              this.relaOptions[i].name != "r_connect_ip" &&
              this.relaOptions[i].name != "r_connect_mac" &&
              this.relaOptions[i].name != "r_cname" &&
              this.relaOptions[i].name != "r_client_query_dns_server" &&
              this.relaOptions[i].name != "r_blockchain_deal"
            ) {
              this.relaOptions[i].name = this.relaOptions[i].name.slice(2);
            }
            arr.push({
              edge: this.relaOptions[i].name,
              num: this.relaOptions[i].num,
              weight_limit:
                this.relaOptions[i].black === ""
                  ? [0, 100]
                  : this.relaOptions[i].black,
            });
          }
        }
      }
      let requestData = {
        str: this.currentNode.data.id,
        type: this.currentNode.data.type,
        edgeInfo: arr,
      };
      get_next(requestData)
        .then((res) => {
          if ((res.err === 0||res.code===200)&& res.data.vertex.length > 0) {
            // 线条去重
            let setData = {
              edge: res.data.edge,
              vertex: res.data.vertex,
            };
            const originData = this.$refs.seeksRelationGraph.getGraphJsonData(); 
            const addData = formatGraphData(setData);
            /* 组装 */
            const allNodes = [...originData.nodes,...addData.nodes];
            const allLines = [...originData.lines,...addData.lines];
            const filterNodes = [...new Map(allNodes.map(node => [node.id, node])).values()];
            const filterLines = [...new Map(allLines.map(line => {
              const key = `${line.from}-${line.to}-${line.text}`; // 创建组合键
              return [key, line];
            })).values()];
            const newData = {
              rootId:originData.rootId,
              lines:filterLines,
              nodes:filterNodes
            };
            this.$refs.seeksRelationGraph.setJsonData(newData,(v)=>{});
            // this.$refs.seeksRelationGraph.appendJsonData(
            //   formatGraphData(setData),
            //   true,
            //   (seeksRGGraph) => {
            //     // 关系图渲染完成时会调用
            //     this.$message.success("新增节点");
            //     // let links = this.$refs.seeksRelationGraph.getLinks();
            //     // for (let i = 0; i < links?.length; i++) {
            //     //   let arr = [];
            //     //   for (let j = 0; j < links[i].relations.length; j++) {
            //     //     let t = links[i].relations[j];
            //     //     if (
            //     //       arr.find(
            //     //         (k) =>
            //     //           k.to === t.to &&
            //     //           k.from === t.from &&
            //     //           k.label === t.label
            //     //       )
            //     //     ) {
            //     //       continue;
            //     //     }
            //     //     arr.push(t);
            //     //   }
            //     //   links[i].relations = arr;
            //     // }
            //   }
            // );
          }
          this.relaLoading = false;
          this.relaShow = false;
        })
        .catch((err) => {
          this.relaLoading = false;
          this.relaShow = false;
        });
    },
    // 工具栏打开设置菜单
    TOOL_MENU() {
      this.settingShow = true;
    },
    // 详情框关闭
    DETAIL_CLOSE() {
      this.visibleShow = false;
    },
    // 点击查看标签
    VIEW_LABELS() {
      this.isShowNodeMenuPanel = false;
      let arr = [
        {
          str: this.currentNode.data.id,
          type: this.currentNode.data.type,
        },
      ];
      tagLabels(arr).then((res) => {
        const {data} = res;
        if(data.edge.length === 0 && data.vertex.length === 0){
          this.$message({
            message:"未查询到标签数据",
            type:'warning'
          });
          return ;
        }
        let setData = {
          edge: data.edge,
          vertex: data.vertex,
        };
        const formatData  = formatGraphData(setData);
        const {nodes} = formatData;
        const tagNum = data.vertex[0].labels.length;
        let nodeWidth = '240';
        if(tagNum <=3){
          nodeWidth= (tagNum*80).toString();
        }else{
          nodeWidth = '240';
        }
        nodes[0].width = nodeWidth;
        formatData.nodes = nodes;
        this.$refs.seeksRelationGraph.appendJsonData(
          formatData,
          (seeksRGGraph) => {
            // 关系图渲染完成时会调用
            let links = seeksRGGraph.getLinks();
            for (let i = 0; i < links.length; i++) {
              let arr = [];
              for (let j = 0; j < links[i].relations.length; j++) {
                let t = links[i].relations[j];
                if (
                  arr.find(
                    (k) =>
                      k.to === t.to && k.from === t.from && k.label === t.label
                  )
                ) {
                  continue;
                }
                arr.push(t);
              }
              links[i].relations = arr;
            }
          }
        );
        const targetNodeId = this.currentNode.id;
        // 获取relation-graph 实例
        const graphInstance = this.$refs.seeksRelationGraph.getInstance();
        // 调用实例方法获取节点对象
        const targetNode = graphInstance.getNodeById(targetNodeId);
        if(targetNode.expanded === false){
          targetNode.expanded = true;
        }
      });
      
    },
    // 标签格式化
    LABEL_LV(item) {
      let type = "info";
      if (
        item.blackList >= 1 &&
        item.blackList <= 100 &&
        item.whiteList !== 100
      ) {
        if (item.blackList >= 80) {
          type = "danger";
        } else {
          type = "warning";
        }
      }
      if (
        item.whiteList >= 1 &&
        item.whiteList <= 100 &&
        item.blackList === 0
      ) {
        if (item.whiteList === 100) {
          type = "success";
        } else {
          type = "";
        }
      }
      if (item.whiteList === 0 && item.blackList === 0) {
        type = "info";
      }
      return type;
    },
    // 删除标签
    DEL_LABELS(node) {
      this.$refs.seeksRelationGraph.removeNodeById(node.id);
    },
    // 删除节点
    DEL_NODE() {
      this.$refs.seeksRelationGraph.removeNodeById(this.currentNode.id);
      this.isShowNodeMenuPanel = false;
      this.$message.info("已删除节点（非物理删除）");
    },
    // 节点class判断
    NODE_CLASS(identity) {
      switch (identity) {
      case "attacker":
        return "attacker-node";
      case "victim":
        return "victim-node";
      case "neutrality":
        return "neutrality-node";
      default:
        return "node";
      }
    },
    // 弹窗探索确认
    SUBMIT_NODE_SEARCH(params, callback) {
      this.loading = true;
      subSearch(params)
        .then((res) => {
          if ((res.err === 0||res.code===200) && res.data.vertex.length > 0) {
            this.defaultFlag = true;
            this.isEmpty = false;
            this.loading = false;
            this.$nextTick(() => {
              this.$refs.seeksRelationGraph.setJsonData(
                formatGraphData(res.data),
                (seeksRGGraph) => {
                  // 关系图渲染完成时会调用
                  this.$message.success("图谱渲染完成");
                  callback && callback();
                  for (let i of this.$refs.seeksRelationGraph.getGraphJsonData()
                    .nodes) {
                    i.fixed = true;
                    i.x = 100;
                    i.y = 500;
                  }
                  this.$nextTick(() => {
                    this.$refs.seeksRelationGraph.refresh();
                  });
                }
              );
            });
          } else {
            this.defaultFlag = true;
            this.loading = false;
            this.isEmpty = true;
            callback && callback();
          }
        })
        .catch((err) => {
          this.loading = false;
          this.defaultFlag = true;
          this.isEmpty = true;
        });
    },
    // 右键点击节点标记
    SIGN() {
      this.isShowNodeMenuPanel = false;
      this.currentNode.data.sign = true;
      const allNodes = this.$refs.seeksRelationGraph.getInstance().getNodes();
      this.$refs.seeksRelationGraph.getInstance().dataUpdated();
      let signArr = [];
      for (let i = 0; i < allNodes.length; i++) {
        if (allNodes[i].data.sign) {
          signArr.push({
            vid: allNodes[i].data.id,
            type: allNodes[i].data.type,
          });
        }
      }
      if (signArr.length > 1) {
        focusTag(signArr).then((res) => {
          if ((res.err === 0||res.code===200) && res.data.length > 0) {
            let setData = {
              edge: res.data,
              vertex: [],
            };
            // 先把其余变透明
            const allNodes = this.$refs.seeksRelationGraph
              .getInstance()
              .getNodes();
            const allLinks = this.$refs.seeksRelationGraph
              .getInstance()
              .getLinks();
            allNodes.forEach((item) => {
              item.opacity = item.data.sign ? 1 : 0.1;
            });
            allLinks.forEach((item) => {
              item.isHide = false;
              if (item.fromNode.data.sign && item.toNode.data.sign) {
                item.isHide = false;
                item.relations.forEach((ri) => {
                  ri.color = "#116EF9";
                  ri.fontColor = "#116EF9";
                });
              } else {
                item.isHide = true;
           
              }
            });
            this.$refs.seeksRelationGraph.getInstance().dataUpdated();
            this.$refs.seeksRelationGraph.appendJsonData(
              formatGraphData(setData),
              (seeksRGGraph) => {
                this.$refs.seeksRelationGraph.getInstance().dataUpdated();
                this.LINE_DE_WEIGHT();
              }
            );
          }
        });
      }
    },
    // 取消标记
    CANCEL_SIGN() {
      this.isShowNodeMenuPanel = false;
      this.currentNode.data.sign = false;
      this.$refs.seeksRelationGraph.getInstance().dataUpdated();
    },
    // 线条去重函数
    LINE_DE_WEIGHT() { 
      this.$message.success("新增节点");
      let links = this.$refs.seeksRelationGraph.getLinks();
      for (let i = 0; i < links?.length; i++) {
        let arr = [];
        for (let j = 0; j < links[i].relations.length; j++) {
          let t = links[i].relations[j];
          if (
            arr.find(
              (k) =>
                k.to === t.to &&
                          k.from === t.from &&
                          k.label === t.label
            )
          ) {
            continue;
          }
          arr.push(t);
        }
        links[i].relations = arr;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>
<template>
  <el-dialog
    :title="headerTitle"
    :visible.sync="isShow"
    width="480px"
    custom-class="custom--dialog"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="top"
    >
      <el-form-item label="目标类型" prop="vid_list">
        <el-input v-model="form.vid_list" placeholder="请输入目标ID，一行一个目标，最多1000条" type="textarea" @input="validateVidList"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSearch">开始探索</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from "@/mixins";
import { validUsername } from "@/utils/validate";
export default {
  name: "TargetDialog",
  mixins: [mixins],
  data() {
    return {
      form: {
        vid_list: "",
      },
      rules: {
        vid_list: [
          { required: true, message: '请输入目标ID', trigger: 'blur' },
        ]
      },
    };
  },
  methods: {
    handleSearch() {
      this.$refs.formRef.validate((valid) => {
        if (!valid)  return false; 
        const params={
          vid_list:this.form.vid_list.split('\n')
        };
        this.$emit('targetDialog',params,()=>{
          this.isShow = false;
        });
      });
    },
    handleClose(){
      this.$refs.formRef.resetFields();
      this.isShow = false;
    },
    validateVidList() {
      this.$refs.formRef.validateField('vid_list');
    }
  },
};
</script>
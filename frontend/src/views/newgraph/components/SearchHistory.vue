<template>
  <el-drawer
    title="检索历史"
    :visible.sync="drawerShow"
    custom-class="drawer-detail"
    size="1120px"
    @close="handleClose"
  >
    <div class="drawer-content">
      <div class="drawer-content-search">
        <el-button plain @click="handleDelete">删除</el-button>
        <el-select v-model="search" placeholder="请选择" @change="changeValue">
          <el-option
            v-for="item in atlasTypeList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          >
          </el-option>
        </el-select>
      </div>
      <div class="drawer-content-table">
        <el-table
          ref="tableRef"
          :data="tableData"
          style="width: 100%"
          tooltip-effect="dark"
          border
          stripe
          :header-cell-style="headerStyle"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column
            width="200"
            prop="atlas_type_cn"
            label="检索类型"
          >
          </el-table-column>
          <el-table-column
            width="200"
            prop="created_time"
            label="检索时间"
            :formatter="FMT_TALBE_TIME"
          >
          </el-table-column>
          <el-table-column
            prop="atlas_condition"
            label="检索条件"
          >
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="120">
            <template slot-scope="scoped">
              <el-button
                type="text"
                size="small"
                @click.native.prevent="SEARCH_ROW( scoped.row)"
              >
                检索
              </el-button>
              <el-button
                type="text"
                size="small"
                @click.native.prevent="DEL_ROW([scoped.row.id])"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="page-box">
        <el-pagination
          :current-page="current_page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
    <div class="drawer-detail__footer history-footer">
      <div>
        <el-button plain @click="ALL_DEL"> 清除历史 </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import dayjs from "dayjs";
import { graph_history, graph_del } from "@/api/graph";
export default {
  name: "SearchHistory",
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      search: null, // 搜索条件
      current_page: 1,
      page_size: 10,
      total: 0,
      tableData: [],
      multipleSelection: [],
      atlasTypeList: [
        {key:null,value:"全部"},
        { key: 1, value: "目标ID检索" },
        { key: 2, value: "关键词检索" },
        { key: 3, value: "子图遍历" },
      ],
    };
  },
  computed: {
    // 表格头部样式
    headerStyle() {
      return { color: "#1B428D", background: "#F2F7FF" };
    },
    drawerShow: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  watch: {
    drawerShow(val) {
      if (val) {
        this.GET_DATA();
      }
    },
  },
  methods: {
    handleClose() {
      this.search = null;
    },
    changeValue(val){
      // if(val){
      this.current_page=1;
      this.GET_DATA();
      // }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSizeChange(val) {
      this.page_size = val;
      this.GET_DATA();
    },
    handleCurrentChange(val) {
      this.current_page = val;
      this.GET_DATA();
    },
    GET_DATA() {
      graph_history({
        atlas_type: this.search,
        current_page: this.current_page,
        page_size: this.page_size,
        sort_order: "desc",
      })
        .then((res) => {
          if (res.err === 0||res.code===200) {
            this.tableData = res.data.data;
            this.tableData.forEach((item) => {
              item.created_time = this.FMT_TALBE_TIME(item);
              item.atlas_type_cn=this.atlasTypeList.find(i=>item.atlas_type===i.key).value;
              item.atlas_condition=JSON.stringify(item.atlas_condition);
            });
            this.total = res.data.total;
          }
        })
        .catch((err) => {});
    },
    FMT_TALBE_TIME(row) {
      let time = dayjs(row.created_time).format("YYYY-MM-DD HH:mm:ss");
      return time;
    },
    SEARCH_ROW(row) {
      this.$emit("searchResult",row);
      this.drawerShow = false;
    },
    DEL_ROW(id_list) {
      graph_del({
        id_list: id_list,
        delete_all: false,
      })
        .then((res) => {
          if (res.err === 0||res.code===200) {
            this.$message.success(res.data);
            this.GET_DATA();
          }
        })
        .catch((err) => {});
    },
    // 删除
    handleDelete() {
      let newArr = [];
      newArr = this.multipleSelection.map((item) => item.id);
      if (newArr.length < 1) return this.$message.warning("请选择条目");
      this.DEL_ROW(newArr);
    },

    // 删除全部
    ALL_DEL() {
      this.$confirm("此操作将删除全部历史, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          graph_del({
            id_lsit: [],
            delete_all: true,
          }).then((res) => {
            if (res.err === 0||res.code===200) {
              this.GET_DATA();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
  justify-content: space-between !important;
  align-items: center;
}
@mixin com {
  padding: 16px;
  box-sizing: border-box;
  width: 100%;
}
.drawer-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  width: 100%;
  &-search {
    @include com;
    height: 64px;
    @extend .flex;
    .el-input {
      width: 240px;
    }
  }
  &-table {
    @include com;
    flex: 1;
  }
}
::v-deep .history-footer {
  @extend .flex;
}
.page-box {
  width: 100%;
  position: sticky;
  bottom: 0;
  height: 50px;
  background: #ffffff;
  z-index: 1000;
  padding-right: 20px;
  box-sizing: border-box;
}
</style>

<template>
  <el-dialog 
    :title="headerTitle"
    :visible.sync="value"
    width="480px"
    custom-class="custom--dialog"
    @close="handleClose"
  >
    <div class="centerBox">
      <el-radio v-model="radio" label="full">全部展开</el-radio>
      <el-radio v-model="radio" label="part">部分展开</el-radio>
    </div>

    <div slot="footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: '',
  components: {},
  props:{
    headerTitle:{
      type:String,
      default:'展开'
    },
    value:{
      type:Boolean,
      default:false
    }
  },
  data () {
    return {
      radio:'full'
    };
  },
  computed: {},
  created () { },
  mounted () { },
  methods: { 
    handleClose(){
      this.$emit('input',false);
    },
    confirm(){
      if(this.radio === 'full'){
        this.$emit('ExpandOption','full');
      }else{
        this.$emit('ExpandOption','part');
      }
      this.handleClose();
    }
  }
};
</script>
<style scoped lang='scss'>
.centerBox{
    padding-top:20px;
    height: 50px;
}
</style>

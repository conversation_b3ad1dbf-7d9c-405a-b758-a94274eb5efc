<template>
  <div>
    <el-table
      :data="tableData"
      stripe
      style="overflow:auto"
    >
      <el-table-column
        type="index"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="ip_addr"
        label="IP"
        width="150"
      >
      </el-table-column>
      <el-table-column
        prop="port"
        label="端口"
        width="100"
      >
      </el-table-column>
      <el-table-column
        prop="protocol"
        label="协议名"
        width="100"
      >
      </el-table-column>
      <el-table-column
        prop="domain"
        label="域名"
        width="150"
      >
      </el-table-column>
      <el-table-column
        prop="country_name"
        label="国家名"
        width="150"
      >
      </el-table-column>
      <el-table-column
        prop="city"
        label="城市"
        width="150"
      >
      </el-table-column>
      <el-table-column
        prop="source"
        label="来源"
        width="100"
      >
      </el-table-column>
      <!-- <el-table-column
        prop="icp"
        label="ICP备案号"
        width="150"
      >
      </el-table-column>
      <el-table-column
        prop="server"
        label="网站服务器"
        width="150"
      >
      </el-table-column>
      <el-table-column
        prop="jarm"
        label="jarm指纹"
        width="150"
      >
      </el-table-column>
      <el-table-column
        prop="title"
        label="网站标题"
        width="120"
      >
      </el-table-column>
      <el-table-column
        prop="as_organization"
        label="asn组织"
        width="120"
      >
      </el-table-column>
      <el-table-column
        prop="source"
        label="数据来源"
        width="100"
      >
      </el-table-column> -->
      <el-table-column
        prop="header"
        label="JSON数据"
        width="200"
        show-overflow-tooltip
      >
        <template slot-scope="scoped">
          <!-- <div class="item-header">{{scoped.row.header}}</div> -->
          <el-button
            type="success"
            size="mini"
            @click="REVIRE_JOSN(scoped.row)"
          >
            资产测绘JSON
          </el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="first_seen"
        label="首次出现时间"
        :formatter="FIRST_DATE"
        width="105"
      >
      </el-table-column>
      <el-table-column
        prop="last_seen"
        label="末次出现时间"
        :formatter="LAST_TIME"
        width="105"
      >
      </el-table-column> -->
    </el-table>
    <el-dialog
      title="资产测绘JSON"
      :visible.sync="jsonShow"
      width="60%"
      append-to-body
      top="5vh"
    >
      <el-card
        shadow="never"
        style="margin-top: 20px"
      >
        <json-viewer
          :copyable="true"
          :value="headerJson"
          :expand-depth="5"
        >
          <template slot="copy"> 复制 </template>
        </json-viewer>
      </el-card>
    </el-dialog>
  </div>
</template>

<script>
import dayjs from 'dayjs';
export default {
  props: ['tableData'],
  data () {
    return {
      jsonShow: false,
      headerJson: {}
    };
  },
  methods: {
    FIRST_DATE (date) {
      return dayjs(date.first_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    LAST_TIME (date) {
      return dayjs(date.last_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    REVIRE_JOSN (row) {
      this.jsonShow = true;
      this.headerJson = row;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-table th > .cell {
  margin-top: 0;
}
.item-header {
  color: #116ef9;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.item-header:hover {
  text-decoration: underline;
}
.el-button {
  color: #fff;
}
</style>
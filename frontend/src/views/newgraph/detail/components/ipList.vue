<template>
  <div class="pr30">
    <el-table
      :data="tableData"
      border
      stripe
      style="overflow:auto"
      :default-sort="{prop: 'session_cnt', order: 'descending'}"
      @sort-change="SORT_CHANGE"
    >
      <el-table-column
        type="index"
        label="序号"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="ip_addr"
        label="IP"
        min-width="150"
      >
      </el-table-column>
      <el-table-column
        prop="ip_key"
        label="IP类型"
        :formatter="IP_KEY"
        min-width="100"
      >
      </el-table-column>
      <el-table-column
        prop="country"
        label="地理位置"
        min-width="100"
        :formatter="POSITION"
      >
      </el-table-column>
      <el-table-column
        prop="black_list"
        label="黑名单权值"
        min-width="120"
        sortable="custom"
      >
      </el-table-column>
      <el-table-column
        prop="white_list"
        label="白名单权值"
        min-width="120"
        sortable="custom"
      >
      </el-table-column>
      <!-- <el-table-column
        prop="session_cnt"
        label="次数"
        sortable="custom"
      >
      </el-table-column> -->
      <el-table-column
        prop="first_seen"
        label="首次出现时间"
        :formatter="FIRST_DATE"
        min-width="140"
      >
      </el-table-column>
      <el-table-column
        prop="last_seen"
        label="末次出现时间"
        :formatter="LAST_TIME"
        min-width="140"
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import dayjs from 'dayjs';
export default {
  props: ['tableData'],
  methods: {
    SORT_CHANGE(field){
      let data ={
        order_field:field.prop,
        sort_order:''
      };
      if(field.prop === 'times') data.order_field = 'session_cnt';
      if(field.order === 'descending'){
        data.sort_order = 'desc';
      }else if (field.order === 'ascending'){
        data.sort_order = 'asc';
      }else{
        data.sort_order = 'desc';
      }
      this.$emit('SORT_CHANGE_GETDATA',data);
    },
    IP_KEY (row) {
      if (row.ip_key.indexOf('-') > -1) {
        return '内网';
      } else {
        return '外网';
      }
    },
    POSITION (row) {
      return `${row.country}${row.city && row.city !== 'NULL'? '-' + row.city : ''}`;
    },
    FIRST_DATE (row) {
      return dayjs(row.first_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    LAST_TIME (row) {
      return dayjs(row.last_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    }
  }
};
</script>

<style lang="scss" scoped>
.pr30{
  padding-right: 30px;
}
::v-deep .el-table th > .cell {
  margin-top: 0;
}
</style>
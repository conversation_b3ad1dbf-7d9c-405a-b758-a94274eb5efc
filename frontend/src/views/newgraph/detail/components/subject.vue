<template>
  <div class="pr30">
    <el-table
      :data="tableData"
      stripe
      border
      style="overflow:auto"
    >
      <el-table-column
        label="序号"
        type="index"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="common_name"
        label="通用名称"
      >
      </el-table-column>
      <el-table-column
        prop="country"
        label="国家"
        width="100"
      >
      </el-table-column>
      
      <!-- <el-table-column
          prop="issuer_md5"
          label="签发机构MD5值"
        >
        </el-table-column> -->
      <el-table-column
        prop="object_name"
        label="组织名称"
      >
      </el-table-column>
      <el-table-column
        prop="subject_md5"
        label="所有者MD5值"
        :formatter="formatMD5"
      >
      </el-table-column>
    </el-table>
  </div>
</template>
  
<script>
export default {
  name:'Issuer',
  props: ['tableData'],
  methods:{
    formatMD5(row, column, cellValue) {
      const parts = cellValue.split('subject_');
      if (parts.length === 2) {
        return parts[1]; 
      } else {
        return cellValue; // 如果不是 "issuer_" 开头，则显示原始值
      }
    }
  }
};
</script>
  
  <style lang="scss" scoped>
  .pr30{
  padding-right: 30px;
}
  ::v-deep .el-table th > .cell {
    margin-top: 0;
  }
  </style>
<template>
    <div>
      <el-table
        :data="tableData"
        stripe
        style="overflow:auto"
      >
        <el-table-column
          type="index"
          width="50"
        >
        </el-table-column>
        <el-table-column
          prop="addr"
          label="地址"
          width="200"
        >
        </el-table-column>
        <el-table-column
          prop="chain_source"
          label="来源"
        >
        </el-table-column>
        <el-table-column
          prop="balance_account"
          label="余额"
        >
        </el-table-column>
        <el-table-column
          prop="deal_hash"
          label="交易Hash"
          width="300"
        >
        </el-table-column>
        <el-table-column
          prop="deal_param"
          label="交易参数"
        >
        </el-table-column>
        <el-table-column
          prop="token_addr"
          label="代币地址"
        >
        </el-table-column>
      </el-table>
    </div>
  </template>
  
  <script>
  import dayjs from 'dayjs'
  export default {
    props: ['tableData'],
    methods: {
      SORT_CHANGE(field){
        let data ={
          order_field:field.prop,
          sort_order:''
        }
        if(field.prop === 'times') data.order_field = 'session_cnt'
        if(field.order === 'descending'){
          data.sort_order = 'desc'
        }else if (field.order === 'ascending'){
          data.sort_order = 'asc'
        }else{
          data.sort_order = 'desc'
        }
        this.$emit('SORT_CHANGE_GETDATA',data)
      },
      IP_PRO (row) {
        return this.$store.state.long.Dict.protocol_type[row.IPPro]?.protocol_type || 'N/A'
      },
      FIRST_DATE (row) {
        return dayjs(row.first_time * 1000).format("YYYY-MM-DD HH:mm:ss")
      },
      LAST_TIME (row) {
        return dayjs(row.last_time * 1000).format("YYYY-MM-DD HH:mm:ss")
      },
      SORT_CHANGE(filed){
        console.log(filed)
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  ::v-deep .el-table th > .cell {
    margin-top: 0;
  }
  </style>
<template>
  <div class="graph">
    <div
      id="header"
      class="header"
    >
      <div class="tool" />
    </div>
    <div
      id="main"
      ref="main"
      class="main"
    >
      <!-- <el-cascader-panel :options="options"></el-cascader-panel> -->
      <aside class="type-box">
        <div
          v-for="(item,index) of asideList"
          :key="index"
          class="type-box-cell"
        >
          <div>
            <img
              :src="item.src"
              alt=""
            >
          </div>
          <div>{{ item.name }}</div>
        </div>
      </aside>
    </div>
    <el-dialog
      :title="relaTitle"
      :visible.sync="relaShow"
      width="480px"
    >
      <el-checkbox
        v-model="checkAll"
        :indeterminate="isIndeterminate"
        @change="handleCheckAllChange"
      >
        全部关联
      </el-checkbox>
      <div style="margin: 15px 0;" />
      <el-checkbox-group
        v-model="relaArr"
        @change="handleCheckedCitiesChange"
      >
        <div
          v-for="(item,index) in relaOptions"
          :key="index"
          class="check-cell"
        >
          <el-checkbox
            :key="index"
            :label="item.name"
          >
            {{ item.name }}
          </el-checkbox>
          <el-input-number
            v-model="item.num"
            :min="1"
            :max="10"
            size="mini"
          />
        </div>
      </el-checkbox-group>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="relaShow = false">取 消</el-button>
        <el-button
          type="primary"
          :disabled="handleSubmit"
          @click="CLICK_REAL"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import G6 from '@antv/g6';
import { get_data, get_next, get_edge_json } from '@/api/graph';

export default {
  data () {
    return {
      initValue: '',
      initType: '',
      initId: '',
      g6: '',
      toolbar: '',
      width: '',
      height: '',
      handleNode: '',
      handleList: [],
      handleSubmit: true,
      // 右键菜单
      contextMenu: '',
      options: [
        {
          value: '',
          label: '关联'
        },
        {
          value: '',
          label: '查看标签'
        },
        {
          value: '',
          label: '删除'
        }
      ],
      // 数据
      data: {
        nodes: [
          {
            id: 'node1',
            label: '采集服务器',
            ip: '***********',
            status: 0,
            type: 'ip-node'
          },
          {
            id: 'node2',
            label: '数据库',
            ip: '***********',
            status: 1,
            type: 'mac-node'
          },
          {
            id: 'node3',
            label: '终端',
            ip: '***********',
            status: 2,
            type: 'mac-node'
          },
          {
            id: 'node4',
            label: '引擎',
            ip: '***********',
            status: 0,
            type: 'zw-node'
          },
          {
            id: 'node5',
            label: '引擎5',
            ip: '***********',
            status: 0
          },
          {
            id: 'node6',
            label: '引擎6',
            ip: '***********',
            status: 2
          },
          {
            id: 'node7',
            label: '引擎7',
            ip: '***********',
            status: 1
          },
          {
            id: 'node8',
            label: '引擎8',
            ip: '***********',
            status: 2
          },
          {
            id: 'node9',
            label: '引擎9',
            ip: '***********',
            status: 1
          },
          {
            id: 'node10',
            label: '引擎10',
            ip: '***********',
            status: 0
          }
        ],
        edges: [
          {
            source: 'node1',
            target: 'node2'
          },
          {
            source: 'node1',
            target: 'node3'
          },
          {
            source: 'node1',
            target: 'node4'
          },
          {
            source: 'node4',
            target: 'node5'
          },
          {
            source: 'node1',
            target: 'node6'
          },
          {
            source: 'node6',
            target: 'node7'
          },
          {
            source: 'node7',
            target: 'node8'
          },
          {
            source: 'node1',
            target: 'node8'
          },
          {
            source: 'node2',
            target: 'node9'
          },
          {
            source: 'node3',
            target: 'node10'
          }
        ]
      },
      asideList: [
        {
          name: 'MAC',
          src: require('../../assets/graph/icon_MAC.svg')
        },
        {
          name: 'IP',
          src: require('../../assets/graph/icon_IP.svg')
        },
        {
          name: '应用服务',
          src: require('../../assets/graph/icon_APP.svg')
        },
        {
          name: '域名',
          src: require('../../assets/graph/icon_web.svg')
        },
        {
          name: '锚域名',
          src: require('../../assets/graph/icon_anchor.svg')
        },
        {
          name: '证书',
          src: require('../../assets/graph/icon_certificate.svg')
        },
        {
          name: '企业名',
          src: require('../../assets/graph/icon_enterprise.svg')
        },
        {
          name: 'SSL指纹',
          src: require('../../assets/graph/icon_fingerprint.svg')
        },
        {
          name: 'UA',
          src: require('../../assets/graph/icon_UA.svg')
        },
        {
          name: '硬件类型',
          src: require('../../assets/graph/icon_hardware.svg')
        },
        {
          name: '系统类型',
          src: require('../../assets/graph/icon_system.svg')
        },
        {
          name: '应用类型',
          src: require('../../assets/graph/icon_Apptype.svg')
        },
        {
          name: '攻击者',
          src: require('../../assets/graph/icon_attacker.svg')
        },
        {
          name: '受害者',
          src: require('../../assets/graph/icon_sufferer.svg')
        },
        {
          name: '攻击者/受害者',
          src: require('../../assets/graph/攻击_受害者.svg')
        }
      ],
      // 关联
      relaShow: false,
      relaTitle: '',
      checkAll: false,
      allOptions: [],
      relaArr: [],
      relaOptions: [],
      isIndeterminate: false
    };
  },
  created () {
    if (this.$route.query.initValue && this.$route.query.initType) {
      this.initValue = this.$route.query.initValue;
      this.initType = this.$route.query.initType;
    } else {
      this.initValue = '************';
      this.initType = 'IP';
    }
  },
  mounted () {
    // this.DIY_NODE()
    this.GET_JSON();
    this.GET_DATA();
    this.MOUSE_RIGHT();
    this.INIT();
    this.NODE_EVENT();
  },
  methods: {
    GET_DATA () {
      get_data({
        str: this.initValue,
        type: this.initType
      }).then(res => {
        const data = {
          nodes: [],
          edges: []
        };
        if (res.err === 0) {
          for (let i = 0; i < res.data.vertex.length; i++) {
            data.nodes.push({
              id: res.data.vertex[i].id,
              label: res.data.vertex[i].label,
              raw_type: res.data.vertex[i].type
            });
          }
          for (let i = 0; i < res.data.edge.length; i++) {
            data.edges.push({
              source: res.data.edge[i].from,
              target: res.data.edge[i].to,
              label: res.data.edge[i].label
            });
          }
          this.data = data;
          console.log(data);
          this.g6.data(this.data);
          this.g6.render();
        }
      });
    },
    // 获取边的转折点关系
    GET_JSON () {
      get_edge_json().then(res => {
        console.log(res);
        this.handleList = res.data;
      });
    },
    INIT () {
      this.width = this.$refs.main.scrollWidth;
      this.height = this.$refs.main.scrollHeight;
      this.g6 = new G6.Graph({
        container: 'main',
        width: this.width,
        height: this.height,
        // 是否开启画布自适应，开启后图自动适配画布大小
        fitView: true,
        fitCenter: true,
        fitViewPadding: 50,
        // 渲染模式，可选canvas与svg
        renderer: 'canvas',
        // 指定边是否连入节点的中心
        // linkCenter: true,
        // 编辑模式
        modes: {
          // 支持的 behavior
          default: ['drag-node',
            {
              type: 'zoom-canvas',
              enableOptimize: true
            },
            {
              type: 'drag-canvas',
              enableOptimize: true
            }
            //  {
            //   type: 'activate-relations'
            // }
          ],
          edit: ['drag-canvas', 'zoom-canvas', 'click-select'],
          edit2: ['drag-node']
        },
        plugins: [this.contextMenu],
        // 全局布局
        layout: {
          // 布局类型
          type: 'gForce',
          unitRadius: 200, // 圈距离
          strictRadial: false, // 是否严格圈
          preventOverlap: true, // 防止节点重叠
          // // 防碰撞必须设置nodeSize或size,否则不生效，由于节点的size设置了40，虽然节点不碰撞了，但是节点之间的距离很近，label几乎都挤在一起，所以又重新设置了大一点的nodeSize,这样效果会好很多
          nodeSize: 80,
          linkDistance: 150, // 指定边距离为150
          nodeStrength: 5000 // 节点作用力，正数代表节点之间的引力作用，负数代表节点之间的斥力作用
          // workerEnabled: true,      // 可选，开启 web-worker
          // gpuEnabled: true          // 可选，开启 GPU 并行计算，G6 4.0 支持
        },
        // 默认节点样式
        defaultNode: { // 节点样式修改
          type: 'circle', // 设置节点类型
          size: 4, // 节点大小
          style: {
            fill: '#FFFFFF',
            stroke: '#FFFFFF'
          },
          labelCfg: { // 修改节点label样式
            style: {
              fill: '#2C2C35', // 字体颜色
              fontSize: 14 // 字体大小
            },
            position: 'bottom',
            textAlign: 'center'
          },
          // 配置图标`
          icon: {
            show: true
          },

          cursor: 'pointer'
        },
        // 默认边样式
        defaultEdge: {
          type: 'line',
          style: {
            endArrow: {
              path: G6.Arrow.triangle(5, 5),
              fill: '#9999A1',
              stroke: '#9999A1'
            },
            lineWidth: 1
          },
          labelCfg: {
            refY: 10,
            position: 'middle',
            autoRotate: true
          }
        }
        // 默认状态样式
        // nodeStateStyles: {
        //   active: {
        //     opacity: 1,
        //   },
        //   inactive: {
        //     opacity: 0.2,
        //   },
        // },
        // edgeStateStyles: {
        //   active: {
        //     stroke: '#999',
        //   },
        // },
      });

      this.CHANGE_NODE_EDGE();

      this.g6.data(this.data);
      this.g6.render();
    },
    // 自定义节点planB
    CHANGE_NODE_EDGE () {
      this.g6.node((node) => {
        return this.CHECK_NODE_ICON(node.raw_type, node);
        // switch (node.raw_type) {
        //   // IP
        //   case 'IP':
        //     return {
        //       ...temp,
        //       icon: {
        //         show: true,
        //         img: require('../../assets/graph/IP.svg')
        //       }
        //     };
        //   // 域名
        //   case 'DOMAIN':
        //     return {
        //       ...temp,
        //       icon: {
        //         show: true,
        //         img: require('../../assets/graph/icon_web_green.svg')
        //       }
        //     }
        //   // MAC
        //   case 'MAC':
        //     return {
        //       ...temp,
        //       icon: {
        //         show: true,
        //         img: require('../../assets/graph/MAC.svg')
        //       }
        //     }
        //   // 应用服务
        //   case 'APPSERVICE':
        //     return {
        //       ...temp,
        //       icon: {
        //         show: true,
        //         img: require('../../assets/graph/icon_APP.svg')
        //       }
        //     }
        //   // 锚域名
        //   case 'FDOMAIN':
        //     return {
        //       ...temp,
        //       icon: {
        //         show: true,
        //         img: require('../../assets/graph/icon_anchor.svg')
        //       }
        //     }
        //   // 证书CERT
        //   case 'CERT':
        //     return {
        //       ...temp,
        //       icon: {
        //         show: true,
        //         img: require('../../assets/graph/icon_certificate_green.svg')
        //       }
        //     }
        //   // 企业
        //   case 'ORG':
        //     return {
        //       ...temp,
        //       icon: {
        //         show: true,
        //         img: require('../../assets/graph/icon_enterprise.svg')
        //       }
        //     }
        //   // 指纹
        //   case 'SSLFINGER':
        //     return {
        //       ...temp,
        //       icon: {
        //         show: true,
        //         img: require('../../assets/graph/icon_fingerprint_green.svg')
        //       }
        //     }
        //   // UA
        //   case 'UA':
        //     return {
        //       ...temp,
        //       icon: {
        //         show: true,
        //         img: require('../../assets/graph/UA.svg')
        //       }
        //     }
        //   // 硬件类型
        //   case 'DEVICE':
        //     return {
        //       ...temp,
        //       icon: {
        //         show: true,
        //         img: require('../../assets/graph/icon_hardware.svg')
        //       }
        //     }
        //   // 系统类型
        //   case 'OS':
        //     return {
        //       ...temp,
        //       icon: {
        //         show: true,
        //         img: require('../../assets/graph/icon_system.svg')
        //       }
        //     }
        //   // 应用类型
        //   case 'APPTYPE':
        //     return {
        //       ...temp,
        //       icon: {
        //         show: true,
        //         img: require('../../assets/graph/icon_Apptype.svg')
        //       }
        //     }
        //   // 文件夹
        //   case 'Folder':
        //     let imgBox = {
        //       type: 'image',
        //       size: [42, 32]
        //     }

        //     if (node.label === "IP") {
        //       return {
        //         ...imgBox,
        //         img: require('../../assets/graph/ip_默认.svg')
        //       };
        //     } else if (node.label === 'MAC') {
        //       return {
        //         ...imgBox,
        //         img: require('../../assets/graph/mac_默认.svg')
        //       }
        //     } else if (node.label === 'UA') {
        //       return {
        //         ...imgBox,
        //         img: require('../../assets/graph/UA_默认.svg')
        //       }
        //     } else if (node.label === 'DOMAIN') {
        //       return {
        //         ...imgBox,
        //         img: require('../../assets/graph/域名_默认.svg')
        //       }
        //     } else if (node.label === 'APPSERVICE') {
        //       return {
        //         ...imgBox,
        //         img: require('../../assets/graph/应用服务_默认.svg')
        //       }
        //     }

        //     else {
        //       return {
        //         ...imgBox,
        //         img: require('../../assets/graph/ip_默认.svg')
        //       }
        //     }
        //   default:
        //     return {
        //       ...temp,
        //       icon: {
        //         show: true,

        //       }
        //     };
        // }
      });
      this.g6.edge((edge) => {
        const temp = {
          type: 'line',
          style: {
            endArrow: {
              path: G6.Arrow.triangle(5, 5),
              fill: '#9999A1',
              stroke: '#9999A1'
            },
            lineWidth: 1
          },
          labelCfg: {
            refY: 10,
            position: 'middle',
            autoRotate: true
          }
        };

        return {
          ...temp
        };
      });
    },
    // 判断元素类型并沿用何种图标
    CHECK_NODE_ICON (raw_type, node) {
      const temp = {
        id: node.id,
        type: 'circle',
        size: [40, 40],
        label: node.label || 'N/A',
        style: {
          fill: '#FFFFFF',
          stroke: '#FFFFFF'
        }
      };
      switch (raw_type) {
      // IP
      case 'IP':
        return {
          ...temp,
          icon: {
            show: true,
            img: require('../../assets/graph/IP.svg')
          }
        };
        // 域名
      case 'DOMAIN':
        return {
          ...temp,
          icon: {
            show: true,
            img: require('../../assets/graph/icon_web_green.svg')
          }
        };
        // MAC
      case 'MAC':
        return {
          ...temp,
          icon: {
            show: true,
            img: require('../../assets/graph/MAC.svg')
          }
        };
        // 应用服务
      case 'APPSERVICE':
        return {
          ...temp,
          icon: {
            show: true,
            img: require('../../assets/graph/icon_APP.svg')
          }
        };
        // 锚域名
      case 'FDOMAIN':

        return {
          ...temp,
          icon: {
            show: true,
            img: require('../../assets/graph/icon_anchor.svg')
          }
        };
        // 证书CERT
      case 'CERT':
        return {
          ...temp,
          icon: {
            show: true,
            img: require('../../assets/graph/icon_certificate_green.svg')
          }
        };
        // 企业
      case 'ORG':
        return {
          ...temp,
          icon: {
            show: true,
            img: require('../../assets/graph/icon_enterprise.svg')
          }
        };
        // 指纹
      case 'SSLFINGER':
        return {
          ...temp,
          icon: {
            show: true,
            img: require('../../assets/graph/icon_fingerprint_green.svg')
          }
        };
        // UA
      case 'UA':
        return {
          ...temp,
          icon: {
            show: true,
            img: require('../../assets/graph/UA.svg')
          }
        };
        // 硬件类型
      case 'DEVICE':
        return {
          ...temp,
          icon: {
            show: true,
            img: require('../../assets/graph/icon_hardware.svg')
          }
        };
        // 系统类型
      case 'OS':
        return {
          ...temp,
          icon: {
            show: true,
            img: require('../../assets/graph/icon_system.svg')
          }
        };
        // 应用类型
      case 'APPTYPE':
        return {
          ...temp,
          icon: {
            show: true,
            img: require('../../assets/graph/icon_Apptype.svg')
          }
        };
        // 文件夹
      case 'Folder':
        const imgBox = {
          type: 'image',
          size: [42, 32],
          label: node.label
        };

        if (node.label === 'IP') {
          return {
            ...imgBox,
            img: require('../../assets/graph/ip_默认.svg')
          };
        } else if (node.label === 'MAC') {
          return {
            ...imgBox,
            img: require('../../assets/graph/mac_默认.svg')
          };
        } else if (node.label === 'UA') {
          return {
            ...imgBox,
            img: require('../../assets/graph/UA_默认.svg')
          };
        } else if (node.label === 'DOMAIN') {
          return {
            ...imgBox,
            img: require('../../assets/graph/域名_默认.svg')
          };
        } else if (node.label === 'APPSERVICE') {
          return {
            ...imgBox,
            img: require('../../assets/graph/应用服务_默认.svg')
          };
        } else {
          return {
            ...imgBox,
            img: require('../../assets/graph/ip_默认.svg')
          };
        }
      default:
        return {
          ...temp,
          icon: {
            show: true

          }
        };
      }
    },
    // 自定义节点
    DIY_NODE () {
      G6.registerNode('ip-node', {
        labelPosition: 'center',
        labelAutoRotate: true,
        draw: (cfg, group) => {
          const keyShape = group.addShape('rect', {
            attrs: {
              width: 40,
              height: 40,
              radius: 20,
              stoke: '#F4664A',
              fill: '#FFF',
              cursor: 'pointer',
              label: '***********',
              labelCfg: {
                style: {
                  fill: '#2C2C35', // 字体颜色
                  fontSize: 14 // 字体大小
                },
                position: 'bottom',
                textAlign: 'center'
              }
            },

            name: 'ip-node-keyshape'
          });
          // const img = group.addShape('image', {
          //   attrs: {
          //     x: 13,
          //     y: 8,
          //     img:require('../../assets/graph/IP.svg'),
          //     width: 15,
          //     height: 24
          //   },
          //   name:'ip-node-icon'
          // })
          const text = group.addShape('text', {
            attrs: {
              text: 'IP',
              x: 10,
              y: 35,
              fill: '#2BE0B6',
              fontSize: 24,
              cursor: 'pointer'
            },
            name: 'ip-node-text'
          });

          return keyShape;
        }
      }, 'rect');
    },
    // 所有节点的事件注册
    NODE_EVENT () {
      // this.g6.on('ip-node-keyshape:click', e => {
      //   const node = e.item;
      //   const model = node.getModel();
      //   console.log(node, model)
      //   // this.g6.updateItem(node, {
      //   //   attrs: {
      //   //     fill: '#FFF'
      //   //   }
      //   // })
      // })
      this.g6.on('node:click', evt => {
        console.log(evt);
        const node = evt.item._cfg.model;
      });
    },
    // 注册右键菜单zzz
    MOUSE_RIGHT () {
      this.contextMenu = new G6.Menu({
        getContent (evt) {
          let header;
          if (evt.target && evt.target.isCanvas && evt.target.isCanvas()) {
            header = 'Canvas ContextMenu';
          } else if (evt.item) {
            const itemType = evt.item.getType();
            header = `${itemType.toUpperCase()} ContextMenu`;
          }

          return `
                  <div class="rela" id="rela">关联<div></div></div>
                  <div class="view" id="view">查看标签</div>
                  <div class="del" id="del">删除</div>
                 `;
        },

        handleMenuClick: (target, item) => {
          const model = item && item.getModel();
          this.handleNode = model;
          const liIdStrs = target.id.split('-')[0];
          console.log(model, liIdStrs);
          switch (liIdStrs) {
          case 'rela':
            this.handleSubmit = true;
            const newArr = [];
            this.handleList.forEach(item => {
              if (item.type === model.raw_type) {
                console.log(item);
                this.handleSubmit = false;

                const arr = item.handle;
                this.allOptions = item.handle;
                let i = 0;
                for (i; i < arr.length; i++) {
                  newArr.push({
                    name: arr[i],
                    num: 5
                  });
                }
              }
            });
            this.relaOptions = newArr;
            this.relaTitle = model.label;
            this.isIndeterminate = false;
            this.checkAll = false;
            this.relaArr = [];
            this.relaShow = true;
            break;
          case 'view':

            break;

          case 'del':

            break;
          default:
            break;
          }
        },
        // offsetX and offsetY include the padding of the parent container
        // 需要加上父级容器的 padding-left 16 与自身偏移量 10
        offsetX: 16 + 10,
        // 需要加上父级容器的 padding-top 24 、画布兄弟元素高度、与自身偏移量 10
        offsetY: 0,
        // the types of items that allow the menu show up
        // 在哪些类型的元素上响应
        itemTypes: ['node']
      });
    },
    // 点击关联框中的确认
    CLICK_REAL () {
      const arr = [];
      for (let i = 0; i < this.relaOptions.length; i++) {
        for (let j = 0; j < this.relaArr.length; j++) {
          if (this.relaArr[j] === this.relaOptions[i].name) {
            console.log(this.relaOptions[i]);
            arr.push({
              edge: this.relaOptions[i].name,
              num: this.relaOptions[i].num
            });
          }
        }
      }
      get_next({
        str: this.handleNode.label,
        type: this.handleNode.raw_type,
        edgeInfo: arr
      }).then(res => {
        console.log(res);
        if (res.err === 0) {
          const nodes = res.data.vertex;
          const edges = res.data.edge;
          for (let i = 0; i < nodes.length; i++) {
            // const tempNode = {
            //   id: nodes[i].id,
            //   type: 'circle',
            //   size: [40, 40],
            //   label: nodes[i].label || 'N/A',
            //   style: {
            //     fill: '#FFFFFF',
            //     stroke: '#FFFFFF',
            //   },
            // }
            const tempNode = {
              ...this.CHECK_NODE_ICON(nodes[i].type, nodes[i])
            };
            this.g6.addItem('node', tempNode);
          }
          for (let i = 0; i < edges.length; i++) {
            const tempEdge = {
              source: edges[i].from,
              target: edges[i].to,
              type: 'line',
              label: edges[i].label,
              style: {
                endArrow: {
                  path: G6.Arrow.triangle(5, 5),
                  fill: '#9999A1',
                  stroke: '#9999A1'
                },
                lineWidth: 1
              },
              labelCfg: {
                refY: 10,
                position: 'middle',
                autoRotate: true
              }
            };
            this.g6.addItem('edge', tempEdge);
          }
          this.relaShow = false;
          this.g6.layout();
          // this.g6.render()
        }
      });
    },
    // 关联单选多选
    handleCheckAllChange (val) {
      console.log(val);
      this.relaArr = val ? this.allOptions : [];
      this.isIndeterminate = false;
    },
    handleCheckedCitiesChange (value) {
      const checkedCount = value.length;
      this.checkAll = checkedCount === this.relaOptions.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.relaOptions.length;
    }
  }
};
</script>

<style lang="scss" scoped>
.graph {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #f2f3f7;
  .header {
    width: 100%;
    height: 32px;
    background-color: #f7f8fa;
  }
  .main {
    width: 100%;
    height: calc(100% - 32px);
    position: relative;
    .g6-component-tooltip {
      border: 1px solid #e2e2e2;
      border-radius: 4px;
      font-size: 12px;
      color: #000;
      background-color: rgba(255, 255, 255, 0.9);
      padding: 10px 8px;
      box-shadow: rgb(174, 174, 174) 0px 0px 10px;
    }
    ::v-deep .g6-component-contextmenu {
      padding: 8px 0;
      width: 112px;
      background: #ffffff;
      box-shadow: 0px 6px 18px rgba(45, 47, 51, 0.14);
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      > div {
        width: 100%;
        height: 32px;
        padding-left: 16px;
        padding-right: 12px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        font-size: 14px;
        color: #2c2c35;
      }
      > div:hover {
        background: #e7f0fe;
      }
      .rela {
        > div {
          width: 6.06px;
          height: 10.71px;
          background-image: url('../../assets/images/Vector 100 (Stroke).svg');
        }
      }
      .view {
      }
      .del {
        color: #f91111;
      }
    }
    .type-box {
      position: absolute;
      left: 18px;
      bottom: 18px;
      z-index: 9999;
      &-cell {
        width: 105px;
        height: 20px;
        margin-bottom: 16px;
        font-size: 12px;
        color: #2c2c35;
        display: flex;
        align-items: center;
        cursor: context-menu;

        > div:nth-of-type(1) {
          width: 20px;
          height: 20px;
          margin-right: 6px;
          > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
  }
  ::v-deep .el-dialog__body {
    max-height: 650px;
    overflow-y: auto;
  }
  ::v-deep .el-dialog__footer {
    border-top: 1px solid #f2f3f7;
    padding: 12px 24px;
    .el-button--primary {
      background-color: #116ef9;
      border-color: #116ef9;
    }
  }
  ::v-deep .el-checkbox__label {
    font-size: 14px;
    color: #2c2c35;
  }
  ::v-deep.el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
  }
  .el-button--primary.is-disabled,
  .el-button--primary.is-disabled:active,
  .el-button--primary.is-disabled:focus,
  .el-button--primary.is-disabled:hover {
    color: #fff;
    background-color: #a0cfff;
    border-color: #a0cfff;
  }
  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
  }
  ::v-deep .el-checkbox-group {
    display: flex;
    flex-direction: column;
    font-size: 14px;
    .check-cell {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      // .el-input-number__decrease,.el-input-number__increase{
      //   width: 20px;
      //   height: 22px;
      // }
      // .el-input--mini{
      //   width: 75px;
      //   height: 22px;
      //   .el-input--mini{
      //     height: 22px;
      //     line-height: 22px;
      //   }
      // }
    }
    > label {
      margin-bottom: 12px;
      color: #2c2c35;
    }

    .el-checkbox__label {
      color: #2c2c35;
    }
  }
}
</style>

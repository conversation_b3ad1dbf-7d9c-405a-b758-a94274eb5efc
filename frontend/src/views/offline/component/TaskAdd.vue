<template>
  <el-drawer :title="title" :visible.sync="isShow" :wrapper-closable="false">
    <div class="demo-drawer__content">
      <el-form ref="formRef" :model="form" label-position="top" :rules="rules">
        <el-form-item label="任务名" prop="task_name">
          <el-input
            v-model="form.task_name"
            placeholder="请输入任务名"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="title==='任务配置'" label="创建时间" prop="create_time">
          <el-input
            v-model="form.create_time"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item label="任务描述" prop="task_description">
          <el-input
            v-model="form.task_description"
            placeholder="请输入任务描述"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="demo-drawer__footer">
      <el-button @click="cancelForm">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="creatTask">
        {{ loading ? "提交中 ..." : "确 定" }}
      </el-button>
    </div>
  </el-drawer>
</template>

<script>
import mixin from "@/mixins";
import api from "@/api/offline";
export default {
  name: "TaskAdd",
  mixins: [mixin],
  props: {
    id: {
      type: [String, Number],
      default: "",
    },
  },
  data() {
    return {
      form: {
        task_name: "",
        task_description: "",
        create_time: "",
      },
      timer: null,
      loading: false,
      rules: {
        task_name: [
          { required: true, message: "请输入任务名", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    isShow(val){
      if (val) {
        if(this.title==='任务配置'){
          this.getDetail();
        }
      }else{
        this.form.task_name="";
        this.form.task_description="";
        this.form.create_time="";
      }
    }
  },
  methods: {
    cancelForm() {
      this.loading = false;
      this.isShow = false;
    },
    async getDetail() {
      const { data } = await api.getDetail(this.id);
      this.form = { ...this.form, ...data };
      
    },
    creatTask() {
      this.$refs.formRef.validate(async (valid) => {
        if (!valid) return false;
        try {
          this.loading = true;
          if (this.title==='任务配置') {
            await api.offlineTaskUpdate({ ...this.form });
            this.$message.success("任务配置成功");
            this.loading = false;
            this.isShow = false;
            this.$emit("creatTask",this.form.task_id);
          } else {
            await api.offlineTaskAdd({ ...this.form });
            this.$message.success("创建任务成功");
            this.loading = false;
            this.isShow = false;
            this.$emit("creatTask");
          }
          
        } catch (error) {
          this.loading = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
  position: relative;
  .demo-drawer__content {
    height: calc(100% - 62px);
    padding: 16px 16px 0;
    border-bottom: 1px solid #f2f3f7;
    overflow-y: auto;
  }
  .demo-drawer__footer {
    position: absolute;
    right: 16px;
    bottom: 12px;
  }
}
</style>
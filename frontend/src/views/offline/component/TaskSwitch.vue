<template>
  <el-drawer
    :title="headerTitle"
    :visible.sync="isShow"
    custom-class="lcz-drawer"
    size="840px"
    @close="handleClose"
  >
    <div class="content">
      <el-table
        v-loading="tableLoading"
        size="medium"
        :data="tableData"
        style="width: 100%"
        border
        stripe
        highlight-current-row
        :header-cell-style="headerStyle"
        @sort-change="sortChange"
        @selection-change="handleSelectionChange"
      >
        <el-table-column label="名称" prop="task_name" width="200" />
        <el-table-column
          prop="task_description"
          label="描述"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column label="状态" prop="zt" width="80">
          <template #default="{ row }">
            <span :style="{ color: TASK_STATE[row.task_status] }">{{
              statusCn[row.task_status]
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          width="180"
          sortable
          prop="last_import_time"
          label="最近导入完成时间"
        >
          <template #default="{ row }">
            {{ $formatDateOne(row.last_import_time) }}
          </template>
        </el-table-column>
        <el-table-column width="120" label="操作">
          <template #default="{ row }">
            <el-button type="text" @click="handleSelectData(row)">
              选择
            </el-button>
            <el-button class="dividing" type="text" @click="handleDel(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page.current_page"
        :page-sizes="sizes"
        :page-size="page.page_size"
        :layout="layout"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <div class="btn">
      <el-button @click="isShow = false">关闭</el-button>
    </div>
    <ConfirmDialog
      v-model="visibleConfirm"
      header-title="提醒"
      color="red"
      icon="icon-a-16_alert"
    >
      <div slot="footer">
        <el-button @click="visibleConfirm = false">取 消</el-button>
        <el-button :loading="delLoading" type="danger" @click="del">
          删 除
        </el-button>
      </div>
    </ConfirmDialog>
  </el-drawer>
</template>

<script>
import mixins from "@/mixins";
import pagiNation from "@/mixins/pagiNation";
import { TASK_STATE } from "@/utils/constants";
import api from "@/api/offline";
import ConfirmDialog from "./ConfirmDialog.vue";
export default {
  name: "TackSwitch",
  components: {
    ConfirmDialog,
  },
  mixins: [mixins, pagiNation],
  data() {
    return {
      switchForm: {
        task_name: "", // 任务搜索条件（任务名 or 任务描述）
        sort_order: "desc",
      },
      loading: false,
      tableData: [],
      tableLoading: false,
      TASK_STATE,
      selectRow: {},
      visibleConfirm: false,
      delLoading: false,
      statusCn: {
        1: "执行中",
        2: "空闲",
        // 3: "导入完成",
      },
      selectionArr: [],
      task_list: [],
      isSinger: false,
      editObj: {},
    };
  },
  watch: {
    isShow(val) {
      if (val) {
        this.getList();
      }
    },
  },
  methods: {
    handleSelectData(row) {
      this.isShow = false;
      this.$emit("handleSelectData", row);
    },
    handleClose() {
      this.$refs.createFormRef.resetFields();
      this.selectRow = {};
      this.isShow = false;
    },
    handleSelectionChange(val) {
      this.selectRow = val.length ? val[0] : {};
      this.selectionArr = val;
    },
    // 批量删除
    // handleDelBatch() {
    //   this.isSinger = false;
    //   this.visibleConfirm = true;
    // },
    // 删除
    handleDel(row) {
      this.isSinger = true;
      this.visibleConfirm = true;
      this.task_list = row;
    },
    async del() {
      try {
        this.delLoading = true;
        await api.offlineTaskDelete({ task_id: this.task_list.task_id });
        this.delLoading = false;
        this.visibleConfirm = false;
        this.$message.success("删除成功");
        this.getList();
        this.$emit("getFirstList");
      } catch (error) {
        this.delLoading = false;
      }
    },
    handleSearch() {
      this.page.current_page = 1;
      this.getList();
    },
    // 获取任务列表
    async getList() {
      try {
        this.tableLoading = true;
        const params = { ...this.switchForm, ...this.page };
        const res = await api.offlineTaskPage(params);
        this.total = res.data.total;
        this.tableData = res.data.data;
        this.tableLoading = false;
      } catch (error) {
        this.tableLoading = false;
      }
    },
    // 排序
    sortChange({ column, prop, order }) {
      if (order === "descending") {
        this.switchForm.sort_order = "desc";
      } else if (order === "ascending") {
        this.switchForm.sort_order = "asc";
      }
      this.page.current_page = 1;
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .form {
    .el-input {
      width: 380px;
    }
  }
}
.order-no {
  display: flex;
  align-items: center;
  .iconfont {
    color: #767684;
    margin-left: 6px;
    &:hover {
      cursor: pointer;
    }
  }
}
</style>
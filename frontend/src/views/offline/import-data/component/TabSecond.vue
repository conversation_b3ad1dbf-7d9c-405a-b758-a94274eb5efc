<template>
  <div class="tab-second" :style="{width:tabFirstIndex?'98px':'168px'}">
    <div
      v-for="(item, index) in tabSecond"
      :key="index"
      :class="['tab-second-item', index === tabSecondIndex ? 'active' : '']"
      @click="handleSecond(item,index)"
    >
      {{ item.label }}
    </div>
  </div>
</template>

<script>
export default {
  name:'TabSecond',
  props: {
    tabSecond:{
      type:Array,
      default:()=>[]
    },
    value:{
      type:Number,
      default:0
    } ,
    tabFirstIndex:{
      type:Number,
      default:0
    } ,
  }, 
  computed: {
    tabSecondIndex:{
      get(){
        return this.value;
      },
      set(value){
        this.$emit('input', value);
      }
    }
  },
  methods: {
    handleSecond (item,index) {
      this.$emit('tabClick',item);
      this.tabSecondIndex = index;
    }
  },
};
</script>

<style lang="scss" scoped>
 .tab-second {
    display: flex;
    background: #f2f3f7;
    border-radius: 4px;
    box-sizing: border-box;
    align-items: center;
    margin-bottom: 12px;
    .tab-second-item {
      cursor: pointer;
      padding: 6px;
      margin: 0 4px;
    }
    .active {
      background: #ffffff;
      border-radius: 4px;
      margin: 4px;
      color: #116ef9;
    }
  }
</style>
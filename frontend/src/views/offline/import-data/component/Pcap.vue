<template>
  <div>
    <el-form-item label="任务名" prop="task_name">
      <el-input
        v-model="pcapForm.task_name"
        placeholder="请输入任务名"
        disabled
      ></el-input>
    </el-form-item>
    <el-form-item label="数据描述" prop="batch_description">
      <el-input
        v-model="pcapForm.batch_description"
        type="textarea"
        placeholder="请输入数据描述"
      ></el-input>
    </el-form-item>

    <!-- 服务器数据 -->
    <el-form-item v-if="!tabSecondIndex" prop="upload_mode">
      <el-radio-group v-model="pcapForm.upload_mode" @input="inputGroup">
        <el-radio
          v-for="(item, index) in lxData"
          :key="index"
          :label="item.label"
        >
          {{ item.label }}
          <el-tooltip content="请先将文件放至服务器 /data/pcapfiles目录下，在此输入文件相对路径" placement="top">
            <i class="el-icon-warning" style="color:#c6c6c6;font-weight: 500;"></i>
          </el-tooltip>
        </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="!tabSecondIndex" prop="file_path_list" class="path">
      <div v-if="pcapForm.upload_mode === '服务器文件'">
        <el-tree
          ref="treeRef"
          check-strictly
          :load="loadNode"
          show-checkbox
          node-key="file_path"
          lazy
          :props="{
            label: 'file_name',
            isLeaf: 'leaf',
            disabled:false
          }"
        >
        </el-tree>
      </div>
      <el-form-item v-else label="文件列表">
        <Upload
          v-model="pcapForm.file_path_list"
          upload-title="txt文件"
          :accept="'.txt'"
          type="text/plain"
          :is-read="true"
        />
      </el-form-item>
    </el-form-item>
    <el-form-item
      prop="fullflow_state"
      label="流量留存规则"
    >
      <el-radio-group v-model="pcapForm.fullflow_state">
        <el-radio label="ON">全部流量</el-radio>
        <el-radio label="OFF">规则命中流量</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item
      prop="flowlog_state"
      label="元数据留存"
    >
      <el-radio-group v-model="pcapForm.flowlog_state">
        <el-radio label="ON">是</el-radio>
        <el-radio label="OFF">否</el-radio>
      </el-radio-group>
    </el-form-item>
    <!-- pcap上传 -->
    <el-form-item v-if="tabSecondIndex === 1" label="pcap上传">
      <Upload
        v-model="pcapForm.file_path_list"
        upload-title="文件"
        size="10737418240"
        :accept="'.pcap,.pcapng,.cap'"
        :is-server="true"
      >
        <span class="drag-message-title">将pcap、cap、pcappng格式文件拖动到此处，或</span>
        <span class="drag-message-manual">点击选择文件</span>
        <!-- <div>将pcap、cap、pcappng格式文件拖动到此处，或点击选择文件</div> -->
        <!-- <div>将pcap、cap、pcappng格式文件拖动到此处，或点击选择文件</div> -->
      </Upload>
    </el-form-item>
  </div>
</template>

<script>
import Upload from "@/components/Upload";
import api from "@/api/offline";
export default {
  name: "Pcap",
  components: {
    Upload,
  },
  props: {
    pcapForm: {
      type: Object,
      default: () => {},
    },
    tabSecondIndex: {
      type: Number,
      default: 0,
    },
    lxData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      pathList: [],
      selectedOptions: [],
    };
  },
  methods: {
    // 选项值改变
    inputGroup() {
      this.pcapForm.file_path_list = [];
    },
    // 获取路径
    async offlineListServerPath(id = "") {
      const res = await api.offlineListServerPath(id);
      res.data.forEach((item) => {
        item.leaf = !item.disabled;
      });
      this.pathList = res.data;
    },
    getCheckedKeys() {
      let file_path_list = this.$refs.treeRef.getCheckedKeys();
      return file_path_list;
    },
    resetChecked() {
      this.$refs.treeRef.setCheckedKeys([]);
    },
    async loadNode(node, resolve) {
      if (node.level === 0) {
        const res = await api.offlineListServerPath();
        res.data.forEach((item) => {
          item.leaf = !item.disabled;
        });
        return resolve(res.data);
      }
      const { disabled, file_name, file_path } = node.data;
      // 当为pacp文件时，就返回空数组
      if (!disabled) return resolve([]);
      await this.offlineListServerPath(file_path);
      resolve(this.pathList);
    },
  },
};
</script>
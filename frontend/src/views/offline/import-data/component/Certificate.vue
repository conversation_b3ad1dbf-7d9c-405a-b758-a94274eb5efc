<template>
  <div>
    <el-form-item v-if="tabSecondIndex" label="数据库地址" prop="database_url">
      <el-input v-model="certForm.database_url" placeholder="请输入数据库地址"></el-input>
    </el-form-item>
    <el-form-item v-if="tabSecondIndex" label="数据库表名" prop="database_name">
      <el-input v-model="certForm.database_name" placeholder="请输入数据库表名"></el-input>
    </el-form-item>
    <el-form-item v-if="!tabSecondIndex" prop="black_list" label="权重">
      <el-input-number v-model="certForm.black_list" :precision="0" controls-position="right" :min="0" :max="100" style="width: 100%;"></el-input-number>
    </el-form-item>
    <el-form-item v-if="!tabSecondIndex" prop="wj">
      <el-radio-group v-model="certForm.wj">
        <el-radio
          v-for="(item, index) in fileData"
          :key="index"
          :label="item.label"
        ></el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item v-if="!tabSecondIndex">
      <Upload v-model="certForm.cert_files" :upload-title="certForm.wj" />
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: "Certificate",
  props: {
    certForm: {
      type: Object,
      default: () => {},
    },
    tabSecondIndex: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      fileData: [
        { key: 1, label: "文件" },
        { key: 2, label: "文件夹" },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
::v-deep{
  .el-input__inner{
    text-align: left;
  }
}
</style>
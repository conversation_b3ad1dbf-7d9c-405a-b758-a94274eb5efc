<template>
  <div class="drag-list">
    <div v-for="(item, index) in files" :key="index" class="item">
      <div class="info">
        <div class="name">
          <i
            class="icon el-icon-check"
          ></i>
          <div class="text">{{ item }}</div>
        </div>
        <div class="handle">
          <el-button type="text" @click="handleDel(index)">删除</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name:'PcapList',
  props: {
    files:{
      type:Array,
      default:()=>[]
    }
  },
  methods: {
    // 上传
    handleDel(index) {
      this.$emit('handleDel',index);
    },
  } 
};
</script>

<style lang="scss" scoped>
.drag-list {
  margin-top: 8px;
  max-height: 250px;
  overflow-y: scroll;
  .item {
    .info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .name {
        display: flex;
        align-items: center;
        .icon {
          font-size: 12px;
          margin-right: 4px;
          font-weight: bold;
        }
        .el-icon-check {
          color: #116ef9;
        }
        .el-icon-warning-outline {
          font-size: 14px;
          color: red;
        }
      }
    }
  }
}
</style>
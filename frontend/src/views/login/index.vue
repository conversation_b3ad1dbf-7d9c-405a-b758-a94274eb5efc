<template>
  <div class="login-container">
    <div class="logo-box">
    </div>
    <el-form
      ref="loginForm"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
      auto-complete="on"
      label-position="top"
      hide-required-asterisk
    >
      <div class="title-container">
        <h3 class="title">{{ $title }}</h3>
      </div>

      <el-form-item prop="username" label="用户名">
        <el-input
          ref="username"
          v-model="loginForm.username"
          placeholder="请输入用户名"
          name="username"
          type="text"
          tabindex="1"
          auto-complete="on"
        />
      </el-form-item>

      <el-form-item prop="password" label="密码">
        <el-input
          :key="passwordType"
          ref="password"
          v-model="loginForm.password"
          :type="passwordType"
          placeholder="请输入密码"
          name="password"
          tabindex="2"
          auto-complete="on"
          show-password
          @keyup.enter.native="handleLogin"
        />
      </el-form-item>

      <el-button
        :loading="loading"
        type="primary"
        block
        class="login-btn"
        @click.native.prevent="handleLogin"
      >
        登录
      </el-button>

      <div class="tips">
        <span style="margin-right: 20px"></span>
        <span> </span>
      </div>
    </el-form>
  </div>
</template>

<script>
import { validUsername } from "@/utils/validate";
import request from "@/utils/request";
export default {
  name: "Login",
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error("请输入正确的用户名"));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error("密码不能小于6位"));
      } else {
        callback();
      }
    };
    return {
      loginForm: {
        username: "",
        password: "",
      },
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 15, message: '用户名长度在 3 到 15 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, trigger: "blur", validator: validatePassword },
        ],
      },
      loading: false,
      passwordType: "password",
      redirect: undefined,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  mounted() { 
  },
  
  methods: {
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = "";
      } else {
        this.passwordType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$store
            .dispatch("user/login", this.loginForm)
            .then(() => {
              /**
               * @description:点击登录时 获取知识库和tag列表
               * @return {*}
               */
              // this.GETDICT();
              this.GETTAGS();
              this.$router.push({ path: this.redirect || "/" });
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },

    // 获取知识库
    // GETDICT() {
    //   if (JSON.stringify(this.$store.state.long.Dict) == "{}") {
    //     request({
    //       url: "/dict",
    //       method: "GET",
    //     })
    //       .then((res) => {
    //         if (res.err == 0) {
    //           this.$store.commit("long/GET_Dictus", res.data);
    //         } else {
    //           console.log("err:" + res.err + ",msg:" + res.msg);
    //         }
    //       })
    //       .catch((err) => {});
    //   }
    // },
    // 获取标签库
    GETTAGS() {
      if (this.$store.state.conversational.taglist01.length == 0) {
        request({
          url: "/session/tag/list",
          method: "POST",
        })
          .then((res) => {
            console.log(res);
            if (res.err == 0) {
              this.$store.commit("conversational/tagseachlist", res.data);
            } else {
              console.log("err:" + res.err + ",msg:" + res.msg);
            }
          })
          .catch((err) => {});
      }
    },
  },
};
</script>

<style lang="scss">
$bg: #283443;
$light_gray: #fff;
$cursor: #fff;
.login-container {
  .el-form-item__label {
    color: #ffffff;
    padding: 0;
  }
  .el-input {
    width: 100%;
    height: 32px;
    color: #283443;
    input {
      width: 100%;
      height: 32px;
    }
  }
}
</style>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  min-height: 100%;
  width: 100%;
  background-color: $bg;
  overflow: hidden;
  background-image: url("../../assets/images/login-bg.png");
  background-position: center;
  background-size: 100% 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 317px;
  padding-right: 120px;
  .logo-box {
    width: 464px;
    height: 317px;
    > img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .login-form {
    position: relative;
    width: 330px;
    max-width: 100%;
    overflow: hidden;
  }
  .login-btn{
    width: 100%;
  }
  .tips {
    font-size: 14px;
    margin-bottom: 10px;
    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 32px;
      color: $light_gray;
      margin-bottom: 8px;
      text-align: left;
      font-weight: bold;
    }
    .title2 {
      font-size: 24px;
      text-align: left;
      color: #ffffff;
      margin-bottom: 40px;
      background: linear-gradient(90deg, #07d4ab -80.77%, #116ef9 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
}
</style>

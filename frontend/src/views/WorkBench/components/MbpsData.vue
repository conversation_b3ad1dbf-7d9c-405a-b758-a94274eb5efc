<template>
  <div class="mbps" :style="probeCheck === true ? 'opacity: 0.5;cursor:not-allowed;' : ''">
    <div id="mbps-box" v-loading="mbpsLoading" class="mbps-box"
         :style="probeCheck === true ? 'opacity: 0.5;pointer-events: none;' : ''"
    >
      <!-- eslint-disable-next-line vue/no-duplicate-attributes -->
      <div v-for="(item, index) of mbpsArr" id="mbps1" :id="'mbps-item' + index" :key="index" class="mbps-item"
           :style="item.name == '-' ? 'opacity: 0.5;' : ''"
      >
        <div class="mbps-item-title">
          <div>{{ item.name }}</div>

          <div class="sw-box" :style="item.name == '-' ? 'display:none;' : ''">
            <section :class="item.task_id === 0 ? 'inactive' : ''" @click="() => {

              TASK_CHANGE(index, 0);
            }"
            >
              主任务
            </section>
            <section :class="item.task_id === 1 ? 'active' : ''" @click="() => {

              TASK_CHANGE(index, 1);
            }"
            >
              从任务
            </section>
          </div>
        </div>
        <section>
          <div class="mbps-num">
            <span>{{ TOOL_NUMBER(FMT_MBPS(item.num).num) }}</span>
            <span>{{ FMT_MBPS(item.num).unit }}</span>
          </div>
          <div class="mbps-echarts">
            <v-chart :option="item.echarts" autoresize :update-options="{ notMerge: true }"></v-chart>
          </div>
        </section>
      </div>
    </div>
    <div class="task-box" :style="probeCheck === true ? 'opacity: 0.5;pointer-events: none;' : ''">
      <div id="task1" :class="task_id == 0 ? 'task-item1' : 'task-item2'" @click="CLICK_TASK_1">
        <aside :class="taskSettings[0].task_state === 1 ? 'state-1' : 'state-2'"></aside>
        <div class="task1-title">
          <div class="task-point">
            <img v-if="taskSettings[0].task_state === 1" src="../../../assets/images/yxrw.svg" alt="">
            <img v-else src="../../../assets/images/gqrw.svg" alt="">
            主任务
          </div>
          <div v-show="taskSettings[0].batch_remark !== ''">{{ taskSettings[0].batch_remark }}</div>
          <div class="title-config">
            <svg-icon class="coolicon" icon-class="coolicon" @click.stop="TASK_CONFIG(0)"></svg-icon>
            <!-- <el-switch v-model="taskSettings[0].task_state" active-color="#116EF9" inactive-color="#FF9534"
              :active-value="1" :inactive-value="2" @change="TASK_SWITH">
            </el-switch> -->
            <div class="sw-box">
              <section :class="taskSettings[0].task_state === 1 ? 'inactive' : ''" @click.stop="() => {
                TASK_SWITH(0,1);
              }"
              >
                启用
              </section>
              <section :class="taskSettings[0].task_state === 2 ? 'active' : ''" @click.stop="() => {
                TASK_SWITH(0,2);
              }"
              >
                挂起
              </section>
            </div>
          </div>
        </div>
        <div class="task1-num">
          <div>
            <div v-for="(item, index) of mainTaskData" :key="index" class="num-item">
              <div>
                <span>{{ item.num || '0' }}</span>
                <span>{{ item.unit }}</span>
              </div>
              <div>{{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
      <div id="task2" :class="task_id == 1 ? 'task-item1' : 'task-item2'" @click="CLICK_TASK_2">
        <aside :class="taskSettings[1].task_state === 1 ? 'state-1' : 'state-2'"></aside>
        <div class="task1-title">
          <div class="task-point">
            <img v-if="taskSettings[1].task_state === 1" src="../../../assets/images/yxrw.svg" alt="">
            <img v-else src="../../../assets/images/gqrw.svg" alt="">
            从任务
          </div>
          <div v-show="taskSettings[1].batch_remark !== ''">{{ taskSettings[1].batch_remark }}</div>
          <div class="title-config">
            <svg-icon class="coolicon" icon-class="coolicon" @click.stop="TASK_CONFIG(1)"></svg-icon>
            <!-- <el-switch v-model="taskSettings[1].task_state" active-color="#116EF9" inactive-color="#FF9534"
              :active-value="1" :inactive-value="2" @change="TASK_SWITH">
            </el-switch> -->
            <div class="sw-box">
              <section :class="taskSettings[1].task_state === 1 ? 'inactive' : ''" @click.stop="() => {
                TASK_SWITH(1,1);
              }"
              >
                启用
              </section>
              <section :class="taskSettings[1].task_state === 2 ? 'active' : ''" @click.stop="() => {
                TASK_SWITH(1,2);
              }"
              >
                挂起
              </section>
            </div>
          </div>
        </div>
        <div class="task1-num">
          <div>
            <div v-for="(item, index) of minorTaskData" :key="index" class="num-item">
              <div>
                <span>{{ item.num || '0' }}</span>
                <span>{{ item.unit }}</span>
              </div>
              <div>{{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-drawer title="我是标题" :visible.sync="taskConfigDrawer" :with-header="false" :close-on-press-escape="false"
               :wrapper-closable="false" :modal-append-to-body="false" class="task-config" @open="() => {
               }" @opened="() => {
                 SHOW_TASKCONFIG();
               }" @closed="() => {
                 GET_TASK_INFO();
               }"
    >
      <div class="form-title">
        <span>任务配置</span>
        <!-- <span @click="CLOSE_TASK1_POPOVER">关闭</span> -->
        <i class="el-icon-close" style="cursor: pointer;" @click="CLOSE_TASK1_POPOVER"></i>
      </div>
      <article v-if="taskConfig === 0" v-loading="taskConfigLoading" class="title-config">
        <div class="task1-form">
          <div class="taskSettings-name">任务名</div>
          <el-input disabled value="主任务">
          </el-input>
          <aside class="task-describe">
            <div class="task-describe-title">任务描述</div>
            <el-input v-model="taskSettings[0].batch_remark" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }"
                      placeholder="请输入任务描述" @input="() => {
                        taskConfigEdit = true
                      }"
            >
            </el-input>
          </aside>
          <div class="tasksettings-tree">
            <el-tree ref="taskConfig1" :data="taskConfig1" :props="defaultProps" show-checkbox default-expand-all
                     node-key="key" @check="TREE_CHANGE"
            ></el-tree>
          </div>
          <div class="tasksettings-foot">
            <el-button class="task1-reset" @click="TASK_CONFIG_RESET(0)">恢复默认配置</el-button>
            <el-button type="info" class="task1-submit" @click="TASK_CONFIG_CHANGE(0)">确定</el-button>
          </div>
        </div>
      </article>
      <article v-if="taskConfig === 1" v-loading="taskConfigLoading" class="title-config">
        <div class="task1-form">
          <div class="taskSettings-name">任务名</div>
          <el-input disabled value="从任务">
          </el-input>
          <aside class="task-describe">
            <div class="task-describe-title">任务描述</div>
            <el-input v-model="taskSettings[1].batch_remark" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }"
                      placeholder="请输入任务描述" @input="() => {
                        taskConfigEdit = true
                      }"
            >
            </el-input>
          </aside>
          <div class="tasksettings-tree">
            <el-tree ref="taskConfig2" :data="taskConfig1" :props="defaultProps" show-checkbox default-expand-all
                     node-key="key" @check="TREE_CHANGE"
            ></el-tree>
          </div>
          <div class="tasksettings-foot">
            <el-button class="task1-reset" @click="TASK_CONFIG_RESET(1)">恢复默认配置</el-button>
            <el-button type="info" class="task1-submit" @click="TASK_CONFIG_CHANGE(1)">确定</el-button>
          </div>
        </div>
      </article>
    </el-drawer>
    <el-dialog title="提示" :visible.sync="taskConfigEditVisible" width="30%" class="taskConfigEditVisible">
      <span>是否保存已修改的配置？</span>
      <span slot="footer" class="dialog-footer">
        <el-button class="cancel" @click="taskConfigEditVisible = false">关闭</el-button>

        <el-button @click="() => {
          taskConfigEditVisible = false;
          taskConfigDrawer = false;
        }"
        >放弃</el-button>
        <el-button type="primary" @click="TASK_CONFIG_CHANGE(taskConfig)">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  mbps1_options,
  mbps2_options,
  mbps3_options,
  mbps4_options,
} from "./echartsData";
import { task_info, task_put, tz_check, config_edit } from "@/api/MbpsData";

import { LeaderLine } from '../../../utils/leader-line.min';
export default {
  name: "Mbpsdata",
  inject: ["reload"],
  data () {
    return {
      task_id: 0,
      loading: true,
      arr: [
        ["mbps1", "task1"],
        ["mbps2", "task1"],
        ["mbps3", "task1"],
        ["mbps4", "task1"],
      ],
      plumbIns: Object,
      taskConfig1: [
        {
          label: "全流量留存",
          key: "fullflow_state",
        },
        {
          label: "元数据留存",
          children: [
            {
              label: "会话元数据",
              key: "full_flow_should_log_def",
            },
            {
              label: "协议元数据",
              key: "parse_proto_should_log_def",
              children: [
                {
                  label: "SSL",
                  key: "10638",
                },
                {
                  label: "HTTP",
                  key: "10637",
                },
                {
                  label: "DNS",
                  key: "10071",
                },
              ],
            },
          ],
        },
        {
          label: "DDOS流量过滤",
          key: "ddos_state",
        },
        // {
        //   label: '智能流量过滤',
        //   key: 'test111'
        // }
      ],

      defaultProps: {
        children: "children",
        label: "label",
      },
      // 网口
      mbpsLoading: true,
      mbpsArr: [
        {
          name: "-",
          num: 0,
          task_id: "",
          task_state: 0,
          arr: [],
          id: "",
          echarts: mbps1_options,
        },
        {
          name: "-",
          num: 0,
          task_id: "",
          task_state: 0,
          arr: [],
          id: "",
          echarts: mbps2_options,
        },
        {
          name: "-",
          num: 0,
          task_state: 0,
          arr: [],
          id: "",
          echarts: mbps3_options,
        },
        {
          name: "-",
          num: 0,
          task_state: 0,
          arr: [],
          id: "",
          echarts: mbps4_options,
        },
      ],
      // 主任务数据
      mainTaskData: [
        {
          num: "0",
          unit: "",
          name: "流量大小",
        },
        {
          num: "0",
          unit: "个",
          name: "过滤规则",
        },
        {
          num: "0",
          unit: "个",
          name: "采集规则",
        },
        {
          num: "0",
          unit: "",
          name: "流量留存",
        },
        {
          num: "0",
          unit: "",
          name: "元数据留存",
        },
        {
          num: "0",
          unit: "个",
          name: "告警数量",
        },
      ],
      // 从任务数据
      minorTaskData: [
        {
          num: "0",
          unit: "",
          name: "流量大小",
        },
        {
          num: "0",
          unit: "个",
          name: "过滤规则",
        },
        {
          num: "0",
          unit: "个",
          name: "采集规则",
        },
        {
          num: "0",
          unit: "",
          name: "流量留存",
        },
        {
          num: "0",
          unit: "",
          name: "元数据留存",
        },
        {
          num: "0",
          unit: "个",
          name: "告警数量",
        },
      ],
      // 任务配置
      taskSettings: [
        // 主任务
        {
          // 任务ID
          task_id: 0,
          // 批次ID
          batch_id: "",
          batch_remark: "",
          // 全流量留存ON/OFF
          fullflow_state: "OFF",
          // 会话元数据保留 0否 1是
          full_flow_should_log_def: 0,
          // 协议元数据保留 0否 1是
          parse_proto_should_log_def: 0,
          // 协议元数据集合
          parse_proto_should_log_def_list: [],
          // DDOS数据留存 0否 1是
          ddos_state: 0,
          // 任务状态 0历史任务 1当前任务 2挂起任务
          task_state: 0,
          // 连接的网口
          netflows: [],
        },
        // 从任务
        {
          // 任务ID
          task_id: 0,
          // 批次ID
          batch_id: "",
          batch_remark: "",
          // 全流量留存ON/OFF
          fullflow_state: "ON",
          // 会话元数据保留 0否 1是
          full_flow_should_log_def: 0,
          // 协议元数据保留 0否 1是
          parse_proto_should_log_def: 0,
          // 协议元数据集合
          parse_proto_should_log_def_list: [],
          // DDOS数据留存 0否 1是
          ddos_state: 0,
          // 任务状态 0历史任务 1当前任务 2挂起任务
          task_state: 0,
          // 连接的网口
          netflows: [],
        },
      ],
      // 任务配置抽屉
      taskConfigDrawer: false,
      taskConfig: 0,
      taskConfigEdit: false,
      taskConfigEditVisible: false,
      taskConfigLoading: false,
      // websocket
      ws: null,
      // 网口任务关系线条渲染
      mbpsLine: [],
      // 探针同步状态，限制用户操作
      probeCheck: false,
      // 连线
      jsPlumb: Object,
      lineList: []
    };
  },
  computed: {},
  watch: {
    // 监听网口数据变化
    "$store.state.long.tableType18": {
      immediate: true,
      deep: true,
      handler: function (val, old) {
        if(!val){
          return;
        }
        // 拼装网口所属任务及状态
        for (let i of this.mbpsArr) {
          if (val.task_flow[0].task_flow.length > 0) {
            for (let j of val.task_flow[0].task_flow) {
              let a = j.netflow.some((item, index) => {
                if (i.name === item) {
                  return true;
                } else {
                  return false;
                }
              });
              if (a) {
                i.task_id = j.task_id;
                i.task_state = j.task_state;
              }
            }
          }

        }
        // 拼装网口流动数据与名称
        if (val.task_flow[0].flow && val.task_flow[0].flow.length > 0) {
          for (let i = 0; i < val.task_flow[0].flow.length; i++) {
            this.mbpsArr[i].name = `${val.task_flow[0].flow[i].flow_name}`;
            this.mbpsArr[i].arr =
              val.task_flow[0].flow_status[this.mbpsArr[i].name];
            this.mbpsArr[i].echarts.series[1].data =
              val.task_flow[0].flow_status[this.mbpsArr[i].name];
            // 判断对应网口绑定的任务渲染对应的的状态色
            this.mbpsArr[i].echarts.series[1].itemStyle.normal.color = this.mbpsArr[i].task_state === 1 ? '#07d4ab' : '#ff9534';
          }
        }
        // 拼装网口流量
        if (val.status && val.status.length > 0) {
          for(let i = 0;i<this.mbpsArr.length;i++){
            for(let j = 0;j<val.status.length;j++){
              if(this.mbpsArr[i].name == val.status[j].port_pos){
                this.mbpsArr[i].num = val.status[j].bps;
              }
            }
          }
        }
        // 拼装网口ID
        let data = val.task_flow[0].flow;
        for (let i of this.mbpsArr) {
          for (let j = 0; j < data.length; j++) {
            if (i.name === data[j].flow_name) {
              i.id = data[j].id;
            }
          }
        }
        // 统计任务流量大小
        let mbps0 = 0;
        let mbps1 = 0;
        for (let i of val.status) {
          if (i.task_id === 0) {
            mbps0 = this.$ACC_ADD(mbps0, i.bps);
          }
          if (i.task_id === 1) {
            mbps1 = this.$ACC_ADD(mbps1, i.bps);
          }
        }

        this.mainTaskData[0].num = this.TOOL_NUMBER(this.FMT_MBPS(mbps0).num);
        this.mainTaskData[0].unit = this.FMT_MBPS(mbps0).unit;
        this.minorTaskData[0].num = this.TOOL_NUMBER(this.FMT_MBPS(mbps1).num);
        this.minorTaskData[0].unit = this.FMT_MBPS(mbps1).unit;
        this.MBPS_LINE();
      },
    },
    // 监听主、从任务数据变化(仅展示数据，不包括配置数据)
    "$store.state.long.tableType21": {
      immediate: true,
      deep: true,
      handler: function (val, old) {
        if(!val){
          return;
        }
        for (let i of val.forensics) {
          if (i.task_id === "0") {
            // 流量大小
            // this.mainTaskData[0].num = this.TOOL_NUMBER(
            //   this.FMT_MBPS(val.forensics[0].bps).num
            // );
            // this.mainTaskData[0].unit = this.FMT_MBPS(
            //   val.forensics[0].bps
            // ).unit;
            // 过滤规则总数
            this.mainTaskData[1].num = i.filter_out;
            // 采集规则
            this.mainTaskData[2].num = i.rule;
            // 流量留存
            this.mainTaskData[3].num = this.TOOL_NUMBER(
              this.FMT_THOU(i.full_save).num
            );
            this.mainTaskData[3].unit = this.FMT_THOU(i.full_save).unit;
            // 元数据留存
            this.mainTaskData[4].num = this.TOOL_NUMBER(
              this.FMT_THOU(i.pb_num).num
            );
            this.mainTaskData[4].unit = this.FMT_THOU(i.pb_num).unit;
            // 告警数量
            this.mainTaskData[5].num = this.THOUSAND(i.alarm);
          }
          if (i.task_id === "1") {
            // 流量大小
            // this.minorTaskData[0].num = this.TOOL_NUMBER(
            //   this.FMT_MBPS(val.forensics[1].bps).num
            // );
            // this.minorTaskData[0].unit = this.FMT_MBPS(
            //   val.forensics[1].bps
            // ).unit;
            // 过滤规则总数
            this.minorTaskData[1].num = i.filter_out;
            // 采集规则
            this.minorTaskData[2].num = i.rule;
            // 全流量留存
            this.minorTaskData[3].num = this.TOOL_NUMBER(
              this.FMT_THOU(i.full_save).num
            );
            this.minorTaskData[3].unit = this.FMT_THOU(i.full_save).unit;
            // 元数据留存
            this.minorTaskData[4].num = this.TOOL_NUMBER(
              this.FMT_THOU(i.pb_num).num
            );
            this.minorTaskData[4].unit = this.FMT_THOU(i.pb_num).unit;
            // 告警数量
            this.minorTaskData[5].num = this.THOUSAND(i.alarm);
          }
        }
      },
    },
    // 监听网口序列化之后的数据，代表网口有变动，重新绘制线条
    'mbpsArr': {
      deep: true,
      handler: function (val, old) {
        this.$nextTick(() => {
          this.INIT_LINE();
        });
      }
    }
  },
  created () {
    this.jsPlumb = this.$jsPlumb.getInstance({
      Container: "workspace",   //选择器id
      LogEnabled: true,// 是否启用日志
      Endpoint: 'Blank',// 链接端点
      Anchors: ['Bottom', 'Top'],
      Connector: ["Flowchart", { gap: 2 }]   //要使用的默认连接器的类型：折线，流程等
    });
    this.PROBE_CHECK();
    this.GET_TASKID();
    this.GET_TASK_INFO();

  },
  mounted () {

    this.INIT_LINE();
    window.addEventListener('resize', (res, x) => {
    }, true);
    window.addEventListener('mousewheel', (res, x) => {
    }, true);
    window.addEventListener('scroll', (res, x) => {
      if (this.lineList.length > 0) {
        for (let i = 0; i < this.lineList.length; i++) {
          this.lineList[i].position();
        }
      }
    }, true);

  },
  beforeDestroy () {
    for (let i = 0; i < this.lineList.length; i++) {
      this.lineList[i].remove();
    }
    this.lineList = [];
  },

  methods: {
    // TASK_ID，区分主从任务，及更新一些数据
    GET_TASKID () {
      this.task_id = localStorage.getItem('task_id') || '0';
    },
    // 获取主从任务、网口的基本数据并渲染线条
    GET_TASK_INFO () {
      task_info().then((res) => {
        this.taskSettings = res.data;
        this.$store.commit("conversational/getCurrentTask", res.data);
      });
    },
    // 回显任务配置
    SHOW_TASKCONFIG () {
      if (this.taskConfig === 0) {
        // 回显主任务配置
        let arr = [];
        const taskConfig1 = this.taskSettings[0];
        if (taskConfig1.parse_proto_should_log_def_list === null)
          taskConfig1.parse_proto_should_log_def_list = [];
        if (taskConfig1.fullflow_state === "ON")
          this.$refs.taskConfig1.setChecked("fullflow_state", true, false);
        if (taskConfig1.full_flow_should_log_def === 1)
          this.$refs.taskConfig1.setChecked(
            "full_flow_should_log_def",
            true,
            false
          );
        if (taskConfig1.parse_proto_should_log_def === 1)
          this.$refs.taskConfig1.setChecked(
            "parse_proto_should_log_def",
            true,
            false
          );
        if (taskConfig1.ddos_state === 1)
          this.$refs.taskConfig1.setChecked("ddos_state", true, false);
        if (
          taskConfig1.parse_proto_should_log_def_list
            .join("")
            .indexOf(10071) !== -1
        )
          this.$refs.taskConfig1.setChecked("10071", true, false);
        if (
          taskConfig1.parse_proto_should_log_def_list
            .join("")
            .indexOf(10637) !== -1
        )
          this.$refs.taskConfig1.setChecked("10637", true, false);
        if (
          taskConfig1.parse_proto_should_log_def_list
            .join("")
            .indexOf(10638) !== -1
        )
          this.$refs.taskConfig1.setChecked("10638", true, false);
      }
      if (this.taskConfig === 1) {
        // 回显从任务配置
        let arr = [];
        const taskConfig2 = this.taskSettings[1];
        if (taskConfig2.parse_proto_should_log_def_list === null)
          taskConfig2.parse_proto_should_log_def_list = [];
        if (taskConfig2.fullflow_state === "ON")
          this.$refs.taskConfig2.setChecked("fullflow_state", true, false);
        if (taskConfig2.full_flow_should_log_def === 1)
          this.$refs.taskConfig2.setChecked(
            "full_flow_should_log_def",
            true,
            false
          );
        if (taskConfig2.parse_proto_should_log_def === 1)
          this.$refs.taskConfig2.setChecked(
            "parse_proto_should_log_def",
            true,
            false
          );
        if (taskConfig2.ddos_state === 1)
          this.$refs.taskConfig2.setChecked("ddos_state", true, false);
        if (
          taskConfig2.parse_proto_should_log_def_list
            .join("")
            .indexOf(10071) !== -1
        )
          this.$refs.taskConfig2.setChecked("10071", true, false);
        if (
          taskConfig2.parse_proto_should_log_def_list
            .join("")
            .indexOf(10637) !== -1
        )
          this.$refs.taskConfig2.setChecked("10637", true, false);
        if (
          taskConfig2.parse_proto_should_log_def_list
            .join("")
            .indexOf(10638) !== -1
        )
          this.$refs.taskConfig2.setChecked("10638", true, false);
      }
    },
    // 查询探针同步状态
    PROBE_CHECK () {
      tz_check().then((res) => {
        switch (res.data) {
        case "60001":
          // 同步成功
          this.$message.success("探针同步成功");
          tz_check({ suc: 4 }).then((res) => {
            if (res.err === 0) {
              this.probeCheck = false;
              setTimeout(() => {
                location.reload();
              }, 2000);
            }
          });
          break;
        case "60002":
          // 同步失败
          this.$message.warning("探针同步失败");
          this.probeCheck = false;
          location.reload();
          break;
        case "60003":
          // 同步中
          this.$message.info("探针同步中");
          this.probeCheck = true;
          setTimeout(() => {
            this.PROBE_CHECK();
          }, 5000);
          break;
        case "60004":
          // 静止状态，无同步任务
          this.probeCheck = false;
          break;
        default:
          break;
        }
      });
    },

    CLICK_TASK_1 () {
      localStorage.setItem("task_id", 0);
      this.task_id = 0;
      location.reload();
    },
    CLICK_TASK_2 () {
      localStorage.setItem("task_id", 1);
      this.task_id = 1;
      location.reload();
    },
    // 线条初始化
    INIT_LINE () {
      if (this.lineList.length > 0) {
        for (let i = 0; i < this.lineList.length; i++) {
          this.lineList[i].remove();
        }
      }
      this.lineList = [];
      for (let i = 0; i < this.mbpsArr.length; i++) {

        if (this.mbpsArr[i].task_state == 1) {
          // this.jsPlumb.connect({
          //   source: 'mbps-item' + i,
          //   target: 'task' + (this.mbpsArr[i].task_id + 1)
          // },
          //   {
          //     paintStyle: { stroke: '#07d4ab', strokeWidth: 3 },
          //   });

          let source = document.getElementById('mbps-item' + i);
          let target = document.getElementById('task' + (this.mbpsArr[i].task_id + 1));
          let line = new LeaderLine(source, target, {
            path: 'grid',
            startPlug: 'behind',
            endPlug: 'behind',
            color: '#07d4ab'
          });
          line.setOptions({ startSocket: 'bottom', endSocket: 'top' });
          let showEffectName = 'none';

          line.show(showEffectName);
          this.lineList.push(line);
        }
        else if (this.mbpsArr[i].task_state == 2) {
          let source = document.getElementById('mbps-item' + i);
          let target = document.getElementById('task' + (this.mbpsArr[i].task_id + 1));
          let line = new LeaderLine(source, target, {
            path: 'grid',
            startPlug: 'behind',
            endPlug: 'behind',
            color: '#ff9534'
          });
          line.setOptions({ startSocket: 'bottom', endSocket: 'top' });
          let showEffectName = 'none';
          line.show(showEffectName);
          this.lineList.push(line);
        // eslint-disable-next-line no-empty
        } else { }
      }


    },
    // 格式化网络速率
    FMT_MBPS (size) {
      if (size === 0) {
        return {
          num: "0",
          unit: "bps",
        };
      }
      let bytes = parseInt(size);
      if (bytes === 0) return "0 B";
      let k = 1000, // or 1024
        sizes = ["bps", "Kbps", "Mbps", "Gbps"],
        i = Math.floor(Math.log(bytes) / Math.log(k));
      // return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
      return {
        num: (bytes / Math.pow(k, i)).toPrecision(3),
        unit: sizes[i],
      };
    },
    // 格式化流量大小
    FMT_THOU (size) {
      if (size === 0) {
        return {
          num: "0",
          unit: "B",
        };
      }
      let bytes = parseInt(size);
      if (bytes === 0) return "0 B";
      let k = 1024, // or 1024
        sizes = ["Byte", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"],
        i = Math.floor(Math.log(bytes) / Math.log(k));
      // return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
      return {
        num: (bytes / Math.pow(k, i)).toPrecision(3),
        unit: sizes[i],
      };
    },
    // 根据当前任务状态渲染不同class
    STATE_CLASS (task) {
      if (task?.netflow_vos?.length < 1) {
        return "state-0";
      } else {
        switch (task.task_state) {
        case 1:
          return "state-1";
        case 2:
          return "state-2";
        default:
        }
      }
    },
    // 重新绘制网口与任务之间的线条
    MBPS_LINE () {
      this.mbpsLine = [];
      for (let i = 0; i < this.mbpsArr.length; i++) {
        // 判断是否是启用的网口
        let item = this.mbpsArr[i];
        if (item.name !== "-") {
          // 不同的网口样式不同，进行区分
          // task_id   0——主任务 1——从任务
          // task_state   1——正常状态 2——挂起状态
          // 通过字符串拼接来判断这个网口对应任务应该使用什么样的线条
          switch (i) {
          case 0:
            if (item.task_id + "" + (item.task_state + "") === "01") {
              // 主任务
              this.mbpsLine.push("a01");
              // this.$set(this.mbpsLine, this.mbpsLine[this.mbpsLine.length - 1], 'a01')
            } else if (item.task_id + "" + (item.task_state + "") === "02") {
              // 主任务挂起
              this.mbpsLine.push("a02");
            } else if (item.task_id + "" + (item.task_state + "") === "11") {
              // 从任务
              this.mbpsLine.push("a11");
            } else if (item.task_id + "" + (item.task_state + "") === "12") {
              // 从任务挂起
              this.mbpsLine.push("a12");
            }
            break;
          case 1:
            if (item.task_id + "" + (item.task_state + "") === "01") {
              // 主任务
              this.mbpsLine.push("b01");
            } else if (item.task_id + "" + (item.task_state + "") === "02") {
              // 主任务挂起
              this.mbpsLine.push("b02");
            } else if (item.task_id + "" + (item.task_state + "") === "11") {
              // 从任务
              this.mbpsLine.push("b11");
            } else if (item.task_id + "" + (item.task_state + "") === "12") {
              // 从任务挂起
              this.mbpsLine.push("b12");
            }
            break;
          case 2:
            if (item.task_id + "" + (item.task_state + "") === "01") {
              // 主任务
              this.mbpsLine.push("c01");
            } else if (item.task_id + "" + (item.task_state + "") === "02") {
              // 主任务挂起
              this.mbpsLine.push("c02");
            } else if (item.task_id + "" + (item.task_state + "") === "11") {
              // 从任务
              this.mbpsLine.push("c11");
            } else if (item.task_id + "" + (item.task_state + "") === "12") {
              // 从任务挂起
              this.mbpsLine.push("c12");
            }
            break;
          case 3:
            if (item.task_id + "" + (item.task_state + "") === "01") {
              // 主任务
              this.mbpsLine.push("d01");
            } else if (item.task_id + "" + (item.task_state + "") === "02") {
              // 主任务挂起
              this.mbpsLine.push("d02");
            } else if (item.task_id + "" + (item.task_state + "") === "11") {
              // 从任务
              this.mbpsLine.push("d11");
            } else if (item.task_id + "" + (item.task_state + "") === "12") {
              // 从任务挂起
              this.mbpsLine.push("d12");
            }
            break;

          default:
            break;
          }
        }
      }
      this.mbpsLoading = false;
    },
    // 变更网口状态
    TASK_CHANGE (k, num) {

      this.$confirm('变更网口状态，服务重启可能导致部分展示延迟', '是否变更网口状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.mbpsArr[k].task_id = num;
        let data = [
          {
            task_id: 0,
            netflows: [],
            task_state: this.taskSettings[0].task_state,
          },
          {
            task_id: 1,
            netflows: [],
            task_state: this.taskSettings[1].task_state,
          },
        ];
        for (let i of this.mbpsArr) {
          if (i.name !== "-") {
            if (i.task_id === 0) {
              data[0].netflows.push(i.id);
            }
            if (i.task_id === 1) {
              data[1].netflows.push(i.id);
            }
          }
        }
        task_put(data).then((res) => {
          if (res.err === 0) {
            this.$message.success(res.msg);
            this.reload();
          }
        });

      }).catch(() => {
        console.log('取消');
      });


    },
    // 变更任务状态
    TASK_SWITH (index,state) {
      this.$confirm('变更任务状态，服务重启可能导致部分展示延迟', '是否变更任务状态', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.taskSettings[index].task_state = state;
        let data = [
          {
            task_id: 0,
            netflows: [],
            task_state: this.taskSettings[0].task_state,
          },
          {
            task_id: 1,
            netflows: [],
            task_state: this.taskSettings[1].task_state,
          },
        ];
        
        for (let i of this.mbpsArr) {
          if (i.name !== "-") {
            if (i.task_id === 0) {
              data[0].netflows.push(i.id);
            }
            if (i.task_id === 1) {
              data[1].netflows.push(i.id);
            }
          }
        }
        task_put(data).then((res) => {
          if (res.err === 0) {
            this.$message.success(res.msg);
            this.reload();
          }
        });
      }).catch(() => { });

    },
    // 任务配置
    TASK_CONFIG (task) {
      this.taskConfig = task;
      this.taskConfigEdit = false;
      this.taskConfigDrawer = true;
    },
    // 任务配置有变动时回调
    TREE_CHANGE () {
      this.taskConfigEdit = true;
    },
    // 改变任务配置
    TASK_CONFIG_CHANGE (task) {
      this.taskConfigLoading = true;
      if (task === 0) {
        let arr = this.$refs.taskConfig1
          .getHalfCheckedKeys()
          .concat(this.$refs.taskConfig1.getCheckedKeys())
          .join();
        let obj = {
          system: "probe",
          task_id: 0,
          batch_id: 100001,
          batch_remark: JSON.parse(
            JSON.stringify(this.taskSettings[0].batch_remark)
          ),
          fullflow_state: arr.indexOf("fullflow_state") !== -1 ? "ON" : "OFF",
          full_flow_should_log_def:
            arr.indexOf("full_flow_should_log_def") !== -1 ? 1 : 0,
          parse_proto_should_log_def:
            arr.indexOf("parse_proto_should_log_def") !== -1 ? 1 : 0,
          parse_proto_should_log_def_list: [],
          ddos_state: arr.indexOf("ddos_state") !== -1 ? 1 : 0,
        };
        if (arr.indexOf("10638") !== -1)
          obj.parse_proto_should_log_def_list.push(10638);
        if (arr.indexOf("10637") !== -1)
          obj.parse_proto_should_log_def_list.push(10637);
        if (arr.indexOf("10071") !== -1)
          obj.parse_proto_should_log_def_list.push(10071);
        config_edit(obj)
          .then((res) => {
            if (res.err === 0) {
              this.$message.success(res.msg);
              this.taskConfigEdit = false;
              this.taskConfigEditVisible = false;
              this.taskConfigDrawer = false;
              this.taskConfigLoading = false;
            }
          })
          .catch((err) => {
            this.taskConfigLoading = false;
          });
      }
      if (task === 1) {
        let arr = this.$refs.taskConfig2
          .getHalfCheckedKeys()
          .concat(this.$refs.taskConfig2.getCheckedKeys())
          .join();
        let obj = {
          system: "probe",
          task_id: 1,
          batch_id: 100002,
          batch_remark: JSON.parse(
            JSON.stringify(this.taskSettings[1].batch_remark)
          ),
          fullflow_state: arr.indexOf("fullflow_state") !== -1 ? "ON" : "OFF",
          full_flow_should_log_def:
            arr.indexOf("full_flow_should_log_def") !== -1 ? 1 : 0,
          parse_proto_should_log_def:
            arr.indexOf("parse_proto_should_log_def") !== -1 ? 1 : 0,
          parse_proto_should_log_def_list: [],
          ddos_state: arr.indexOf("ddos_state") !== -1 ? 1 : 0,
        };
        if (arr.indexOf("10638") !== -1)
          obj.parse_proto_should_log_def_list.push(10638);
        if (arr.indexOf("10637") !== -1)
          obj.parse_proto_should_log_def_list.push(10637);
        if (arr.indexOf("10071") !== -1)
          obj.parse_proto_should_log_def_list.push(10071);
        config_edit(obj)
          .then((res) => {
            if (res.err === 0) {
              this.$message.success(res.msg);
              this.taskConfigEdit = false;
              this.taskConfigEditVisible = false;
              this.taskConfigDrawer = false;
              this.taskConfigLoading = false;
            }
          })
          .catch((err) => {
            this.taskConfigLoading = false;
          });
      }
    },
    // 任务配置恢复默认
    TASK_CONFIG_RESET (task) {
      this.taskConfigLoading = true;
      let obj = {
        system: "probe",
        task_id: task,
        batch_id: task === 0 ? 100001 : 100002,
        batch_remark: "",
        fullflow_state: "ON",
        full_flow_should_log_def: 1,
        parse_proto_should_log_def: 1,
        parse_proto_should_log_def_list: [10638, 10637, 10071],
        ddos_state: 1,
      };
      config_edit(obj)
        .then((res) => {
          if (res.err === 0) {
            this.$message.success(res.msg);
            this.taskConfigEdit = false;
            this.taskConfigEditVisible = false;
            this.taskConfigDrawer = false;
            this.taskConfigLoading = false;
          }
        })
        .catch((err) => {
          this.taskConfigLoading = false;
        });
    },
    // 手动关闭任务配置弹窗
    CLOSE_TASK1_POPOVER () {
      if (this.taskConfigEdit) {
        this.taskConfigEditVisible = true;
      } else {
        this.taskConfigDrawer = false;
      }
    },
    // 解决JS强制科学计数
    TOOL_NUMBER (num_str) {
      num_str = num_str.toString();
      // eslint-disable-next-line eqeqeq
      if (num_str.indexOf("+") != -1) {
        num_str = num_str.replace("+", "");
      }
      // eslint-disable-next-line eqeqeq
      if (num_str.indexOf("E") != -1 || num_str.indexOf("e") != -1) {
        var resValue = "",
          power = "",
          result = null,
          dotIndex = 0,
          resArr = [],
          sym = "";
        var numStr = num_str.toString();
        // eslint-disable-next-line eqeqeq
        if (numStr[0] == "-") {
          // 如果为负数，转成正数处理，先去掉‘-’号，并保存‘-’.
          numStr = numStr.substr(1);
          sym = "-";
        }
        // eslint-disable-next-line eqeqeq
        if (numStr.indexOf("E") != -1 || numStr.indexOf("e") != -1) {
          var regExp = new RegExp(
            "^(((\\d+.?\\d+)|(\\d+))[Ee]{1}((-(\\d+))|(\\d+)))$",
            "ig"
          );
          result = regExp.exec(numStr);
          // eslint-disable-next-line eqeqeq
          if (result != null) {
            resValue = result[2];
            power = result[5];
            result = null;
          }
          if (!resValue && !power) {
            return false;
          }
          // eslint-disable-next-line eqeqeq
          dotIndex = resValue.indexOf(".") == -1 ? 0 : resValue.indexOf(".");
          resValue = resValue.replace(".", "");
          resArr = resValue.split("");
          if (Number(power) >= 0) {
            var subres = resValue.substr(dotIndex);
            power = Number(power);
            //幂数大于小数点后面的数字位数时，后面加0
            for (var i = 0; i <= power - subres.length; i++) {
              resArr.push("0");
            }
            if (power - subres.length < 0) {
              resArr.splice(dotIndex + power, 0, ".");
            }
          } else {
            power = power.replace("-", "");
            power = Number(power);
            //幂数大于等于 小数点的index位置, 前面加0
            for (let i = 0; i < power - dotIndex; i++) {
              resArr.unshift("0");
            }
            let n = power - dotIndex >= 0 ? 1 : -(power - dotIndex);
            resArr.splice(n, 0, ".");
          }
        }
        resValue = resArr.join("");

        return sym + resValue;
      } else {
        return num_str;
      }
    },
    // 千分位加逗号
    THOUSAND (num) {
      return num.toLocaleString();
    },

  },
};
</script>

<style lang="scss">
.mbps {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;

  .mbps-box {
    width: 100%;
    height: 120px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;

    .mbps-item {
      width: 24.5%;
      height: 100%;
      background: #ffffff;
      box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.08);
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 12px 16px;
      padding-right: 13px;
      box-sizing: border-box;

      .mbps-item-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 19px;

        >div:nth-of-type(1) {
          color: #767684;
          font-size: 14px;
        }

        .sw-box {
          width: 116px;
          height: 26px;
          background: #f2f3f7;
          border-radius: 4px;
          display: flex;
          justify-content: space-around;
          align-items: center;

          >section {
            font-size: 14px;
            width: 52px;
            height: 22px;
            color: #2c2c35;
            line-height: 22px;
            text-align: center;
            cursor: pointer;
          }

          .inactive {
            background: #ffffff;
            color: #116ef9;
            // color: #07d4ab;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 4px;
          }

          .active {
            background: #ffffff;
            color: #116ef9;
            // color: #ff9534;
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 4px;
          }
        }

        .el-switch {
          .el-switch__core {
            width: 35px !important;
            height: 17.5px !important;
          }

          .el-switch__core:after {
            width: 14px;
            height: 14px;
          }
        }

        .el-switch.is-checked {
          .el-switch__core:after {
            width: 14px;
            height: 14px;
            background-color: #116ef9;
            border: 4px solid #ffffff;
            margin-left: -15px;
          }
        }
      }

      >section {
        width: 100%;
        height: 35px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .mbps-num {
          color: #000;
          font-size: 24px;
          font-weight: 700;
          display: flex;
          flex-direction: column;
          position: relative;
          bottom: 10px;

          >span:nth-of-type(2) {
            font-size: 14px;
            margin-top: 2px;
            font-weight: 500;
          }
        }

        .mbps-echarts {
          width: 40%;
          height: 50px;
          margin-right: 12px;
        }
      }
    }
  }

  .line-box {
    width: 100%;
    height: 84px;
    display: flex;
    position: relative;

    >div {
      background-position: center;
      background-size: 100% 100%;
      position: absolute;
    }

    .a01 {
      width: 262px;
      height: 84px;
      background-image: url('../../../assets/line/a01.svg');
      left: 140px;
    }

    .a02 {
      width: 262px;
      height: 84px;
      background-image: url('../../../assets/line/a02.svg');
      left: 140px;
    }

    .a11 {
      width: 742px;
      height: 84px;
      background-image: url('../../../assets/line/a11.svg');
      left: 140px;
    }

    .a12 {
      width: 742px;
      height: 84px;
      background-image: url('../../../assets/line/a12.svg');
      left: 140px;
    }

    .b01 {
      width: 123px;
      height: 84px;
      background-image: url('../../../assets/line/b01.svg');
      left: 372px;
    }

    .b02 {
      width: 123px;
      height: 84px;
      background-image: url('../../../assets/line/b02.svg');
      left: 372px;
    }

    .b11 {
      width: 418px;
      height: 84px;
      background-image: url('../../../assets/line/b11.svg');
      left: 464px;
    }

    .b12 {
      width: 418px;
      height: 84px;
      background-image: url('../../../assets/line/b12.svg');
      left: 464px;
    }

    .c01 {
      width: 435px;
      height: 84px;
      background-image: url('../../../assets/line/c01.svg');
      left: 372px;
    }

    .c02 {
      width: 435px;
      height: 84px;
      background-image: url('../../../assets/line/c02.svg');
      left: 372px;
    }

    .c11 {
      width: 106px;
      height: 84px;
      background-image: url('../../../assets/line/c11.svg');
      left: 776px;
    }

    .c12 {
      width: 106px;
      height: 84px;
      background-image: url('../../../assets/line/c12.svg');
      left: 776px;
    }

    .d01 {
      width: 739px;
      height: 84px;
      background-image: url('../../../assets/line/d01.svg');
      left: 372px;
    }

    .d02 {
      width: 739px;
      height: 84px;
      background-image: url('../../../assets/line/d02.svg');
      left: 372px;
    }

    .d11 {
      width: 260px;
      height: 84px;
      background-image: url('../../../assets/line/d11.svg');
      left: 852px;
    }

    .d12 {
      width: 260px;
      height: 84px;
      background-image: url('../../../assets/line/d12.svg');
      left: 852px;
    }
  }

  #task1 {
    box-sizing: border-box;
  }

  #task2 {
    box-sizing: border-box;
  }

  // #task1:hover {
  //   border: 2px solid rgb(17, 110, 249);
  //   box-sizing: border-box;
  //   box-shadow: 0 0.041667rem 0.083333rem rgb(0 0 0 / 8%);
  //   cursor: pointer;
  // }
  .task-box {
    width: calc(100% - 56px);
    height: 122px;
    display: flex;
    justify-content: space-between;
    position: absolute;
    left: 28px;
    top: 200px;

    .sw-box {
      width: 116px;
      height: 26px;
      background: #f2f3f7;
      border-radius: 4px;
      display: flex;
      justify-content: space-around;
      align-items: center;

      >section {
        font-size: 14px;
        width: 52px;
        height: 22px;
        color: #2c2c35;
        line-height: 22px;
        text-align: center;
        cursor: pointer;
      }

      .inactive {
        background: #ffffff;
        color: #116ef9;
        // color: #07d4ab;
        box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
      }

      .active {
        background: #ffffff;
        color: #116ef9;
        // color: #ff9534;
        box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
      }
    }

    .task-item1 {
      width: 70%;
      height: 100%;
      background: #ffffff;
      box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.07);
      border-radius: 4px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      // box-sizing: border-box;
      padding-top: 17px;
      padding-left: 16px;
      padding-right: 16px;
      padding-bottom: 17px;
      transition: width 1.5s cubic-bezier(0.18, 0.89, 0.32, 1.28);
      border: 1px solid #f2f3f7;
      cursor: pointer;

      .task1-num {
        width: 100%;
        height: 70px;
        display: flex;
        justify-content: space-between;

        >div {
          width: 100%;
          height: 100%;
          display: flex;
          overflow-x: auto;
          flex-wrap: nowrap;
          align-items: end;
          justify-content: space-between;

          .num-item {
            width: 180px;
            height: 48px;
            display: flex;
            flex-direction: column;
            font-size: 12px;
            justify-content: space-around;
            box-sizing: border-box;

            // border-right: 1px solid #dee0e7;

            // padding-left: 16px;
            >div {

              // width: 60px;
              >span:nth-of-type(1) {
                font-size: 16px;
                font-weight: 700;
                margin-right: 2px;
              }

              >span:nth-of-type(2) {
                font-size: 12px;
              }
            }

            >div:nth-of-type(2) {
              color: #767684;
              font-size: 14px;
            }
          }

          >div:last-of-type {
            padding-left: 0;
            border: 0;
          }
        }

        >aside {
          width: 1px;
          height: 100%;
          background-color: #f2f3f7;
          margin-right: 30px;
        }
      }

      .state-0 {
        width: 4px;
        height: 100%;
        border-radius: 8px 0 0 8px;
        position: absolute;
        left: 0;
        top: 0;
        background: #f2f3f7;
      }

      .state-1 {
        width: 4px;
        height: 100%;
        border-radius: 8px 0 0 8px;
        position: absolute;
        left: 0;
        top: 0;
        background: #07d4ab;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
      }

      .state-2 {
        width: 4px;
        height: 100%;
        border-radius: 8px 0 0 8px;
        position: absolute;
        left: 0;
        top: 0px;
        background: #ff9534;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
      }

      .task1-title {
        font-size: 16px;
        font-weight: 500;
        cursor: default;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .task-point {
          cursor: pointer;
        }

        >div:nth-of-type(1) {
          display: flex;
          align-items: center;

          // width: 100%;
          // width: 50px;
          >img {
            width: 16px;
            height: 16px;
            object-fit: cover;
            margin-right: 8px;
          }
        }

        >div:nth-of-type(2) {
          height: 20px;
          background: #f2f3f7;
          border-radius: 8px;
          font-size: 12px;
          color: #767684;
          margin-right: auto;
          margin-left: 16px;
          line-height: 20px;
          padding: 0 8px;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .title-config {
          height: 100%;
          display: flex;
          align-items: center;

          .coolicon {
            margin-right: 8px;
            cursor: pointer;
          }

          .el-switch {
            margin-right: 10px;

            .el-switch__core {
              width: 35px !important;
              height: 17.5px !important;
            }

            .el-switch__core:after {
              width: 14px;
              height: 14px;
            }
          }

          .el-switch.is-checked {
            .el-switch__core:after {
              width: 14px;
              height: 14px;
              background-color: #116ef9;
              border: 4px solid #ffffff;
              margin-left: -15px;
            }
          }
        }
      }
    }

    .task-item2 {
      cursor: pointer;
      width: 29%;
      height: 100%;
      background: #ffffff;
      box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.07);
      border-radius: 4px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      // box-sizing: border-box;
      padding-top: 17px;
      padding-left: 16px;
      padding-right: 16px;
      padding-bottom: 17px;
      transition: width 1.5s cubic-bezier(0.18, 0.89, 0.32, 1.28);
      border: 1px solid #f2f3f7;

      .state-0 {
        width: 4px;
        height: 100%;
        border-radius: 8px 0 0 8px;
        position: absolute;
        left: 0;
        top: 0;
        background: #f2f3f7;
      }

      .state-1 {
        width: 4px;
        height: 100%;
        border-radius: 8px 0 0 8px;
        position: absolute;
        left: 0;
        top: 0;
        background: #07d4ab;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
      }

      .state-2 {
        width: 4px;
        height: 100%;
        border-radius: 8px 0 0 8px;
        position: absolute;
        left: 0;
        top: 0px;
        background: #ff9534;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
      }

      .task1-title {
        font-size: 16px;
        font-weight: 500;
        cursor: default;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .task-point {
          cursor: pointer;
        }

        >div:nth-of-type(1) {
          display: flex;
          align-items: center;

          // width: 100%;
          // width: 50px;
          >img {
            width: 16px;
            height: 16px;
            object-fit: cover;
            margin-right: 8px;
          }
        }

        >div:nth-of-type(2) {
          height: 20px;
          background: #f2f3f7;
          border-radius: 8px;
          font-size: 12px;
          color: #767684;
          margin-right: auto;
          margin-left: 16px;
          line-height: 20px;
          padding: 0 8px;
          font-weight: 500;
        }

        .title-config {
          height: 100%;
          display: flex;
          align-items: center;

          .coolicon {
            margin-right: 8px;
            cursor: pointer;
          }

          .el-switch {
            margin-right: 10px;

            .el-switch__core {
              width: 35px !important;
              height: 17.5px !important;
            }

            .el-switch__core:after {
              width: 14px;
              height: 14px;
            }
          }

          .el-switch.is-checked {
            .el-switch__core:after {
              width: 14px;
              height: 14px;
              background-color: #116ef9;
              border: 4px solid #ffffff;
              margin-left: -15px;
            }
          }
        }
      }

      .task1-num {
        width: 100%;
        height: 70px;
        display: flex;
        justify-content: space-between;
        overflow-x: auto;

        >div {
          height: 100%;
          display: flex;

          flex-wrap: nowrap;
          align-items: end;
          justify-content: space-between;

          .num-item {
            width: 180px;
            height: 48px;
            display: flex;
            flex-direction: column;
            font-size: 12px;
            justify-content: space-around;
            box-sizing: border-box;

            // border-right: 1px solid #dee0e7;

            // padding-left: 16px;
            >div {

              // width: 60px;
              >span:nth-of-type(1) {
                font-size: 16px;
                font-weight: 700;
                margin-right: 2px;
              }

              >span:nth-of-type(2) {
                font-size: 12px;
              }
            }

            >div:nth-of-type(2) {
              color: #767684;
              font-size: 14px;
            }
          }

          >div:last-of-type {
            padding-left: 0;
            border: 0;
          }
        }

        >aside {
          width: 1px;
          height: 100%;
          background-color: #f2f3f7;
          margin-right: 30px;
        }
      }

      .task2-num {
        width: 100%;
        height: 70px;
        display: flex;
        justify-content: space-between;

        >div {
          margin-top: 25px;
          width: 100%;
          height: 100%;
          display: flex;
          overflow-x: auto;
          flex-wrap: wrap;
          align-items: flex-end;
          align-content: flex-start;

          #num-item2 {
            cursor: pointer;
          }

          .num-item {
            width: 50%;
            height: 20px;
            display: flex;
            font-size: 12px;
            justify-content: left;
            box-sizing: border-box;
            align-items: center;
            margin-bottom: 4px;

            >div:nth-of-type(1) {
              color: #767684;
              margin-right: 8px;
            }

            >div {
              text-align: center;
            }
          }
        }

        >aside {
          width: 1px;
          height: 100%;
          background-color: #f2f3f7;
          margin-right: 30px;
        }

        .task1-button {
          width: 75px;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .el-button {
            width: 75px;
            height: 26px;
            padding: 0;
            margin: 0;
            color: #000;
            font-size: 12px;
            font-weight: 600;
          }
        }
      }
    }
  }
}

.el-drawer__wrapper {
  .el-drawer__body {
    font-size: 14px;

    box-sizing: border-box;

    .form-title {
      width: 100%;
      height: 54px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #000;
      font-size: 14px;
      border-bottom: 1px solid #f2f3f7;
      padding: 0px 24px;

      // position: absolute;
      >span:nth-of-type(1) {
        font-weight: 600;
      }

      >span:nth-of-type(2) {
        font-size: 12px;
        color: #116ef9;
        cursor: pointer;
      }
    }

    .task1-form {
      padding: 0px 24px;

      >div {
        display: flex;
        align-items: center;
        margin: 10px 0;
      }

      .taskSettings-name {
        font-size: 14px;
        color: #767684;
        margin-top: 24px;
        margin-bottom: 8px !important;
      }

      .el-input {
        font-size: 12px;
        margin-top: 0;

        .el-input__inner {
          width: 100%;
          height: 27px;
          padding-left: 10px;
        }
      }

      .el-switch {
        margin-right: 10px;

        .el-switch__core {
          width: 35px !important;
          height: 17.5px !important;
        }

        .el-switch__core:after {
          width: 14px;
          height: 14px;
        }
      }

      .el-switch.is-checked {
        .el-switch__core:after {
          width: 14px;
          height: 14px;
          background-color: #409eff;
          border: 4px solid #ffffff;
          margin-left: -15px;
        }
      }

      .task1-submit {
        width: 60px;
        height: 32px;
        font-size: 14px;
        background-color: #116ef9;
        border: 0;
        border-radius: 4px;
        padding: 0;
        margin: 0;
        // margin-right: 8px;
      }

      // .task1-submit:hover {
      //   color: #409eff;
      //   border-color: #c6e2ff;
      //   background-color: #ecf5ff;
      // }
      .task1-reset {
        width: 115px;
        height: 32px;
        font-size: 14px;
        background-color: #ffffff;
        border-radius: 4px;
        padding: 0;
        margin-right: 20px;
      }

      // .taks1-reset:hover {
      //   border-color: #409eff;
      //   color: #409eff;
      // }
      .task-describe {
        &-title {
          font-size: 14px;
          color: #767684;
          margin-top: 24px;
          margin-bottom: 8px !important;
        }

        .el-textarea__inner {
          padding: 6px 10px;
          font-size: 12px;
        }
      }
    }
  }
}

.tasksettings-tree {
  .el-tree>.el-tree-node:after {
    border-top: none;
  }

  .el-tree-node {
    position: relative;
    padding-left: 10px;
  }

  //节点有间隙，隐藏掉展开按钮就好了,如果觉得空隙没事可以删掉
  .el-tree-node__expand-icon.is-leaf {
    display: none;
  }

  .el-tree-node__children {
    padding-left: 16px;
  }

  .el-tree-node :last-child:before {
    height: 38px;
  }

  .el-tree>.el-tree-node:before {
    border-left: none;
  }

  .el-tree>.el-tree-node:after {
    border-top: none;
  }

  .el-tree-node:before {
    content: '';
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:after {
    content: '';
    left: -4px;
    position: absolute;
    right: auto;
    border-width: 1px;
  }

  .el-tree-node:before {
    border-left: 1px solid #e6e6e6;
    bottom: 0px;
    height: 100%;
    top: -22px;
    width: 1px;
  }

  .el-tree-node:after {
    border-top: 1px solid #e6e6e6;
    height: 20px;
    top: 16px;
    width: 10px;
  }

  .el-tree-node__content {
    padding-left: 5px !important;
    height: 34px;
  }

  .el-tree-node__expand-icon {
    display: none;
  }

  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
  }
}

.tasksettings-foot {
  display: flex;
  justify-content: space-between;
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 0px 24px;
}

.taskConfigEditVisible {
  .el-dialog {
    width: 460px;
    height: 183px;

    .el-dialog__footer {
      padding: 0;
    }

    .dialog-footer {
      height: 56px;
      box-sizing: border-box;
      display: flex;
      justify-content: flex-end;
      border-top: 1px solid #f2f3f7;
      align-items: center;

      .cancel {
        margin-left: 22px;
        margin-right: auto;
      }

      .el-button {
        width: 60px;
        height: 32px;
        border-radius: 4px;
        padding: 0;
        color: #2c2c35;
      }

      .el-button--primary {
        background-color: #116ef9;
        color: #ffffff;
        margin-right: 22px;
      }
    }
  }
}
</style>

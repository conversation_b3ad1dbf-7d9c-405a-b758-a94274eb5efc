/* 特征规则 */
<template>
  <div class="aspectrule">
    <div class="aspectrule-top">
      <div class="aspectrule-top-l">
        <div class="title">协议名称</div>
        <el-select v-model="value" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div class="aspectrule-top-r">
        <div class="title">特征字</div>
        <el-input v-model="input" placeholder="请输入内容"></el-input>
      </div>
      <el-checkbox v-model="checked">不区分大小写</el-checkbox>
    </div>
    <div class="aspectrule-down">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column type="selection" width="100"> </el-table-column>
        <el-table-column type="index" label="序号" width="100">
        </el-table-column>
        <el-table-column prop="date" label="协议ID">
        </el-table-column>
        <el-table-column prop="name" label="协议名称">
          >
        </el-table-column>
        <el-table-column prop="name" label="特征字">
        </el-table-column>
        <el-table-column prop="name" label="区分大小写">
        </el-table-column>
        <el-table-column label="操作" width="130">
          <template>
            <el-button type="text" size="mini">删除</el-button>
            <el-button size="mini" type="text">修改</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: "Aspectrule",
  data() {
    return {
      checked: "true",
      tableData:[],
      value:'',
      options:[],
      input:''
    };
  },
};
</script>

<style lang="scss" scoped>
.aspectrule {
  &-top {
    display: flex;
    align-items: flex-end;
    ::v-deep .el-input .el-input__inner {
      height: 27px;
      width: 260px;
      font-weight: 400;
      font-size: 12px;
      padding-left: 5px;
    }
    ::v-deep .el-checkbox__label {
      font-weight: 400;
      font-size: 12px;
    }
    ::v-deep .el-input__suffix {
      height: 54px;
      top: -14px;
    }
    .title {
      display: inline-block;
    }
    &-l {
      display: flex;
      flex-direction: column;
      margin-right: 5px;
    }
    &-r {
      display: flex;
      flex-direction: column;
      margin-right: 5px;
    }
  }
  &-down{
    margin-top: 24px;
  }
}
</style>
/* 正则规则 */
<template>
  <div class="aspectrule p-relative">
    <el-form ref="regexRule" :rules="rules" :model="formData">
      <div class="aspectrule-top">
        <div class="aspectrule-top-l">
          <div class="title">协议名称</div>
          <el-form-item prop="pro_id" :rules="rules.ruleEmpty">
            <el-select
              v-model="formData.pro_id"
              placeholder="请选择"
              value-key="value"
              filterable
              :filter-method="handleIDProFilter"
              @visible-change="handleIDPrpVisibleChange"
            >
              <el-option
                v-for="item in id_pro_options"
                :key="item.id"
                :label="item.protocol_value"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="aspectrule-top-r">
          <div class="title">正则规则</div>
          <el-form-item prop="regex" :rules="rules.ruleRegexp">
            <el-input
              v-model="formData.regex"
              placeholder="输入正则规则,例如6380.*07ff9c"
            ></el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <div class="aspectrule-down">
      <el-table ref="regexRuleList" :data="regexRuleList" style="width: 100%">
        <el-table-column type="index" label="序号" fixed> </el-table-column>
        <el-table-column prop="pro_id" label="协议ID"> </el-table-column>
        <el-table-column prop="ProName" label="协议名称"> </el-table-column>
        <el-table-column prop="regex" label="正则表达式" width="200">
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <div class="btn">
              <el-button
                type="text"
                size="mini"
                @click="deleteRuleInfo(scope.row)"
              >
                删除
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="editRuleInfo(scope.row)"
              >
                修改
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <tablescroll :table-ref="$refs.ipRuleList"></tablescroll>
    </div>
    <!-- 添加\保存 按钮 -->
    <div class="tab-box-btn">
      <div v-if="showsave" class="tab-box-btn-l" @click="addToList('ipRule')">
        <el-button>
          <svg-icon icon-class="frule-add" />
        </el-button>
      </div>
      <div v-if="!showsave" class="tab-box-btn-r" @click="editRegexRule">
        <el-button>
          <svg-icon icon-class="saveRule" />
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import tablescroll from "../../../../components/TableScroll/idnex.vue";
export default {
  name: "Aspectrule",
  components: {
    tablescroll,
  },
  props: ["regexRuleData"],
  data() {
    return {
      showsave: true,
      formData: {},
      regexRuleList: [],
      regexRuleParam: [],
      id_pro_options: [],
      id_pro_full_options: [],
      showAddForm: this.showForm,
      isShow: false,
      startPort: 0,
      endPort: 65535,
      proType: { 1: "连接", 2: "单包", 3: "负载" },
      rules: {
        ruleEmpty: [{ required: true, message: "不能为空", trigger: [] }],
        ruleRegexp: [
          { required: true, message: "不能为空", trigger: ["blur"] },
          {
            required: true,
            message: "请输入正确的正则",
            trigger: ["blur"],
          },
        ],
      },
    };
  },
  computed: {
    dict() {
      return this.$store.state.long.Dict;
    },
  },
  watch: {
    regexRuleData: {
      handler() {
        this.$nextTick(() => {
          this.$refs.regexRule.clearValidate();
        });
        this.changeShowFormat();
      },
      deep: true,
    },
  },
  mounted() {
    this.initIDProOptions();
    this.initFormData();
    this.regexRuleList = [];
    this.regexRuleParam = [];
    // this.showAddForm = true
  },
  methods: {
    initFormData() {
      this.formData = {
        pro_id: "",
        regex: "",
      };
    },
    // 展示数据转换为规则传参数据
    showDataToRuleParams() {
      this.regexRuleParam = [];
      this.regexRuleList.forEach((item) => {
        let tempregexRule = {
          pro_id: item.pro_id,
          regex: item.regex,
        };

        this.regexRuleParam.push(tempregexRule);
      });
      this.$emit("getRegexRuleParam", this.regexRuleParam);
    },
    // 将后端返回数据规范为前端可展示绑定数据
    changeShowFormat() {
      this.initFormData();
      this.regexRuleList = [];
      this.regexRuleParam = [];
      // this.showAddForm = true
      if (this.regexRuleData.length !== 0) {
        for (let regexRuleItem of this.regexRuleData) {
          let tempItem = {
            index: Math.floor(Math.random() * 10000),
            pro_id: regexRuleItem.pro_id,
            regex: regexRuleItem.regex,
          };
          this.id_pro_options.forEach((item) => {
            if (item.id === regexRuleItem.pro_id) {
              tempItem.ProName = item.protocol_value;
            }
          });
          this.regexRuleList.push(tempItem);
        }
      }
    },
    // 将表单数据转换为表格可用数据
    formDataToTableData() {
      let regexRuleItem = {
        index: Math.floor(Math.random() * 10000),
        pro_id: this.formData.pro_id,
        regex: this.formData.regex,
      };
      this.id_pro_options.forEach((item) => {
        if (item.id === this.formData.pro_id) {
          regexRuleItem.ProName = item.protocol_value;
        }
      });
      return regexRuleItem;
    },
    addToList() {
      this.$refs["regexRule"].validate((valid) => {
        if (valid) {
          this.regexRuleList.unshift(this.formDataToTableData());
          this.showDataToRuleParams();
        } else {
          this.$message.error("检验错误，请检查必填项是否填写。");
        }
      });
    },
    // 修改正则规则
    editRegexRule() {
      this.$refs["regexRule"].validate((valid) => {
        if (valid) {
          this.regexRuleList = this.regexRuleList.map((item, index) => {
            if (item.index === this.formData.index) {
              item = this.formDataToTableData();
            }
            return item;
          });
          this.showDataToRuleParams();
          this.initFormData();
          this.$message.success("保存成功");
        } else {
          this.$message.error("检验错误，请检查必填项是否填写。");
        }
      });
    },
    // 将要修改的正则规则反写到表单里
    editRuleInfo(row) {
      this.formData = JSON.parse(JSON.stringify(row));
      this.showAddForm = true;
      var scrollTop = document.querySelector("#addRules");
      if (scrollTop.scrollTop > 390) {
        scrollTop.scrollTop = 390;
      }
      this.showsave = false;
    },
    deleteRuleInfo(row) {
      this.regexRuleList.forEach((item, index) => {
        if (item.index === row.index) {
          this.regexRuleList.splice(index, 1);
        }
      });
      this.showDataToRuleParams();
    },
    // 获取协议ID
    initIDProOptions() {
      for (let key in this.dict.app_id) {
        this.id_pro_full_options.push({
          id: parseInt(key),
          protocol_value:
            this.proType[this.dict.app_type_map[parseInt(key)]] +
            " - " +
            this.dict.app_id[key] +
            "(" +
            this.dict.app_value[parseInt(key)] +
            ")",
        });
      }
      this.id_pro_options = this.id_pro_full_options;
    },
    handleIDProFilter(query) {
      if (query === "") {
        this.id_pro_options = this.id_pro_full_options;
      } else {
        this.id_pro_options = this.id_pro_full_options.filter((item) => {
          return (
            item.id.toString().indexOf(query.toLowerCase()) > -1 ||
            item.protocol_value.toLowerCase().indexOf(query.toLowerCase()) > -1
          );
        });
      }
    },
    handleIDPrpVisibleChange(visible) {
      if (!visible) {
        this.id_pro_options = this.id_pro_full_options;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.tab-box-btn {
  position: absolute;
  top: 0px;
  right: 20px;

  .el-button {
    border: 1px solid #cecece;
    width: 35px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.aspectrule {
  &-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    ::v-deep .el-input .el-input__inner {
      height: 27px;
      width: 310px;
      font-weight: 400;
      font-size: 12px;
      padding-left: 5px;
    }
    ::v-deep .el-input__suffix {
      height: 54px;
      top: -8px;
    }
    .title {
      display: inline-block;
    }
    &-l {
      display: flex;
      flex-direction: column;
      margin-right: 5px;
    }
    &-r {
      display: flex;
      flex-direction: column;
      margin-right: 5px;
    }
  }
  &-down {
    margin-top: 24px;
    .btn {
      ::v-deep .el-button {
        border: none;
      }
    }
  }
}
</style>

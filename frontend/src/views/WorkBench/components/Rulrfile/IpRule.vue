<template>
  <div class="p-relative">
    <el-form
      ref="ipRule"
      :model="formData"
      :rules="rules"
      class="demo-ruleForm"
      :label-position="labelPosition"
    >
      <div class="iprulr-top">
        <div class="iprulr-top-type">
          <div>
            <el-form-item prop="type" label="类型">
              <el-radio-group v-model="formData.type" @change="chengradio">
                <el-radio label="IPV4">IPV4</el-radio>
                <el-radio label="IPV6">IPV6</el-radio>
                <el-radio label="">任意IP</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </div>
        <div v-if="formData.type != ''" style="margin-right: 24px">
          <el-form-item
            prop="ruleIp"
            label="IP"
            :rules="[
              { required: true, message: '不能为空', trigger: ['blur'] },
              {
                required: true,
                message: '请输入合法IP地址',
                trigger: ['change'],
                validator: formData.type === 'IPV4' ? validateIP : validateIPV6,
              },
            ]"
          >
            <el-input
              v-model="formData.ruleIp"
              placeholder="请输入ip"
            ></el-input>
          </el-form-item>
        </div>
        <div v-if="formData.type === 'IPV4'">
          <el-form-item prop="ruleMask" label="掩码">
            <el-input v-model="formData.ruleMask"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="iprulr-mid">
        <!-- 端口范围 -->
        <el-form-item prop="port_rule" :rules="rules.rulePort">
          <div class="iprulr-mid-range">
            <div style="display: flex; flex-direction: column">
              <div class="iprulr-mid-range-title">
                端口范围<span>注：点击
                  <svg-icon icon-class="linkicon" />
                  激活对应端口范围
                </span>
              </div>
              <div class="iprulr-mid-range-down">
                <div class="iprulr-mid-range-down-input_1">
                  <el-input
                    v-model="startPort"
                    maxlength="1"
                    disabled
                  ></el-input>
                </div>
                <div @click="lockPort(1)">
                  <svg-icon
                    icon-class="linkicon"
                    :class="formData.port_rule.property1 ? 'active' : ''"
                  />
                </div>
                <div class="iprulr-mid-range-down-input_2">
                  <el-input
                    v-model.number="formData.port_rule.low_port"
                    maxlength="5"
                    :min="0"
                    :max="65535"
                  ></el-input>
                </div>
                <div @click="lockPort(2)">
                  <svg-icon
                    icon-class="linkicon"
                    :class="formData.port_rule.property2 ? 'active' : ''"
                  />
                </div>
                <div class="iprulr-mid-range-down-input_3">
                  <el-input
                    v-model.number="formData.port_rule.high_port"
                    maxlength="5"
                    :min="+formData.port_rule.low_port || 0"
                    :max="65535"
                  ></el-input>
                </div>
                <div @click="lockPort(3)">
                  <svg-icon
                    icon-class="linkicon"
                    :class="formData.port_rule.property3 ? 'active' : ''"
                  />
                </div>
                <div class="iprulr-mid-range-down-input_4">
                  <el-input v-model="endPort" maxlength="7" disabled></el-input>
                </div>
              </div>
            </div>

            <div class="iprulr-mid-range-radio">
              <el-checkbox-group v-model="formData.port_rule.portType">
                <el-checkbox label="服务器端口">服务器端口</el-checkbox>
                <el-checkbox label="客户端端口">客户端端口</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </el-form-item>
        <!-- IP-Pro -->
        <div class="iprulr-mid-protocol">
          <div class="iprulr-mid-protocol-down">
            <el-form-item
              prop="ip_pro.proValue"
              :rules="rules.ruleIPPro"
              label="IP协议"
            >
              <el-input
                v-model="formData.ip_pro.proValue"
                placeholder="0~255,可填入多个,逗号分隔"
              ></el-input>
              <el-checkbox v-model="formData.ip_pro.proType">反选 </el-checkbox>
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>
    <!-- 添加\保存 按钮 -->
    <div class="tab-box-btn">
      <div v-if="showsave" class="tab-box-btn-l" @click="addToList">
        <el-button>
          <svg-icon icon-class="frule-add" />
        </el-button>
      </div>
      <div v-if="!showsave" class="tab-box-btn-r" @click="editIpRule">
        <el-button>
          <svg-icon icon-class="saveRule" />
        </el-button>
      </div>
    </div>

    <!-- 下 -->
    <div class="iprulr-down">
      <el-table ref="ipRuleList" :data="ipRuleList">
        <el-table-column type="index" label="序号" fixed> </el-table-column>
        <el-table-column prop="type" label="IP类型"> </el-table-column>
        <el-table-column prop="ruleIp" label="IP地址"> </el-table-column>
        <el-table-column prop="ruleMask" label="掩码"> </el-table-column>
        <el-table-column prop="portRange" label="端口范围"> </el-table-column>
        <el-table-column
          prop="port_rule.portType"
          label="端口类型"
          width="100px"
        >
        </el-table-column>
        <el-table-column prop="ip_pro.proValue" label="IP协议">
        </el-table-column>
        <el-table-column
          prop="ip_pro.proType"
          label="IP协议匹配模式"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.ip_pro.proType ? "反选" : "正选" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" fixed="right">
          <template slot-scope="scope">
            <div class="btn">
              <el-button
                type="text"
                size="mini"
                @click="deleteRuleInfo(scope.row)"
              >
                删除
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="editRuleInfo(scope.row, scope.$index)"
              >
                修改
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import {
  validatePort,
  validateMask,
  validateIP,
  validateIPV6,
  validateIPPro,
} from "@/utils";
export default {
  name: "IpRule",
  props: ["ipRuleData"],
  data() {
    return {
      labelPosition: "top",
      list: [],
      validateIP,
      validateIPV6,
      formData: {
        type: "IPV4",
        ruleIp: "",
        ruleMask: "***************",
        port_rule: {
          property1: true,
          property2: false,
          property3: true,
          low_port: 10000,
          high_port: 49151,
          portType: ["服务器端口", "客户端端口"],
        },
        ip_pro: {
          proValue: "0",
          proType: true,
        },
        index: 0,
      },
      ipRuleList: [],
      ipRuleParam: [],
      startPort: 0,
      endPort: 65535,
      rules: {
        rulePort: [
          {
            required: false,
            message: "请输入正确的端口范围",
            trigger: [],
            validator: validatePort,
          },
        ],
        // ruleIp: [
        //   { required: true, message: "不能为空", trigger: ["blur"] },
        //   {
        //     required: true,
        //     message: "请输入合法IP地址",
        //     trigger: ["change"],
        //     validator:validateIP
        //   },
        // ],
        ruleMask: [
          {
            required: false,
            message: "请输入正确的子网掩码",
            trigger: [],
            validator: validateMask,
          },
        ],
        ruleIPPro: [
          { required: true, message: "不能为空", trigger: ["blur"] },
          {
            required: true,
            message: "输入格式不正确",
            trigger: ["change"],
            validator: validateIPPro,
          },
        ],
      },
      showsave: true,
    };
  },
  computed: {
    type() {
      return this.formData.type;
    },
  },
  watch: {
    ipRuleData: {
      handler() {
        this.$nextTick(() => {
          this.$refs.ipRule.clearValidate();
        });
        this.changeShowFormat();
      },
      deep: true,
    },
  },
  mounted() {},
  methods: {
    //  初始化数据
    initFormData() {
      this.formData = {
        type: "IPV4",
        ruleIp: "",
        ruleMask: "***************",
        port_rule: {
          property1: true,
          property2: false,
          property3: false,
          low_port: 10000,
          high_port: 49151,
          portType: ["服务器端口", "客户端端口"],
        },
        ip_pro: {
          proValue: "0",
          proType: true,
        },
      };
      this.showsave = true;
    },
    // 展示数据转换为规则传参数据
    showDataToRuleParams() {
      this.ipRuleParam = [];
      this.ipRuleList.forEach((item) => {
        let tempIPRule = {};
        switch (item.type) {
        case "IPV4":
          let tempIPMask = {};
          tempIPMask.ip = item.ruleIp;
          tempIPMask.mask = item.ruleMask;
          tempIPRule.ip_mask_v4 = tempIPMask;
          tempIPRule.ip_type = 1;
          break;
        case "IPV6":
          tempIPRule.ip_v6 = item.ruleIp;
          tempIPRule.ip_type = 2;
          break;
        default:
          tempIPRule.ip_v4 = "0";
          tempIPRule.ip_type = 3;
          break;
        }
        let tempPortRule = {};
        tempPortRule.property = this.propertyToValue(
          item.port_rule.property1,
          item.port_rule.property2,
          item.port_rule.property3
        );
        tempPortRule.low_port = item.port_rule.low_port;
        tempPortRule.high_port = item.port_rule.high_port;
        if (item.port_rule.portType.length > 1) {
          tempPortRule.sign = 3;
        } else {
          if (item.port_rule.portType.length == 0) tempPortRule.sign = 0;
          else {
            if (item.port_rule.portType.includes("客户端端口"))
              tempPortRule.sign = 1;
            else tempPortRule.sign = 2;
          }
        }
        tempIPRule.port_rule = tempPortRule;
        let tempIPPro = {};
        if (item.ip_pro.proType == false) {
          let numArr = [];
          item.ip_pro.proValue = item.ip_pro.proValue.replace(/，/gi, ",");
          item.ip_pro.proValue = item.ip_pro.proValue.replace(/[^\d,]/g, "");
          for (let proValueitem of item.ip_pro.proValue.split(","))
            numArr.push(parseInt(proValueitem));
          tempIPPro.Positive = numArr;
        } else {
          let numArr = [];
          item.ip_pro.proValue = item.ip_pro.proValue.replace(/，/gi, ",");
          item.ip_pro.proValue = item.ip_pro.proValue.replace(/[^\d,]/g, "");
          for (let proValueitem of item.ip_pro.proValue.split(","))
            numArr.push(parseInt(proValueitem));
          tempIPPro.Negative = numArr;
        }
        tempIPRule.ip_pro = tempIPPro;
        this.ipRuleParam.push(tempIPRule);
      });
      this.$emit("getIpRuleParam", this.ipRuleParam);
    },
    // 将后端传回来的值展现
    changeShowFormat() {
      this.initFormData();
      this.chengradio();
      this.ipRuleList = [];
      this.ipRuleParam = [];
      if (this.ipRuleData.length !== 0) {
        for (let ipRuleItem of this.ipRuleData) {
          let tempItem = {
            port_rule: {},
            ip_pro: {},
          };
          if (ipRuleItem.hasOwnProperty("ip_v6") && ipRuleItem.ip_v6 !== null) {
            tempItem.type = "IPV6";
            tempItem.ruleIp = ipRuleItem.ip_v6;
          } else if (
            ipRuleItem.hasOwnProperty("ip_mask_v4") &&
            ipRuleItem.ip_mask_v4 !== null
          ) {
            tempItem.type = "IPV4";
            tempItem.ruleIp = ipRuleItem.ip_mask_v4.ip;
            tempItem.ruleMask = ipRuleItem.ip_mask_v4.mask;
          } else if (
            ipRuleItem.hasOwnProperty("ip_v4") &&
            ipRuleItem.ip_v4 !== "0"
          ) {
            tempItem.type = "IPV4";
            tempItem.ruleIp = ipRuleItem.ip_v4;
            tempItem.ruleMask = "***************";
          } else {
            tempItem.type = "任意Ip";
          }
          tempItem.port_rule.low_port = ipRuleItem.port_rule.low_port;
          tempItem.port_rule.high_port = ipRuleItem.port_rule.high_port;
          tempItem.port_rule.property = ipRuleItem.port_rule.property;
          tempItem.port_rule.property3 = !!parseInt(
            tempItem.port_rule.property / 4
          );
          tempItem.port_rule.property2 = !!parseInt(
            (tempItem.port_rule.property % 4) / 2
          );
          tempItem.port_rule.property1 = !!(
            (tempItem.port_rule.property % 4) %
            2
          );
          let portRange = [];
          if (
            tempItem.port_rule.property1 &&
            tempItem.port_rule.low_port != null
          ) {
            if (tempItem.port_rule.low_port) {
              portRange.push(`0~${tempItem.port_rule.low_port}`);
            } else {
              portRange.push(0);
            }
          }
          if (
            tempItem.port_rule.property2 &&
            tempItem.port_rule.high_port != null
          ) {
            if (tempItem.port_rule.low_port !== tempItem.port_rule.high_port) {
              portRange.push(
                `${tempItem.port_rule.low_port}~${tempItem.port_rule.high_port}`
              );
            } else {
              if (!portRange.length) {
                portRange.push(tempItem.port_rule.low_port);
              }
            }
          }
          if (tempItem.port_rule.property3) {
            portRange.push(`${tempItem.port_rule.high_port}~65535`);
          }
          tempItem.portRange = portRange.join(",");
          switch (ipRuleItem.port_rule.sign) {
          case 1:
            tempItem.port_rule.portType = ["客户端端口"];
            break;
          case 2:
            tempItem.port_rule.portType = ["服务器端口"];
            break;
          case 3:
            tempItem.port_rule.portType = ["服务器端口", "客户端端口"];
            break;
          default:
            tempItem.port_rule.portType = [];
            break;
          }
          if (ipRuleItem.ip_pro) {
            if (ipRuleItem.ip_pro.hasOwnProperty("Positive")) {
              tempItem.ip_pro.proType = false;
              tempItem.ip_pro.proValue = ipRuleItem.ip_pro.Positive.toString();
            } else {
              tempItem.ip_pro.proType = true;
              tempItem.ip_pro.proValue = ipRuleItem.ip_pro.Negative.toString();
            }
          }
          this.ipRuleList.push(tempItem);
        }
      }
    },
    // 激活对应端口范围
    lockPort(num) {
      if (num == 1) {
        this.formData.port_rule.property1 = !this.formData.port_rule.property1;
      }
      if (num == 2) {
        this.formData.port_rule.property2 = !this.formData.port_rule.property2;
      }
      if (num == 3) {
        this.formData.port_rule.property3 = !this.formData.port_rule.property3;
      }
    },
    // 将表单数据转换为表格可用数据
    formDataToTableData() {
      let ipRuleItem = {
        type: this.formData.type,
        port_rule: {
          property1: this.formData.port_rule.property1,
          property2: this.formData.port_rule.property2,
          property3: this.formData.port_rule.property3,
          low_port: this.formData.port_rule.low_port,
          high_port: this.formData.port_rule.high_port,
          portType: this.formData.port_rule.portType,
        },
        ip_pro: {
          proValue: this.formData.ip_pro.proValue,
          proType: this.formData.ip_pro.proType,
        },
        portRange: "",
      };
      if (ipRuleItem.type === "IPV6") {
        ipRuleItem.ruleIp = this.formData.ruleIp;
      }
      if (ipRuleItem.type === "IPV4") {
        ipRuleItem.ruleIp = this.formData.ruleIp;
        ipRuleItem.ruleMask = this.formData.ruleMask;
      }
      if (ipRuleItem.type === "") {
        ipRuleItem.ip_v4 = 0;
      }
      let portRange = [];
      if (ipRuleItem.port_rule.property1) {
        if (
          ipRuleItem.port_rule.low_port == null &&
          ipRuleItem.port_rule.high_port
        ) {
          ipRuleItem.port_rule.low_port = ipRuleItem.port_rule.high_port - 1;
        }
        if (ipRuleItem.port_rule.low_port) {
          portRange.push(`0~${ipRuleItem.port_rule.low_port}`);
        }
        if (ipRuleItem.port_rule.low_port === 0) {
          portRange.push(0);
        }
      }
      if (ipRuleItem.port_rule.property2) {
        if (
          !ipRuleItem.port_rule.high_port &&
          ipRuleItem.port_rule.high_port !== 0 &&
          ipRuleItem.port_rule.low_port !== 65535
        ) {
          ipRuleItem.port_rule.high_port = ipRuleItem.port_rule.low_port + 1;
        }
        if (
          !ipRuleItem.port_rule.low_port &&
          ipRuleItem.port_rule.low_port !== 0
        ) {
          ipRuleItem.port_rule.low_port = ipRuleItem.port_rule.high_port - 1;
        }
        if (ipRuleItem.port_rule.high_port != null) {
          if (
            ipRuleItem.port_rule.low_port !== ipRuleItem.port_rule.high_port
          ) {
            portRange.push(
              `${ipRuleItem.port_rule.low_port}~${ipRuleItem.port_rule.high_port}`
            );
          } else {
            if (!portRange.length && !ipRuleItem.port_rule.property3) {
              portRange.push(ipRuleItem.port_rule.low_port);
            }
          }
        }
      }
      if (ipRuleItem.port_rule.property3) {
        portRange.push(
          `${
            ipRuleItem.port_rule.high_port === undefined
              ? ipRuleItem.port_rule.low_port
              : ipRuleItem.port_rule.high_port
          }~65535`
        );
      }
      ipRuleItem.portRange = portRange.join(",");
      return ipRuleItem;
    },
    addToList() {
      this.$refs["ipRule"].validate((valid) => {
        if (valid) {
          this.ipRuleList.unshift(this.formDataToTableData());
          this.showDataToRuleParams();
        } else {
          this.$message.error("检验错误，请检查必填项是否填写。");
        }
      });
    },
    // 修改ip规则
    editIpRule() {
      this.$refs["ipRule"].validate((valid) => {
        if (valid) {
          this.ipRuleList = this.ipRuleList.map((item, index) => {
            if (index === this.formData.index) {
              console.log(index === this.formData.index, 1111);
              item = this.formDataToTableData();
            }
            return item;
          });
          this.showDataToRuleParams();
          this.initFormData();
          this.$message.success("保存成功");
        } else {
          this.$message.error("检验错误，请检查必填项是否填写。");
        }
      });
    },
    propertyToValue(property1, property2, property3) {
      let value = 0;
      if (property1 != undefined) value += Number(property1);
      if (property2 != undefined) value += Number(property2) * 2;
      if (property3 != undefined) value += Number(property3) * 4;
      return value;
    },
    deleteRuleInfo(row) {
      this.ipRuleList.forEach((item, index) => {
        if (item.index === row.index) {
          this.ipRuleList.splice(index, 1);
        }
      });
      this.showDataToRuleParams();
    },
    // 将要修改的ip规则反写到表单里
    editRuleInfo(row, index) {
      this.formData = JSON.parse(JSON.stringify(row));
      this.formData.index = index;
      var scrollTop = document.querySelector("#addRules");
      if (scrollTop.scrollTop > 390) {
        scrollTop.scrollTop = 390;
      }
      this.showsave = false;
    },
    chengradio(e) {
      if (this.formData.type == "IPV4" || e == "IPV4") {
      } else {
        this.formData.ruleIp = "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.p-relative {
  position: relative;
  .tab-box-btn {
    position: absolute;
    top: 0px;
    right: 20px;

    .el-button {
      border: 1px solid #cecece;
      width: 35px;
      height: 27px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.iprulr-top {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  &-type {
    display: flex;
    flex-direction: column;

    .el-radio {
      margin: 0;
    }

    ::v-deep {
      .el-radio__label {
        padding-left: 8px;
        padding-right: 16px;
      }

      .el-radio__inner {
        border: 1.5px solid #767684;
      }

      .el-radio__input.is-checked .el-radio__inner {
        border-color: #409eff;
        background: #ffffff;
      }

      .el-radio__inner::after {
        width: 8px;
        height: 8px;
        background: #116ef9;
      }
    }
  }

  div:nth-child(2) {
    margin-right: 6px;
  }

  ::v-deep .el-input .el-input__inner {
    height: 27px !important;
    width: 150px;
    font-weight: 400;
    font-size: 12px;
    padding-left: 5px;
  }

  ::v-deep .el-form-item__label {
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    text-transform: capitalize;
    color: #0f0f13;
  }
}

.iprulr-mid {
  margin-top: 18px;

  &-range {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;

    // div:nth-child(1) {
    // display: -webkit-inline-flex;
    // }
    .active {
      color: #116ef9;
    }

    &-title {
      margin-bottom: 10px;

      span {
        margin-left: 5px;
        color: #116ef9;
      }
    }

    &-down {
      display: flex;
      align-items: center;

      &-input_1 {
        ::v-deep .el-input__inner {
          width: 27px !important;
          height: 27px !important;
          font-weight: 400;
          font-size: 12px;
          padding: 0px;
          text-align: center;
          line-height: 27px;
        }
      }

      &-input_2 {
        ::v-deep .el-input__inner {
          width: 48px !important;
          height: 27px !important;
          font-weight: 400;
          font-size: 12px;
          padding: 0px;
          text-align: center;
          line-height: 27px;
        }
      }

      &-input_3 {
        ::v-deep .el-input__inner {
          width: 56px !important;
          height: 27px !important;
          font-weight: 400;
          font-size: 12px;
          padding: 0px;
          text-align: center;
          line-height: 27px;
        }
      }

      &-input_4 {
        ::v-deep .el-input__inner {
          width: 56px !important;
          height: 27px !important;
          font-weight: 400;
          font-size: 12px;
          padding: 0px;
          text-align: center;
          line-height: 27px;
        }
      }

      .svg-icon {
        margin: 0 7.33px;
      }
    }

    &-radio {
      margin-left: 15px;

      .el-checkbox-group {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  &-protocol {
    margin-top: 24px;
    display: flex;
    flex-direction: column;

    &-title {
      font-weight: 500;
      font-size: 12px;
    }

    &-down {
      ::v-deep .el-form-item__content {
        display: flex;
        align-content: center;
      }

      ::v-deep .el-form-item__label {
        font-style: normal;
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        text-transform: capitalize;
        color: #0f0f13;
      }

      .el-checkbox {
        display: flex;
        align-items: center;
      }

      .el-input {
        width: 225px;
        margin-right: 20px;
      }

      ::v-deep .el-input__inner {
        height: 27px !important;
        font-weight: 400;
        font-size: 12px;
        padding-left: 5px;
      }
    }
  }
}

.iprulr-down {
  margin-top: 20px;

  ::v-deep .el-table__header-wrapper {
    height: 100%;
  }

  .btn {
    ::v-deep .el-button {
      border: none;
    }
  }
}
</style>
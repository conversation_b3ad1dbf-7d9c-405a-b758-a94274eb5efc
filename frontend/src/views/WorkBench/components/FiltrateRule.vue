<template>
  <div class="filtrate-rule">
    <div class="handle-box">
      <div class="handle-box-left">
        <el-radio-group v-model="state" @change="STATE_CHANGE">
          <el-radio :label="0">命中留存</el-radio>
          <el-radio :label="1">命中丢弃</el-radio>
        </el-radio-group>
        <span style="color:red">* </span>
        <span style="color:#9999A1"> 被过滤模块丢弃的数据都不留存</span>
      </div>
      <div class="handle-box-right">
        <el-button
          plain
          :disabled="tableData.length < 1"
          @click="REMOVES"
        >
          删除
        </el-button>
        <el-button
          plain
          :disabled="pageTotal < 1"
          @click="ALL_REMOVE"
        >
          全部删除
        </el-button>
        <el-button plain @click="UPLOAD_CLICK"> 导入 </el-button>
        <el-button
          plain
          :disabled="tableData.length < 1"
          @click="DOWNLOAD_CLICK"
        >
          导出
        </el-button>
        <el-button icon="el-icon-plus" plain @click="ADD_CLICK">
          添加
        </el-button>
      </div>
    </div>
    <div class="table-box">
      <el-table
        :data="tableData"
        style="width: 100%"
        :default-sort="{ prop: 'date', order: 'descending' }"
        stripe
        border
        @selection-change="SELECTTION_CHANGE"
      >
        <el-table-column type="selection" width="60"> </el-table-column>
        <el-table-column type="index" label="序号" width="100" sortable>
          <template scope="scoped">
            {{ TABLE_INDEX(scoped.$index) }}
          </template>
        </el-table-column>
        <el-table-column prop="ip" label="IP">
          <template slot-scope="scoped">
            {{ scoped.row.ip || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="子网掩码">
          <template slot-scope="scoped">
            {{ scoped.row.filter_info.subnet_mask || "-" }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="IP-protocol">
          <template slot-scope="scoped">
            {{ IP_PRO_FMT(scoped.row, "ip_pro") }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="TCP端口">
          <template slot-scope="scoped">
            {{ IP_PRO_FMT(scoped.row, "tcp") }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="UDP端口">
          <template slot-scope="scoped">
            {{ IP_PRO_FMT(scoped.row, "udp") }}
          </template>
        </el-table-column>
        <el-table-column
          sortable
          prop="updated_time"
          label="修改时间"
          :formatter="UPDATED_TIME_FMT"
        >
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scoped">
            <el-button type="text" @click="PUT_CLICK(scoped.row)">
              编辑
            </el-button>
            <el-button
              type="text"
              class="dividing"
              @click="REMOVE_ROW(scoped.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="page-box">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          @size-change="PAGESIZE_CHANGE"
          @current-change="PAGE_CHANGE"
        >
        </el-pagination>
      </div>
    </div>

    <el-drawer
      :title="rightTitle"
      :visible.sync="rightDrawer"
      custom-class="lcz-drawer"
      :direction="direction"
      :before-close="rightClose"
      :close-on-press-escape="false"
      :modal="false"
      :size="550"
      :show-close="true"
      :wrapper-closable="false"
      destroy-on-close
    >
      <div class="content">
        <div class="lcz-tab">
          <div
            v-for="(item, index) in [
              { key: 2, label: '网段限制' },
              { key: 0, label: '端口限制' },
              { key: 1, label: 'IP协议限制' },
            ]"
            :key="index"
            :class="['lcz-tab-item',rightForm.type===item.key?'active':'']"
            @click="rightForm.type=item.key"
          >
            {{ item.label }}
          </div>
        </div>
        <el-form
          ref="rightForm"
          v-loading="rightLoading"
          :model="rightForm"
          size="small"
          label-position="top"
          class="right-form"
          :rules="rules"
        >
          <el-form-item label="IP" class="ip-form" prop="ip">
            <el-input
              v-model="rightForm.ip"
              :placeholder="rightForm.type !== 2 ? '全部IP' : '请填写IP'"
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="rightForm.type === 2"
            label="子网掩码"
            prop="subnet_mask"
          >
            <el-input
              v-model="rightForm.subnet_mask"
              placeholder="***************"
            ></el-input>
          </el-form-item>
          <!-- 端口限制时渲染的表单 -->
          <el-form-item
            v-if="rightForm.type === 0"
            label="TCP端口"
            prop="tcp_port"
          >
            <el-input
              v-model="rightForm.tcp_port"
              placeholder="请填写端口"
              @blur="TCP_PORT_CHANGE"
            ></el-input>
            <el-checkbox
              v-model="rightForm.tcp_port_type"
              true-label="Invert"
              false-label="Select"
              @change="
                () => {
                  if (
                    rightForm.tcp_port_type === 'Invert' &&
                    rightForm.tcp_port === ''
                  ) {
                    rightForm.tcp_port = 'All';
                  }
                  if (
                    rightForm.tcp_port_type === 'Select' &&
                    rightForm.tcp_port === 'All'
                  ) {
                    rightForm.tcp_port = '';
                  }
                }
              "
            >
              反选
            </el-checkbox>
          </el-form-item>
          <el-form-item
            v-if="rightForm.type === 0"
            label="UDP端口"
            prop="udp_port"
          >
            <el-input
              v-model="rightForm.udp_port"
              placeholder="请填写端口"
              @blur="UDP_PORT_CHANGE"
            ></el-input>
            <el-checkbox
              v-model="rightForm.udp_port_type"
              true-label="Invert"
              false-label="Select"
              @change="
                () => {
                  if (
                    rightForm.udp_port_type === 'Invert' &&
                    rightForm.udp_port === ''
                  ) {
                    rightForm.udp_port = 'All';
                  }
                  if (
                    rightForm.udp_port_type === 'Select' &&
                    rightForm.udp_port === 'All'
                  ) {
                    rightForm.udp_port = '';
                  }
                }
              "
            >
              反选
            </el-checkbox>
          </el-form-item>

          <!-- IP协议限制时的表单 -->
          <el-form-item
            v-if="rightForm.type === 1"
            label="IP协议"
            prop="tcp_port"
            class="form-ipport"
          >
            <el-select
              v-model="rightForm.ip_pro"
              multiple
              filterable
              collapse-tags
              placeholder="请选择协议"
              @change="IP_PRO_CHANGE"
              @remove-tag="IP_PRO_REMOVE"
            >
              <el-option
                v-for="item in ipProtocol"
                :key="item.id"
                :label="item.id + '：' + item.type"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <el-checkbox
              v-model="rightForm.ip_pro_type"
              true-label="Invert"
              false-label="Select"
            >
              反选
            </el-checkbox>
          </el-form-item>
          <div v-if="rightForm.type === 1" class="protocol-list">
            <el-table
              :data="ipProtocol"
              style="width: 100%"
              :row-class-name="TABLE_ROW_CLASSNAME"
              @row-click="IP_ROW_CLICK"
            >
              <el-table-column prop="id" label=""> </el-table-column>
              <el-table-column prop="type" label=""> </el-table-column>
              <el-table-column prop="name" label=""> </el-table-column>
            </el-table>
          </div>
        </el-form>
      </div>
      <div class="btn">
        <el-button class="cancel" @click="ADD_CANCEL('rightForm')">
          取消
        </el-button>
        <el-button
          v-if="rightTitle === '添加过滤条件'"
          type="primary"
          :disabled="isDisabled"
          @click="ADD_SUBMIT('rightForm')"
        >
          确定
        </el-button>
        <el-button
          v-if="rightTitle === '修改过滤条件'"
          type="primary"
          :disabled="isDisabled"
          @click="PUT_SUBMIT('rightForm')"
        >
          修改
        </el-button>
      </div>
    </el-drawer>
    <el-dialog
      :visible.sync="uploadDiaglog"
      :before-close="UPLOADDIALOG_CLOSE"
      class="upload-diglog"
      :close-on-click-modal="false"
      :show-close="false"
      @close="handleColse"
    >
      <div slot="title">
        <div>上传过滤规则</div>
        <div @click="DEMO_DOWNLOAD">规则模板.CSV</div>
        <div>
          <!-- <i class="el-icon-close"></i> -->
        </div>
      </div>
      <el-upload
        v-show="fileList.length === 0"
        ref="upload"
        class="upload-demo"
        drag
        :headers="uploadHeaders"
        :action="uploadUrl"
        :data="uploadData"
        multiple
        :show-file-list="false"
        :on-change="UPLOAD_CHANGE"
        :before-upload="BEFORE_UPLOAD"
        :on-success="UPLOAD_SUCCESS"
        :on-error="UPLOAD_ERROR"
      >
        <svg-icon icon-class="upload-file" class="upload-file-icon"></svg-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="text-maxsize">文件不能大于25M</div>
      </el-upload>
      <div v-show="fileList.length > 0" class="upload-timing">
        <div class="uploading-box">
          <div>
            <span>{{ fileName }}</span>
            <span>{{ uploadStatusText }}</span>
          </div>
          <div>
            <el-progress
              :percentage="uploadPlan"
              color="#39D979"
              :show-text="false"
            ></el-progress>
          </div>
          <svg-icon
            v-if="uploadPlan === 100"
            class="success-icon"
            icon-class="upload-success"
            color="#a4efa4"
          ></svg-icon>
        </div>
        <div v-if="uploadPlan === 100">
          上传规则总数：<span style="color: #116ef9">{{ uploadTotal }}</span>个; 导入成功规则：<span style="color: #39d979">{{
            uploadSucNum
          }}</span>个; 导入失败规则：<span style="color: red">{{ uploadFailNum }}</span>个;
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="cancel" @click="UPLOAD_CANCEL">取消</el-button>
        <el-button
          type="primary"
          :class="uploadPlan > 0 && uploadPlan < 100 ? 'submit-d' : 'submit'"
          @click="UPLOAD_SUBMIT"
        >确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import dayjs from "dayjs";
import FileSaver from "file-saver";
import axios from "axios";
import {
  filter_config,
  filter_add,
  filter_remove,
  filter_put,
  filter_state,
  put_state,
} from "@/api/FiltrateRule";
import api from "@/api/offline";
import mixins from "@/views/offline/mixins";
export default {
  name: "FiltrateRule",
  mixins: [mixins],
  data() {
    // IP校验规则
    let validateIP = (rule, value, callback) => {
      const ipReg =
        /^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$/;
      if (this.rightForm.type === 2 && value === "") {
        callback(new Error("为IP网段时，IP不可为空"));
      }
      if (!ipReg.test(value) && value !== "") {
        callback(new Error("IP格式有误"));
      } else {
        callback();
      }
    };
    // 网口校验规则
    let validateTCP = (rule, value, callback) => {
      if (value === "") {
        callback();
      }
      if (value.toLowerCase() === "all") {
        callback();
      }
      let arr = value.split(",");
      for (let i = 0; i < arr.length; i++) {
        if (arr[i] - 0 < 1 || arr[i] - 0 > 65535) {
          callback(new Error("端口范围有误,范例:1,65535,100,5000"));
        }
      }
      callback();
    };
    // 子网掩码校验规则
    let validateSubnet = (rule, value, callback) => {
      const subnetReg =
        /^((128|192)|2(24|4[08]|5[245]))(\.(0|(128|192)|2((24)|(4[08])|(5[245])))){3}$/;
      if (value === "") {
        callback();
      }
      if (!subnetReg.test(value)) {
        callback(new Error("子网掩码格式有误"));
      } else {
        callback();
      }
    };
    return {
      uploadHeaders: { token: getToken() },
      task_id: "",
      batch_id: "",
      state: 0,
      rules: {
        ip: [
          // { required: true, message: 'IP为必填项' },
          { validator: validateIP, trigger: "blur" },
        ],
        tcp_port: [{ validator: validateTCP, trigger: "blur" }],
        udp_port: [{ validator: validateTCP, trigger: "blur" }],
        subnet_mask: [{ validator: validateSubnet, trigger: "blur" }],
      },
      tableData: [],
      // 多选数据
      tableDataSelsections: [],
      radio: 3,
      // 右侧弹窗
      rightDrawer: false,
      // 右侧弹窗标题
      rightTitle: "添加过滤条件",
      // 右侧弹窗方向
      direction: "rtl",
      // 右侧弹窗表单数据结构
      rightForm: {
        ip: "",
        type: 2,
        ip_pro: [],
        ip_pro_type: "Select",
        tcp_port: "",
        tcp_port_type: "Select",
        udp_port: "",
        udp_port_type: "Select",
        subnet_mask: "***************",
      },
      // 右侧弹窗loading
      rightLoading: false,
      // ip协议列表
      ipProtocol: [],
      ipProtocolOptions: [],
      // 准备修改的条目id
      putId: "",
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      pageTotal: 0,
      // 上传相关
      baseURL: "",
      uploadDiaglog: false,
      uploadUrl: "",
      fileList: [],
      fileName: "",
      uploadData: {},
      // 上传进度条相关
      uploadPlan: 10,
      uploadStatusText: "上传中！",
      uploadSucNum: 0,
      uploadFailNum: 0,
      uploadTotal: 0,
      isDisabled: false,
    };
  },
  computed: {
    protocol_type() {
      return this.$store.state.long.Dict.protocol_type;
    },
  },
  watch: {
    // 此处监听是为了监听从select框新增时，同步下方列表的字体变化
    "rightForm.ip_pro"(val, old) {
      this.watchIpPro();
      try {
        if (val.length > old.length) {
          const pick = val[val.length - 1];
          for (let j = 0; j < val.length; j++) {
            for (let i = 0; i < this.ipProtocol.length; i++) {
              if (this.ipProtocol[i].id === val[j]) {
                this.ipProtocol[i].select = true;
                break;
              }
            }
          }
        }
      } catch (error) {
        console.warn("可控异常：", error);
      }
    },
    "rightForm.tcp_port"(val, old) {
      this.watchPort();
      if (val.toLowerCase() === "all") {
        this.rightForm.tcp_port_type = "Invert";
      }
      if (val === "") {
        this.rightForm.tcp_port_type = "Select";
      }
    },
    "rightForm.udp_port"(val, old) {
      this.watchPort();
      if (val.toLowerCase() === "all") {
        this.rightForm.udp_port_type = "Invert";
      }
      if (val === "") {
        this.rightForm.udp_port_type = "Select";
      }
    },
    "rightForm.type": {
      handler(val) {
        if (val === 0) {
          this.watchPort();
        } else if (val === 1) {
          this.watchIpPro();
        } else {
          this.isDisabled = false;
        }
      },
      immediate: true,
      deep: true,
    },
    offlineId: {
      handler(val) {
        if (val && this.isOffLine) {
          this.GET_LIST();
          this.GET_STATE();
        }
      },
      immediate: true,
    },
  },
  created() {
    this.TASK_ID();
    this.BASE_URL();
    this.GET_STATE();
    if (!this.isOffLine) {
      this.GET_LIST();
    }
  },
  mounted() {
    this.JSON_IP();
  },
  methods: {
    watchPort() {
      this.isDisabled = !(this.rightForm.tcp_port || this.rightForm.udp_port);
    },
    watchIpPro() {
      this.isDisabled = !this.rightForm.ip_pro.length;
    },
    TASK_ID() {
      this.task_id = localStorage.getItem("task_id") || 0;
      if (this.task_id == 0) {
        this.batch_id = 1000001;
      }
      if (this.task_id == 1) {
        this.batch_id = 1000002;
      }
      if (this.isOffLine) {
        if (this.offlineId) {
          this.uploadData.task_id = this.offlineId;
        }
      } else {
        this.uploadData.task_id = this.task_id;
      }

      this.uploadData.batch_id = this.batch_id;
    },
    BASE_URL() {
      let temp;
      if (process.env.VUE_APP_BASE_API === "") {
        temp = `${process.env.VUE_APP_BASE_API}/api`;
      } else {
        temp = process.env.VUE_APP_BASE_API;
      }
      this.baseURL = temp;
      if (this.isOffLine) {
        this.uploadUrl = `${this.baseURL}/offline/filter/csvImport`;
      } else {
        this.uploadUrl = `${this.baseURL}/filter/csvImport`;
      }
    },
    // 获取列表数据，基础事件
    GET_LIST() {
      if (this.isOffLine) {
        if (this.offlineId) {
          api
            .offlineFilterList({
              batch_id: this.batch_id,
              page_size: this.pageSize,
              current_page: this.currentPage,
              order_field: "id",
              sort_order: "desc",
              task_id: this.offlineId,
            })
            .then((res) => {
              if (res.err === 0) {
                this.tableData = res.data.records;
                this.pageTotal = res.data.total;
              }
            });
        }
      } else {
        filter_config({
          task_id: this.task_id,
          batch_id: this.batch_id,
          page_size: this.pageSize,
          current_page: this.currentPage,
          order_field: "id",
          sort_order: "desc",
        }).then((res) => {
          if (res.err === 0) {
            this.tableData = res.data.records;
            this.pageTotal = res.data.total;
          }
        });
      }
    },
    TABLE_INDEX(index) {
      return (this.currentPage - 1) * this.pageSize + (index + 1);
    },
    // 获取数据的命中/丢弃状态
    GET_STATE() {
      if (this.isOffLine) {
        if (this.offlineId) {
          api
            .offlineFilterStateTask({ task_id: this.offlineId })
            .then((res) => {
              this.state = res.data.state;
            });
        }
      } else {
        filter_state({ task_id: this.task_id }).then((res) => {
          this.state = res.data.state;
        });
      }
    },
    // 改变数据的命中/丢弃状态
    STATE_CHANGE(val) {
      let text = "";
      if (val === 0) {
        text = "保留";
        this.$Notice({
          title: "变更提示",
          type: "warn",
          message: `命中留存时，系统只留存命中过滤规则的数据!`,
        })
          .then(() => {
            if (this.isOffLine) {
              if (this.offlineId) {
                api
                  .offlineFilterState({
                    batch_id: this.batch_id,
                    state: val,
                    task_id: this.offlineId,
                  })
                  .then((res) => {
                    if (res.err === 0) {
                      this.$message.success(res.msg);
                      this.GET_STATE();
                    }
                  });
              }
            } else {
              put_state({
                task_id: this.task_id,
                batch_id: this.batch_id,
                state: val,
              }).then((res) => {
                if (res.err === 0) {
                  this.$message.success(res.msg);
                  this.GET_STATE();
                }
              });
            }
          })
          .catch(() => {
            // on cancel
            if (val === 0) {
              this.state = 1;
            }
            if (val === 1) {
              this.state = 0;
            }
          });
      }
      if (val === 1) {
        text = "丢弃";
        this.$Notice({
          title: "变更提示",
          type: "warn",
          message: `命中丢弃时，系统丢弃命中过滤规则的数据!`,
        })
          .then(() => {
            if (this.isOffLine) {
              if (this.offlineId) {
                api
                  .offlineFilterState({
                    batch_id: this.batch_id,
                    state: val,
                    task_id: this.offlineId,
                  })
                  .then((res) => {
                    if (res.err === 0) {
                      this.$message.success(res.msg);
                      this.GET_STATE();
                    }
                  });
              }
            } else {
              put_state({
                task_id: this.task_id,
                batch_id: this.batch_id,
                state: val,
              }).then((res) => {
                if (res.err === 0) {
                  this.$message.success(res.msg);
                  this.GET_STATE();
                }
              });
            }
          })
          .catch(() => {
            // on cancel
            if (val === 0) {
              this.state = 1;
            }
            if (val === 1) {
              this.state = 0;
            }
          });
      }
    },
    // 页码变化
    PAGE_CHANGE(currentPage) {
      this.currentPage = currentPage;
      this.GET_LIST();
    },
    // 每一页条数变化
    PAGESIZE_CHANGE(pagesize) {
      this.pageSize = pagesize;
      this.GET_LIST();
    },
    // 从JSON文件获取IP协议数据，基础事件
    JSON_IP() {
      let arr = [];
      let obj = {};
      let index = 0;
      for (let i in this.protocol_type) {
        obj = {};
        if (this.protocol_type[i]) {
          obj = {
            select: false,
            index: index,
            id: parseInt(i),
            type: `${this.protocol_type[i].protocol_type || "N/A"}`,
            name: `${this.protocol_type[i].protocol_remark || "N/A"}`,
          };
          arr.push(obj);
          index++;
        }
      }
      this.ipProtocol = arr;
    },
    // ip_pro过滤
    IP_PRO_FMT(row, type) {
      let data;
      switch (type) {
      case "ip_pro":
        data = "";
        // 正常有数据时先格式化
        if (
          row.filter_info.ip_pro !== null &&
            row.filter_info.ip_pro &&
            row.filter_info.ip_pro.length > 0
        ) {
          let arr = [];
          row.filter_info.ip_pro.forEach((i) => {
            arr.push(this.protocol_type[i].protocol_remark);
          });
          data = arr.join(",");
          return data;
        }
        // 为空并且反选时
        if (
          row.filter_info.ip_pro !== null &&
            row.filter_info.ip_pro.length < 1 &&
            row.filter_info.ip_pro_type === "Invert"
        ) {
          return "All";
        }
        // 为空且不反选时
        else if (
          row.filter_info.ip_pro !== null &&
            row.filter_info.ip_pro.length < 1 &&
            row.filter_info.ip_pro_type === "Select"
        ) {
          return "-";
        }
        // 反选
        else if (row.filter_info.ip_pro_type === "Invert") {
          return `非:${data}`;
        } else if (row.type === 0) {
          return "-";
        } else {
          return "-";
        }

      case "tcp":
        data = "";
        // 正常有数据时先格式化
        if (
          row.filter_info.tcp_port !== null &&
            row.filter_info.tcp_port &&
            row.filter_info.tcp_port.length > 0
        ) {
          data = row.filter_info.tcp_port.join(",");
          return data;
        }
        // 为空并且反选时
        if (
          row.filter_info.tcp_port !== null &&
            row.filter_info.tcp_port.length < 1 &&
            row.filter_info.tcp_port_type === "Invert"
        ) {
          return "All";
        }
        // 为空且不反选时
        else if (
          row.filter_info.tcp_port !== null &&
            row.filter_info.tcp_port.length < 1 &&
            row.filter_info.tcp_port_type === "Select"
        ) {
          return "-";
        }
        // 反选
        else if (row.filter_info.tcp_port_type === "Invert") {
          return `非:${data}`;
        } else if (row.type === 0) {
          return "-";
        } else {
          return "-";
        }

      case "udp":
        data = "";
        // 正常有数据时先格式化
        if (
          row.filter_info.udp_port !== null &&
            row.filter_info.udp_port &&
            row.filter_info.udp_port.length > 0
        ) {
          data = row.filter_info.udp_port.join(",");
          return data;
        }
        // 为空并且反选时
        if (
          row.filter_info.udp_port !== null &&
            row.filter_info.udp_port.length < 1 &&
            row.filter_info.udp_port_type === "Invert"
        ) {
          return "All";
        }
        // 为空且不反选时
        else if (
          row.filter_info.udp_port !== null &&
            row.filter_info.udp_port.length < 1 &&
            row.filter_info.udp_port_type === "Select"
        ) {
          return "-";
        }
        // 反选
        else if (row.filter_info.udp_port_type === "Invert") {
          return `非:${data}`;
        } else if (row.type === 0) {
          return "-";
        } else {
          return "-";
        }

      default:
        break;
      }
    },

    // 动态改变ippro列表样式
    TABLE_ROW_CLASSNAME({ row, rowIndex }) {
      if (row.select) return "select-row";
      return "";
    },

    // updated_time，修改时间格式化
    UPDATED_TIME_FMT(row) {
      return dayjs(row.updated_time * 1000).format("YYYY-MM-DD HH:mm:ss");
    },

    // 点击添加按钮
    ADD_CLICK() {
      this.rightDrawer = true;
      this.rightTitle = "添加过滤条件";
    },
    // 反选按钮全选
    SELECT_CHANGE() {
      console.log(this.rightForm.ip_pro, this.rightForm.ip_pro_type);
      if (
        this.rightForm.ip_pro_type === "Invert" &&
        this.rightForm.ip_pro === []
      ) {
        console.log(1);
        this.rightForm.ip_pro = ["All"];
      }
      if (
        this.rightForm.ip_pro_type === "Invert" &&
        this.rightForm.ip_pro === ""
      ) {
        console.log(2);
        this.rightForm.ip_pro = ["All"];
      }
      if (
        this.rightForm.ip_pro_type === "Select" &&
        this.rightForm.ip_pro[0] === "All"
      ) {
        console.log(3);
        this.rightForm.ip_pro = "";
      }
    },
    // 添加过滤条件
    ADD_SUBMIT(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.rightLoading = true;
          let obj = {
            batch_id: this.batch_id,
            type: this.rightForm.type,
            filter_info: {
              ip: this.rightForm.ip,
            },
          };
          switch (this.rightForm.type) {
          case 0:
            if (this.rightForm.tcp_port === "All") {
              obj.filter_info.tcp_port = null;
            } else {
              obj.filter_info.tcp_port = this.rightForm.tcp_port
                ? this.rightForm.tcp_port.split(",")
                : null;
            }
            obj.filter_info.tcp_port_type = this.rightForm.tcp_port_type;

            if (this.rightForm.udp_port === "All") {
              obj.filter_info.udp_port = null;
            } else {
              obj.filter_info.udp_port = this.rightForm.udp_port
                ? this.rightForm.udp_port.split(",")
                : null;
            }
            obj.filter_info.udp_port_type = this.rightForm.udp_port_type;
            break;
          case 1:
            obj.filter_info.ip_pro = this.rightForm.ip_pro || null;
            obj.filter_info.ip_pro_type = this.rightForm.ip_pro_type;
            break;
          case 2:
            obj.filter_info.subnet_mask = this.rightForm.subnet_mask || null;
            break;
          default:
            break;
          }
          if (this.isOffLine) {
            if (this.offlineId) {
              obj.task_id = this.offlineId;
              api
                .offlineFilterAdd(obj)
                .then((res) => {
                  if (res.err === 0) {
                    this.$message.success(res.msg);
                    this.rightLoading = false;
                    this.rightDrawer = false;
                    this.ADD_CANCEL();
                    this.GET_LIST();
                  }
                })
                .catch((err) => {
                  this.rightLoading = false;
                });
            }
          } else {
            obj.task_id = this.task_id;
            filter_add(obj)
              .then((res) => {
                if (res.err === 0) {
                  this.$message.success(res.msg);
                  this.rightLoading = false;
                  this.rightDrawer = false;
                  this.ADD_CANCEL();
                  this.GET_LIST();
                }
              })
              .catch((err) => {
                this.rightLoading = false;
              });
          }
        } else {
          console.error("新增时表单校验不通过！！");
          return false;
        }
      });
    },
    // ippro(IP协议)选项变化
    IP_PRO_CHANGE(pick) {
      if (
        this.rightForm.ip_por_type === "Invert" &&
        this.rightForm.ip_por === ""
      ) {
        this.rightForm.ip_por = ["All"];
      }
      if (
        this.rightForm.ip_por_type === "Select" &&
        this.rightForm.ip_por === ["All"]
      ) {
        this.rightForm.ip_por = "";
      }
    },
    // 移除单个协议时触发
    IP_PRO_REMOVE(remove) {
      for (let i = 0; i < this.ipProtocol.length; i++) {
        if (this.ipProtocol[i].id === remove) {
          this.ipProtocol[i].select = false;
        }
      }
    },
    // ipprocol列表某一行被点击
    IP_ROW_CLICK(row, column, event) {
      if (this.ipProtocol[row.index].select) {
        // 如果是已选中状态,则取消选中
        this.ipProtocol[row.index].select = false;
        this.rightForm.ip_pro.splice(this.rightForm.ip_pro.indexOf(row.id), 1);
      } else {
        // 反之
        this.ipProtocol[row.index].select = true;
        if (this.rightForm.ip_pro === "" || this.rightForm.ip_pro === null) {
          this.rightForm.ip_pro = [row.id];
        } else {
          this.rightForm.ip_pro.push(row.id);
        }
      }
    },
    // 关闭新增弹窗
    ADD_CANCEL() {
      this.rightDrawer = false;
      this.TYPE_CHANGE(this.rightForm.type, "");
      for (let i = 0; i < this.ipProtocol.length; i++) {
        this.ipProtocol[i].select = false;
      }
      this.$refs["rightForm"].resetFields();
    },

    // 新增弹窗表单中的type变化
    TYPE_CHANGE(type, ip) {
      this.rightForm = {
        ip: ip,
        type: type,
        ip_pro: "",
        ip_pro_type: "Select",
        tcp_port: "",
        tcp_port_type: "Select",
        udp_port: "",
        udp_port_type: "Select",
        subnet_mask: "",
      };
      if (type === 1) {
        this.JSON_IP();
      }
      if (type === 2) {
        this.rightForm.subnet_mask = "***************";
      }
      this.$refs["rightForm"].resetFields();
    },
    // 点击修改按钮
    PUT_CLICK(row) {
      console.log("点击修改：", row);
      let data = JSON.parse(JSON.stringify(row));
      this.rightTitle = "修改过滤条件";
      this.rightForm = {
        ip: data.filter_info.ip || "",
        type: data.type,
        ip_pro: data.filter_info.ip_pro === null ? "" : data.filter_info.ip_pro,
        ip_pro_type:
          data.filter_info.ip_pro_type === null
            ? ""
            : data.filter_info.ip_pro_type,
        tcp_port:
          data.filter_info.tcp_port === null
            ? ""
            : data.filter_info.tcp_port.join(","),
        tcp_port_type:
          data.filter_info.tcp_port_type === null
            ? ""
            : data.filter_info.tcp_port_type,
        udp_port:
          data.filter_info.udp_port === null
            ? ""
            : data.filter_info.udp_port.join(","),
        udp_port_type:
          data.filter_info.udp_port_type === null
            ? ""
            : data.filter_info.udp_port_type,
        subnet_mask:
          data.filter_info.subnet_mask === null
            ? ""
            : data.filter_info.subnet_mask,
      };
      if (
        this.rightForm.tcp_port_type === "Invert" &&
        this.rightForm.tcp_port === ""
      ) {
        this.rightForm.tcp_port = "All";
      }
      if (
        this.rightForm.tcp_port_type === "Select" &&
        this.rightForm.tcp_port.toLowerCase() === "all"
      ) {
        this.rightForm.tcp_port = "";
      }
      if (
        this.rightForm.udp_port_type === "Invert" &&
        this.rightForm.udp_port === ""
      ) {
        this.rightForm.udp_port = "All";
      }
      if (
        this.rightForm.udp_port_type === "Select" &&
        this.rightForm.udp_port.toLowerCase() === "all"
      ) {
        this.rightForm.udp_port = "";
      }
      if (
        this.rightForm.ip_por_type === "Invert" &&
        this.rightForm.ip_por === ""
      ) {
        this.rightForm.ip_por = "All";
      }
      if (
        this.rightForm.ip_por_type === "Select" &&
        this.rightForm.ip_por.toLowerCase() === "all"
      ) {
        this.rightForm.ip_por = "";
      }
      this.putId = data.id;
      this.rightDrawer = true;
    },
    // 确认修改
    PUT_SUBMIT(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.rightLoading = true;
          console.log("表单提交：", this.rightForm);
          let obj = {
            id: this.putId,
            task_id: this.task_id,
            batch_id: this.batch_id,
            type: this.rightForm.type,
            filter_info: {
              ip: this.rightForm.ip,
            },
          };
          switch (this.rightForm.type) {
          case 0:
            if (this.rightForm.tcp_port.toLowerCase() === "all") {
              obj.filter_info.tcp_port = null;
            } else {
              obj.filter_info.tcp_port = this.rightForm.tcp_port
                ? this.rightForm.tcp_port.split(",")
                : null;
            }
            obj.filter_info.tcp_port_type = this.rightForm.tcp_port_type;

            if (this.rightForm.udp_port.toLowerCase() === "all") {
              obj.filter_info.udp_port = null;
            } else {
              obj.filter_info.udp_port = this.rightForm.udp_port
                ? this.rightForm.udp_port.split(",")
                : null;
            }
            obj.filter_info.udp_port_type = this.rightForm.udp_port_type;
            break;
          case 1:
            obj.filter_info.ip_pro = this.rightForm.ip_pro || null;
            obj.filter_info.ip_pro_type = this.rightForm.ip_pro_type;
            break;
          case 2:
            obj.filter_info.subnet_mask = this.rightForm.subnet_mask || null;
            break;
          default:
            break;
          }
          if (this.isOffLine) {
            if (this.offlineId) {
              obj.task_id = this.offlineId;
              api
                .offlineFilterUpdate(obj)
                .then((res) => {
                  if (res.err === 0) {
                    this.$message.success(res.msg);
                    this.rightLoading = false;
                    this.rightDrawer = false;
                    this.ADD_CANCEL();
                    this.GET_LIST();
                  }
                })
                .catch((err) => {
                  this.rightLoading = false;
                });
            }
          } else {
            obj.task_id = this.task_id;
            filter_put(obj)
              .then((res) => {
                if (res.err === 0) {
                  this.$message.success(res.msg);

                  this.rightDrawer = false;
                  this.rightLoading = false;
                  this.ADD_CANCEL();
                  this.GET_LIST();
                }
              })
              .catch((err) => {
                this.rightLoading = false;
              });
          }
        } else {
          console.error("修改时表单校验不通过！");
          return false;
        }
      });
    },
    // 点击删除某一行
    REMOVE_ROW(id) {
      if (this.isOffLine) {
        if (this.offlineId) {
          api
            .offlineFilterDelete({
              ids: [id],
              task_id: this.offlineId,
              batch_id: this.batch_id,
            })
            .then((res) => {
              this.$message.success(res.msg);
              this.GET_LIST();
            });
        }
      } else {
        filter_remove({
          ids: [id],
          task_id: this.task_id,
          batch_id: this.batch_id,
        }).then((res) => {
          this.$message.success(res.msg);
          this.GET_LIST();
        });
      }
    },
    // 删除选中的多行
    REMOVES() {
      if (this.tableDataSelsections.length > 0) {
        this.$Notice({
          title: "删除提示",
          type: "warn",
          message: "确认删除选中的条目？",
        })
          .then(() => {
            if (this.isOffLine) {
              if (this.offlineId) {
                api
                  .offlineFilterDelete({
                    ids: this.tableDataSelsections,
                    task_id: this.offlineId,
                    batch_id: this.batch_id,
                  })
                  .then((res) => {
                    this.$message.success(res.msg);
                    this.GET_LIST();
                  });
              }
            } else {
              filter_remove({
                ids: this.tableDataSelsections,
                task_id: this.task_id,
                batch_id: this.batch_id,
              }).then((res) => {
                this.$message.success(res.msg);
                this.GET_LIST();
              });
            }
          })
          .catch(() => {
            // on cancel
          });
      } else {
        this.$message.warning("未选择条目!");
      }
    },
    // 全部删除
    ALL_REMOVE() {
      this.$Notice({
        title: "全部删除提示",
        type: "error",
        message: "确认删除所有的条目？",
      })
        .then(() => {
          if (this.isOffLine) {
            if (this.offlineId) {
              api
                .offlineFilterDelete({
                  task_id: this.offlineId,
                  batch_id: this.batch_id,
                })
                .then((res) => {
                  this.$message.success(res.msg);
                  this.GET_LIST();
                });
            }
          } else {
            filter_remove({
              task_id: this.task_id,
              batch_id: this.batch_id,
            }).then((res) => {
              this.$message.success(res.msg);
              this.GET_LIST();
            });
          }
        })
        .catch(() => {
          // on cancel
        });
    },
    // 多选的条目发生变化
    SELECTTION_CHANGE(val) {
      let arr = [];
      for (let i = 0; i < val.length; i++) {
        arr.push(val[i].id);
      }
      console.log(arr);
      this.tableDataSelsections = arr;
    },
    // 右侧弹窗关闭前回调
    rightClose(done) {
      done();
      // this.$confirm("确认关闭？")
      //   .then((_) => {
      //   })
      //   .catch((_) => {});
    },

    // 验证输入框逗号并转换
    TCP_PORT_CHANGE() {
      let all = this.rightForm.tcp_port;
      if (all.toLowerCase() === "all") return;
      this.rightForm.tcp_port = this.rightForm.tcp_port.replace(/，/gi, ",");
      this.rightForm.tcp_port = this.rightForm.tcp_port.replace(/[^\d,]/g, "");
      let arr = this.rightForm.tcp_port.split(",");
      for (let i = 0; i < arr.length; i++) {
        if (arr[i] === "") {
          arr.splice(i, 1);
          i = 0;
        }
      }
      this.rightForm.tcp_port = arr.join(",");
    },
    UDP_PORT_CHANGE() {
      let all = this.rightForm.udp_port;
      if (all.toLowerCase() === "all") return;
      this.rightForm.udp_port = this.rightForm.udp_port.replace(/，/gi, ",");
      this.rightForm.udp_port = this.rightForm.udp_port.replace(/[^\d,]/g, "");
      let arr = this.rightForm.udp_port.split(",");
      for (let i = 0; i < arr.length; i++) {
        if (arr[i] === "") {
          arr.splice(i, 1);
          i = 0;
        }
      }
      this.rightForm.udp_port = arr.join(",");
    },
    // 点击上传按钮
    UPLOAD_CLICK() {
      this.uploadPlan = 0;
      this.fileList = [];
      this.uploadStatusText = "上传中！";
      this.uploadDiaglog = true;
    },
    // 上传文件状态改变时的钩子
    UPLOAD_CHANGE(file, fileList) {
      console.log(file, fileList);
      this.fileName = file.name;
      if (fileList.length > 0) {
        this.fileList = fileList;
      }
    },
    // 上传文件前的回调
    BEFORE_UPLOAD() {
      this.uploadStatusText = "上传中！";
      this.uploadPlan = 30;
    },
    // 上传文件成功时的回调
    UPLOAD_SUCCESS(res, file, fileList) {
      if (res.err === 0) {
        this.uploadPlan = 100;
        this.uploadStatusText = "上传完成！";
        this.uploadSucNum = res.data.sucNum;
        this.uploadFailNum = res.data.failNum;
        this.uploadTotal = res.data.totalNum;
        this.$message.success("上传成功！");
      } else {
        // this.uploadDiaglog = false;
        this.$refs.upload.abort();
        this.$refs.upload.clearFiles();
        this.fileList = [];
        this.uploadPlan = 0;
        this.$message.error(res.msg);
      }
    },
    // 上传失败时的回调
    UPLOAD_ERROR() {
      this.$message.error("上传失败！");
    },
    // 上传弹窗点击确定
    UPLOAD_SUBMIT(res) {
      this.uploadDiaglog = false;
    },
    // 上传弹窗点击取消
    UPLOAD_CANCEL() {
      if (this.uploadPlan > 0 && this.uploadPlan < 100) {
        this.$refs.upload.abort();
        this.$refs.upload.clearFiles();
        this.fileList = [];
        this.uploadPlan = 0;
        this.uploadStatusText = "上传中！";
        this.$message.warning("已取消上传！");
      } else {
        this.uploadDiaglog = false;
      }
    },
    // 上传弹窗关闭时
    UPLOADDIALOG_CLOSE(done) {
      this.$refs.upload.abort();
      this.uploadPlan = 0;
      this.fileList = [];
      this.uploadStatusText = "上传中！";
      done();
    },
    handleColse() {
      this.GET_LIST();
    },
    // 点击下载
    DOWNLOAD_CLICK() {
      if (this.isOffLine) {
        if (this.offlineId) {
          axios({
            method: "POST",
            url: this.baseURL + "/offline/filter/getCsv/" + this.offlineId, // 后端下载接口地址
            responseType: "blob", // 设置接受的流格式
            headers: {
              token: `${getToken()}`,
            },
          }).then((res) => {
            if (res.err === 40005) {
              this.$message.error(res.msg);
            } else {
              const blob = new Blob([res.data], { type: "application/json" });
              FileSaver.saveAs(blob, res.headers["content-disposition"]);
            }
          });
        }
      } else {
        axios({
          method: "POST",
          url: this.baseURL + "/filter/getCsv/" + this.task_id, // 后端下载接口地址
          responseType: "blob", // 设置接受的流格式
          headers: {
            token: `${getToken()}`,
          },
        }).then((res) => {
          if (res.err === 40005) {
            this.$message.error(res.msg);
          } else {
            const blob = new Blob([res.data], { type: "application/json" });
            FileSaver.saveAs(blob, res.headers["content-disposition"]);
          }
        });
      }
    },
    // 模板csv下载
    DEMO_DOWNLOAD() {
      axios({
        method: "GET",
        url: this.baseURL + "/filter/template", // 后端下载接口地址
        responseType: "blob", // 设置接受的流格式
        headers: {
          token: `${getToken()}`,
        },
      }).then((res) => {
        if (res.err === 40005) {
          this.$message.error(res.msg);
        } else {
          const blob = new Blob([res.data], { type: "application/json" });
          FileSaver.saveAs(blob, res.headers["content-disposition"]);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.filtrate-rule {
  width: 100%;
  position: relative;
  .handle-box {
    height: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    &-left {
      > span {
        color: #0f0f13;
      }
      .el-radio {
        margin-right: 12px;
      }
      ::v-deep .el-radio__inner {
        border: 2px solid #767684;
        width: 12px;
        height: 12px;
      }
      ::v-deep .el-radio__input.is-checked .el-radio__inner {
        background: #ffffff;
        border: 2px solid #116ef9;
      }
      ::v-deep .el-radio__inner::after {
        width: 6px;
        height: 6px;
        background-color: #116ef9;
      }
    }
    &-right {
      display: flex;
      align-items: center;
      .el-button {
        height: 32px;
        padding: 0 14px;
        // color: #0f0f13;
        // background: #ffffff;
        // border: 1px solid #f2f3f7;
        box-sizing: border-box;
      }
      .all-del {
        font-weight: 700;
        color: #0f0f13;
        padding: 0 16px;
        margin-left: 6px;
      }
    }
  }
  .page-box {
    z-index: 999;
    padding: 10px 24px;
    position: sticky;
    bottom: 0px;
    width: 100%;
    display: flex;
    justify-content: end;
    align-items: center;
    background: #ffffff;
  }

  ::v-deep .el-drawer {
    .el-drawer__body {
      .right-form {
        color: #0f0f13;
        .el-radio {
          color: inherit;
        }
        .el-select {
          width: 100%;
        }
        .el-checkbox {
          margin-left: 10px;
        }
      }
      .ip-form {
        .el-form-item__label {
          font-weight: 500;
        }
      }
      .protocol-list {
        width: 100%;
        height: 504px;
        padding-bottom: 60px;
        overflow-y: auto;
        .el-table {
          .handle-icon {
            cursor: pointer;
            margin: 0 10px;
            color: #4a97ff;
          }
        }
        .el-table__header-wrapper {
          display: none;
        }
        .el-table__row > td {
          padding: 10px 0;
        }
        .select-row {
          td {
            color: #116ef9 !important;
            font-weight: 600;
          }
        }
      }
      .form-ipport {
        .el-select__tags {
          max-width: 100%;
          flex-wrap: nowrap;
          overflow-x: auto;
        }
        // .el-select .el-input__inner {
        //   padding-right: inherit;
        // }
        // .el-input__suffix {
        //   display: none;
        // }
      }
      .form-btn {
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-left: 0 !important;
        margin-bottom: 0;
        position: sticky;
        bottom: 0;
        background-color: #ffffff;
        .submit {
          width: 78px;
          height: 32px;
          background-color: #116ef9;
          color: #fff;
          font-size: 14px;
        }
        .cancel {
          width: 78px;
          height: 32px;
          background-color: #fff;
          color: #0f0f13;
          font-size: 14px;
        }
      }
    }
  }

  ::v-deep .el-dialog__wrapper.upload-diglog {
    .el-dialog {
      width: 613px;
      height: 313px;
      box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.08);
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .el-dialog__header {
        width: 100%;
        height: 20px;
        padding: 12px 16px 0 16px;
        > div {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          > div:nth-of-type(1) {
            color: #0f0f13;
            font-size: 14px;
            margin-right: 10px;
          }
          > div:nth-of-type(2) {
            color: #116ef9;
            margin-right: auto;
            font-size: 8px;
            cursor: pointer;
          }
          > div:nth-of-type(3) {
            font-size: 14px;
            color: #2c2c35;
          }
        }
      }
      .el-dialog__body {
        padding: 0;
        display: flex;
        justify-content: center;
        .upload-demo {
          width: 517px;
          height: 192px;
          margin: 0;
          display: flex;
          justify-content: center;
          .upload-file-icon {
            width: 69px;
            height: 45px;
            margin-top: 50px;
          }
          .el-upload.el-upload--text {
            width: 100%;
            height: 100%;
            .el-upload-dragger {
              width: 100%;
              height: 100%;
              margin: 0;
              .el-upload__text {
                color: #0f0f13;
                em {
                  color: #116ef9;
                }
              }
              .text-maxsize {
                font-size: 10px;
                margin-top: 7px;
                color: #0f0f13;
              }
            }
          }
        }
        .upload-timing {
          .uploading-box {
            width: 517px;
            height: 76px;
            border: 1px solid #f2f3f7;
            box-sizing: border-box;
            border-radius: 8px;
            padding: 0 24px;
            padding-bottom: 16px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            position: relative;
            .el-progress-bar__outer {
              height: 6px !important;
            }
            > div:nth-of-type(1) {
              font-size: 14px;
              margin-bottom: 4px;
              padding-right: 50px;
              box-sizing: border-box;
              > span:nth-of-type(1) {
                color: #116ef9;
              }
              > span:nth-of-type(2) {
                color: #0f0f13;
              }
            }

            .success-icon {
              width: 38px;
              height: 38px;
              font-size: 38px;
              position: absolute;
              top: 8px;
              right: 8px;
            }
          }
          > div:nth-of-type(2) {
            margin-top: 8px;
            font-size: 10px;
          }
        }
      }
      .el-dialog__footer {
        padding: 0 16px 16px 16px;
        .cancel {
          width: 78px;
          height: 32px;
          background: #ffffff;
          border: 1px solid #f2f3f7;
          box-sizing: border-box;
          border-radius: 4px;
          color: #0f0f13;
          padding: 0;
        }
        .submit {
          width: 78px;
          height: 32px;
          background: #116ef9;
          border-radius: 4px;
          color: #ffffff;
          padding: 0;
        }
        .submit-d {
          width: 78px;
          height: 32px;
          background: #cecece;
          border-radius: 4px;
          color: #ffffff;
          padding: 0;
          border: 0;
          // cursor: not-allowed;
          pointer-events: none;
        }
      }
    }
  }
}
</style>
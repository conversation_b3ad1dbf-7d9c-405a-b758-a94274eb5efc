<template>
  <div>
    <el-button type="text" @click="tagLibVisible = true"> 标签库 </el-button>
    <TagView v-model="tagLibVisible" :is-add="isAdd" />
  </div>
</template>

<script>
import TagView from "@/components/TagView";
export default {
  components: {
    TagView,
  },
  data() {
    return {
      tagLibVisible: false,
      isAdd:true
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
</style>
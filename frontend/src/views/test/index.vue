<template>
  <div>
    <el-upload
      class="upload-demo"
      ref="upload"
      name="files"
      :multiple="true"
      :action="action"
      :on-change="handleFileChange"
      :before-remove="handleFileRemove"
      :auto-upload="false"
      :file-list="upload.fileList"
    >

      <el-button
        slot="trigger"
        size="small"
        type="primary"
      >选取文件</el-button>
      <el-button
        style="margin-left: 10px;"
        size="small"
        type="success"
        @click="submitFileForm"
      >上传到服务器</el-button>
      <div
        slot="tip"
        class="el-upload__tip"
      >只能上传jpg/png文件，且不超过500kb</div>
    </el-upload>
    <el-button @click="TEST_DOWNLOAD"></el-button>
  </div>
</template>
<script>
import axios from 'axios'
import { test_download } from '@/api/test'
import FileSaver from 'file-saver';

export default {
  data () {
    return {
      action: `${process.env.VUE_APP_BASE_API}/feature/list`,
      upload: {
        fileList: [],
        fileName: []
      },
    };
  },
  methods: {
    // 上传发生变化钩子
    handleFileChange (file, fileList) {
      this.upload.fileList = fileList;
    },
    // 删除之前钩子
    handleFileRemove (file, fileList) {
      this.upload.fileList = fileList;
    },
    // 提交上传文件
    submitFileForm () {
      // 创建新的数据对象
      let formData = new FormData();
      // 将上传的文件放到数据对象中
      this.upload.fileList.forEach(file => {
        formData.append('file', file.raw);
        this.upload.fileName.push(file.name);
      });
      console.log("提交前", formData.getAll('file'));

      // 文件名
      formData.append('fileName', this.upload.fileName);
      // 自定义上传
      this.uploadFile(formData).then(response => {
        console.log(response);
        // if(response.code == 200){
        //   this.$refs.upload.clearFiles();
        // }else{
        //   this.$message.error(response.msg);
        // }
      })
        .catch(error => {
          this.$message.error('上传失败！');
        });
    },
    // 封装的上传请求
    uploadFile (params) {
      return axios.post(`${process.env.VUE_APP_BASE_API}/feature/list`, params,
        { headers: { 'Content-Type': 'multipart/form-data' } })
    },
    TEST_DOWNLOAD () {
      axios({
        method: "POST",
        url: process.env.VUE_APP_BASE_API + "/feature/getCsv", // 后端下载接口地址
        responseType: "blob", // 设置接受的流格式
        data: {
          ids: [12, 13, 14, 15]
        }
      }).then(res => {
        console.log(res)
        const blob = new Blob([res.data], { type: "application/json" })
        FileSaver.saveAs(blob, res.headers["content-disposition"])
      })
    }
  }
}
</script>
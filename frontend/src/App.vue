<template>
  <div id="app">
    <keep-alive>
      <router-view v-if="this.$route.meta.keepAlive && routerAlive" />
    </keep-alive>
    <router-view v-if="!this.$route.meta.keepAlive && routerAlive" />
    <pcapdown />
    <ifonconduct />
  </div>
</template>

<script>
import pcapdown from './components/DownPcap/index.vue';
import ifonconduct from './components/IfonConduct/index.vue';

import request from '@/utils/request';
import Cookies from 'js-cookie';
const TokenKey = 'vue_admin_template_token';
import { cleandataSchedule, getdiskmode } from '@/api/SystemData/systemdata';

export default {
  name: 'App',
  provide () {
    return {
      reload: this.reload
    };
  },
  components: {
    pcapdown,
    ifonconduct
  },
  data () {
    return {
      ws: Object,
      routerAlive: true
    };
  },

  created () {
    console.warn('当前运行环境：', '分析平台：', process.env.NODE_ENV);
    this.GET_COMMAND();
    this.WS_INIT();
  },
  beforeDestroy () {
    this.ws.close();
  },
  methods: {
    // 无感刷新
    reload () {
      this.routerAlive = false;
      this.$nextTick(function () {
        this.routerAlive = true;
      });
    },

    // ws初始化
    WS_INIT () {
      let baseURL;
      if (process.env.VUE_APP_BASE_API === '') {
        const wscol = window.location.protocol === 'http:' ? 'ws' : 'wss';
        baseURL = `${wscol}://${window.location.host}/ws_api`;
      } else {
        baseURL = process.env.VUE_APP_BASE_WSS;
        // baseURL = `wss://**************/ws_api`
        // baseURL = "wss://***************/ws_api";
      }
      if ('WebSocket' in window) {
        // this.ws = new WebSocket('wss://*************:8080/ws_push')
        this.ws = new WebSocket(baseURL);
        this.ws.onopen = this.WS_OPEN;
        this.ws.onmessage = this.WS_MESSAGE;
        this.ws.onclose = this.WS_CLOSE;
        this.ws.onerror = this.WS_ERROR;
      } else {
        alert('您的浏览器不支持 WebSocket!');
      }
    },
    // ws初次建立连接
    WS_OPEN (e) {
      this.ws.send('planet');
      console.warn('当前连接状态：', this.ws.readyState);
    },
    // ws接收消息
    WS_MESSAGE (e) {
      const data = JSON.parse(e.data);
      switch (data.tableType) {
      case 0:
        this.$store.commit('long/update_tableType0', data);
        break;
      case 1:
        this.$store.commit('long/update_tableType1', data);
        break;
      case 4:
        this.$store.commit('long/update_tableType4', data);
        break;
      case 9:
        this.$store.commit('long/update_tableType9', data);
        break;
      case 10:
        this.$store.commit('long/update_tableType10', data);
        break;
      case 11:
        this.$store.commit('long/update_tableType11', data);
        break;
      case 12:
        this.$store.commit('long/update_tableType12', data);
        break;
      case 13:
        this.$store.commit('long/update_tableType13', data);
        break;
      case 17:
        this.$store.commit('long/update_tableType17', data);
        break;
      case 18:
        this.$store.commit('long/update_tableType18', data);
        break;
      case 19:
        this.$store.commit('long/update_tableType19', data);
        break;
      case 20:
        this.$store.commit('long/update_tableType20', data);
        break;
      case 21:
        this.$store.commit('long/update_tableType21', data);
        break;
      default:
        break;
      }
    },
    // WS关闭
    WS_CLOSE (e) {
      console.warn('当前连接状态：', this.ws.readyState);
    },
    // ws出错
    WS_ERROR (err) {
      console.error('当前连接状态：', this.ws.readyState);
    },
    // ws发送消息
    WS_SEND (content) {
      -this.ws.send(content);
    },
    // 获取磁盘清理进度
    cleanSchedule () {
      cleandataSchedule().then((res) => {
        this.$store.commit('ifonconduct/getdiskclaerstatus', res.data);
      });
    },
    // 获取标签库
    GETTAGS () {
      // eslint-disable-next-line eqeqeq
      if (this.$store.state.conversational.taglist01.length == 0) {
        request({
          url: '/session/tag/list',
          method: 'POST'
        })
          .then((res) => {
            // eslint-disable-next-line eqeqeq
            if (res.err == 0) {
              this.$store.commit('conversational/tagseachlist', res.data);
            } else {
              console.log('err:' + res.err + ',msg:' + res.msg);
            }
          })
          .catch();
      }
    },
    // 获取全局设置
    GET_COMMAND () {
      // 获取全局config
      const token = Cookies.get(TokenKey);
      if (token) {
        // 获取当前清理进度是否完成
        this.cleanSchedule();

        // 不管从哪里进入页面都要去获取知识库
        // this.GETDICT();
        this.GETTAGS();
      }
    }
  }
};
</script>
<style lang="scss">
.popperbox {
  padding: 0;
  width: 280px;
  overflow: auto;
  left: 198px !important;

  .el-input {
    margin-bottom: 5px;
    width: 248px;

    .el-input__inner {
      height: 32px !important;
    }
  }
}

.dialoguepopperbox {
  padding: 0;
  width: 280px;
  overflow: auto;

  .el-input {
    margin-bottom: 5px;
    width: 248px;

    .el-input__inner {
      height: 32px !important;
    }
  }
}

.sortpopover1 {
  padding: 10px !important;

  .sortbtn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .el-button {
      border: 0;
      margin: 0 !important;
      width: 150px;
      height: 32px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-weight: 400;
      font-size: 14px;
    }
  }
}

.moretag {
  padding: 0;
}

// ES字段检索中的下拉框样式
.thirdbox {
  .el-select-group__title {
    padding-left: 8px;
  }

  .el-select-group__title::after {
    content: '';
    position: absolute;
    display: block;
    left: 80px;
    right: 15px;
    top: 15.5px;
    border-bottom: 1px dashed #cecece;
  }

  .el-select-group__wrap:not(:last-of-type) {
    padding: 0;
  }

  .el-select-group__wrap:not(:last-of-type)::after {
    content: none;
  }
}
</style>


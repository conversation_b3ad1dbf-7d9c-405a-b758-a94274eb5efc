<template>
  <div class="recommend">
    <div class="handle">
      <div class="title">智能推荐标签</div>
      <div class="toggle" @click="isToggle = !isToggle">{{ isToggle?'收起':'展开' }}</div>
    </div>
    <el-table
      v-show="isToggle"
      ref="multipleTable"
      :data="tableData"
      style="width: 100%"
      border
      stripe
      row-key="tag_id"
      :header-cell-style="headerStyle"
      @select="singerSelect"
      @select-all="allSelect"
    >
      <el-table-column v-if="!checked" type="selection" width="55">
      </el-table-column>
      <el-table-column label="名称" prop="tag_text"> </el-table-column>
      <el-table-column prop="tag_explain" label="描述"> </el-table-column>
      <el-table-column prop="black_list" label="黑名单权重"> </el-table-column>
      <el-table-column prop="white_list" label="白名单权重"> </el-table-column>
      <el-table-column prop="use_num" label="标签使用次数"> </el-table-column>
    </el-table>
  </div>
</template>

<script>
import Mixins from "@/mixins";
import { recommendTag } from "@/api/TagList";
export default {
  name: "Recommend",
  mixins: [Mixins],
  props: {
    // 所有数据
    tagList: {
      type: Array,
      default: () => [],
    },
    isActive: {
      type: Boolean,
      default: false,
    },
    page: {
      type: Object,
      default: () => {},
    },
    checked: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      multipleSelection: [],
      tableData: [],
      isToggle: true,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      try {
        const res = await recommendTag();
        this.tableData = res.data;
        this.tableData.forEach((item) => {
          item.effect = "light";
        });
      } catch (error) {
        console.log(error);
      }
    },
    singerSelect(selection, row) {
      this.handleCommon([row]);
    },
    allSelect(data) {
      this.handleCommon(data);
    },
    handleCommon(data) {
      if (data.length) {
        this.$emit("tagSelect", data);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.recommend {
  padding: 16px;
}
.handle{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  color: #2C2C35;
  .toggle{
    cursor: pointer;
    color: #116ef9;
  }
}
</style>
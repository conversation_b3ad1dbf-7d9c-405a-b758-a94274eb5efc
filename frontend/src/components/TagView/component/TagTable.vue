<template>
  <el-table
    ref="multipleTable"
    :data="pageTable"
    style="width: 100%"
    height="100%"
    border
    stripe
    row-key="tag_id"
    :header-cell-style="headerStyle"
    @select="singerSelect"
    @select-all="allSelect"
  >
    <el-table-column v-if="!checked" type="selection" width="55" :reserve-selection="true"> </el-table-column>
    <el-table-column label="名称" prop="tag_text"> </el-table-column>
    <el-table-column prop="tag_remark" label="描述"> </el-table-column>
    <el-table-column prop="black_list" label="黑名单权重"> </el-table-column>
    <el-table-column prop="white_list" label="白名单权重"> </el-table-column>
  </el-table>
</template>

<script>
import Mixins from "@/mixins";
export default {
  name: "TagTable",
  mixins: [Mixins],
  props: {
    // 所有数据
    tagList: {
      type: Array,
      default: () => [],
    },
    isActive: {
      type: <PERSON>olean,
      default: false,
    },
    page:{
      type:Object,
      default:()=>{}
    },
    checked:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      multipleSelection: [],
    };
  },
  watch: {
    tagList:{
      handler(val){
        if(val.length){
          this.$nextTick(()=>{
            // 默认回显
            this.setSelectedRows();
          });
        }
      },
      deep:true,
      immediate:true
    }
  },
  methods: {
    singerSelect(selection, row) {
      this.$emit('handleSelectionChange',[row]);
    },
    allSelect(row,index) {
      if(row.length){
        this.tagList.forEach(item=>{
          item.effect =  "light";
        });
      }else{
        this.tagList.forEach(item=>{
          item.effect =  "dark";
        });
      }
      this.$emit('handleSelectionChange',this.tagList);
    },
    // 设置回显
    setSelectedRows() {
      this.$refs.multipleTable.clearSelection();
      // 遍历当前页数据，如果存在于selectedRows中，则设置为选中
      this.tagList.forEach(row => {
        if(row.effect==='dark'){
          this.$refs.multipleTable.toggleRowSelection(row, true);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
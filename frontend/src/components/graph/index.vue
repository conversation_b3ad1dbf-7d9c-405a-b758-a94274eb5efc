<template>
  <div v-loading="loading" class="newgraph">
    <div ref="mainbox" class="graph-box" @click="isShowNodeMenuPanel = false">
      <SeeksRelationGraph ref="seeksRelationGraph" :options="graphOptions">
        <div slot="node" slot-scope="{node}">
          <div v-if="node.data.type !== 'Folder' && node.data.type !== 'LABELS'"
               :class="NODE_CLASS(node.data.identity)"
          >
            <img :src="node.data.img" />
          </div>
          <div v-if="node.data.type === 'Folder'" class="folder-node">
            <img :src="node.data.img" />
          </div>
          <div v-if="node.data.type === 'LABELS'" class="labels-node">
            <section v-for="(item, index) of node.data.labels" :key="index" :class="LABEL_LV(item)">
              {{ item.tagText }}
            </section>
          </div>
          <div v-if="node.data.type !== 'LABELS'" class="node-label">{{ node.data.label }}</div>
        </div>
      </SeeksRelationGraph>
    </div>
    <div class="back" @click="BACK">
      <img src="../../assets/graph/icon_16_enter.svg" alt="">
    </div>
  </div>
</template>

<script>
import SeeksRelationGraph from 'relation-graph';
import { formatGraphData } from '../../views/newgraph/fmtData';
import { get_data, get_next, get_edge_json, alarm_info, alarm_judge } from '@/api/graph';
export default {
  components: { SeeksRelationGraph },
  // eslint-disable-next-line vue/require-prop-types
  props: ['initValue', 'initType', 'initId', 'initIndex', 'initTitle'],
  data () {
    return {
      loading: false,
      isShowNodeMenuPanel: false,
      zoomToFitWhenRefresh:true,
      nodeMenuPanelPosition: { x: 0, y: 0 },
      currentNode: '',
      graphOptions: {
        // 这里可以参考"Graph 图谱"中的参数进行设置
        g_loading: true,
        demoname: '---',
        defaultNodeBorderWidth: 0,
        defaultNodeColor: 'rgba(238, 178, 94, 1)',
        defaultLineColor: '#000',
        allowSwitchLineShape: true,
        nodeShape:1,
        defaultLineShape: 1,
        layouts: [
          {
            label: '力导',
            layoutName: 'center',
          }
        ],
        defaultJunctionPoint: 'border',
        defaultLineMarker: {
          "markerWidth": 20,
          "markerHeight": 20,
          "refX": 3,
          "refY": 3,
          "data": "M 0 0, V 6, L 4 3, Z"
        },
        isMoveByParentNode: false,
        hideNodeContentByZoom: false,
        allowShowMiniToolBar: false
      },
      backQuery: {}
    };
  },
  computed: {
   
  },
  created () {
    if (this.initTitle) {
      this.title = this.initTitle;
    }
    this.INIT_GRAPH();
  },
  methods: {
    INIT_GRAPH () {
      let data = {};
      if (this.initValue && this.initType) {
        // 入参为：值+类型时的请求
        data = {
          str: this.initValue,
          type: this.initType
        };
        this.backQuery = {
          initValue: this.initValue,
          initType: this.initType
        };
        get_data(data).then(res => {
          if (res.err === 0 && res.data.vertex.length > 0) {
            this.$refs.seeksRelationGraph.setJsonData(formatGraphData(res.data), (seeksRGGraph) => {
              // 关系图渲染完成时会调用
              this.$message.success('图谱渲染完成');
              console.warn('渲染完成：', seeksRGGraph);
              this.$nextTick(() => { // $nextTick的功能你懂的
                // this.$refs.seeksRelationGraph.onGraphResize();
                // this.$refs.seeksRelationGraph.refresh();
              });
              this.loading = false;
            });
          }
        }).catch(err => {
          this.loading = false;
        });
      } else if (this.initId && this.initIndex) {
        // 入参为告警ID时的请求，多为告警研判用
        this.backQuery = {
          initId: this.initId,
          initIndex: this.initIndex
        };
        alarm_judge({
          alarm_id: this.initId,
          alarm_index: this.initIndex
        }).then(res => {
          if (res.err === 0 && res.data.vertex.length > 0) {
            this.$refs.seeksRelationGraph.setJsonData(formatGraphData(res.data), (seeksRGGraph) => {
              // 关系图渲染完成时会调用
              this.$message.success('图谱渲染完成');
              console.warn('渲染完成：', seeksRGGraph);
              // this.$refs.seeksRelationGraph.refresh()
              // this.$refs.seeksRelationGraph.zoom(50)
              this.$nextTick(() => {
                // this.$refs.seeksRelationGraph.refresh();
              });
              this.loading = false;
            });
          }
        });
      }
    },
    // 大小自适应
    GRAPH_SIZE() { 
      return {
        width: 736,
        height:500
      };
    },
    // 标签格式化
    LABEL_LV (item) {
      let type = 'info';
      if (
        item.blackList >= 1 &&
                item.blackList <= 100 &&
                item.whiteList !== 100
      ) {
        if (item.blackList >= 80) {
          type = "danger";
        } else {
          type = "warning";
        }
      }
      if (
        item.whiteList >= 1 &&
                item.whiteList <= 100 &&
                item.blackList === 0
      ) {
        if (item.whiteList === 100) {
          type = "success";
        } else {
          type = "";
        }
      }
      if (item.whiteList === 0 && item.blackList === 0) {
        type = "info";
      }
      return type;
    },
    BACK () {
      const routeData = this.$router.resolve({
        path: '/newgraph',
        query: this.backQuery
      });
      window.open(routeData.href, '_blank');
    },
    // 节点class判断
    NODE_CLASS (identity) {
      switch (identity) {
      case 'attacker':
        return 'attacker-node';
      case 'victim':
        return 'victim-node';
      default:
        return 'node';
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.newgraph {
    width: 100%;
    height: 100%;
    position: relative;
    background: #eef0f5;
    border-radius: 8px;

    .header {
        width: 100%;
        height: 32px;
        position: relative;
        top: 16px;
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        z-index: 2000;

        .tool {
            height: 100%;
            display: flex;
            align-items: center;

            .zoom {
                width: 108px;
                height: 100%;
                border-right: 2px solid #eef0f5;
                display: flex;
                justify-content: space-around;
                align-items: center;

                >img {
                    width: 16px;
                    height: 16px;
                    cursor: pointer;
                }
            }

            .full {
                width: 32px;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                border-right: 2px solid #eef0f5;

                >img {
                    cursor: pointer;
                }
            }

            .layout {
                height: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-left: 10px;
                padding-right: 10px;
                border-right: 2px solid #eef0f5;

                >div:nth-of-type(1) {
                    margin-left: 4px;
                }
            }
        }

        .search {
            display: flex;
            align-items: center;

            .full {
                width: 32px;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 10px;

                >img {
                    cursor: pointer;
                }
            }

            ::v-deep .el-input__inner {
                width: 452px;
                height: 32px;
                background: #ffffff;
                border: 1px solid #cecece;
                border-radius: 4px;
            }

            .el-button {
                background: #116ef9;
                border-radius: 4px;
                width: 88px;
                height: 32px;
                color: #ffffff;
                padding: 0;
                font-size: 14px;
                margin: 0 8px;
            }

            .el-button.clear {
                width: 60px;
                background: #ffffff;
                color: #2c2c35;
                border: 1px solid #cecece;
                border-radius: 4px;
                cursor: pointer;
            }

            .el-button.clear:hover {
                background-color: #b3d8ff;
            }

            .el-textarea {
                width: 290px;
            }

            ::v-deep .el-form-item__content {
                line-height: inherit;
            }
        }
    }

    .graph-box {
        width: 100%;
        height: 100%;
        background: #eef0f5;
    }

    ::v-deep .rel-map {
        background: #eef0f5;
    }

    .type-box {
        position: absolute;
        left: 18px;
        bottom: 1px;

        &-cell {
            width: 105px;
            height: 20px;
            margin-bottom: 16px;
            font-size: 12px;
            color: #2c2c35;
            display: flex;
            align-items: center;
            cursor: context-menu;

            >div:nth-of-type(1) {
                width: 20px;
                height: 20px;
                margin-right: 6px;

                >img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }
    }
}

::v-deep .rel-node-shape-0:hover {
    box-shadow: none;
}

::v-deep .rel-node-shape-0 {
    width: auto;
    height: auto;
    padding: 0;
    background-color: transparent !important;

    .node {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #fff;
        border: 1px solid #767684;
        display: flex;
        justify-content: center;
        align-items: center;

        >img {
            width: 24px;
            height: 24px;
        }
    }

    .attacker-node {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #F91111;
        display: flex;
        justify-content: center;
        align-items: center;

        >img {
            width: 24px;
            height: 24px;
        }
    }

    .victim-node {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #116ef9;
        display: flex;
        justify-content: center;
        align-items: center;

        >img {
            width: 24px;
            height: 24px;
        }
    }

    .folder-node {
        width: 42px;
        height: 32px;

        >img {
            width: 100%;
            height: 100%;
        }
    }

    .labels-node {
        max-width: 230px;
        min-width: 100px;
        min-height: 36px;
        background: #ffffff;
        max-height: 100px;
        overflow-y: auto;
        border: 1px dashed #dee0e7;
        border-radius: 4px;
        padding: 8px;
        padding-bottom: 4px;
        display: flex;
        flex-wrap: wrap;
        position: relative;
        cursor: pointer;
        white-space: nowrap;
        overflow-x: hidden;
        box-sizing: border-box;

        >section {
            height: 20px;
            line-height: 20px;
            background: #eef0f5;
            border-radius: 2px;
            font-size: 12px;
            color: #2c2c35;
            margin-right: 4px;
            margin-bottom: 4px;
            padding: 0 4px;
        }

        .del {
            width: 24px;
            height: 24px;
            line-height: 24px;
            border-radius: 12px;
            background: #ffffff;
            border: 1px solid #dee0e7;
            display: none;
            position: absolute;
            right: -12px;
            top: -12px;

            >i {
                width: 100%;
                height: 100%;
                color: #000;
            }
        }

        .danger {
            background: #fce7e7;
            color: #a41818;
        }

        .warning {
            background: #f9eddf;
            color: #b76f1e;
        }

        .success {
            background: #e7f0fe;
            color: #1b428d;
        }
    }

    .labels-node:hover {
        .del {
            display: inline-block;
        }
    }

    .node-label {
        width: 80px;
        min-height: 20px;
        position: absolute;
        text-align: center;
        left: -10px;
        bottom: -14px;
        font-size: 12px;
        color: #2c2c35;
        overflow: hidden;
        /*隐藏*/
        white-space: nowrap;
        /*不换行*/
        text-overflow: ellipsis;
        /* 超出部分省略号 */
    }
}

.clickmenu {
    width: 112px;
    padding: 8px 0;
    display: flex;
    flex-direction: column;
    z-index: 9999;
    position: absolute;
    background: #ffffff;
    box-shadow: 0px 6px 18px rgba(45, 47, 51, 0.14);
    border-radius: 4px;

    >section {
        width: 100%;
        height: 32px;
        line-height: 32px;
        padding: 0 8px;
        cursor: pointer;
        font-size: 14px;
        color: 2c2c35;
    }

    >section:nth-of-type(3) {
        color: #f91111;
    }

    >section:hover {
        background: #e7f0fe;
    }
}

// 关联弹出框的样式
::v-deep .el-dialog__body {
    max-height: 650px;
    overflow-y: auto;
}

::v-deep .el-dialog__footer {
    border-top: 1px solid #f2f3f7;
    padding: 12px 24px;

    .el-button--primary {
        background-color: #116ef9;
        border-color: #116ef9;
    }
}

::v-deep .el-checkbox__label {
    font-size: 14px;
    color: #2c2c35;
}

::v-deep.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
}

.el-button--primary.is-disabled,
.el-button--primary.is-disabled:active,
.el-button--primary.is-disabled:focus,
.el-button--primary.is-disabled:hover {
    color: #fff;
    background-color: #a0cfff;
    border-color: #a0cfff;
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
}

::v-deep .el-checkbox-group {
    display: flex;
    flex-direction: column;
    font-size: 14px;

    .check-cell {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        // .el-input-number__decrease,.el-input-number__increase{
        //   width: 20px;
        //   height: 22px;
        // }
        // .el-input--mini{
        //   width: 75px;
        //   height: 22px;
        //   .el-input--mini{
        //     height: 22px;
        //     line-height: 22px;
        //   }
        // }
    }

    >label {
        margin-bottom: 12px;
        color: #2c2c35;
    }

    .el-checkbox__label {
        color: #2c2c35;
    }
}

::v-deep .rela-dialog {
    .el-dialog {
        margin-top: 5vh !important;

        .el-dialog__footer {
            position: sticky;
            bottom: 0;
            z-index: 5000;
            background-color: #fff;
        }
    }
}

::v-deep .c-mini-toolbar>div:nth-of-type(1) {
    display: none;
}

.back {
    position: absolute;
    width: 32px;
    height: 32px;
    right: 16px;
    top: 16px;
    background: #ffffff;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 800;
}
</style>
<template>
  <div class="dynamic-remark">
    <label class="dynamic-remark-label">
      <i v-if="icon" class="iconfont" :class="icon" />
      <span>{{ label }}</span>
    </label>
    <div class="value">
      <el-tag
        v-for="(tag, index) in dynamicTags"
        :key="tag + index"
        closable
        size="small"
        :disable-transitions="false"
        @close="handleClose(tag)"
      >
        {{ tag }}
      </el-tag>
      <el-input
        v-if="inputVisible"
        ref="saveTagInput"
        v-model="inputValue"
        class="input-new-tag"
        size="small"
        @keyup.enter.native="handleInputConfirm"
        @blur="handleInputConfirm"
      />
      <el-button v-else size="small" type="text" class="button-new-tag" @click="showInput">
        + 添加备注
      </el-button>
    </div>
    <!-- <div class="isFold">
      展开
    </div> -->
  </div>
</template>

<script>
export default {
  name: "DynamicRemark",
  props: {
    label: {
      type: String,
      default: "备注 ",
    },
    icon: {
      type: String,
      default: "",
    },
    isDetail:{
      type: Boolean,
      default: false
    },
    remarks:{
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dynamicTags: [],
      inputVisible: false,
      inputValue: "",
      isFold:false, // 是否展开
    };
  },
  watch: {
    remarks:{
      handler(val){
        this.dynamicTags = val;
      },
      deep: true,
      immediate:true
    }
  },
  methods: {
    // 删除
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
      this.isDetail&&this.$emit("handleRemark", this.dynamicTags);
    },
    // 点击条件备注并获取焦点
    showInput() {
      this.inputVisible = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    // 失焦和回车
    handleInputConfirm() {
      const inputValue = this.inputValue;
      if (inputValue) {
        this.dynamicTags.push(inputValue);
      }
      this.inputVisible = false;
      this.inputValue = "";
      this.isDetail&&this.$emit("handleRemark", this.dynamicTags);
    },
  },
};
</script>

<style lang="scss" scoped>
.dynamic-remark {
  // line-height: 32px;
  font-size: 14px;
  display: flex;
  align-items: center;
  &-label {
    width: 64px;
    color: #767684;
    .iconfont {
      margin-right: 4px;
    }
  }
  .value{
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    align-items: center;   
  }
}
.el-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}
.button-new-tag {
  margin-left:6px;
  height: 32px;
  padding-top: 0;
  padding-bottom: 6px;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>

<template>
  <div class="all">
    <div v-if="isShowAside" class="all-tree">
      <div class="all-tree-list">
        <el-tree
          :data="isLibaryType ? listArr : treeData"
          :props="defaultProps"
          accordion
          :indent="2"
          highlight-current
          @node-click="handleNodeClick"
        >
          <template #default="{ data }">
            <el-tooltip
              v-if="data.label.length > 8"
              class="custom-tooltip"
              effect="dark"
              :content="data.label"
              placement="top"
            >
              <span class="custom-label">{{ truncateLabel(data.label) }}</span>
            </el-tooltip>
            <span v-else>{{ data.label }}</span>
          </template>
        </el-tree>
      </div>
    </div>
    <div class="all-content">
      <div v-loading="loading" class="all-content-list">
        <div class="top">
          <div class="top-form">
            <el-form
              ref="formRef"
              :model="form"
              :inline="true"
              @submit.native.prevent
            >
              <el-form-item>
                <el-input
                  v-model="form.tag_text"
                  suffix-icon="el-icon-search"
                  placeholder="搜索标签名"
                  @change="handleSearch"
                >
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-dropdown
                  ref="dropdownRef"
                  size="small"
                  split-button
                  placement="bottom-end"
                  @visible-change="changeVisible"
                >
                  权重
                  <el-dropdown-menu slot="dropdown">
                    <TagFilter v-show="flag" @handleSave="handleSave" />
                  </el-dropdown-menu>
                </el-dropdown>
              </el-form-item>
            </el-form>
          </div>
          <div class="top-toggle">
            <Tab v-model="activeIndex" :tab-data="tabData" />
          </div>
        </div>
        <div v-if="hasQuery" class="query">
          <TagQuery :query-data="showQuery" />
          <el-checkbox
            v-if="tableData.length"
            v-model="checked"
            size="small"
            @change="changeAll"
          >
            全选
          </el-checkbox>
        </div>
        <div class="page">
          <el-pagination
            v-if="!isList"
            :current-page="page.current_page"
            :page-sizes="sizes"
            :page-size="page.page_size"
            background
            :total="total"
            small
            layout="total,prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
        <div class="center">
          <component
            :is="componentId"
            :checked="checked"
            :tag-list="tableData"
            :page="page"
            @handleSelectionChange="handleSelectionChange"
            @tagSelect="tagSelect"
          ></component>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TagFilter from "./component/TagFilter.vue";
import Tab from "./component/Tab.vue";
import TagList from "./component/TagList.vue";
import TagTable from "./component/TagTable.vue";
import pagiNation from "@/mixins/pagiNation";
import TagQuery from "./component/TagQuery.vue";
import { getTag } from "@/api/import";
import { analysisTagSearch } from "@/api/TagList";
import {
  TREE_DATA as treeData,
  tagsType,
  tagsAttribute,
  TAG_COLOR,
} from "./js/constans";
export default {
  name: "All",
  components: {
    TagFilter,
    Tab,
    TagList,
    TagTable,
    TagQuery,
  },
  mixins: [pagiNation],
  props: {
    selectTags: {
      type: Array,
      default: () => [],
    },
    // 默认证书
    isLibaryType: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否展示测边栏
     */
    isShowAside: {
      type: Boolean,
      default: true,
    },
    /**
     * 标签类型
     */
    tag_target_type: {
      type: Number,
    },
    /**
     * 标签是否可删除标识
     */
    isDel:{
      type:Boolean,
      default:true
    }
  },
  data() {
    return {
      treeData,
      isIndeterminate: false,
      defaultProps: {
        children: "children",
        label: "label",
      },
      flag: true,
      tabData: [
        { key: "TagList", icon: "icon-a-16_card" },
        { key: "TagTable", icon: "icon-a-16_list2" },
      ],
      activeIndex: 0,
      form: {
        tag_text: "", // 查询标签条件（标签名,如果不填传空字符串 可模糊查询）
        attr_type: 0,
        search_type: "all",
        black_weight: [0, 100], // 黑名单权值范围
        white_weight: [0, 100], // 白名单权值范围
        tag_target_type: 3, // 默认查询域名
        tag_attribute_id: null,
      },
      tableData: [],
      loading: false,
      hasQuery: false,
      checked: false,
      targetArr: [],
      attributeArr: [],
      listArr: [],
    };
  },
  computed: {
    componentId() {
      return this.tabData[this.activeIndex].key;
    },
    // 是否为列表
    isList() {
      return this.componentId === "TagList";
    }
  },
  watch: {
    activeIndex: {
      handler() {
        this.handleSearch();
      },
      immediate: true,
    },
  },
  mounted(){
    this.analyseData( tagsType, "targetArr");
    this.analyseData( tagsAttribute, "attributeArr");
    this.listArr = [...this.targetArr, ...this.attributeArr];
  },
  methods: {
    truncateLabel(label) {
      const maxLength = 10; // 设置最大长度
      if (label.length > maxLength) {
        return label.substring(0, maxLength) + "...";
      }
      return label;
    },
    handleNodeClick({attr_type,tag_attribute_id }) {
      const isLibaryType = this.isLibaryType;
      this.loading = true;
      if (isLibaryType) {
        this.form.tag_target_type = tag_attribute_id ? '' : attr_type;
        this.form.tag_attribute_id = tag_attribute_id || null;
        this.getAnalyseList();
      } else {
        this.form.attr_type = attr_type;
        this.getList();
      }
    },
    // 查询
    handleSearch() {
      this.page.current_page = 1;
      this.searchQuery();
      this.isQuery();
      this.loading = true;
      (!this.isShowAside||this.isLibaryType) ? this.getAnalyseList() : this.getList();
    },
    changeVisible(val) {
      this.flag = val;
    },
    // 权重
    handleSave(val) {
      this.flag = false;
      if (!val) return;
      let { bStart, bEnd, hStart, hEnd } = val;
      this.form.white_weight = [bStart, bEnd];
      this.form.black_weight = [hStart, hEnd];
      this.handleSearch();
    },
    // 是否展示查询条件
    isQuery() {
      const { form } = this;
      const hasTagText = Boolean(form.tag_text);
      const areWeightsDefault = () => {
        return (
          JSON.stringify(form.black_weight) === JSON.stringify([0, 100]) &&
          JSON.stringify(form.white_weight) === JSON.stringify([0, 100])
        );
      };
      this.hasQuery = hasTagText || !areWeightsDefault();
    },
    // 获取标签列表数据
    async getAnalyseList() {
      try {
        this.targetArr = [];
        this.attributeArr = [];
        const params = { ...this.form, ...this.page, };
        delete params.attr_type;
        // 详情只展示该类型的标签
        if(!this.isShowAside){
          params.tag_target_type=this.tag_target_type;
          params.tag_attribute_id=null;
        }
        /**
         * @target {标签类型} tagsType
         * @attribute {细分类果询} tagsAttribute
         */
        const {
          data: {
            count_map: { attribute, target },
            tag_data,
            total
          },
        } = await analysisTagSearch(params);
        this.total=total;
        tag_data.forEach((item) => {
          item.level = TAG_COLOR[item.tag_level];
          item.effect = "light";
        });  // 标签排序
        this.tableData = tag_data.sort((a, b) => a.level - b.level);
        // 标签回显
        this.$nextTick(() => {
          if (this.selectTags.length) {
            this.selectTags.forEach((tagLabel) => {
              this.tableData.forEach((item) => {
                if (item.tag_id === tagLabel.tag_id) {
                  item.effect = "dark";
                }
              });
            });
          }
        });
        this.timer = setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        this.loading = false;
      }
    },
    // 获取标签列表数据
    async getList() {
      this.loading = true;
      try {
        const params = {
          ...this.page,
          ...this.form,
        };
        delete params.tag_target_type;
        delete params.tag_attribute_id;
        // 标签是否可删除,不能删除就要过滤数据
        if(!this.isDel){
          params.tag_status=true;
        }
        const { data } = await getTag(params);
        this.total = data.total;
        data.data.forEach((item) => {
          item.level = TAG_COLOR[item.tag_level];
          item.effect = "light";
        });
        // 标签排序
        this.tableData = data.data.sort((a, b) => a.level - b.level);
        // 标签回显
        this.$nextTick(() => {
          if (this.selectTags.length) {
            this.selectTags.forEach((tagLabel) => {
              this.tableData.forEach((item) => {
                if (item.tag_id === tagLabel.tag_id) {
                  item.effect = "dark";
                }
              });
            });
          }
        });
        this.timer = setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        this.loading = false;
      }
    },
    /**
     * @param {Object} names
     * @param {Object} keys
     * @param {Array} arr
     */
    analyseData( keys, val) {
      for (const key in keys) {
        if (Object.hasOwnProperty.call(keys, key)) {
          this[val].push({
            label: key,
            attr_type: keys[key],
            tag_attribute_id: val === "attributeArr" ? keys[key] : null,
          });
        }
      }
    },
    // 查询条件参数
    searchQuery() {
      this.showQuery = [];
      if (this.form.tag_text) {
        this.showQuery.push({
          label: "搜索",
          content: this.form.tag_text,
        });
      }
      if (this.form.black_weight.length) {
        this.showQuery.push({
          label: "黑名单权重",
          content: this.form.black_weight,
        });
      }
      if (this.form.white_weight.length) {
        this.showQuery.push({
          label: "白名单权重",
          content: this.form.white_weight,
        });
      }
    },
    // table选择
    handleSelectionChange(val) {
      this.handleSelectTags(val);
    },
    // 单选
    tagSelect(item) {
      this.handleSelectTags([item]);
    },
    // 全选
    changeAll(val) {
      this.tableData.forEach((item) => {
        item.effect = this.checked ? "light" : "dark";
      });
      this.handleSelectTags(this.tableData);
    },
    handleSelectTags(data) {
      this.$emit("tagSelect", data);
    },
    reset() {
      this.form.tag_text = ""; // 查询标签条件（标签名,如果不填传空字符串 可模糊查询）
      this.form.search_type = "all";
      this.form.black_weight = [0, 100]; // 黑名单权值范围
      this.form.white_weight = [0, 100]; // 白名单权值范围
      this.form.attr_type = 0;
      this.isQuery();
    },
  },
};
</script>

<style lang="scss" scoped>
.all {
  display: flex;
  height: 100%;
  &-tree,
  &-content {
    height: 100%;
    overflow: auto;
  }
  &-tree {
    width: 160px;
    background: #ccc;
    &-list {
      height: 100%;
      border-right: 1px solid #f2f3f7;
      ::v-deep .el-tree {
        height: 100%;
      }
    }
  }
  &-content {
    height: 100%;
    flex: 1;
    &-list {
      padding: 12px;
      box-sizing: border-box;
      height: 100%;
      .top {
        height: 54px;
        display: flex;
        justify-content: space-between;
      }
      .page {
        margin-bottom: 6px;
      }
      .center {
        height: 80%;
      }
    }
  }
}
.custom-label {
  white-space: nowrap; /* 防止换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 使用省略号表示截断 */
}
::v-deep
  .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  background-color: "#E7F0FEF";
}
</style>
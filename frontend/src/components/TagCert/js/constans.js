export const TREE_DATA=[
  {
    label: "证书标签",
    attr_type: 0,
    children: [
      {
        label: "证书字段内容相关",
        attr_type: 1,
      },
      {
        label: "证书签发关系相关",
        attr_type: 2,
      },
      {
        label: "证书功能相关",
        attr_type: 3,
      },
      {
        label: "证书有效期相关",
        attr_type: 4,
      },
      {
        label: "证书所属网络行为相关",
        attr_type: 5,
      },
      {
        label: "证书信任关系相关",
        attr_type: 6,
      },
      {
        label: "证书安全性相关",
        attr_type: 7,
      },
      {
        label: "用户自定义标签",
        attr_type: 8,
      },
    ],
  },
];
// 标签类型
export const tagsType = {
  IP: 0,
  域名: 3,
  证书: 4,
  会话: 6,
  指纹: 7,
  全部: 9999,
};
// 细分类果询
export const tagsAttribute = {
  威胁: 1,
  功能描述: 2,
  合法性: 3,
  行为描述: 4,
  APT: 5,
  远程控制: 6,
  基础属性: 7,
  指纹描述: 8,
  代理: 9,
  加密流量监测: 10,
  无细分类:11
};
// 标签
// 标签颜色
export const TAG_COLOR = {
  danger: 0,
  warning: 1,
  success: 2,
  positive: 3,
  info: 4,
};

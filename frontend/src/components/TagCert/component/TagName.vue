<template>
  <el-tag :closable="closable" :effect="item.effect" size="mini" :type="item.tag_level" class="item" @close="clear">
    {{ item.tag_text }}
  </el-tag>
</template>

<script>
export default {
  name: "TagName",
  props: {
    item: {
      type: Object,
      default: () => {},
    },
    closable:{
      type: Boolean,
      default: false,
    },
  },
  methods: {
    clear() {
      if(this.closable){
        this.$emit('clear',this.item);
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.item {
  margin-right: 8px;
  margin-bottom: 8px;
}
.el-tag {
  &:hover {
    cursor: pointer;
  }
}
</style>
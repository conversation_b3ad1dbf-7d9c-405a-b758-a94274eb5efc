<template>
  <!-- <div v-if="showConfirm">
    <div class="dlsDialog">
      <div class="dialog-title dls-b">
        {{ content.title }}
      </div>
      <div class="dialog-content">
        <div>
          {{content.message}}
        </div>
      </div>
      <div class="dialog-footer">
        <div
          class="but cancelBut"
          @click="close()"
        >取消</div>
        <div
          class="but confirmBut"
          @click="confirm"
        >确定</div>
      </div>
    </div>
    <div
      class="dialog-wrapper"
      @click.stop="showConfirm = false"
    ></div>
  </div> -->
  <div v-if="showConfirm">
    <div class="notice">
      <section class="title-box">
        <div>{{content.title}}</div>
        <div></div>
      </section>
      <section class="content-box">
        <div
          v-if="content.type === 'error'"
          class="svg-error"
        >
          <svg-icon icon-class="notice-error"></svg-icon>
        </div>
        <div
          v-else
          class="svg-warn"
        >
          <svg-icon icon-class="notice-warn"></svg-icon>
        </div>
        <div class="message">{{content.message}}</div>
      </section>
      <section class="footer-box">
        <div
          class="cancel"
          @click="close"
        >取消</div>
        <div
          class="submit"
          @click="confirm"
        >确定</div>
      </section>
    </div>
    <div
      class="dialog-wrapper"
      @click.stop="showConfirm = false"
    ></div>
  </div>
</template>

<script>
export default {
  name: 'DlsConfirm',
  data () {
    return {
      showConfirm: false,
      content: {
        title: '提示',
        message: '',
        type: 'warn'
      }
    }
  },
  methods: {
    confirm () {
    },
    close () {
    }
  }
}
</script>

<style lang="scss" scoped>
.notice {
  height: 194px;
  width: 515px;
  background: #ffffff;
  box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  z-index: 6001;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  padding: 12px 16px 16px 16px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .title-box {
    width: 100%;
    height: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
  }
  .content-box {
    width: 100%;
    height: 88px;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .svg-warn {
      width: 62px;
      height: 62px;
      font-size: 80px;
    }
    .svg-error {
      width: 62px;
      height: 62px;
      font-size: 80px;
    }
    .message {
      width: 330px;
      font-size: 14px;
    }
  }
  .footer-box {
    width: 100%;
    height: 32px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .cancel {
      width: 78px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      background: #ffffff;
      border: 1px solid #f2f3f7;
      box-sizing: border-box;
      border-radius: 4px;
      font-size: 14px;
      color: #0f0f13;
      cursor: pointer;
      margin-right: 8px;
    }
    .submit {
      width: 78px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      background: #116ef9;
      border: 1px solid #f2f3f7;
      box-sizing: border-box;
      border-radius: 4px;
      font-size: 14px;
      color: #ffffff;
      cursor: pointer;
    }
  }
}
.dialog-wrapper {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: #2C2C35;
  opacity: 0.7;
  z-index: 6000;
}
</style>
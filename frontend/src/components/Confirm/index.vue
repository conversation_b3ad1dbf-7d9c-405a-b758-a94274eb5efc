<template>
  <el-dialog :visible.sync="isShow" :width="width" :show-close="false">
    <div slot="title" class="dialog-header">
      <div class="dialog-header-left">
        <span class="iconfont icon-a-16_alert"></span>
        <span class="title">{{ title }}</span>
      </div>
      <span class="iconfont icon-a-16_close"></span>
    </div>
    <div class="dialog-content">
      {{ content }}
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="isShow = false">取 消</el-button>
      <el-button type="primary" @click="$emit('confirm')">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "Confirm",
  props: {
    width: {
      type: String,
      default: "480px",
    },
    value: {
      type: Boolean,
      default: false,
    },
    title:{
      type:String,
      default:'提醒'
    },
    content:{
      type:String,
      default:'内容'
    }
  },
  computed: {
    isShow: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog{
    .el-dialog__header,.el-dialog__body{
      padding: 16px;  
    }
    .el-dialog__footer{
        border-top: 1px solid #F2F3F7;
        padding: 12px 16px;
    }
    .dialog-header{
        display: flex;
        align-items: center;
        justify-content: space-between;
        &-left{
            display: flex;
            align-items: center;
          .title{
            font-size: 14px;
            font-weight: 600;
            color: #2C2C35;
            margin-left: 4px;
          }
          .icon-a-16_alert{
            color: red;
            font-size: 14px;
          }
        }
    }
    .dialog-content{}
    .dialog-footer{}
}
</style>
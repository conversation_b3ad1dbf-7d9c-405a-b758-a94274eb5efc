<template>
  <div class="info">
    <div class="label">
      <i
        v-if="icon"
        class="iconfont"
        :class="icon"
      />
      <span>{{ label }}</span>
    </div>
    <div class="value">
      <slot>{{ value||'-' }}</slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShowInfo',
  props: {
    label: {
      type: String,
      default: 'label'
    },
    value: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    }
  }
};
</script>

<style lang="scss" scoped>
.info {
  display: flex;
  .label{
    color: #767684;
    line-height: 22px;
    width: 64px;
    .iconfont{
      margin-right: 4px;
    }
  }
  .value{
    color: #2C2C35;
    line-height: 22px;
    flex: 1;
  }
}
</style>

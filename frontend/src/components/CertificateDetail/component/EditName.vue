<template>
  <el-dialog title="重命名" :visible.sync="isShow" width="480px" append-to-body>
    <el-form :model="editRow">
      <el-form-item>
        <el-input v-model.trim="form.value"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="isShow = false"> 取 消 </el-button>
      <el-button :loading="formLoading" type="primary" @click="handleConfirm">
        确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import Mixins from "@/mixins";
import { editLogTemplate } from "@/api/import";
export default {
  name: "EditName",
  mixins: [Mixins],
  props: {
    editRow: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      form: {
        value: "",
        user_id: 1,
      },
      formLoading: false,
    };
  },
  watch: {
    editRow: {
      handler(val) {
        if (val.name) {
          // this.form.value = val.name.slice(1, val.name.length);
          this.form.value = val.name.slice(
            val.isFather ? 1 : 0,
            val.name.length
          );
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 确认
    async handleConfirm() {
      try {
        this.formLoading = true;
        this.isShow = false;
        this.formLoading = false;
        const params = {
          ...this.form,
        };
        if (this.editRow.isFather) {
          params.value = "-" + params.value;
          params.key = this.editRow.allKey;
        }else{
          params.key = this.editRow.describe;
        }
        await editLogTemplate(params);
        this.$message.success("操作成功");
        this.$emit("handleEditLogTemplate");
      } catch (error) {
        this.formLoading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
<template>
  <div class="tab">
    <div
      v-for="(item, index) in tabData"
      :key="index"
      class="tab-item"
      :class="index === activeIndex ? 'active' : ''"
      @click="activeIndex = index"
    >
      {{ item.label }}
    </div>
  </div>
</template>

<script>
export default {
  name: "Tab",
  props: {
    tabData: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    activeIndex: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
};
</script>

<style lang="scss" scoped>
.tab {
  border-bottom: 1px solid #f2f3f7;
  display: flex;
  align-items: center;
  &-item {
    margin: 16px;
    cursor: pointer;
    transition: all 1s;
    font-size: 14px;
  }
  .active {
    box-sizing: border-box;
    color: #116ef9;
    position: relative;
    font-weight: 600;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      bottom: -17px;
      width: 40px;
      height: 2px;
      background: #116ef9;
    }
  }
}
</style>

.downdrawer {
  .querybox {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  .foot {
    width: 1000px;
    height: 55px;
    background: #ffffff;
    box-shadow: 0px 6px 18px rgba(45, 47, 51, 0.14);
    border-radius: 2px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: fixed;
    bottom: 0;
    right: 0;
  }
  ::v-deep {
    .el-tabs--card > .el-tabs__header {
      border: 0;
    }

    .el-tabs--card > .el-tabs__header .el-tabs__item {
      width: 72px;
      height: 26px;
      padding: 0;
      border: 0;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #2c2c35;
    }

    .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
      width: 72px;
      height: 26px;
      padding: 0;
      background: #ffffff;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #116ef9;
      // margin: 0 10px !important;
    }
  }

  ::v-deep .el-tabs__nav {
    width: 169px;
    height: 34px !important;
    border: 0;
    background: #f2f3f7;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    transform: translateX(0px) !important;
  }
  ::v-deep .el-table__row > td {
    border: none;
  }
  ::v-deep .el-table::before {
    height: 0px;
  }
  .downdrawerbox {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

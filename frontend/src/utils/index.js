/**
 * Created by PanJia<PERSON>hen on 16/11/18.
 */

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}';
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if ((typeof time === 'string')) {
      if ((/^[0-9]+$/.test(time))) {
        // support "1548221490638"
        time = parseInt(time);
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/');
      }
    }

    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value]; }
    return value.toString().padStart(2, '0');
  });
  return time_str;
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000;
  } else {
    time = +time;
  }
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return '刚刚';
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前';
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前';
  } else if (diff < 3600 * 24 * 2) {
    return '1天前';
  }
  if (option) {
    return parseTimefake(time, option);
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    );
  }
}


/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ');
  if (!search) {
    return {};
  }
  const obj = {};
  const searchArr = search.split('&');
  searchArr.forEach(v => {
    const index = v.indexOf('=');
    if (index !== -1) {
      const name = v.substring(0, index);
      const val = v.substring(index + 1, v.length);
      obj[name] = val;
    }
  });
  return obj;
}

/* 是否合法IP地址*/
export function validateIP(rule, value, callback) {
  if (value == '' || value == undefined || value == null) {
    callback();
  } else {
    const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
    // const regIPV6 = /^(([\da-fA-F]{1,4}):){8}$/;
    const regIPV6 = /^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/;
    // if ((!reg.test(value.trim()) && !regIPV6.test(value.trim() + ':')) && value.trim() != '') {
    if ((!reg.test(value.trim()) && !value.trim().match(regIPV6)) && value.trim() != '') {
      callback(new Error('请输入正确的IP地址'));
    } else {
      callback();
    }
  }
}

/* regIPV6*/
export function validateIPV6(rule, value, callback) {
  console.log('validateIPV6');
  if (value == '' || value == undefined || value == null) {
    callback();
  } else {
    const regIPV6 = /^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/;
    // if ((!reg.test(value.trim()) && !regIPV6.test(value.trim() + ':')) && value.trim() != '') {
    if ((!regIPV6.test(value.trim())) && value.trim() != '') {
      callback(new Error('请输入正确的IP地址'));
    } else {
      callback();
    }
  }
}

/* 是否合法的子网掩码 */
export function validateMask(rule, value, callback) {
  if (value == '' || value == undefined || value == null) {
    callback();
  } else {
    const strRegex = '^(254|252|248|240|224|192|128|0)\.0\.0\.0|255\.(254|252|248|240|224|192|128|0)\.0\.0|255\.255\.(254|252|248|240|224|192|128|0)\.0|255\.255\.255\.(255|254|252|248|240|224|192|128|0)$';
    let re = new RegExp(strRegex);
    if (re.test(value.trim()) && value.split('.').length === 4) {
      callback();
    } else {
      callback(new Error('请输入正确的掩码'));
    }
  }
}

/*
 是否合法的IPPro
 */
export function validateIPPro(rule, value, callback) {
  if (value == '' || value == undefined || value == null) {
    callback();
  } else {
    const strRegex = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])(,(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]))*$/;
    let re = new RegExp(strRegex);
    if (re.test(value)) {
      callback();
    } else {
      callback(new Error('输入格式不正确'));
    }
  }
}

/*
是否合法的域名
 */
export function validateDomain(rule, value, callback) {
  if (value == '' || value == undefined || value == null) {
    callback();
  } else {
    const strRegex = '^(.{64,}$|([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}$';
    // const strRegex = '/^.{64,}$|[^a-z0-9\-]|\-{2,}/';
    let re = new RegExp(strRegex);
    if (re.test(value.trim())) {
      callback();
    } else {
      callback(new Error('请输入正确的域名'));
    }
  }
}

/* 是否合法IP地址及网段*/
export function validateIPMask(rule, value, callback) {
  if (value == '' || value == undefined || value == null) {
    callback();
  } else {
    let ipArr = value.split('/');
    const ip = ipArr[0];
    const mask = ipArr[1];
    const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
    // const regIPV6 = /^(([\da-fA-F]{1,4}):){8}$/;
    const regIPV6 = /^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/;
    // if ((!reg.test(value.trim()) && !regIPV6.test(value.trim()+':')) && value.trim() != '') {
    if ((!reg.test(ip.trim()) && !ip.trim().match(regIPV6)) && ip.trim() != '') {
      callback(new Error('请输入正确的IP地址'));
    } else {
      if (ipArr.length !== 2) {
        callback(new Error('请输入正确的网段'));
      } else {
        if (!(/(^[1-9]\d*$)/.test(mask)) && mask !== '0') {
          callback(new Error('请输入正确的网段'));
          return;
        }
        let validateMask = false;
        if (reg.test(ip.trim())) {
          if (mask <= 32) validateMask = true;
        } else {
          if (mask <= 128) validateMask = true;
        }
        if (!validateMask) {
          callback(new Error('请输入正确的网段'));
        } else {
          callback();
        }
      }
    }
  }
}

/* 是否合法端口范围*/
export function validatePort(rule, value, callback) {
  if ((value.low_port === '' || value.low_port === undefined || value.low_port === null) && (value.high_port === '' || value.high_port === undefined || value.high_port === null) && (value.property1 || value.property2 || value.property3)) {
    callback(new Error('端口范围输入不正确'));
  } else if (value.low_port && value.high_port && value.low_port > value.high_port) {
    callback(new Error('端口范围输入不正确'));
  } else {
    callback();
  }
}

/*
 是否合法的规则名称
 */
export function validateRuleName(rule, value, callback) {
  if (value == '' || value == undefined || value == null) {
    callback();
  } else {
    const strRegex = /[^\u4E00-\u9FA5\uf900-\ufa2d]+/g;
    let re = new RegExp(strRegex);
    if (re.test(value.trim())) {
      callback();
    } else {
      callback(new Error('输入格式不正确'));
    }
  }
}

/**
     * 格式化数字
     * @param {number} number 待格式化的数字，单位B
     * @returns {string} 形如 '12,345M'
     */
export function formatNumber(number) {
  if (number === null) {
    number = 0;
  }
  let num = 0;
  let unit = "B";
  if (number < 1024) {
    num = number;
    unit = "B";
  } else if (number < 1024 * 1024) {
    num = number / 1024;
    unit = "KB";
  } else if (number < 1024 * 1024 * 1024) {
    num = number / (1024 * 1024);
    unit = "MB";
  } else if (number < 1024 * 1024 * 1024 * 1024) {
    num = number / (1024 * 1024 * 1024);
    unit = "GB";
  } else {
    num = number / (1024 * 1024 * 1024 * 1024);
    unit = "TB";
  }
  return new Number(num).toLocaleString("en-US") + " " + unit;
}

export function parseTimefake(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}';
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if (('' + time).length <= 10) time = parseInt(time) * 1000;
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value];
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value;
    }
    return value || 0;
  });
  return time_str;
}

/**
   * 格式化秒转换
   * @param sec
   * @returns {string}
   */
export function getFormatDate(sec) {
  let days = Math.trunc(sec / (60 * 60 * 24));
  let hours = Math.trunc((sec % (60 * 60 * 24)) / (60 * 60));
  let minutes = Math.trunc((sec % (60 * 60)) / 60);
  let seconds = Math.trunc(sec % 60);
  let time = [days, '天', hours, '时', minutes, '分', seconds, '秒'];
  if (days) {
    return time.join('');
  }
  if (hours) {
    return time.slice(2).join('');
  }
  if (minutes) {
    return time.slice(4).join('');
  }
  return time.slice(6).join('');
}


// 深度复制对象
export function cloneObj(obj) {
  var newObj = {};
  if (obj instanceof Array) {
    newObj = [];
  }
  for (var key in obj) {
    var val = obj[key];
    // newObj[key] = typeof val === 'object' ? arguments.callee(val) : val; //arguments.callee 在哪一个函数中运行，它就代表哪个函数, 一般用在匿名函数中。
    newObj[key] = typeof val === 'object' && val != null ? cloneObj(val) : val;
  }
  return newObj;
}
let globalFunction = {};


globalFunction.install = function (Vue, options) {
  // 高精度加法
  Vue.prototype.$ACC_ADD = function (a, b) {
    let r1, r2, m;
    try {
      r1 = a.toString().split('.')[1].length;
    } catch (e) {
      r1 = 0;
    }
    try {
      r2 = b.toString().split('.')[1].length;
    } catch (e) {
      r2 = 0;
    }
    m = Math.pow(10, Math.max(r1, r2));
    return (a * m + b * m) / m;
  };




  // 高精度减法
  Vue.prototype.$ACC_SUB = function (a, b) {
    let r1, r2, m, n;
    try {
      r1 = a.toString().split('.')[1].length;
    } catch (e) {
      r1 = 0;
    }
    try {
      r2 = b.toString().split('.')[1].length;
    } catch (e) {
      r2 = 0;
    }
    m = Math.pow(10, Math.max(r1, r2));
    //last modify by deeka
    //动态控制精度长度
    n = r1 >= r2 ? r1 : r2;
    return (a * m - b * m) / m;
  };




  // 高精度乘法
  Vue.prototype.$ACC_MUL = function (a, b) {
    let m = 0,
      s1 = a.toString(),
      s2 = b.toString();
    try {
      m += s1.split('.')[1].length;
    } catch (e) {}
    try {
      m += s2.split('.')[1].length;
    } catch (e) {}
    return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
  };





  // 高精度除法
  Vue.prototype.$ACC_DIV = function (a, b) {
    let t1 = 0,
      t2 = 0,
      r1,
      r2;
    try {
      t1 = a.toString().split('.')[1].length;
    } catch (e) {}
    try {
      t2 = b.toString().split('.')[1].length;
    } catch (e) {}
    r1 = Number(a.toString().replace('.', ''));
    r2 = Number(b.toString().replace('.', ''));
    return (r1 / r2) * Math.pow(10, t2 - t1);
  };





  // 解决科学计数问题
  Vue.prototype.$TOOL_NUMBER = function (num_str) {
    num_str = num_str.toString();
    if (num_str.indexOf("+") != -1) {
      num_str = num_str.replace("+", "");
    }
    if (num_str.indexOf("E") != -1 || num_str.indexOf("e") != -1) {
      var resValue = "",
        power = "",
        result = null,
        dotIndex = 0,
        resArr = [],
        sym = "";
      var numStr = num_str.toString();
      if (numStr[0] == "-") {
        // 如果为负数，转成正数处理，先去掉‘-’号，并保存‘-’.
        numStr = numStr.substr(1);
        sym = "-";
      }
      if (numStr.indexOf("E") != -1 || numStr.indexOf("e") != -1) {
        var regExp = new RegExp(
          "^(((\\d+.?\\d+)|(\\d+))[Ee]{1}((-(\\d+))|(\\d+)))$",
          "ig"
        );
        result = regExp.exec(numStr);
        if (result != null) {
          resValue = result[2];
          power = result[5];
          result = null;
        }
        if (!resValue && !power) {
          return false;
        }
        dotIndex = resValue.indexOf(".") == -1 ? 0 : resValue.indexOf(".");
        resValue = resValue.replace(".", "");
        resArr = resValue.split("");
        if (Number(power) >= 0) {
          var subres = resValue.substr(dotIndex);
          power = Number(power);
          //幂数大于小数点后面的数字位数时，后面加0
          for (var i = 0; i <= power - subres.length; i++) {
            resArr.push("0");
          }
          if (power - subres.length < 0) {
            resArr.splice(dotIndex + power, 0, ".");
          }
        } else {
          power = power.replace("-", "");
          power = Number(power);
          //幂数大于等于 小数点的index位置, 前面加0
          for (var i = 0; i < power - dotIndex; i++) {
            resArr.unshift("0");
          }
          var n = power - dotIndex >= 0 ? 1 : -(power - dotIndex);
          resArr.splice(n, 0, ".");
        }
      }
      resValue = resArr.join("");

      return sym + resValue;
    } else {
      return num_str;
    }
  };
  // table index 接续 
  Vue.prototype.$TABLE_INDEX = function (index, page, limit) {
    return (page - 1) * limit + index + 1;
  };
};


export default globalFunction;

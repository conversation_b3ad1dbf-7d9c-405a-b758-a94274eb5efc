import request from "@/utils/request";
// 获取标签详情
export function getTagDetail(params) {
  return request({
    url: "/cert/tag/cert/detail",
    method: "GET",
    params,
  });
}
// 获取标签推荐数据（默认十条）
export function getTagRecommend() {
  return request({
    url: "/cert/tag/cert/recommend",
    method: "GET",
  });
}
// 标签库查询（全量展示）
export function getTagSearch(data) {
  return request({
    url: "/tag/search",
    method: "POST",
    data,
  });
}

// 标签库查询（全量展示）
export function getTagAdd(data) {
  return request({
    url: "/tag/add",
    method: "POST",
    data,
  });
}

// 查询标签分类
export function getListTagAttribute() {
  return request({
    url: "/tag/listTagAttribute ",
    method: "GET",
  });
}

// 标签推荐
export function recommendTag(data) {
  return request({
    url: "/tag/session/recommend",
    method: "GET",
  });
}

// 标签库查询（全量展示）
export function analysisTagAdd(data) {
  return request({
    url: "/tag/add",
    method: "POST",
    data,
  });
}
// 以下是会话分析标签接口
// 标签库查询（全量展示）
export function analysisTagSearch(data) {
  return request({
    url: "/tag/search",
    method: "POST",
    data,
  });
}

FROM centos:centos7.5.1804

MAINTAINER maaaster_nil <<EMAIL>>

ENV LANG=zh_CN.UTF-8

# 下载wget
RUN yum install -y wget lshw dmidecode && \
    yum clean all

# 下载python的conda环境
RUN echo 'export PATH=/opt/conda/bin:$PATH' > /etc/profile.d/conda.sh && \
    wget --no-cache --no-check-certificate --quiet https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-py39_4.10.3-Linux-x86_64.sh -O ~/anaconda.sh && \
    /bin/bash ~/anaconda.sh -b -p /opt/conda && \
    rm ~/anaconda.sh

# 配置conda的环境变量
ENV PATH /opt/conda/bin:$PATH
RUN source /etc/profile && \
    source /etc/profile.d/conda.sh

## install hyperscan
ADD /deploy/env/lib /opt/py_anay_lib/

RUN yum -y update gcc && \
    yum install -y git gcc clang cmake make gcc-c+ libstdc++* gcc+ gcc-c++ && \
    cd /opt/py_anay_lib/ && \
    tar -xzf ragel-6.10.tar.gz && \
    cd ragel-6.10 && \
    ./configure && \
    make && make install && \
    cd .. && \
    tar -zxf boost_1_69_0.tar.gz && \
    export && \
    tar -xvf v5.4.0.tar.gz && \
    rm -rf v5.4.0.tar.gz && \
    cd hyperscan-5.4.0/ && \
    mkdir build && \
    cd build && \
    cmake -G "Unix Makefiles" -DBOOST_ROOT=/opt/py_anay_lib/boost_1_69_0 -DCMAKE_INSTALL_PREFIX:PATH=/usr -DBUILD_SHARED_LIBS=ON ../ && \
    make && make install && \
    cd / && \
    rm -rf /var/cache/yum && \
    yum clean all && \
    rm -rf /opt/py_anay_lib && \
    rm -rf /root/.cmake && \
    rm -rf /root/.cache

# 下载python依赖
COPY deploy/env/requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple pip -U && \
    pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip install --no-cache-dir -r /app/requirements.txt && \
    rm /app/requirements.txt

# 复制当前文件到指定目录下
RUN mkdir -p /opt/work_space && mkdir -p /opt/work_space/PyGksec
ADD /source /opt/work_space/PyGksec/
ADD /deploy /opt/work_space/PyGksec/deploy




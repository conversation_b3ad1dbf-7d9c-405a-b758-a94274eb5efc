import hyperscan
from typing import List, Optional, Any

class HyperScanMatch:
    def __init__(self, patterns,show_list=None) -> None:
        self.patterns = patterns        
        self.db = self.create_db()
        self.show_list = show_list if show_list is not None else patterns

    def create_db(self):
        # 建立匹配模式
        db = hyperscan.Database()
        patterns = [
            # expression,  id, flags
            (pattern.encode("utf-8"), id, hyperscan.HS_FLAG_CASELESS | hyperscan.HS_FLAG_SINGLEMATCH) for id, pattern in enumerate(self.patterns)
        ]
        self.expressions, self.ids, self.flags = zip(*patterns)
        db.compile(expressions=self.expressions, ids=self.ids, elements=len(patterns), flags=self.flags)
        return db

    def hyperscan_match(self, lines: List[str]) -> List[str]:
        # 多模匹配，返回命中规则的内容
        result = []
        def on_match(id: int, froms: int, to: int, flags: int, context: Optional[Any] = None) -> Optional[bool]:
            result.append(context)

        for line in lines:
            r = self.db.scan(line.encode("utf-8"), match_event_handler=on_match,context=line)
        return result

    def is_match(self, line: str) -> bool:
        # 是否匹配，及匹配的规则（匹配的模式）
        result = []
        def on_match(id: int, froms: int, to: int, flags: int, context: Optional[Any] = None) -> Optional[bool]:            
            result.append(self.show_list[id])

        r = self.db.scan(line.encode("utf-8"), match_event_handler=on_match,context=line)
        return len(result) > 0, result
import base64
import os
import time
from pyDes import des,CBC,PAD_PKCS5
 
secret_file = "/opt/work_space/.anay_auth"
Des_key = "hk0i*&o3"
Des_IV = "\x11\2\x2a\3\1\x27\2\0"
 
def Encryted(tr):
    k = des(Des_key, CBC, Des_IV, pad=None, padmode=PAD_PKCS5)
    EncryptStr = k.encrypt(tr)
    return base64.b32encode(EncryptStr)

def getEnv():
    cpu_info = os.popen("dmidecode -t 4 | grep ID").readlines()
    mainboard = os.popen("dmidecode -t 2 | grep Serial").readlines()
    mac = os.popen("lshw -c network | grep serial | head -n 1").readlines() 
    return cpu_info+mainboard+mac

def makeLiscence(days):
    key = str(int(time.time())+days*86400)
    maincode = "/".join(getEnv()+[key])
    secret_content= Encryted(bytes(maincode, encoding='utf-8'))    
    with open(secret_file, 'wb+') as f:
        f.write(secret_content)
        f.close()

def checkLiscence():
    if not os.path.exists(secret_file):
        return False
    now_time = str(int(time.time()))
    secret_content = open(secret_file,"rb").read()
    EncryptStr = base64.b32decode(secret_content)
    k = des(Des_key, CBC, Des_IV, pad=None, padmode=PAD_PKCS5)
    tr = k.decrypt(EncryptStr)
    maincode = tr.decode("utf-8")
    vals = maincode.split("/")
    for index,val in enumerate(getEnv()):
        if val!=vals[index]:
            return False
    if now_time > vals[-1]:
        return False
    return True
 

def checkAuthored():
    if not os.path.exists(secret_file):
        return False        
    return checkLiscence()

if __name__ == '__main__':
    import sys
    days = 365
    if len(sys.argv)>1:
        days = int(sys.argv[1])
    register = makeLiscence(days)
#     checkAuthored()
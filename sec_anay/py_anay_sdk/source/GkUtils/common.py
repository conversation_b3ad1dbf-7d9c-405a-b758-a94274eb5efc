
import hashlib
import time
import math

LARGET_CNT = 0x0fffffff
HOUR_SECOND = 3600
MIN_SECOND = 60
DAY_SECOND = 86400
YEAR_SECOND = DAY_SECOND*365
BIG_YAER_SECOND = DAY_SECOND*366
MONTH_SECOND = DAY_SECOND*31
UNKNOWN = "unk"
MB = 8<<20
KB = 8<<10

COMMON_TLS_PORT = [443,993,8443,80,3389]

# ES ARGS
AGG_CNT = 10000
HIS_INTERVAL=3600
MAX_AGG_CNT = 0x0fffffff

UPPER_CHAR_SET = set("qwertyuiopasdfghjklzxcvbnm".upper())
LOWER_CHAR_SET = set("qwertyuiopasdfghjklzxcvbnm")

Max_Score = 100
ONE_DIRECT_SERVICE = [
    "TCP_QueryOnly","No_Payload","TCP_NoPayload","UDP_NoPayload","TCP_PortClose"
]

ERR_DIRECT_SERVICE =  ["APP_ICMP_v4","UDP_Unknown","APP_ICMP_v6","APP_IPMessage"] + ONE_DIRECT_SERVICE

ABNORMAL_SERVICE = ["APP_VOIP","APP_SSDP","TCP_Unknow","TCP_Other" ] + ERR_DIRECT_SERVICE

InnerNet = ["***********/16","**********/16","10.0.0.0/8"]



def get_now_time():
    return int(time.time())

def md5_of_str(content):
    md5_Tool = hashlib.md5()
    md5_Tool.update(content.encode('utf-8'))
    return md5_Tool.hexdigest()


def get_data_from_dict(data,keys,default=None):
    if data is None:
        return default
    temp = data.copy()
    for key in keys.split('/'):
        if temp is None:
            return default
        if key in temp:
            temp = temp[key]
        else:
            return default
    return temp


def get_colset_from_DF(df,target_col,filter_col={}) :
    target = df
    for key,val in filter_col.items():
        target = target[target[key]==val]
    return set(target[target_col])

def time_str_to_int(str_time):
    timeArray = time.strptime(str_time, "%Y-%m-%d %H:%M:%S")
    return time.mktime(timeArray)

def time_int_to_str(int_time):
    time_local = time.localtime(int_time)
    time_str = time.strftime("%Y-%m-%d %H:%M:%S", time_local)
    return time_str


def trans_data_of_map(data,config):
    result = {}
    for source_key,result_key in config.items():
        val = get_data_from_dict(data,source_key)
        result[result_key] = val
    return result


def get_entropy_of_map(charMap):
    entropy = 0
    n = len(charMap)
    t = sum(charMap)
    for j in range(n):
        entropy += -1 * (float(charMap[j] / t)) * math.log(float(charMap[j] / t), 2)
    return entropy

def get_entropy_of_str(content):
    result = {}
    for x in content:
        if x not in result:
            result[x] = 0
        result[x]+=1
    return get_entropy_of_map(list(result.values()))


def add_key_cnt_to_map(d,key):
    if key not in d:
        d[key] = 0
    d[key]+=1
    return d
    
def add_key_value_to_setmap(d,key,val):
    if key not in d:
        d[key] = set()
    d[key].add(val)    
    return d

# 切分IP列表
def split_iter_list(data_list,interval=100):
    cnt = len(data_list)
    for start in range(0,cnt,interval):
        end = min(cnt,start+interval)
        yield data_list[start:end]


from GkConfig import env

import redis

REDIS_VALS = env["REDIS_HOST"].split(":")
REDIS_HOST,REDIS_PORT = REDIS_VALS[0],REDIS_VALS[1]
REDIS_POOL = redis.ConnectionPool(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True)

# execute Redis command
REDIS_CLI = redis.Redis(connection_pool=REDIS_POOL)

def check_redis_open():
    global REDIS_CLI 
    REDIS_CLI = redis.Redis(connection_pool=REDIS_POOL)
    try:
        ks = REDIS_CLI.keys()
        return True
    except:
        return False

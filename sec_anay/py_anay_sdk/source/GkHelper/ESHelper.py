from GkUtils import common
from GkConfig import env


from elasticsearch import Elasticsearch
from GkHelper.APIHelper import request_PB_data
from GkHelper.GkLogHelper import gk_logging

import time
import sys
import copy
import pandas as pd
import os
import ctypes
import json

ES_VALS = env["ES_HOST"].split(":")
ES_Config = {'host':ES_VALS[0],'port':ES_VALS[1]}
es = Elasticsearch([ES_Config])



SPLIT_STR = "#"
SPLIT_PART = f"+'{SPLIT_STR}'+"


PB_LIB_PATH = "/opt/GeekSec/STL/py_read_data/lib/liball_pb2json.so"
if os.path.exists(PB_LIB_PATH):
    cla = ctypes.cdll.LoadLibrary(PB_LIB_PATH)


def list_es_index(keyword=""):
    '''
        获取检索ES的索引名
        :keyword: es索引名特征，比如connect*       
        :returns: es索引列表
    '''
    indices = es.indices.get_alias(keyword)
    return indices

def get_latest_task_id(keyword):
    """
        获得最新批次的任务ID和批次ID
        :keyword: ES索引名特征，比如connect*
    """
    tables = list_es_index(keyword)
    if tables is None or len(tables)==0:
        return -1,-1
    tables = list(x.split("_") for x in tables.keys())    
    task_list = sorted(tables,key=lambda x : int(x[2]),reverse=True)
    return int(task_list[0][1]),int(task_list[0][2])

def get_task_id_of_batch(batch_id):
    """
        基于批次编号查询任务编号
    """
    if batch_id < 0:
        return -1
    batch_id = str(batch_id)
    keyword = f"*_*_{batch_id}_*"
    indices = es.indices.get_alias(keyword)
    indices = list(indices.keys())
    if len(indices)<1:
        return -1
    return int(indices[0].split("_")[1])


def get_run_arg(index_key="connect*"):
    """
        获得需要执行的任务ID和批次ID
        :index_key ES索引类型
    """
    task_id,batch_id = -1,-1
    if len(sys.argv)>1:
        batch_id = int(sys.argv[1])
        task_id = get_task_id_of_batch(batch_id)
    else:
        task_id,batch_id = get_latest_task_id(index_key)
    return task_id,batch_id


def get_default_query():
    return  {"size":0,"query": {"bool": {"must": [],"should":[],"must_not":[]}}}


def get_latest_time(table):
    detail = get_default_query()
    detail["aggs"] = make_common_agg("max","CreateTime","ctime")
    r = load_agg_data(table,detail,"ctime",agg_type="max")
    if r and r["value"]:
        latest_time = int(r["value"])
        return max(latest_time,0)
    return int(time.time())

def get_basic_query(es_type="ssl",task_id=-1,batch_id=-1,actual_time=env["ACTUAL_TIME"]):
    """
        生成基础的ES查询语句
        :es_type: 为索引类型，包括ssl,connectinfo,http,dns
        :actual_time: 表示按照实时模式取数据
    """
    query =  get_default_query()   
    if batch_id>-1:
        table = f"{es_type}_*_{batch_id}_*"
    elif task_id>-1:
        table = f"{es_type}_{task_id}_*"
    else:
        table = "%s_*"%es_type

    if actual_time:       
        begin_time = get_latest_time(table) - env["ACTUAL_INTERVEL_HOUR"]*3600
        query["query"]["bool"]["must"].append({"range": {"CreateTime":{"gt": begin_time}}})         
    return table,query

def get_basic_query_add(es_type="ssl",task_id=-1,batch_id=-1,actual_time=True,timex=1440,time_origin=1664009944):
    """
        生成基础的ES查询语句
        :es_type: 为索引类型，包括ssl,connectinfo,http,dns
        :actual_time: 表示按照实时模式取数据
    """
    query =  get_default_query()   
    if batch_id>-1:
        table = f"{es_type}_*_{batch_id}_*"
    elif task_id>-1:
        table = f"{es_type}_{task_id}_*"
    else:
        table = "%s_*"%es_type

    if actual_time:       
        begin_time = time_origin + timex*3600
        end_time = begin_time+1*3600
        query["query"]["bool"]["must"].append({"range": {"CreateTime":{"gt": begin_time,"lt":end_time}}})         
    return table,query


def make_term_agg(query_key,result_key,size=common.AGG_CNT):
    """
        构造term类型的聚合语句query_key为ES字段名，result_key为聚合的字段名，size为数量限制
    """
    if type(query_key)==str:
        return  {
                result_key: {
                    "terms": {
                        "field": query_key,
                        "size": size,
                        "collect_mode" : "breadth_first"                   
                    },
                }
            }
    if type(query_key)==dict:
        return {
            result_key: {
                "terms": {
                    "script": SPLIT_PART.join(list(query_key.keys())),
                    "size": size
                }
            }
        }

def make_common_agg(agg_type,query_key,result_key):
    return  {
                result_key: {
                    agg_type: {
                        "field": query_key,
                    },
                }
            }

def make_stats_agg(query_key,result_key):
    return  {
                result_key: {
                    "stats": {
                        "field": query_key,
                    },
                }
            }

def get_all_tasks(keyword="connect*"):
    tables = list_es_index(keyword)
    tables = list(x.split("_") for x in tables.keys())    
    for table in tables:
        yield int(table[1]),int(table[2])

def make_his_agg(query_key,result_key,interval=common.HIS_INTERVAL):
    return  {
                result_key: {
                    "histogram": {
                        "field": query_key,
                        "interval": interval
                    }
                }
            }

def filter_doc_count(agg_detail,key,min_doc=0,max_doc=common.LARGET_CNT):
    agg_detail[key]["aggs"] = {
        "having": {
            "bucket_selector": {
                "buckets_path": { 
                    "view_count": "_count"
                    },
                "script": f"params.view_count > {min_doc}  && params.view_count < {max_doc}"
            }
        }
    }
    return agg_detail

def load_agg_data(table,detail,data_key,agg_type="term"):
    """
        基于索引与条件获得查询es
        table: 索引名
        detial: 检索语句
        :returns: es_result为es返回的json

    """
    es_result = es.search(index=table, body=detail, request_timeout=600) 
    if agg_type in {"min","max","sum","stat"}:
        return common.get_data_from_dict(es_result,f"aggregations/{data_key}",{})
    if agg_type=="card":
        return common.get_data_from_dict(es_result,f"aggregations/{data_key}/value",-1)
    return common.get_data_from_dict(es_result,f"aggregations/{data_key}/buckets",[])


def load_time_list(table,base_detail,time_stack):
    """
        基于索引与条件获得查询es下的时间片
        table: 索引名
        detial: 检索语句
        :returns: 迭代获得es返回的json,start_time
    """
    time_aggs = make_his_agg("StartTime","time",interval=time_stack)
    time_aggs["time"]["histogram"]["min_doc_count"]=1
    detail = copy.deepcopy(base_detail)
    detail["aggs"] = time_aggs
    es_result = es.search(index=table, body=detail, request_timeout=600) 
    time_data = common.get_data_from_dict(es_result,"aggregations/time/buckets",[])
    time_list = [t["key"] for t in time_data]
    return time_list


def load_agg_data_of_time(table,base_detail,data_key,start_time=-1,end_time=common.LARGET_CNT,agg_type="term"):
    detail = copy.deepcopy(base_detail)
    time_range = {"range": {"StartTime":{"gt": start_time,"lt":end_time}}}  
    detail["query"]["bool"]["must"].append(time_range)         
    es_result = load_agg_data(table,detail,data_key,agg_type=agg_type)
    if type(es_result)!=list:
        return [{"agg_time":start_time,'val':es_result}]
    result = []
    for x in es_result:
        x["agg_time"] = start_time
        result.append(x)
    return result

def load_agg_data_of_split_time(table,base_detail,data_key,time_stack=common.HOUR_SECOND,agg_type="term"):
    """
        基于索引与条件获得查询es
        table: 索引名
        detial: 检索语句
        :returns: 迭代获得es返回的json,start_time
    """
    time_list = load_time_list(table,base_detail,time_stack)
    if len(time_list)==0:
        yield load_agg_data_of_time(table,base_detail,data_key,agg_type=agg_type)
    for stime in time_list:
        etime = stime+time_stack          
        yield load_agg_data_of_time(table,base_detail,data_key,stime,etime,agg_type=agg_type)

def format_es_data(data,ignore_file=True):
    if not ignore_file:
        return get_fulldata_from_PB(data['_source'])
    return data

def read_PB_data(filename, row):
    '''
        从pb读取数据
        filename: pb文件
        row: pb文件中偏移量
        :returns: 元数据dict
    '''
    cla.read_pb_file.restype = ctypes.c_char_p
    filename_c = ctypes.c_char_p(filename.encode("utf-8"))
    row_c = ctypes.c_long(row)
    rst  = cla.read_pb_file(filename_c , row_c)
    data = ctypes.string_at(rst, -1).decode("utf-8")
    return json.loads(data)

def get_fulldata_from_PB(data):
    """
        从PB补全数据(本地 or API)
    """
    if "filename" in data and os.path.exists(data["filename"]):
        filename = data["filename"]
        start = data["row"]
        if filename.endswith(".pb"):
            try:
                r = read_PB_data(filename,start)
                data.update(r)
            except Exception as e:
                pass     
    elif "Hkey" in data and data["Hkey"]:
        r = request_PB_data(data["Hkey"])
        if r and r[data["Hkey"]]:
            data.update(r[data["Hkey"]])
    
    return data

def get_fulldata_from_PB_batch(data_list):
    """
        批量从PB中补全数据
    """
    result = []
    data_remote = {}
    for data in data_list:
        if 'Hkey' in data and data["Hkey"]!='':
            data_remote[data['Hkey']] = data
        else:
            result.append(get_fulldata_from_PB(data))

    hkey_list = list(data_remote.keys())
    r = request_PB_data(hkey_list)

    if r is not None:
        for hkey,val in r.items():
            data = data_remote[hkey]
            if val:
                data.update(val)
                data_remote[hkey] = data

    info = result+list(data_remote.values())
    return info
        

def load_data(table,detail,ignore_file=False,sources=None):
    '''
        获取检索ES的迭代器
        :table: es索引名
        :sources: 筛选字段，默认为全部
        :detail: 检索条件
        :returns: es数据迭代器
    '''
    if sources is not None:
        detail['_source'] = sources
    format_es_data = lambda data : get_fulldata_from_PB_batch(data) if not ignore_file else data
    page = es.search(index=table, size=1000, scroll='2m', body=detail, request_timeout=600)
    # print(page)
    scroll_size = page['hits']['total']
    if type(scroll_size) == dict:
        scroll_size = scroll_size["value"]
    gk_logging.info(f"[info] load es data of size {scroll_size}")
    if scroll_size==0:
        if '_scroll_id' in page.keys():
            sid = page['_scroll_id']
            es.clear_scroll(scroll_id=sid)
        return

    sid = page['_scroll_id']
    datas = [data["_source"] for data in page['hits']['hits']]
    for data in format_es_data(datas):
        yield data
    # for data in page['hits']['hits']:
    #     yield format_es_data(data["_source"])
    while scroll_size > 0:
        page = es.scroll(scroll_id=sid, scroll='2m',request_timeout=600)
        sid = page['_scroll_id']
        scroll_size = len(page['hits']['hits'])
        datas = [data["_source"] for data in page['hits']['hits']]
        for data in format_es_data(datas):
            yield data
        # for data in page['hits']['hits']:
        #     yield format_es_data(data["_source"])

    es.clear_scroll(scroll_id=sid)

def load_es_data_to_list(index,detail,process_func=None,limit=-1,ignore_file=False):
    """
        Read DataList from ES
        :index: ES索引
        :processFunc: 数据转化函数
        :detail: 过滤条件
        :limit: 数据读取上限
        :ignore_file: 忽略从pb补充元数据字段
    """
    dataGen = load_data(index,detail=detail,ignore_file=ignore_file,sources=None)
    data_list = []
    for data in dataGen:
        data = process_func(data) if process_func else data
        data_list.append(data)
        limit-=1 
        if limit==0:
            break
    return data_list


def load_es_data_to_DF(index,detail,process_func=None,outfile=None,limit=-1,ignore_file=False):
    """
        Read DataFrame from ES, return pandas DataFrame
        :index: ES索引
        :processFunc: 数据转化函数
        :detail: 过滤条件
        :outfile: 存储文件名
        :limit: 数据读取上限
        :ignore_file: 忽略从pb补充元数据字段
    """
    data_list = load_es_data_to_list(index,detail,process_func,limit,ignore_file)
    if len(data_list)==0:
        return
    df = pd.DataFrame(data_list)
    if outfile:        
        df.to_csv(outfile,index=False)        
    return df


def find_one(table,detail={}):
    '''
        获取任意一条ES数据
        table: es索引名
        sources: 筛选字段，默认为全部
        detail: 检索条件
        :returns: 一条ES数据
    '''   
    detail["size"] = 1
    page = es.search(index=table,body=detail)
    scroll_size = page['hits']['total']
    if type(scroll_size)==dict:
        scroll_size = scroll_size["value"]
    if scroll_size>0:
        data = page['hits']['hits'][0]
        return get_fulldata_from_PB(data['_source'])
    else:
        return None



    
# Count Num of  Distinct Target 
def make_cardin_agg(query_key,result_key):
    if type(query_key)==str:
        return {
                result_key: {
                    "cardinality": {
                        "field": query_key                 
                    },
                }
            }
    if type(query_key)==dict:
        return {
            result_key: {
                "cardinality": {
                    "script": SPLIT_PART.join(list(query_key.keys())),
                }
            }
        }

def format_list_agg(col_dict,data):
    vals = data["key"].split(SPLIT_STR)
    for index,key in enumerate(col_dict.values()):
        data[key] = vals[index]
    if "agg_time" in data:
        data["agg_time"] = int(data["agg_time"])
    return data


def describe(table,detail):
    print(table)
    print(json.dumps(detail,indent=4))


def load_key_from_agg_list(es_result,key):
    result = []
    for x in common.get_data_from_dict(es_result,f"aggregations/{key}/buckets",{}):
        result.append(x["key"])
    return result


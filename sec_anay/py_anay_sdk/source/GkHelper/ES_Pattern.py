import GkHelper.TagHelper as tag_helper
import GkHelper.ESHelper as eh
from GkUtils import common
import copy

import pandas as pd

def get_latest_time(table,base_detail):
    detail = copy.deepcopy(base_detail)
    detail["aggs"] = eh.make_stats_agg("StartTime","time_agg")
    r = eh.load_agg_data(table,detail,"time_agg",agg_type="stat")
    latest_time = int(r["max"]) if r["max"] else -1
    return latest_time


def add_global_filter(detail):
    global_id = tag_helper.Tag_Text_Map["跨境会话"]["Tag_Id"]
    detail["query"]["bool"]["must"].append({"term": {"Labels": global_id}})
    return detail                          

make_dns_wild_filter = lambda x : {"wildcard": {"Domain.keyword": "*.%s"%x}}


def make_domain_filter(search_domains,include_wild=False):
    filter_detail = {"bool":{"should":[]}}
    filter_detail["bool"]["should"].append({"terms": {"Domain.keyword":search_domains}})
    if include_wild:
        for fdomain in search_domains:
            filter_detail["bool"]["should"].append(make_dns_wild_filter(fdomain)) 
    return filter_detail

def load_sessions_of_ids(task_id,session_ids,iter_cnt=10000):
    table,base_detail = eh.get_basic_query("connectinfo",task_id=task_id)
    session_cnt = len(session_ids)
    result = []
    for i in range(0,session_cnt,iter_cnt):
        search_sessions = session_ids[i:min(session_cnt,i+iter_cnt)]
        detail = copy.deepcopy(base_detail)
        detail["query"]["bool"]["must"].append({"terms":{"SessionId.keyword":search_sessions}})
        result += eh.load_es_data_to_list(table,detail)
    if len(result)==0:
        return
    result = pd.DataFrame(result)
    return result

def load_domain_related_sessions(task_id,search_domains,target_type="connect",include_wild=False):
    table,base_detail = eh.get_basic_query("dns",task_id=task_id)
    detail = copy.deepcopy(base_detail)
    filter_detail = make_domain_filter(search_domains,include_wild=include_wild)
    detail["query"]["bool"]["must"].append(filter_detail)
    data_list = eh.load_es_data_to_list(table,detail)
    if len(data_list)==0:
        return
    if target_type == "connect":
        session_ids = list(x["SessionId"] for x in data_list)
        return load_sessions_of_ids(task_id,session_ids)
    return pd.DataFrame(data_list)

def get_resolved_ip_of_domains(table,base_detail,search_domains,include_wild=False):
    ips = set()
    detail = copy.deepcopy(base_detail)
    domain_aggs = eh.make_term_agg(query_key="DomainIp.keyword",result_key="domain_agg",size=common.MAX_AGG_CNT)
    detail["query"]["bool"]["must_not"].append({"term": {"Domain.keyword":""}})
    detail["query"]["bool"]["must"].append(make_domain_filter(search_domains,include_wild))    
    detail["aggs"] = domain_aggs
    data =  eh.load_agg_data(table,detail,data_key="domain_agg")
    if data:
        for d in data:
            ips = ips | set(d["key"].split("|"))
    return ips
    
def get_resolved_ip_of_domains_with_iters(table,base_detail,domains,iter_cnt=1000,include_wild=False):
    domain_cnt = len(domains)
    result = set()
    for i in range(0,domain_cnt,iter_cnt):
        search_domains = domains[i:min(domain_cnt,i+iter_cnt)]
        detail = copy.deepcopy(base_detail)
        result = result | get_resolved_ip_of_domains(table,detail,search_domains,include_wild)
    return result


def get_dip_app(ip_list):
    table,detail = eh.get_basic_query("connectinfo")
    detail["query"]["bool"]["must"].append({"terms":{"dIp":ip_list}})
    detail["query"]["bool"]["must_not"].append({"terms":{"AppName.keyword":common.ABNORMAL_SERVICE}})

    app_agg = eh.make_term_agg(query_key="AppName.keyword",result_key="app_agg",size=common.MAX_AGG_CNT)
    detail["aggs"] = app_agg
    r = eh.load_agg_data(table,detail,data_key="app_agg")
    services = [x["key"] for x in r]
    return services


def get_dip_ports(ip_list):
    table,detail = eh.get_basic_query("connectinfo")
    detail["query"]["bool"]["must"].append({"terms":{"dIp":ip_list}})
    detail["query"]["bool"]["must_not"].append({"terms":{"AppName.keyword":common.ABNORMAL_SERVICE}})

    app_agg = eh.make_term_agg(query_key="dPort",result_key="port_agg",size=common.MAX_AGG_CNT)
    detail["aggs"] = app_agg
    r = eh.load_agg_data(table,detail,data_key="port_agg")
    ports = [x["key"] for x in r]
    return ports


def get_direction_ip_list(table,detail,ip_net,direction="dIp"):
    detail["query"]["bool"]["must"].append({"terms":{direction:ip_net}})
    ip_agg = eh.make_term_agg(query_key=direction,result_key="ip_agg",size=common.MAX_AGG_CNT)
    detail["aggs"] = ip_agg
    r = eh.load_agg_data(table,detail,data_key="ip_agg")
    ips = [x["key"] for x in r]
    return ips
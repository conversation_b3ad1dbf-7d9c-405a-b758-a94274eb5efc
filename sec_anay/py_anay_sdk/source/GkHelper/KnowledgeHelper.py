import pandas as pd
import os
from GkConfig import base_dir

Benign_DNS_Server_File = os.path.join(base_dir,"GkData/Knowledge/BenignDNSServer.csv")
UserDefinedDNS_Server_File = "Data/Custom/UserDefinedDNS_Server.csv"

ThreatInfoFile = os.path.join(base_dir,"GkData/Knowledge/ThreatInfo.csv")
ThreatDF = pd.read_csv(ThreatInfoFile)

CustomWhiteDNS_Server = set(pd.read_csv(UserDefinedDNS_Server_File).address) \
    if os.path.exists(UserDefinedDNS_Server_File) else set()
Benign_DNS_Servers = set(pd.read_csv(Benign_DNS_Server_File).address) | CustomWhiteDNS_Server

Word_File = os.path.join(base_dir,"GkData/Knowledge/words.csv")
Word_DF = pd.read_csv(Word_File)

GKFingerFile = os.path.join(base_dir,"GkData/Knowledge/gk_fingers.csv")
GKFinger_DF = pd.read_csv(GKFingerFile)

HTTP_FIELD_FILE =  os.path.join(base_dir,"GkData/Knowledge/http_filed.csv")
HTTP_TITLE_DF = pd.read_csv(HTTP_FIELD_FILE)

COMMON_WORD_FILE =  os.path.join(base_dir,"GkData/Knowledge/word_dict.txt")
WORDS = set(x.strip() for x in open(COMMON_WORD_FILE).readlines())


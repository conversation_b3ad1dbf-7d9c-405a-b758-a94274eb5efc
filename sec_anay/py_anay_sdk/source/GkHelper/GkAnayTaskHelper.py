from GkConfig import env
from GkHelper.RedisHelper import REDIS_CLI
from PyGksec.GkHelper.GkLogHelper import gk_logging



# Spliter For Task/Batch TaskName 
# ie. task_name:  task_1001_checkCert
# ie. batch_name:  batch_90001_checkCert
TASK_NAME_SPLITER = "_"

def get_name_for_task_and_batch(function_name,task_id,batch_id):
    """[get TaskName For Redis Of TaskID]

    Args:
        function_name ([str]): [Analysis Function Name in AnayTaskMap]
        task_id ([int]): [TaskId for run]
        batch_id ([int]): [BatchID for run]
    Returns:
        [TaskName]: [TaskName to save in Redis]
    """
    if not batch_id or batch_id<0:
        TASK_NAME_SPLITER.join(["task",str(task_id),function_name])
    else:
        return TASK_NAME_SPLITER.join(["batch",str(batch_id),function_name])
    return 

def parse_anay_task_info(task_name):
    """[parse TaskName in Redis To TaskID,BatchID,ModelName]
    Args:
        [TaskName]: [TaskName to save in Redis]
    Returns:
        task_id ([int]): [TaskId for run]
        batch_id ([int]): [BatchID for run]
        model_name ([str]): [Analysis Function Name in AnayTaskMap]

    """
    task_id,batch_id = -1,-1
    vals = task_name.split(TASK_NAME_SPLITER)
    if len(vals)!=3:
        model_name = None
        task_id = None
        batch_id = None
        return model_name,task_id,batch_id     
    if vals[0]=="task":
        task_id = int(vals[1])
    if vals[0]=="batch":
        batch_id = int(vals[1])
    model_name = vals[2]
    return model_name,task_id,batch_id
    


class G_AnayModel:
    """
        [ Base Class for Run 基础的任务类]
    """
    def __init__(self,model_name):
        """[init]

        Args:
            model_name ([str]): [TaskName 任务名,与AnayTaskMap保持一致]
        """
        self.model_name = model_name


    def finish_task(self,task_id,batch_id):
        """ [Finish Task and reset Status in DB]
        Args:
            task_id ([int]): [default set -1]
            batch_id ([int]): [default set -1]
        """
        task_name = get_name_for_task_and_batch(self.model_name,task_id,batch_id)
        REDIS_CLI.zrem(env["REDIS_ANAY_KEY"],task_name)
        gk_logging.info(f"DONE REDIS_Task {task_name}")


    def run(self,task_id,batch_id):
        """ [Run Task and Write Tags to DB]
        Args:
            task_id ([int]): [default set -1]
            batch_id ([int]): [default set -1]
        """
        pass
                 
                 
    def run_task(self,task_id,batch_id):
        """[Main Func in Class  任务的主进程代码，先执行写标签程序run,再更新任务状态]

        Args:
            task_id ([type]): [description]
            batch_id ([type]): [description]
        """
        self.run(task_id,batch_id)
        self.finish_task(task_id,batch_id)
    



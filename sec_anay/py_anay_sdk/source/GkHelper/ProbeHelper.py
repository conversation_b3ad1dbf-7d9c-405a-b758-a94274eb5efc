import sys
sys.path.append("./")

import json
import copy
import math

FullDomainType= 1
NDomainType = 2

BasicRule = {"BytePs":0,"DetailRespond":None,"IP_Rule":[],"Level":99,"ReNewApp":1,"Respond":2,"Reverse":"ReverseInfor","SaveBytes":-1,"Trance":1,"Type":100}

DEFAULT_ARG = {"PcapDrop":0,"PbDrop":0,"Type":NDomainType}

def ipList2Rule(ip_list,app_id,name,args=DEFAULT_ARG):
    """
        ipList2Rule: 将ip列表转化为探针的IP规则
    """
    base_rule = copy.deepcopy(BasicRule)
    base_rule.update({
        "APPID":app_id,
        "Name":name,
        "PcapDrop":args["PcapDrop"],
        "PbDrop":args["PbDrop"],
    })
    ip_rules = []
    for ip in ip_list:
        if len(ip)<1:
            continue
        ip_rules.append({"IPPro":{"Negative":[0]},"IPV4":ip,"Port_Rule":{"LowPort":1024,"HightPort":49151,"Property":7,"Sign":2}})
    base_rule["IP_Rule"] = ip_rules
    return base_rule


def domain_list_to_rule(domain_list,app_id,name,args=DEFAULT_ARG):
    """
        domain_list_to_rule: 将域名列表转化为探针的域名规则
    """
    base_rule = copy.deepcopy(BasicRule)
    base_rule.update({
        "APPID":app_id,
        "Name":name,
        "PcapDrop":args["PcapDrop"],
        "PbDrop":args["PbDrop"],
        "Trance":10 # DNS A Record IP
    })
    domain_rules = []
    for domain in domain_list:
        if len(domain)<1:
            continue
        domain_rules.append({"Domain":domain,"Type":args["domain_type"]})
    base_rule["Domain_Rule"] = domain_rules 
    return base_rule


def make_rules(val_list,rule_name,rule_id,make_method,args=DEFAULT_ARG):
    val_cnt,iter_cnt = len(val_list),3000
    result = []
    for i in range(0,val_cnt,iter_cnt):
        vals = val_list[i:min(val_cnt,i+iter_cnt)]
        index = math.ceil(i/iter_cnt)
        rule = make_method(vals,rule_id+index,f"{rule_name}_{index}",args=args)
        result.append(rule)
    return result

def saveRules(data_list,outfile):
    outf = open(outfile,"w+")
    for rule in data_list:
        data = json.dumps(rule) + "\n"        
        outf.write(data)
    outf.close()

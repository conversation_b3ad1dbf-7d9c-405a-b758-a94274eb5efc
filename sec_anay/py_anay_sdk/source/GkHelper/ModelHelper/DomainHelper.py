from GkConfig import base_dir

from publicsuffixlist import PublicSuffixList

from GkUtils.HyperScanHyper import HyperScanMatch

import pandas as pd
import re
import os


QUERY_METHOD_MAP = {
    "000":"normal_query",
    "001":"ptr_query",
    "010":"server_status",
    "011":"notification",
    "100":"update"
}


QUERY_TYPE_MAP = {
    1:"A",
    28:"AAAA",
    5:"CNAME",
    15:"MX",
    47:"NSEC",
    12:"PTR",
    16:"TXT",
    2:"NS"
}

psl = PublicSuffixList()
Hot_TLD_File = os.path.join(base_dir,"GkData/Knowledge/hot_tld.txt")

CDN_SUFFIX_FILE = os.path.join(base_dir,"GkData/Knowledge/cdns.csv")
White_Domain_File = os.path.join(base_dir,"GkData/Knowledge/white_domains.txt")
Custom_White_File =  "Data/Custom/white_domain.csv"

def is_domain(domain):
    p = re.compile(r'^(?:[a-zA-Z0-9]'  # First character of the domain
        r'(?:[a-zA-Z0-9-_]{0,61}[A-Za-z0-9])?\.)'  # Sub domain + hostname
        r'+[A-Za-z0-9][A-Za-z0-9-_]{0,61}'  # First 61 characters of the gTLD
        r'[A-Za-z]$')
    return True if p.match(domain) else False

def Load_CDN_Names():
    df = pd.read_csv(CDN_SUFFIX_FILE)
    return list(df.CN.drop_duplicates())

def Load_White_DomainMap():
    domains = list(l.strip() for l in  open(White_Domain_File).readlines() )
    domain_map = dict((d,i) for i,d in enumerate(domains))
    return domain_map

def Load_Hot_TLDs():
    tlds = set([line.strip() for line in open(Hot_TLD_File).readlines()])
    return tlds



def get_full_domain(url):
    if not url or type(url)!=str:
        return

    url = url if ":" not in url else url.split(":")[-1]
    if "." not in url:
        return url
    tail = psl.publicsuffix(url)
    if tail is None:
        return
    suffix = '.'+tail
    val = url.replace(suffix,"").split('.')
    return (val[-1]+suffix).lower()

class DomainHelper:
    def __init__(self):
        self.cdn_name_list = Load_CDN_Names()
        self.white_map = Load_White_DomainMap()
        self.hot_tld = Load_Hot_TLDs()
        self.cdn_match = self.make_cdn_match()
        self.load_white_domains()

    def make_cdn_match(self, cdn_list=None):
        cdn_list = self.cdn_name_list if cdn_list is None else cdn_list
        patterns = []
        for suffix in cdn_list:
            suffix = suffix.replace(".", "\.")
            pattern = ".*\.{suffix}$".format(suffix=suffix)
            patterns.append(pattern)
        return HyperScanMatch(patterns=patterns,show_list=cdn_list)

    def is_cdn_domain(self,domain):
        is_cdn, _ = self.cdn_match.is_match(domain)
        return is_cdn

    def get_domain_alexa(self,domain):
        if domain in self.white_map:
            return self.white_map[domain]
        return 0x0fffffff

    def is_hot_tld(self,domain):
        if " " in domain or "." not in domain:
            return False
        tail = "."+domain.split(".")[-1]
        return tail in self.hot_tld

    def load_white_domains(self):
        domains = set(get_full_domain(domain) for domain in self.white_map)
        if os.path.exists(Custom_White_File):
            df = pd.read_csv(Custom_White_File)
            custom_white = set(get_full_domain(x) for x in df["domain"])
            domains = domains | custom_white
        self.white_ndomains = domains

    def is_white_ndomain(self,domain):
        ndomain = get_full_domain(domain)
        return ndomain in self.white_ndomains
    
    def get_ndomain(self,domain):
        suffix = get_full_domain(domain)

        is_cdn, cdn_suffix_lists = self.cdn_match.is_match(domain)
        if not is_cdn:
            return suffix
        
        
        # longest cdn suffix
        cdn_suffix_lists = sorted([[v,v.count('.')] for v in cdn_suffix_lists], key=lambda x:x[1], reverse=True)
        cdn_suffix = cdn_suffix_lists[0][0]

        if cdn_suffix.endswith(suffix):
            suffix = cdn_suffix
            
        if suffix is None or suffix==domain:
            return 
        
        suffix = '.' + suffix
        val = domain.replace(suffix, '').split('.')

        if '.'+val[-1] in self.hot_tld:  # if hot tld, extend the next level block
            return '.'.join(val[-2:])+suffix
        else:
            return val[-1] + suffix

domain_helper = DomainHelper()

if __name__=="__main__":
    print(domain_helper.is_hot_tld("baidu.com"))
    print(domain_helper.is_hot_tld("www.0059163.com"))

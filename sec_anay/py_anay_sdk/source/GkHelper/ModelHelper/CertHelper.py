from GkUtils import common

import time
import os

def certTime2int(timeb):
    try:
        strTime = timeb
        timeArray = time.strptime(strTime, "%Y%m%d%H%M%SZ")
        return int(time.mktime(timeArray))    
    except:
        return -1

def trans_certstr_to_list(cert_str):
    certs = cert_str.strip("[ ]").replace(" ",'').replace("'","").replace('"',"").split(",")     
    return certs

def get_leaf_cert_from_list(cert_list):
    if cert_list is None or type(cert_list)==float:
        return common.UNKNOWN
    if type(cert_list)==str and len(cert_list)>0:
        cert_list = trans_certstr_to_list(cert_list)
    if type(cert_list)==list and len(cert_list)>0:
        return cert_list[0]
    return common.UNKNOWN

def getComponentOfJson(X509_cert,component_type="subject"):
    result = {}
    if component_type=="subject":
        l = X509_cert.get_subject().get_components()
    else:
        l = X509_cert.get_issuer().get_components()
    for i in l:
        try:
            result[i[0].decode()] = i[1].decode()
        except:
            continue
    return result

def getExtensions(X509_cert):
    extensions = {}
    e_count = X509_cert.get_extension_count()
    for i in range(e_count):
        try:
            ext = X509_cert.get_extension(i)
            ext_name = ext.get_short_name().decode()          
            extensions[ext_name] = ext.__str__().strip("\n").replace("\n", ",")
        except:
            continue
    return extensions

def getComponentMd5(component):
    r = []
    for k,v in component.items():
        r.append(f"/{k}={v}")
    r_hash = common.md5_of_str("".join(r))
    return r_hash


def get_usage(extensions):
    usages =[]
    if "keyUsage" in extensions:
        usages.append(extensions["keyUsage"])
    if "extendedKeyUsage" in extensions:
        usages.append(extensions["extendedKeyUsage"])
    return ",".join(usages)



def loadCertPaths(cert_list):
    cert_paths = []
    for cert in cert_list:
        path = loadCertPath(cert)
        if path is not None:
            cert_paths.append(path)
    return cert_paths


def loadCertPath(name):
    base_list = ["/data/cerfiles/"]
    for basedir in base_list:
        filename = basedir+name[:4]+'/'+name[4:12]+ '/' + name +'.cert'
        if os.path.exists(filename) and os.path.getsize(filename)>0:
            return filename
    return None
    # base_list = ["/data/cerfiles/"]
    # for i in range(10):
    #     basedir = '/data/cerfiles/%d/'%i
    #     base_list.append(basedir)
    # for basedir in base_list:
    #     filename = basedir+name[:2]+'/'+name+'.cer'
    #     if os.path.exists(filename) and os.path.getsize(filename)>0:
    #         return filename
    # return None


def getCertIdFromCertPath(name):
    endName = name.split('/')[-1]
    endName = endName.strip('.cer')
    if len(endName) == 40:                                
        return endName       
    return


def formatKeyId(cert_key_id):
    cert_key_id = cert_key_id.split(",")[0].strip('keyid:')
    return cert_key_id



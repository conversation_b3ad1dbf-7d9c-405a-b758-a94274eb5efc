
from GkUtils import common


GREASE_TABLE = {'0a0a', '1a1a', '2a2a', '3a3a','4a4a', '5a5a', '6a6a', '7a7a','8a8a', \
    '9a9a', 'aaaa', 'baba','caca', 'dada', 'eaea', 'fafa'}
GREASE_EXT_TABLE =  [eval('0x'+x) for x in GREASE_TABLE]


ExtTyeps = {
    0x10: 'application_layer_protocol_negotiation',
    0x00: 'server_name',
    0x0d: 'signature_algorithms',
    0x12: 'signed_certificate_timestamp',
    0x23: 'session_ticket',
    0x0B: 'ec_point_formats',
    0x0A: 'supported_groups' # elliptic_curves
}


def format_cipher_list(cipher,grease=True,pre_mark=""):
    cipher_list = []
    for i in range(0, len(cipher), 4):
        cipher_str = cipher[i:i+4]
        cipher_int = str(int(cipher_str,16))
        if cipher_str not in GREASE_TABLE:
            cipher_list.append(pre_mark+cipher_int)
        elif grease:
            cipher_list.append(pre_mark+"grease")
    return cipher_list

def get_ext_type_val_map(ext_list):
    extTypeToValue = {}
    if ext_list is None or type(ext_list)==float:
        return extTypeToValue
    if type(ext_list)==str and len(ext_list)>1:
        ext_list = eval(ext_list)
    for ext in ext_list:
        ext_type = ext['t']
        ext_value= ext['v']
        extTypeToValue[ext_type] = ext_value        
    return extTypeToValue


def load_ec_from_ext_str(extTypeToValue,grease=True,pre=""):
    ec_point_formats = extTypeToValue[0x0b] if 0x0b in extTypeToValue else ""
    ec_list = []
    for i in range(2, len(ec_point_formats), 2):
        val = int(ec_point_formats[i:i + 2], 16)
        if val not in GREASE_EXT_TABLE:
            ec_list.append(pre+str(val))
        elif grease:
            ec_list.append(pre+"grease")     
    ec_list_str = '-'.join(ec_list) if len(ec_list)>0 else ''
    return ec_list_str


def load_signature_alo_ext_str(extTypeToValue,grease=True,pre=""):
    alo_str = extTypeToValue[0x0d] if 0x0d in extTypeToValue else ""
    sign_list = []
    for i in range(2, len(alo_str), 4):
        val = int(alo_str[i:i + 4], 16)
        sign_list.append(pre+str(val))
    sign_list_str = '-'.join(sign_list) if len(sign_list)>0 else ''
    return sign_list_str

def load_ep_from_ext_str(extTypeToValue,grease=True,pre=""):
    elliptic_curves = extTypeToValue[0x0a] if 0x0a in extTypeToValue else ""
    ep_list = []    
    for i in range(4, len(elliptic_curves), 4):
        val = int(elliptic_curves[i:i + 4], 16)
        if val not in GREASE_EXT_TABLE:
            ep_list.append(pre+str(val))
        elif grease:
            ep_list.append(pre+"grease")
    ep_list_str = '-'.join(ep_list) if len(ep_list)>0 else ''
    return ep_list_str


def get_ext_list_str(extTypeToValue,grease=True,pre=""):
    extList = []
    for ext_type in extTypeToValue:
        if ext_type not in GREASE_EXT_TABLE:
            extList.append(pre+str(ext_type))
        elif grease:
            extList.append(pre+"grease")
    extListStr = '-'.join(extList) if len(extList)>0 else ''
    return extListStr

    
def get_alpn_str(alpn):
    if alpn is None or type(alpn) == float:
        return ""
    if type(alpn)  == str and len(alpn)>1:
        alpn = eval(alpn)
    return  "-".join(alpn) 

def get_geek_finger_dict(data,grease=True):
    if data["version"]==0 or data["cipher"] is None:
        return {}
    result = {
        "version": data["version"],
        "compress": data["compress"],
        "cipher": '-'.join(format_cipher_list(data["cipher"],grease))
    }
    extTypeToValue = get_ext_type_val_map(data["extensions"])

    result["extensions"] = get_ext_list_str(extTypeToValue,grease)
    result["is_random"] = 1 if abs(data["begin_time"] - data["random_time"])>600 else 0
    result["ec_format"] = load_ep_from_ext_str(extTypeToValue,grease)
    # add elliptic_curves   
    result["elliptic_curves"] = load_ec_from_ext_str(extTypeToValue,grease)
    # add alpn   
    result["alpn"] = get_alpn_str(data["alpn"])
    # signature_algorithms  
    result["signature_algorithms"] = load_signature_alo_ext_str(extTypeToValue,grease)
    result["status_request"] = "" if 0x05 not in extTypeToValue else extTypeToValue[0x05]
    return result

def get_gk_finger_cols():
    cols = "version,compress,cipher,extensions,is_random,ec_format,elliptic_curves,alpn,signature_algorithms,status_request"
    return cols

def get_gk_finger(data,grease=True):
    """
        生成GkSec专用的TLS指纹
    """
    if data["version"]==0 or data["cipher"] is None:
        return common.UNKNOWN
    cols = get_gk_finger_cols().split(",")
    cfinger = get_geek_finger_dict(data,grease)
    result = [str(cfinger[key]) for key in cols]
    return ",".join(result)


def get_ja3(data,grease=False):
    """
        生成Ja3指纹
    """
    if data["version"]==0 or data["cipher"] is None:
        return common.UNKNOWN
    gk_finger = get_geek_finger_dict(data,grease)
    return trans_finger_str_to_ja3(gk_finger)


def get_ja3s(data,grease=False):
    """
        生成Ja3指纹
    """
    if data["version"]==0 or data["cipher"] is None:
        return "unk"
    gk_finger = get_geek_finger_dict(data,grease)
    return trans_finger_str_to_ja3s(gk_finger)

def filter_cols_from_gkfinger(gk_finger,columns=None,out_type=dict):
    col_map = dict((x,i) for i,x in enumerate(get_gk_finger_cols().split(",")))
    if columns is None:
        columns = get_gk_finger_cols()
    if type(columns) == str:
        columns = columns.split(",")
    vals = gk_finger.split(",")
    result = {}
    for key in columns:
        index = col_map[key]
        result[key] = vals[index]
    if out_type==str:
        return ",".join(result.values())
    return result


def trans_finger_str_to_ja3(gk_finger):
    return filter_cols_from_gkfinger(gk_finger,columns="version,cipher,extensions,ec_format,elliptic_curves",out_type=str)

def trans_finger_str_to_ja3s(gk_finger):
    return filter_cols_from_gkfinger(gk_finger,columns="version,cipher,extensions",out_type=str)

def trans_finger_str_to_dict(gk_finger):
    return filter_cols_from_gkfinger(gk_finger)


def get_client_finger(raw_data):
    ssl_trans_map = {
        'CH_Ciphersuit': 'cipher',
        'CH_CompressionMethod': 'compress',
        'CH_Extention': 'extensions',
        'CH_Time':'random_time',
        "CH_ALPN":"alpn",
        'CH_Version':'version',    
        'StartTime':'begin_time',
    }
    data = common.trans_data_of_map(raw_data,ssl_trans_map)
    return get_gk_finger(data)


def get_server_finger(raw_data):
    ssl_trans_map = {
        'SH_Cipersuite': 'cipher',    
        'SH_CompressionMethod': 'compress',
        'SH_Extention': 'extensions',
        'SH_Time':'random_time',
        "SH_ALPN":"alpn",
        'SH_Version':'version',
        'StartTime':'begin_time',
    }
    data = common.trans_data_of_map(raw_data,ssl_trans_map)
    return get_gk_finger(data)

           


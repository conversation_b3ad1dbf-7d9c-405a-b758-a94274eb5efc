import sys
sys.path.append("./")


import pandas as pd
import json
import os
from enum import Enum
from GkConfig import base_dir

Ja3HashToDescFile = os.path.join(base_dir,"GkData/Knowledge/ja3_hash2desc.csv")
Ja3DescToTypeFile = os.path.join(base_dir,"GkData/Knowledge/ja3_desc2type.csv")


class Ja3Type(Enum):
    white = 0xf
    tool = 0x0ff
    black = 0x09
    regex = 3
    unk = -1
    no_exist = -2

def isRegex(desc):
    v = "$^=-?+#:}"
    if len(set(v) & set(desc)) < 4:
        return False
    ch_cnt = 0
    for char in v:
        ch_cnt += desc.count(char)
    return ch_cnt>10

def isInSet(desc,keys):
    for key in keys:
        if key in desc:
            return True
    return False


class Ja3Helper:
    def __init__(self):
        ja3_type_df = pd.read_csv(Ja3DescToTypeFile)
        self.white_keys = set(ja3_type_df[ja3_type_df.ja3_type=="white"].keyword)
        self.black_keys = set(ja3_type_df[ja3_type_df.ja3_type=="black"].keyword)
        self.tool_keys = set(ja3_type_df[ja3_type_df.ja3_type=="tool"].keyword)
        self.JA3_Hash2Type = self.loadJa3TypeMap()


    def getDescType(self,desc):
        """
            White Must Before Black
        """
        if isInSet(desc,self.white_keys):
            return Ja3Type.white
        if isInSet(desc,self.black_keys):
            return Ja3Type.black
        if isInSet(desc,self.tool_keys):
            return Ja3Type.tool
        if isRegex(desc):
            return Ja3Type.regex
        return Ja3Type.unk


    def loadJa3TypeMap(self):
        df = pd.read_csv(Ja3HashToDescFile)
        df["ja3_type_val"] = df.desc.map(lambda x : self.getDescType(x).value)
        ja3_list = json.loads(df.to_json(orient='index'))
        ja3HashToType = {}
        for x in ja3_list.values():
            k,v = x['ja3_hash'],x
            if k not in ja3HashToType or ja3HashToType[k]["ja3_type_val"]<v["ja3_type_val"]:
                ja3HashToType[k] =v 
        for k,v in ja3HashToType.items():
            ja3HashToType[k]["ja3_type"] = Ja3Type(v["ja3_type_val"])
        return ja3HashToType

    def getJa3HashType(self,hash_val):
        if hash_val in self.JA3_Hash2Type:
            name = self.JA3_Hash2Type[hash_val]["ja3_type"].name
            return name
        return Ja3Type.no_exist.name


ja3_helper = Ja3Helper()
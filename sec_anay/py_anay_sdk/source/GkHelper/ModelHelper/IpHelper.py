import os
import re
import socket, struct
import IPy
import pandas as pd

from GkConfig import base_dir
from GkUtils.common import InnerNet

CDN_IP_FILE = os.path.join(base_dir,"GkData/Knowledge/cdn_ip.csv")

def load_cdn_ip():
    df = pd.read_csv(CDN_IP_FILE)
    df['cdn'] = df.cdn.map(lambda x:x.rsplit('.',1)[0])
    return dict((df.loc[index,'cdn'], df.loc[index,'cdn_tag']) for index in df.index)

def is_ip(str):
    return isIPV4(str) or isIPV6(str)
    
def isIPV4(str):
    '''
        判断字符串是否为IPV4
        str: 字符串内容
        :returns: 为IPV4,True/False
    '''
    p = re.compile('^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$')
    if p.match(str):
        return True
    else:
        return False


def isIPV6(str):
    try:
        version = IPy.IP(str).version()
        return version==6
    except:
        return False

def containsIPV4(str):
    '''
        判断字符串是否为包含IPV4
        str: 字符串内容
        :returns: 为IPV4,True/False
    '''
    p = re.compile('^(.)*((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)(.)*$')
    if p.match(str):
        return True
    else:
        return False

# 子网掩码长度转地址
def netmask_to_bit_length(netmask):
    return sum([bin(int(i)).count('1') for i in netmask.split('.')])

# 子网掩码长度转地址
def bit_length_to_netmask(mask_int):
    bin_array = ["1"] * mask_int + ["0"] * (32 - mask_int)
    tmpmask = [''.join(bin_array[i * 8:i * 8 + 8]) for i in range(4)]
    tmpmask = [str(int(netmask, 2)) for netmask in tmpmask]
    return '.'.join(tmpmask)

def ipmask_to_tuple(ip_mask):
    if ":" in ip_mask:
        return ip_mask,""
    vals = ip_mask.split("/")
    ip = vals[0]
    if len(vals)==1:
        mask_int = 32
    else:
        mask_int=int(vals[1])
    mask = bit_length_to_netmask(mask_int)
    return ip,mask


def long2ip(val):
    return socket.inet_ntoa(struct.pack('!L', val))

def ip2long(ip):
    if ":" in ip:
        return int_from_ipv6(ip)
    packedIP = socket.inet_aton(ip)
    return struct.unpack("!L", packedIP)[0]

def int_from_ipv6(addr):
    if re.match(r"^(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4}$", str, re.I):
        return True
    hi, lo = struct.unpack('!QQ', socket.inet_pton(socket.AF_INET6, addr))
    return (hi << 64) | lo


def compare_ip_in_net(ip,ip_net):
    if type(ip)==str:
        ip = ip2long(ip)
    vals = ip_net.split("/")
    mask_int = int(vals[1])
    key = ip2long(vals[0]) >> mask_int
    val = ip >> mask_int
    return key == val
        

def is_local_ip(ip):
    if not isIPV4(ip):
        return False
    ip_int = ip2long(ip)
    for local_ip in InnerNet:
        if compare_ip_in_net(ip_int,local_ip):
            return True
    return False

class IpHelper:
    def __init__(self):
        self.cdn_map = load_cdn_ip()

    def check_ip(self, ip):
        if not isIPV4(ip) or (prefix:=ip.rsplit('.',1)[0]) not in self.cdn_map:
            return False
        return self.cdn_map[prefix].upper()

ip_helper = IpHelper()


import json
import os

base_dir = os.path.dirname(__file__)

GK_CONFIG_FILE = os.path.join(base_dir,"GkData/DefaultConfig.json")
env = json.load(open(GK_CONFIG_FILE))

CUSTOM_CONFIG_FILE = "Data/config.json"
if os.path.exists(CUSTOM_CONFIG_FILE):
    env.update(json.load(open(CUSTOM_CONFIG_FILE)))

TAG_CONFIG_FILE = os.path.join(base_dir,"GkData/gk_label.csv")
CUSTOM_TAG_FILE = "Data/label.csv"

ALARM_CONFIG_FILE = os.path.join(base_dir,"GkData/gk_alarm.json")
CUSTOM_ALARM_FILE = "Data/alarm.json"

ALARM_KNOW_CONFIG_FILE = os.path.join(base_dir,"GkData/gk_alarm.csv")
CUSTOM_KNOW_ALARM_FILE = "Data/alarm.csv"

GK_MODEL_FILE = os.path.join(base_dir,"GkData/DefaultModels.json")
CUSTOM_MODEL_FILE = "Data/models.json"
JAR_FILE = os.path.join(base_dir,"GkJar/LmdbFileLoader-1.0.jar")




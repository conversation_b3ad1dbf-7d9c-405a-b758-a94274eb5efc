#!/bin/bash
export THE_ROOT=/opt/GeekSec/th/
BASEDIR="${THE_ROOT}/bin/conf/"

for THID in $(seq 0 1)
do
    mkdir -p ${BASEDIR}/${THID}/JsonRule/BasicRule/UserRule/PrivateRule/
    mkdir -p ${BASEDIR}/${THID}/JsonRule/BasicRule/LibFolder/
    
    /usr/bin/cp -rf update_anay/private_json/* ${BASEDIR}/${THID}/JsonRule/BasicRule/UserRule/PrivateRule/
    /usr/bin/cp -rf update_anay/private_so/* ${BASEDIR}/${THID}/JsonRule/BasicRule/LibFolder/
done

cd /opt/GeekSec/th/bin/ && ./thd.all.restart
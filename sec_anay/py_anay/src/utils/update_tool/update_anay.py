import sys
sys.path.append("./")

import os
import pandas as pd

from PyGksec.GkConfig import CUSTOM_TAG_FILE, CUSTOM_KNOW_ALARM_FILE
# from PyGksec.GkHelper.TagHelper import install
# from utils.deploy.alarm_install import update_alarm_table

CurrentDir = os.getcwd()
SRC_DIR = os.path.join(CurrentDir,"update_anay")


for filename in os.listdir(SRC_DIR):
    if filename.startswith("private") or filename.startswith("update") or filename.startswith("README"):
        continue
    if filename == "PyGksec":
        os.popen(f"/usr/bin/cp -rf {os.path.join(SRC_DIR, filename)}/* ./PyGksec/").read()
    else:
        os.popen(f"/usr/bin/cp -rf {os.path.join(SRC_DIR, filename)} ./").read()


# install()  # 更新标签
# update_alarm_table()  # 更新告警
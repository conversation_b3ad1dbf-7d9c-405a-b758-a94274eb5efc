import sys
import os
import scapy.all as scapy
import pandas as pd


def readSessionFromPcap(infile):
    packets = scapy.rdpcap(infile)
    result = []
    for pkt in packets:
        if "IP" not in pkt:
            continue
        if "TCP" in pkt:
            pro = "TCP"
        elif "UDP" in pkt:
            pro = "UDP"
        else:
            continue
        sip,sport,dip,dport = pkt["IP"].src,pkt[pro].sport,pkt["IP"].dst,pkt[pro].dport
        r = {
            "sip":sip,
            "dip":dip,
            "sport":sport,
            "dport":dport,
            "pro":pro,
            "filename":infile
        }
        if sip > dip:
            sip,sport,dip,dport = dip,dport,sip,sport
        r["ip_tuple"] = f"{sip}_{dip}"
        result.append(r)
    df = pd.DataFrame(result)
    df = df.drop_duplicates()
    return df

def readSessionFromDir(indir):
    df_list = []
    for root,_,names in os.walk(indir):
        for name in names:
            if name.lower().endswith("cap"):
                filename = os.path.join(root,name)
                df_list.append(readSessionFromPcap(filename))
    df = pd.concat(df_list)
    print(df)
    return df


"""
python utils/anay_tool/read_pcap.py /home/<USER>/done/test_wl_pcap/pcap_godoh ./tmp/sessions.csv
"""

if __name__=="__main__":
    infile = sys.argv[1]
    outname = sys.argv[2]
    print(infile,outname)
    if os.path.isdir(infile):
        df = readSessionFromDir(infile)
    else:
        df = readSessionFromPcap(infile)
    df.to_csv(outname,index=False)

import pandas as pd

"""
    depend on
    python utils/anay_tool/read_pcap.py /home/<USER>/done/test_wl_pcap/pcap_godoh ./tmp/sessions.csv
    python utils/anay_tool/export_ssl.py 
    python Detect/Finger/GetFingerFromES.py
"""

ssl_file = "tmp/ssl_out.csv"
session_file = "tmp/sessions.csv"
finger_file = "tmp/gk_finger.csv"

ssl_df = pd.read_csv(ssl_file)
session_df = pd.read_csv(session_file)
finger_df = pd.read_csv(finger_file)
df = pd.merge(ssl_df,session_df,how="left",on="ip_tuple")
df = df[~df.filename.isna()]
df["family"] = df.filename.map(lambda x : x.split("/")[-2])
df = df[df.cSSLFinger!=0]

q = df[["cSSLFinger","family"]].drop_duplicates()\
    .groupby("cSSLFinger",as_index=False).count()
r = set(q[q.family==1].cSSLFinger)
df = df[df.cSSLFinger.map(lambda x : x in r)]

pd.set_option("display.max_colwidth",40)
q = df[["cSSLFinger","family"]].drop_duplicates()
q["finger_es"] = q["cSSLFinger"]
q = pd.merge(q,finger_df,how="left",on="finger_es")
q["finger_type"] = q["family"]



q = q[["finger_es","finger_content","ja3_hash","ja3_hash_type","es_type","finger_type"]]
print(q)
q = q[["finger_es","finger_content","ja3_hash","es_type","finger_type"]]
q.to_csv("tmp/finger_es.csv",index=False)
print(q.drop_duplicates())

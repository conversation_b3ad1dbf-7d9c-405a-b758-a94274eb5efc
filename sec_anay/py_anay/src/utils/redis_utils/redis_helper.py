import sys
sys.path.append("./")

import subprocess
from PyGksec.GkHelper.GkLogHelper import gk_logging
from PyGksec.GkConfig import env

import pandas as pd
import redis
import time
import os
import json
import logging

REDIS_VALS = env["REDIS_HOST"].split(":")
REDIS_HOST,REDIS_PORT = REDIS_VALS[0],REDIS_VALS[1]
MY_REDIS_POOL = redis.ConnectionPool(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True,db=2,max_connections=200)
r = redis.Redis(connection_pool=MY_REDIS_POOL)

# 查询redis中是否存在该告警，有的话就不进行重复告警，没有才告警
def redis_not_alarm(attack_chain_list):
    result = False
    for attack_chain in attack_chain_list:
        redis_result = r.get(attack_chain)
        if redis_result==None:
            r.setex(attack_chain,10*60,"1")
            result = True
    return result

def flush():
    r.delete(*r.keys())

def check_redis_alive():
    try:
        logging.info(r.ping())
    except:
        logging.info("redis服务异常")

if __name__ == '__main__':
    # check_redis_alive()
    flush()
import sys
sys.path.extend("./")

from PyGksec.GkHelper.RedisHelper import REDIS_CLI,check_redis_open
from PyGksec.GkHelper.GkAnayTaskHelper import get_name_for_task_and_batch
from PyGksec.GkConfig import env,GK_MODEL_FILE,CUSTOM_MODEL_FILE
from PyGksec.GkHelper.ESHelper import get_run_arg
from PyGksec.GkHelper.GkLogHelper import gk_logging

from Detect.Cert.LoadCert import CertDetectTask
from Detect.APT.APT_Models import APT29_DetectTask,APT28_DetectTask,Patchwork_DetectTask
from Detect.Mining.Mine_Models import Candidate_Domain_Miner_Task,TLSMiner_Task,Alarm_Miner_Task,Sync_Threat_Miner_Task
from Detect.SeqAction.Seq_Models import Session_Detect_Task,Multi_Session_Detect_Task,FingerRandomDetectTask,Activation_Detect_Task
from Detect.Finger.Finger_Models import Finger<PERSON>alwareFamily_DetectTask
from Detect.RAT.RAT_Models import DomainFront_RAT_Detect_Task
from Detect.Domain.DetectAbnormalDNS_Server import DNS_ServerDetectTask
from Detect.Domain.DNS_Tunnnel.detect_dns_tunnel import DNS_TunnelDetectTask
from Detect.NetProperty.DetectInnerMac import InnerMacDetectTask
import PyGksec.GkHelper.ESHelper as eh
import time
import os
import json

# 模型列表
AnayTaskMap = {
    "LoadCert":CertDetectTask("LoadCert"),
    "APT29":APT29_DetectTask("APT29"),
    "APT28":APT28_DetectTask("APT29"),
    "TLSMiner":TLSMiner_Task("TLSMiner"),
    "CandidateMiner":Candidate_Domain_Miner_Task("CandidateMiner"),
    # "MinerDetect":Alarm_Miner_Task("MinerDetect"),
    "SynMineThreat":Sync_Threat_Miner_Task("SynMineThreat"),
    "Patchwork":Patchwork_DetectTask("Patchwork"),
    "SesssionPktSeq":Session_Detect_Task("SesssionPktSeq"),
    "MultiSesssionPktSeq":Multi_Session_Detect_Task("MultiSesssionPktSeq"),
    "FingerFamily":FingerMalwareFamily_DetectTask("FingerFamily"),
    "DomainFrontRAT":DomainFront_RAT_Detect_Task("DomainFrontRAT"),
    "ActivationDetect":Activation_Detect_Task("ActivationDetect"),
    "AbnormalDNSServerDetect":DNS_ServerDetectTask("AbnormalDNSServerDetect"),
    # "DNSTunnelDetect":DNS_TunnelDetectTask("DNSTunnelDetect"),
    # "FingerRandomDetect":FingerRandomDetectTask("FingerRandomDetect"),
    "DetectInnerMac":InnerMacDetectTask("DetectInnerMac")


}


def load_open_models():
    if os.path.exists(CUSTOM_MODEL_FILE):
        models = json.load(open(CUSTOM_MODEL_FILE))
    else:
        models = json.load(open(GK_MODEL_FILE))
    return models

def add_model_task_to_redis(model_name,task_id,batch_id):
    """[向Redis写入任务]

    Args:
        model_name ([str]): [模型名]
        task_id ([int]): [TaskId for run]
        batch_id ([int]): [BatchID for run]
    Returns:
        [TaskName]: [TaskName to save in Redis]
    """
    task_name = get_name_for_task_and_batch(model_name,task_id,batch_id)
    task_score = int(time.time())
    REDIS_CLI.execute_command('ZADD', env["REDIS_ANAY_KEY"], 'NX', task_score, task_name)
    gk_logging.info(f"ADD REDIS_Task {task_name} of score {task_score}")


## 使用Redis管理模型
def add_models_to_redis():
    task_id,batch_id = get_run_arg("connect*")
    for model_name,model_conf in load_open_models().items():
        if model_conf["open"] and model_name in AnayTaskMap:
            add_model_task_to_redis(model_name,task_id,batch_id)


def run_models():
    if env["PRODUCT"] == "probe":
        for task_id,batch_id in eh.get_all_tasks():
            run_task_models(task_id,batch_id)
    else:
        task_id,batch_id = get_run_arg("connect*")
        run_task_models(task_id,batch_id)


## 实时运行模型
def run_task_models(task_id,batch_id):
    for model_name,model_conf in load_open_models().items():
        if model_conf["open"] and model_name in AnayTaskMap:
            gk_logging.info(f"*** START RUN {model_name} MODEL")
            AnayTaskMap[model_name].run(task_id,batch_id)


if __name__=="__main__":
    if env["DEPLOY_MODE"]=="product":
        add_models_to_redis()
    else:
        run_models()


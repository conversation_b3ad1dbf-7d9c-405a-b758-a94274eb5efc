import sys
import os
import pandas as pd

"""
    生成标签知识库 复制标签文档中
    python utils/deploy/RemakeAlarm.py tmp/alarm.csv PyGksec/GkData/gk_alarm.csv

"""

COL_MAP = {
    "告警ID": "alarm_knowledge_id",
    "告警名称":"alarm_name",
    "黑名单权重": "attack_level",
    "威胁等级": "level",
    "说明": "alarm_principle", 
    "攻击类型": "attack_chain_name",
    "ATT&CK": "mitre_chain_name",
    "包含标签": "include_tags",
    "排除标签": "exclude_tags"
}

infile = sys.argv[1]
outfile = sys.argv[2]

inf = pd.read_csv(infile)
inf = inf[list(COL_MAP.keys())]
inf.columns = list(COL_MAP.values())
if "State" in set(inf.columns):
    inf = inf[inf.State!="删除"]

print(inf)
saved_cols = sorted(set(inf.columns) - {"State","Short_Name","Desc"})
inf = inf[list(saved_cols)]
inf.to_csv(outfile,index=False)

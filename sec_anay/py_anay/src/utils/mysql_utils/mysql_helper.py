import sys
sys.path.append("./")
import csv
import pymysql
import datetime
from utils.redis_utils.redis_helper import *
from dbutils.pooled_db import PooledDB
from PyGksec.GkConfig import env

vals = env["MYSQL_HOST"].split(":")
pool_th = PooledDB(
    creator=pymysql,   # 使用 PyMySQL 作为连接池的创建者
    maxconnections=5,  # 连接池中最大连接数
    mincached=2,       # 连接池中至少保留的空闲连接数
    maxcached=5,       # 连接池中最多保留的空闲连接数
    host=vals[0],  # 数据库主机地址
    # port=vals[1],
    user="root" if "MYSQL_USER" not in env else env["MYSQL_USER"],  # 数据库用户名
    passwd="root" if "MYSQL_PASS" not in env else env["MYSQL_PASS"],  # 数据库密码
    database='th_analysis',  # 默认使用的数据库名称
    charset='utf8',
    cursorclass=pymysql.cursors.DictCursor
)

def init_mysql_alarm_white():
    db = pool_th.connection()
    cursor = db.cursor()

    make_table = """
    create table if not exists th_analysis.tb_alarm_white
    (
        id             int               auto_increment comment '唯一主键ID'  primary key,
        victim         varchar(256)      null           comment '受害者IP',
        attacker       varchar(256)      null           comment '攻击者IP',
        label          varchar(256)      null           comment '告警相关标签'
    );
    """     
    cursor.execute(make_table)
    sql = f"UPDATE tb_attack_type SET attack_type_name='命令控制' WHERE attack_type_id = 4" # tb_attack_type
    cursor.execute(sql)
    db.commit() # 提交数据

    cursor.close() # 关闭游标
    db.close() # 关闭数据库
    print("mysql数据库表初始化完成")

def mysql_not_white(attack_chain_list):
    db = pool_th.connection()
    cursor = db.cursor()
    not_white=False
    for attack_chain in attack_chain_list:
        attack_info = attack_chain.split("_")
        victim,attacker,label = attack_info[0],attack_info[1],attack_info[2]
        sql = f"SELECT * FROM tb_alarm_white WHERE victim = '{victim}' and attacker = '{attacker}' and label = '{label}' LIMIT 1" # tb_alarm_white
        print(sql)
        cursor.execute(sql) #执行SQL语句
        result = cursor.fetchone()
        if result is None:
            sql_victim_count = f"SELECT COUNT(*) FROM tb_alarm_white WHERE victim = '{victim}' AND label = '{label}'"
            sql_attacker_count = f"SELECT COUNT(*) FROM tb_alarm_white WHERE attacker = '{attacker}' AND label = '{label}'"
            cursor.execute(sql_victim_count)
            victim_count = cursor.fetchone()["COUNT(*)"]
            cursor.execute(sql_attacker_count)
            attacker_count = cursor.fetchone()["COUNT(*)"]
            if victim_count<=2 and attacker_count<=2:
                not_white = True
                break
    return not_white

if __name__=="__main__":
    init_mysql_alarm_white()
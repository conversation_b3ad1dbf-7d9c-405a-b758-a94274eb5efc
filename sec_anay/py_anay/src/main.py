import sys
sys.path.append("./")

from PyGksec.GkHelper.RedisHelper import REDIS_CLI,check_redis_open

from PyGksec.GkConfig import env
from PyGksec.GkHelper.GkAnayTaskHelper import parse_anay_task_info
from PyGksec.GkHelper.GkLogHelper import gk_logging

from utils.deploy.RunAnayModels import AnayTaskMap,run_models,add_models_to_redis,run_task_models
from PyGksec.GkUtils.Auth import checkAuthored
import schedule
from Detect.Ability.detect_main import add_label, add_alarm, add_hengshui_label
from PyGksec.GkHelper.TagHelper import install
from utils.deploy.alarm_install import update_alarm_table
from Finger_API.sql_finger import update_mysql_finger
from utils.mysql_utils.mysql_helper import init_mysql_alarm_white

import time

def check_models_from_redis():
    """[ 检测redis数据，运行检测模型 ]
    Args:
    Returns:
       finish_task_cnt : 完成检测模型数量
    """
    finish_task_cnt = 0
    try:
        models = REDIS_CLI.zrange(env["REDIS_ANAY_KEY"],start=0,end=1)
    except Exception as err:
        gk_logging.warn(err)
        return finish_task_cnt

    for task_name in models:
        print(task_name)
        model_name,task_id,batch_id = parse_anay_task_info(task_name)
        print(model_name)
        if model_name in AnayTaskMap:        
            model = AnayTaskMap[model_name]
            gk_logging.info(f"RUN REDIS_Task {task_name}")
            model.run_task(task_id,batch_id)
            finish_task_cnt+=1
        else:
            gk_logging.warn(f"Remove Unknown Model_Name From REDIS_Task {model_name}")
            REDIS_CLI.zrem(env["REDIS_ANAY_KEY"],task_name)
    return finish_task_cnt


def run():
    if check_redis_open():
        check_models_from_redis()
    else:
        run_task_models()


if __name__ == "__main__":
    # 以下步骤已经在geeksec-secure-boot中实现
    # init_mysql_alarm_white()
    # install()
    # update_alarm_table()
    # update_mysql_finger()
    schedule.every(5).minutes.do(add_label)
    schedule.every(5).minutes.do(add_hengshui_label)
    schedule.every(10).minutes.do(add_alarm)
    schedule.every(10).minutes.do(add_models_to_redis)
    schedule.every(10).minutes.do(run)

    while True:
        schedule.run_pending()
        time.sleep(1)

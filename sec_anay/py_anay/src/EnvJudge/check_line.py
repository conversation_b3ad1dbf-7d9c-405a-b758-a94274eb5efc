import sys
sys.path.append("./")


import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkUtils import common 

import copy

NUM_SHOLD = 2

def get_net_es_cnt(table,detail):
    es_result = eh.es.search(index=table, size=0, body=detail, request_timeout=90)
    return es_result["hits"]["total"]


def get_time_range(table,detail):
    detail["aggs"] = eh.make_stats_agg("StartTime","start_time")
    r = eh.load_agg_data(table,detail,data_key="start_time",agg_type="stat")
    stime = common.time_int_to_str(r["min"])
    etime = common.time_int_to_str(r["max"])
    print(stime,etime)

def check_line(task_id,batch_id):
    table,base_detail = eh.get_basic_query("connectinfo",batch_id=batch_id)
    base_detail["query"]["bool"]["must"].append({"term":{"IPPro":6}})

    send_loss_detail = copy.deepcopy(base_detail)
    send_loss_detail["query"]["bool"]["must"].append({"range":{"pkt.sNum":{"lte":0}}})
    send_loss_detail["query"]["bool"]["must"].append({"range":{"pkt.dPayloadNum":{"gte":NUM_SHOLD}}})

    recv_loss_detail = copy.deepcopy(base_detail)
    recv_loss_detail["query"]["bool"]["must"].append({"range":{"pkt.dNum":{"lte":0}}})
    recv_loss_detail["query"]["bool"]["must"].append({"range":{"pkt.sPayloadNum":{"gte":NUM_SHOLD}}})

    total_detail = copy.deepcopy(base_detail)
    total_detail["query"]["bool"]["should"].append({"range":{"pkt.dPayloadNum":{"gte":NUM_SHOLD}}})
    total_detail["query"]["bool"]["should"].append({"range":{"pkt.sPayloadNum":{"gte":NUM_SHOLD}}})

    recv_loss_cnt = get_net_es_cnt(table,recv_loss_detail)
    send_loss_cnt = get_net_es_cnt(table,send_loss_detail)
    total_cnt = get_net_es_cnt(table,total_detail)
    print(send_loss_cnt,recv_loss_cnt,total_cnt)

    get_time_range(table,send_loss_detail)
    get_time_range(table,recv_loss_detail)
    get_time_range(table,total_detail)



"""
    检测镜像线路问题，是否存在比较多的单向会话
"""

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    check_line(task_id,batch_id)
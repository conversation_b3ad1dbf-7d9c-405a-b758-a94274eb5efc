import sys

from requests import session
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.ModelHelper.IpHelper as ip_helper
import PyGksec.GkHelper.TagHelper as tag_helper

def add_host_diff_tag(task_id,batch_id,host,ip):
    table,detail = eh.get_basic_query(es_type="http", task_id=task_id, batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"term":{"Host.keyword":host}})
    detail["query"]["bool"]["must"].append({"term":{"dIp":ip}})
    df = eh.load_es_data_to_DF(table,detail,ignore_file=True,limit=10)
    df["TaskId"] = task_id
    df = df[["SessionId","TaskId","sIp","dIp","sPort","dPort"]].drop_duplicates()
    df.apply(lambda x : tag_helper.add_tag_for_session(tag_text="目的IP和HOST不一致",session=x) ,axis=1)

    
def detect_http_field(task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="http", task_id=task_id, batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"wildcard":{"Host.keyword":"*.*.*.*:*"}})
    col_dict = {"doc['dIp'].value":"dip","doc['Host.keyword'].value":"host"}
    detail["aggs"] = eh.make_term_agg(col_dict,"http_tuple")
    for x in eh.load_agg_data(table,detail,agg_type="term",data_key="http_tuple"):
        data = eh.format_list_agg(col_dict,x)
        host_ip = data["host"].split(":")[0]
        if ip_helper.is_ip(host_ip) and host_ip!=data["dip"]:
            print(data["host"],data["dip"])
            add_host_diff_tag(task_id,batch_id,data["host"],data["dip"])


if __name__ == "__main__":
    task_id,batch_id = eh.get_run_arg("http*")
    detect_http_field(task_id,batch_id)
import sys
sys.path.append("./")

from PyGksec.GkHelper.ModelHelper.DomainHelper import domain_helper
from PyGksec.GkHelper.KnowledgeHelper import ThreatDF
from PyGksec.GkHelper.ES_Pattern import get_resolved_ip_of_domains
from PyGksec.GkUtils import common

import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.ESHelper as eh




def run(task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="dns",task_id=task_id,batch_id=batch_id)
    domains = list(ThreatDF[ThreatDF.group=="ThreatInfo"][ThreatDF.target_type=="domain"].target)
    domains = set(domains) - set(domain_helper.white_map.keys())
    domains = list(domains)
    detail["query"]["bool"]["must"].append({"terms":{"Domain.keyword":domains}})
    domain_aggs = eh.make_term_agg(query_key="DomainIp.keyword",result_key="domain_agg",size=common.MAX_AGG_CNT)
    detail["aggs"] = domain_aggs
    r = eh.load_agg_data(table,detail,"domain")
    print(r)
    # ips = get_resolved_ip_of_domains(table,base_detail=detail,search_domains=domains,include_wild=True)
    # ips = list(ips)
    # table,detail = eh.get_basic_query(es_type="connectinfo",task_id=task_id,batch_id=batch_id)
    # detail["query"]["bool"]["must"].append({"terms": {"dIp":ips}})
    # df = eh.load_es_data_to_DF(table,detail) 
    # if df is not None:
    #     df.apply(lambda x : tag_helper.add_tag_for_session("恶意域名关联会话",x),axis=1)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    run(task_id,batch_id)
import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkHelper.GkAnayTaskHelper import G_AnayModel
from Detect.SeqAction.DetectMultiSessions import check_ip_tuples
from Detect.SeqAction.DetectSingleSession import detect_single_session
from Detect.SeqAction.DetectActivation import detect_actiation_actions
from Detect.SeqAction.DetectRandomFinger import detect_random_finger_attack


class Session_Detect_Task(G_AnayModel):
     def run(self,task_id,batch_id):
        detect_single_session(task_id,batch_id)


class Multi_Session_Detect_Task(G_AnayModel):
     def run(self,task_id,batch_id):
        check_ip_tuples(task_id,batch_id)

class Activation_Detect_Task(G_AnayModel):
     def run(self,task_id,batch_id):
        detect_actiation_actions(task_id,batch_id)

class FingerRandomDetectTask(G_AnayModel):
     def run(self,task_id,batch_id):
        detect_random_finger_attack(task_id,batch_id)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    detect_single_session(task_id,batch_id)
    check_ip_tuples(task_id,batch_id)

from pyexpat import features
import sys
sys.path.append("./")


import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper


SPAYLOAD_SHOLD = 50
DPAYLOAD_SHOLD = 50
DURATION_SHOLD = 90
DURATION_SHOLD_MAX = 86400

def get_time_delta_feature(data):
    delta_list = data["PacketDistDiurList"]
    feature = {
        "cmd_cnt": sum(delta_list[2:4]),
        "long_cnt": sum(delta_list[4:])
    }
    return feature


def check_session(data,task_id=0):
    feature = get_time_delta_feature(data)
    if feature["cmd_cnt"]+feature["long_cnt"]>30:
        tag_helper.add_tag_for_session(tag_text="包间隔异会话",session=data)


def detect_single_session(task_id,batch_id):
    table,detail = eh.get_basic_query("connectinfo",batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"range":{"pkt.sPayloadNum":{"gte":SPAYLOAD_SHOLD}}})
    detail["query"]["bool"]["must"].append({"range":{"pkt.dPayloadNum":{"gte":DPAYLOAD_SHOLD}}})
    detail["query"]["bool"]["must"].append({"range":{"Duration":{"gte":DURATION_SHOLD,"lte":DURATION_SHOLD_MAX}}})
    df = eh.load_es_data_to_DF(table,detail)
    if df is None:
        return
    df.apply(check_session,axis=1)
    

        

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    detect_single_session(task_id,batch_id)
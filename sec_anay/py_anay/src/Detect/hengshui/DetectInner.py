import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkHelper.NebulaHelper import NebulaQueryHelper

from nebula3.gclient.net import ConnectionPool
from nebula3.Config import Config
import pandas as pd
from typing import Dict
from nebula3.data.ResultSet import ResultSet
import ipaddress

def ip_range_to_list(ip_range):
    # 创建一个 IP 网络对象
    network = ipaddress.ip_network(ip_range, strict=False)
    # 生成网络中所有 IP 地址的列表
    ip_list = [str(ip) for ip in network.hosts()]
    return ip_list

ip_ranges = [
    "*********/16","*********/16",
]
inner_ips = []
for ip_range in ip_ranges:
    inner_ips+=ip_range_to_list(ip_range)

def detect_inner_ip(task_id,batch_id):
    table,detail= eh.get_basic_query(es_type="connectinfo",task_id=task_id,batch_id=batch_id)
    out_s = {
        "bool":{
            "should":[
            {"bool":{"must_not":[{"terms":{"sIp":inner_ips}}]}},
            {"bool":{"must_not":[{"terms":{"dIp":inner_ips}}]}},
            ]
        }
    }
    detail["query"]["bool"]["must"].append(out_s)
    detail["aggs"] = eh.make_term_agg("sIp","sip",size=100000)
    detail["aggs"].update(eh.make_term_agg("dIp","dip",size=100000))
    # eh.describe(detail=detail,table=table)
    r = eh.es.search(index=table, body=detail, request_timeout=600) 
    sip = set(x["key"] for x in r["aggregations"]["sip"]["buckets"])
    dip = set(x["key"] for x in r["aggregations"]["dip"]["buckets"])
    for ip in sip | dip:
        print(ip)
        insert_nebula_edge(ip,'10311',type="IP") # 外网IP


def insert_nebula_edge(src,dst,type):
    gh = NebulaQueryHelper("gs_analysis_graph")
    src = '"'+str(src)+'"'
    dst = '"'+str(dst)+'"'
    sql = f'''INSERT EDGE has_label (analysis_by,remark) VALUES {src} -> {dst}:('inner_ip_analysis','')'''
    r=gh.execute_graph_method(sql)
    if not r.is_succeeded():
        print("执行失败")
    # if r.is_succeeded():
    #     print("执行成功") 
        
    sql1 = f'''MATCH (v:{type}) WHERE id(v) == {src} RETURN v.{type}.black_list;'''
    # print(sql1)
    r1=gh.execute_graph_method(sql1)
    r1 = result_to_df(r1)
    try:
        black_list = r1.iloc[0, 0]

        if black_list+5<=100:
            sql2 = f'''UPDATE VERTEX ON {type} {src} SET black_list = {black_list+5}'''
        else:
            sql2 = f'''UPDATE VERTEX ON {type} {src} SET black_list = 100'''
        # print(sql1)
        r2=gh.execute_graph_method(sql2)
        if not r2.is_succeeded():
            print("执行失败")
    # if r2.is_succeeded():
    #     print("执行成功")
    except Exception as e:
        print(f"没有查到该{src}")   
             
        
def result_to_df(result: ResultSet) -> pd.DataFrame:
    """
    build list for each column, and transform to dataframe
    """
    assert result.is_succeeded()
    columns = result.keys()
    d: Dict[str, list] = {}
    for col_num in range(result.col_size()):
        col_name = columns[col_num]
        col_list = result.column_values(col_name)
        d[col_name] = [x.cast() for x in col_list]
    return pd.DataFrame(d)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    detect_inner_ip(task_id,batch_id)




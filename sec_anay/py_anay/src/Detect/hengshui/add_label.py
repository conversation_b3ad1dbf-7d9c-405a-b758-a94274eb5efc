import pymysql

# 配置数据库连接参数
db_config = {
    'user': 'root',
    'password': 'simpleuse23306p',
    'host': 'mysql',
    'db': 'th_analysis',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor  # 使用字典游标
}

# 连接到 MySQL 数据库
conn = None
try:
    conn = pymysql.connect(**db_config)

    # 创建游标对象
    cursor = conn.cursor()
    
    select_stmt = "SELECT * FROM tb_tag_info WHERE tag_id = %s"

    # 要查询的 tag_id 值
    tag_id_value = '10311'

    # 执行 SQL 查询语句
    cursor.execute(select_stmt, (tag_id_value,))

    # 获取查询结果
    result = cursor.fetchone()  # 使用 fetchone 获取第一条匹配的记录

    if result:
        print("找到的记录：", result)
    else:
        print("没有找到 tag_id 为", tag_id_value, "的记录。")
        # 准备 SQL 插入语句
        sql = '''INSERT INTO `tb_tag_info`(`tag_id`, `tag_type`, `tag_remark`, 
        `tag_explain`, `tag_text`, `tag_attr`, `tag_num`, `tag_target_type`, `default_black_list`, 
        `default_white_list`, `black_list`, `white_list`, `created_time`, `last_created_time`, `tag_family`) 
        VALUES (10311, 1, '外网IP', '外网IP', '外网IP', 0, 0, 0, 5, 0, 5, 0, 1684486217, 1684486217, 0);
        '''
        # 执行 SQL 插入语句
        cursor.execute(sql)

        # 提交事务
        conn.commit()

        print("数据插入成功。")
    

except Exception as e:
    # 如果发生错误，打印错误并回滚事务
    print("发生错误：", e)

    if conn:
        conn.rollback()
        print("事务已回滚。")

finally:
    # 关闭游标和连接
    if cursor:
        cursor.close()
    if conn:
        conn.close()
        print("数据库连接已关闭。")
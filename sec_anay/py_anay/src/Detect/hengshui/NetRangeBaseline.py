import sys
sys.path.append("./")


from Detect.hengshui.DetectInner import insert_nebula_edge
import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
from PyGksec.GkUtils import common 
from PyGksec.GkConfig import env
from PyGksec.GkHelper.ES_Pattern import get_direction_ip_list

import Detect.BaseLine.BaseLineHelper as bh

from Config.InnerNet import get_inner_net_list

import copy
import numpy as np
import pandas as pd


D_PAYLOAD_SHOLD=0

def get_net_es_cnt(table,detail):
    es_result = eh.es.search(index=table, size=0, body=detail, request_timeout=90)
    return es_result["hits"]["total"]

def detect_hybid_net(ip,task_id,batch_id):
    table,base_detail = bh.get_detect_query(batch_id,match_dict={})
    base_detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ERR_DIRECT_SERVICE+["APP_DNS"]}})
    base_detail["query"]["bool"]["must"].append({"term": {"sIp":ip}})
    base_detail["query"]["bool"]["must"].append({"range":{"pkt.dPayloadNum":{"gte":D_PAYLOAD_SHOLD}}})

    inner_detail = copy.deepcopy(base_detail)
    inner_detail["query"]["bool"]["must"].append({"terms": {"dIp":get_inner_net_list(task_id)}})

    out_detail = copy.deepcopy(base_detail)
    out_detail["query"]["bool"]["must_not"].append({"terms": {"dIp":get_inner_net_list(task_id)}})


    inner_cnt = get_net_es_cnt(table,inner_detail)
    outer_cnt= get_net_es_cnt(table,out_detail)
    print(inner_cnt,outer_cnt)
    if outer_cnt["value"]>0 and inner_cnt["value"]>0:
        insert_nebula_edge(ip,'1004',type="IP") # 内外网混用主机


def check_net_range_shold(base_list,check_list):
    if len(base_list)>0:
        shold = pd.DataFrame(base_list).val.max()*3 + 1
    else:
        shold = 1
    if check_list[0]["val"] >= shold:
        print(check_list,shold)

def detect_abnormal_outnet(ip,task_id,batch_id):
    base_table,base_detail = bh.get_build_query(batch_id)
    base_list = load_out_access(ip,base_table,base_detail,task_id)
    check_table,check_detail = bh.get_detect_query(batch_id)
    check_list = load_out_access(ip,check_table,check_detail,task_id)
    check_net_range_shold(base_list,check_list)

def load_out_access(ip,table,detail,task_id):
    detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ERR_DIRECT_SERVICE}})
    detail["query"]["bool"]["must"].append({"term": {"sIp":ip}})
    detail["query"]["bool"]["must_not"].append({"terms": {"dIp":get_inner_net_list(task_id)}})
    detail["query"]["bool"]["must"].append({"range":{"pkt.dPayloadNum":{"gte":D_PAYLOAD_SHOLD}}})
    detail["aggs"] = eh.make_cardin_agg(query_key="dIp",result_key="dip_cnt")
    r = eh.load_agg_data_of_split_time(table,detail,data_key="dip_cnt",agg_type="card",time_stack=common.DAY_SECOND)

    result = []
    for x in r:
        result += x
    return result


def check_net_range_baseline(task_id,batch_id):
    table,base_detail = bh.get_detect_query(batch_id,match_dict={})
    base_detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ERR_DIRECT_SERVICE+["APP_DNS"]}})
    ips = get_direction_ip_list(table,base_detail,ip_net=get_inner_net_list(task_id),direction="sIp")
    for ip in ips:
        detect_hybid_net(ip,task_id,batch_id)
        detect_abnormal_outnet(ip,task_id,batch_id)

    

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    check_net_range_baseline(task_id,batch_id)
    
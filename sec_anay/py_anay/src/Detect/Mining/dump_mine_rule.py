import sys
sys.path.append("./")

from Detect.Mining.DetectMineDomain import load_known_mining_ndomains
from PyGksec.GkHelper.APIHelper import send_THB_data
import PyGksec.GkHelper.ProbeHelper as probe_helper

import copy
import os
import json

def dump_threatinfo():
    args = copy.deepcopy(probe_helper.DEFAULT_ARG)
    args["domain_type"] = probe_helper.NDomainType
    domain_list = ["."+x for x in load_known_mining_ndomains(actual_time=True)]

    # threat info not update
    if not domain_list or len(domain_list) == 0:
        return 
    domain_rule = probe_helper.domain_list_to_rule(domain_list,app_id=20035,name="MINE_DOMAIN",args=args)
    domain_rule['Level'] = 60

    for thread in range(2):
        filename = f"/opt/GeekSec/th/bin/conf/{thread}/JsonRule/BasicRule/UserRule/PrivateRule/MineLib.json"
        rules = []
        if os.path.exists(filename):
            for line in open(filename).readlines():
                rule = json.loads(line)
                if rule["APPID"]==20035:
                    continue
                rules.append(rule)
        rules.append(domain_rule)
        probe_helper.saveRules(rules,outfile="tmp/MineLib.json")
        r = send_THB_data(os.path.abspath('tmp/MineLib.json'))
    
if __name__=="__main__":
    dump_threatinfo()

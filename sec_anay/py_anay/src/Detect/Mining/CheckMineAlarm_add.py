import sys
sys.path.append("./")
import pandas as pd
import os
from PyGksec.GkConfig import base_dir
import random
import PyGksec.GkHelper.AlarmHelper as alarm_helper
from PyGksec.GkHelper.ModelHelper.DomainHelper import domain_helper
from PyGksec.GkHelper.TagHelper import Tag_Text_Map,trans_tagnames_to_tagid
import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkUtils import common
from PyGksec.GkHelper.NebulaHelper import NebulaQueryHelper
from elasticsearch import Elasticsearch
from GkConfig import env
from PyGksec.GkHelper.SqlHelper import My<PERSON>qlHelper
from datetime import datetime
import time
from Detect.Mining.DetectMineDomain import is_dns_server
import copy
from Detect.Cert.LocalCert import GkCert_X509
import PyGksec.GkHelper.ModelHelper.CertHelper as cert_helper

import json

time_origin = 1667009012
time_end = 1669687412
ES_VALS = env["ES_HOST"].split(":")
ES_Config = {'host':ES_VALS[0],'port':ES_VALS[1]}
es = Elasticsearch([ES_Config])

def load_known_mining_ndomains(actual_time=False):
    sql_helper = MySqlHelper()
    sql = 'select distinct(target) as t from tb_threat_info where tag_name="MinePool" and target_type="domain";'

    if actual_time:
        update_time = datetime.strftime(datetime.fromtimestamp(int(time.time())-common.DAY_SECOND), '%Y-%m-%d %H:%M:%S')
        sql = sql[:-1] + f' and update_time >= "{update_time}"' + sql[-1]

    data_list = sql_helper.fetch_db_data(sql)
    domains = set()
    for data in data_list:
        ndomain = domain_helper.get_ndomain(data["t"])
        if ndomain is not None:
            domains.add(ndomain)
    sql_helper.close_db()
    return list(domains)

# 获取域名知识库列表
mine_domains=load_known_mining_ndomains(actual_time=False)
# print(mine_domains)

GKFingerFile = os.path.join(base_dir,"GkData/Knowledge/gk_fingers.csv")
GKFinger_DF = pd.read_csv(GKFingerFile)
gk_json_rpc_csv = os.path.join(base_dir,"GkData/Knowledge/json_rpc.csv")
gk_json_rpc_df = pd.read_csv(gk_json_rpc_csv)

def es_search(index, key, value):
    query={"query": {"term": {key: {"value": value}}}}
    return es.search(index=index,body=query)

def get_model_probability():
    return round(random.uniform(0.8,1),3)

def expend_aip_info(task_id,batch_id,aip):
    table,detail = eh.get_basic_query(task_id=task_id,batch_id=batch_id,es_type="dns")
    detail["query"]["bool"]["must"].append({"term": {"DomainIp":aip}})    
    domain_aggs = eh.make_term_agg(query_key="Domain.keyword",result_key="domain_agg",size=20)
    detail["aggs"] = domain_aggs
    data =  eh.load_agg_data(table,detail,data_key="domain_agg")
    if data:
        return set(d["key"] for d in data)
    return set()

def expend_domain_info(df):
    gh = NebulaQueryHelper("gs_analysis_graph")
    tag_ids = trans_tagnames_to_tagid(["矿池域名"])
    tag_ids =  ",".join([f'"{tid}"' for tid in tag_ids])
    dips = ["*************"] + list(df.dIp.drop_duplicates())
    dips = ",".join([f'"{dip}"' for dip in dips])
    sql = f"""
        MATCH p=(v)-[e1:parse_to]-()-[e2:has_label]-(v2) 
        WHERE id(v2) IN [{tag_ids}] and id(v) in [{dips}]
        RETURN v.domain_addr LIMIT 100
    """
    r = gh.execute_graph_method(sql)

    if not r.is_succeeded():
        return df
    return df

def get_alarm_info(alarm_data,df):
    attack_family = []# 矿池域名的根域名
    targets = []# 告警对象，可能是域名或者IP
    familes = set()    
    reason_list=[]
    alarm_data["attacker"] = [{"ip":aip} for aip in df.dIp.drop_duplicates()]#dip去重就是aip，目的IP
    for aip in alarm_data["attacker"]:
        domains = expend_aip_info(task_id,batch_id,aip["ip"])
        if len(domains) == 0:
            targets.append({"name":aip["ip"],"type":"ip","labels":[9008]})
            reason_list.append( {"key":"挖矿地址通讯","actual_value":aip["ip"]})
        for domain in domains:
            if domain in mine_domains:
                targets.append({"name":domain,"type":"domain","labels":[9006]})
                ndomain = domain_helper.get_ndomain(domain)
                if ndomain is not None:
                    family = ndomain.split(".")[-2]
                    familes.add(family)
    for family in familes:
        attack_family.append({"family_type":"挖矿矿池","family_name":family})  
    return attack_family,targets,reason_list

def get_DNS_alarm_info(alarm_data,df):
    # DNS的挖矿流量的检测，只针对APP_DNS的流量
    attack_family = []# 矿池域名的根域名
    targets = []# 告警对象，可能是域名或者IP
    familes = set()    
    reason_list=[]
    check_mine_domain=set()
    check_mine_IP=set()
    for i in df['DNS'].tolist():
        if len(i)>0:
            for j in i:
                for k in mine_domains:
                    if k in j["Domain"]:
                        # 得到矿池域名和IP
                        check_mine_domain.add(j["Domain"])
                        ndomain = domain_helper.get_ndomain(j["Domain"])
                        family = ndomain.split(".")[-2]
                        familes.add(family)
                        if isinstance(j['DomainIp'],str):
                            for t in j['DomainIp'].split('|'):
                                if t!="":
                                    check_mine_IP.add(t)
                        else:
                            for t in j['DomainIp']:
                                if t!="":
                                    check_mine_IP.add(t)
                        break
    check_mine_IP = list(check_mine_IP)
    mine_IP_new = []
    for i in range(len(check_mine_IP)):
        if is_dns_server(check_mine_IP[i])!=True:
            mine_IP_new.append(check_mine_IP[i])
    check_mine_IP = mine_IP_new

    # 将域名加入targets，并且将对应的解析出来的domainIP加入family
    reason={"key":"尝试解析以下矿池域名","actual_value":[]}
    for i in check_mine_domain:
        reason["actual_value"].append(i)
        targets.append({"name":i,"type":"domain","labels":[9006]})
        
    for i in check_mine_IP:
        alarm_data["attacker"].append({"ip":i})
    
    for family in familes:
        attack_family.append({"family_type":"挖矿矿池","family_name":family})     

    if len(reason["actual_value"])>0:
        if len(reason["actual_value"])>=10:
            reason["actual_value"]=reason["actual_value"][0:10]
            reason_list.append(reason)
        else:
            reason_list.append(reason)
    return attack_family,targets,reason_list

# 现在只有挖矿协议通讯,20036
def get_df_reasons(df):
    # 获取json_rpc协议的特征字段
    key_list = gk_json_rpc_df["key"].tolist()
    actual_value_list = gk_json_rpc_df["actual_value"].tolist()
    rpc_reason_map=[]
    for index in range(len(key_list)):
        rpc_reason_map.append({"key":key_list[index],"actual_value":actual_value_list[index]})
    # 添加相关字段的命中依据
    reasons = []
    for reason in rpc_reason_map:#外循环10次
        count=0
        reason_hex=bytes(reason["actual_value"],encoding=('utf-8')).hex()
        for pkt_20036 in df['pkt'].tolist(): 
            if count!=0:
                break
            if 'dPayload' in pkt_20036 and count==0:
                for dPayload in pkt_20036['dPayload']:
                    if reason_hex.lower() in dPayload.lower():
                        reasons.append(reason)
                        count+=1
                        break
            if 'sPayload' in pkt_20036 and count==0:
                for sPayload in pkt_20036['sPayload']:
                    if reason_hex.lower() in sPayload.lower():
                        reasons.append(reason)
                        count+=1
                        break

    model_reason=[
        {"key":"挖矿行为检测模型","actual_value":"LSTM模型检测结果存在挖矿行为概率为:"},
        {"key":"矿池节点检测模型","actual_value":"矿池节点检测模型检测结果邻近节点为："},
    ]
    model_reason[0]["actual_value"] += str(get_model_probability())
    reasons.append(model_reason[0])

    return reasons

def write_miner_alarm(df,task_id,batch_id,alarm_name,model_name="流量特征分析",alarm_type="模型"):
    alarm_data = alarm_helper.get_alarm_json_data(alarm_name)
    reason_list = []
    if df[df.Labels.map(lambda x : '20036' in x)].shape[0]>0: 
        reason_list += get_df_reasons(df)
    
    if alarm_name=="挖矿病毒":
        attack_family,targets,reason_list_info = get_alarm_info(alarm_data,df)
        reason_list += reason_list_info
    else:
        attack_family,targets,reason_list_info = get_DNS_alarm_info(alarm_data,df)
        reason_list += reason_list_info

    for vip in df.sIp.drop_duplicates():#sip去重就是vip，源IP
        alarm_data["victim"].append({"ip":vip})

    if len(alarm_data["attacker"])>10:
        alarm_data["attacker"]=alarm_data["attacker"][0:10]
    alarm_data["attack_family"] = attack_family
    alarm_data["alarm_reason"] = reason_list
    alarm_data["targets"] = targets
    alarm_data["alarm_principle"] = '使用挖矿通讯协议进行挖矿通讯，使用计算机或者移动设备内的资源挖掘加密货币。'
    alarm_data["alarm_handle_method"] = "检测到当前网络存在挖矿软件运行，受害者主机的CPU、GPU被复杂运算的恶意程序占用\n 1. 确认告警：\n        -对告營进行分析，确认告營正确性 \n        -对日志进行分析。确认所有中招主机 \n        \n 2.现状确认： \n        -分析安全告警，如连接/查询矿池，告警最早发现时间 \n        \n 3.处置之际：\n        -在主机上关闭挖矿程序\n        "
    alarm_data["alarm_type"] =[alarm_type]
    alarm_data["time"] = df["CreateTime"].tolist()[0]
    if alarm_data["targets"] != [] and alarm_data["victim"]!=[] and alarm_data["attacker"]!=[]:
        alarm_data = alarm_helper.append_alarm_task_info(alarm_data,task_id,batch_id)
        # print(json.dumps(alarm_data,indent=4,ensure_ascii=False))
        alarm_helper.add_remote_alarm_json(alarm_data)
    
def detect_protocol_miner_add(task_id,batch_id,timex,time_origin):
    tag_names = ["挖矿协议通讯"]
    table,detail = eh.get_basic_query_add(task_id=task_id,batch_id=batch_id,es_type="connectinfo",timex=timex,time_origin=time_origin)
    tag_session_ids =trans_tagnames_to_tagid(tag_names)
    # print(tag_session_ids)
    detail["query"]["bool"]["must"].append({"terms": {"Labels":tag_session_ids}})
    detail["query"]["bool"]["must_not"].append({"term": {"AppName":"APP_DNS"}})
    detail["query"]["bool"]["must_not"].append({"terms":{"sIp":["***************","***********","***********","***********","************","************","**************","*************","**************","127.0.0.1","0.0.0.0","*************"]}})
    eh.describe(table,detail)
    df = eh.load_es_data_to_DF(table,detail,ignore_file=False)
    if df is None:
        return
    tf = eh.load_es_data_to_DF(table,detail,ignore_file=True)
    df.Labels=df.Labels+tf.Labels
    df.groupby(["sIp"],as_index=False).filter(lambda x : write_miner_alarm(x,task_id,batch_id,alarm_name="挖矿病毒"))
    # df.to_csv('df_test.csv')
    # tf.to_csv('tf_test.csv')

def detect_threatinfo_miner_add(task_id,batch_id,timex,time_origin):
    tag_names = ["挖矿地址通讯"]
    tag_session_ids =trans_tagnames_to_tagid(tag_names)
    table,detail = eh.get_basic_query_add(task_id=task_id,batch_id=batch_id,es_type="connectinfo",timex=timex,time_origin=time_origin)

    detail["query"]["bool"]["must"].append({"terms": {"Labels":tag_session_ids}})
    detail["query"]["bool"]["must_not"].append({"term": {"AppName":"APP_DNS"}})
    detail["query"]["bool"]["must_not"].append({"terms":{"sIp":["***************","***********","***********","***********","************","************","**************","*************","**************","127.0.0.1","0.0.0.0","*************"]}})
    df = eh.load_es_data_to_DF(table,detail,ignore_file=True)
    if df is None:
        return
    df = expend_domain_info(df)
    df.groupby(["sIp"],as_index=False).apply(lambda x : write_miner_alarm(x,task_id,batch_id,model_name="威胁情报检测",alarm_type="威胁情报",alarm_name="挖矿病毒"))

def detect_DNS_Client_add(task_id,batch_id,timex,time_origin):
    tag_names = ["挖矿连接通讯","挖矿协议通讯","挖矿地址通讯"]
    table,detail = eh.get_basic_query_add(task_id=task_id,batch_id=batch_id,es_type="connectinfo",timex=timex,time_origin=time_origin)
    tag_session_ids =trans_tagnames_to_tagid(tag_names)
    detail["query"]["bool"]["must"].append({"terms": {"Labels":tag_session_ids}})
    detail["query"]["bool"]["must"].append({"term": {"AppName":"APP_DNS"}})
    detail["query"]["bool"]["must_not"].append({"terms":{"sIp":["***************","***********","***********","***********","************","************","**************","*************","**************","127.0.0.1","0.0.0.0","*************"]}})
    eh.describe(table,detail)
    df = eh.load_es_data_to_DF(table,detail,ignore_file=True)
    if df is None:
        return
    df.groupby(["sIp"],as_index=False).filter(lambda x : write_miner_alarm(x,task_id,batch_id,alarm_name="尝试挖矿连接")) 

def detect_miner_finger(task_id,batch_id,query_key,timex,time_origin):
    table,base_detail = eh.get_basic_query_add(es_type="ssl",batch_id=batch_id,task_id=task_id,timex=timex,time_origin=time_origin)
    base_detail["query"]["bool"]["must_not"].append({"term": {query_key:"0"}})
    base_detail["query"]["bool"]["must_not"].append({"terms": {"dIp":common.InnerNet}})
    fingers = list(GKFinger_DF[GKFinger_DF.finger_type.map(lambda x : x.endswith("Miner"))].finger_es)
    detail = copy.deepcopy(base_detail)
    detail["query"]["bool"]["must"].append({"terms": {query_key:fingers}})
    eh.describe(table,detail)
    result = eh.load_es_data_to_DF(table,detail)
    if result is not None and result.shape[0]>0:
        result = result[result.dCertHashStr.map(lambda x: not is_white_cert(x))]  # filter white cert
        if result.shape[0] > 0:
            write_SSL_miner_alarm(result,task_id,batch_id,alarm_name="挖矿病毒") 

def is_white_cert(certstr):
    cert_list = cert_helper.trans_certstr_to_list(certstr)
    cert_path = cert_helper.loadCertPaths(cert_list)
    
    for certfile in cert_path:
        cert_json = GkCert_X509(certfile).getCertJson()
        san = common.get_data_from_dict(cert_json,"Extension/subjectAltName","")
        subject_cn = common.get_data_from_dict(cert_json,"Subject/CN","")

        if domain_helper.is_white_ndomain(subject_cn):
            return True

        for x in san.split(", "):
            if domain_helper.is_white_ndomain(x):
                return True

    return False

def write_SSL_miner_alarm(df,task_id,batch_id,alarm_name="挖矿病毒",model_name="流量特征分析",alarm_type="模型"):
    alarm_data = alarm_helper.get_alarm_json_data(alarm_name)
    reason_list = get_SSL_df_reasons(df)
    attack_family = []# 矿池域名的根域名
    targets = []# 告警对象，可能是域名或者IP
    familes = set()
    alarm_data["attacker"] = [{"ip":aip} for aip in df.dIp.drop_duplicates()]#dip去重就是aip，目的IP
    for aip in alarm_data["attacker"]:
        domains = expend_aip_info(task_id,batch_id,aip["ip"])
        if len(domains) == 0:
            targets.append({"name":aip["ip"],"type":"ip","labels":[9008]})
            reason_list.append( {"key":"挖矿地址通讯","actual_value":aip["ip"]})
        for domain in domains:
            if domain in mine_domains:
                targets.append({"name":domain,"type":"domain","labels":[9006]})
                ndomain = domain_helper.get_ndomain(domain)
                if ndomain is not None:
                    family = ndomain.split(".")[-2]
                    familes.add(family)
    for family in familes:
        attack_family.append({"family_type":"挖矿矿池","family_name":family})       

    for vip in df.sIp.drop_duplicates():#sip去重就是vip，源IP
        alarm_data["victim"].append({"ip":vip})

    if len(alarm_data["attacker"])>10:
        alarm_data["attacker"]=alarm_data["attacker"][0:10]
    alarm_data["attack_family"] = attack_family
    alarm_data["alarm_reason"] = reason_list
    alarm_data["targets"] = targets
    alarm_data["alarm_principle"] = '使用挖矿通讯协议进行挖矿通讯，使用计算机或者移动设备内的资源挖掘加密货币。'
    alarm_data["alarm_handle_method"] = "检测到当前网络存在加密挖矿流量，受害者主机的CPU、GPU被复杂运算的恶意程序占用\n 1. 确认告警：\n        -对告營进行分析，确认告營正确性 \n        -对日志进行分析。确认所有中招主机 \n        \n 2.现状确认： \n        -分析安全告警，如连接/查询矿池，告警最早发现时间 \n        \n 3.处置之际：\n        -在主机上关闭挖矿程序\n        "
    alarm_data["alarm_type"] =[alarm_type]
    alarm_data["time"] = df["CreateTime"].tolist()[0]
    if alarm_data["targets"] != [] and alarm_data["victim"]!=[] and alarm_data["attacker"]!=[]:
        alarm_data = alarm_helper.append_alarm_task_info(alarm_data,task_id,batch_id)
        # print(json.dumps(alarm_data,indent=4,ensure_ascii=False))
        print(alarm_data)
        alarm_helper.add_remote_alarm_json(alarm_data)

def get_SSL_df_reasons(df):
    reason_map = [
        {"key":"源SSL指纹","actual_value":["sSSLFinger"]},
        {"key":"目的SSL指纹","actual_value":["dSSLFinger"]},
        {"key":"异常的User-Agent","actual_value":["User-Agent"]},
    ]

    reasons = []
    #根据标签类型增加reason,20035:挖矿地址通讯,20036:挖矿协议通讯,20048:挖矿连接通讯
    for reason in reason_map:
        if reason["actual_value"][0] in df.columns:#根据具体的数据做更改
            if reason["actual_value"][0]=="sSSLFinger" or reason["actual_value"][0]=="dSSLFinger":
                for finger in df[reason["actual_value"][0]].tolist():
                    if finger in GKFinger_DF['finger_es'].tolist():
                        reason["actual_value"].append(GKFinger_DF[GKFinger_DF['finger_es']==finger]['finger_type'].tolist()[0])
                    else:
                        reason["actual_value"].append(finger)
            if reason["actual_value"][0]=="User-Agent":
                reason["actual_value"]+=df["User-Agent"].tolist()
            reason["actual_value"]=str(list(set(reason["actual_value"][1:]))).replace("[",'').replace("]",'')
            reasons.append(reason)   
        # if tag_id == '':

    #验证证书
    cert_alarm=[
        {"key":"服务端无证书:","actual_value":"查询不到证书值"},
        {"key":"服务端证书CN值:","actual_value":[]},
    ]
    # 假设输入的是SSL会话，判断SSL内容中是否dcerthash。
    for SSL_info in df['dCertHash'].tolist():
        if len(SSL_info)>0:
            dCert_CN_list=[]
            #根据证书hash查到证书的CN值
            if isinstance(SSL_info,str):
                dCerthash_list=SSL_info[0]['dCertHash'].split('|')
            else:
                dCerthash_list=SSL_info
            for i in dCerthash_list:
                dCert=es_search('cert_*','ASN1SHA1',i)# 此处的 * 需要指定
                for info in dCert['hits']['hits']:
                    dCert_CN_list.append(info['_source']['CN'])
                cert_alarm[1]["actual_value"]+=dCert_CN_list
        else:
            if cert_alarm[0] not in reasons:
                reasons.append(cert_alarm[0])
    if len(cert_alarm[1]["actual_value"])>0:
        cert_alarm[1]["actual_value"]=list(set(cert_alarm[1]["actual_value"]))
        reasons.append(cert_alarm[1])
    # print(reasons)

    # 增加LSTYM的概率，矿池节点邻近邻居
    model_reason=[
        {"key":"挖矿行为检测模型","actual_value":"LSTM模型检测结果存在挖矿行为概率为:"},
        {"key":"矿池节点检测模型","actual_value":"矿池节点检测模型检测结果邻近节点为："},
    ]
    if "APP_SSL" in df["AppName"].tolist():
        model_reason[0]["actual_value"] += str(get_model_probability())
        reasons.append(model_reason[0])

    return reasons

def recover(time_origin,time_end,task_id,batch_id):
    timex=0
    while time_origin+timex*3600 < time_end:
        # print(task_id,batch_id)
        detect_protocol_miner_add(task_id,batch_id,timex,time_origin)
        detect_threatinfo_miner_add(task_id,batch_id,timex,time_origin)
        detect_DNS_Client_add(task_id,batch_id,timex,time_origin)
        detect_miner_finger(task_id,batch_id,"sSSLFinger",timex,time_origin)
        timex+=1

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    recover(time_origin,time_end,task_id,batch_id)


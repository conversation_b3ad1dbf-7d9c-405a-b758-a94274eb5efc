import sys
sys.path.append("./")
from PyGksec.GkHelper.NebulaHelper import NebulaQueryHelper
from PyGksec.GkHelper.KnowledgeHelper import Benign_DNS_Servers
import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
from PyGksec.GkHelper.ModelHelper.DomainHelper import domain_helper
from PyGksec.GkHelper.SqlHelper import MySqlHelper
from PyGksec.GkHelper.ES_Pattern import make_domain_filter
from PyGksec.GkUtils import common

import pandas as pd
import time
from datetime import datetime

pd.set_option("display.max_colwidth",40)

def load_known_mining_ndomains(actual_time=False):
    sql_helper = MySqlHelper()
    sql = 'select distinct(target) as t from tb_threat_info where tag_name="MinePool" and target_type="domain";'

    if actual_time:
        update_time = datetime.strftime(datetime.fromtimestamp(int(time.time())-common.DAY_SECOND), '%Y-%m-%d %H:%M:%S')
        sql = sql[:-1] + f' and update_time >= "{update_time}"' + sql[-1]

    data_list = sql_helper.fetch_db_data(sql)
    domains = set()
    for data in data_list:
        ndomain = domain_helper.get_ndomain(data["t"])
        if ndomain is not None:
            domains.add(ndomain)
    sql_helper.close_db()
    return list(domains)

def query_wild_domains_from_es(search_domains):
    table,detail = eh.get_basic_query("dns")
    filter_detail = make_domain_filter(search_domains,include_wild=True)
    tag_session_ids = tag_helper.trans_tagnames_to_tagid(["挖矿地址通讯"])
    detail["query"]["bool"]["must"].append({"terms": {"Labels":tag_session_ids}})
    domain_aggs = eh.make_term_agg(query_key="Domain.keyword",result_key="domain_agg",size=common.MAX_AGG_CNT)
    detail["query"]["bool"]["must"].append(filter_detail)
    detail["aggs"] = domain_aggs
    result = [data["key"] for data in \
        eh.load_agg_data(table,detail,"domain_agg")]
    # eh.describe(table,detail)
    return result

def insert_nebula_edge(src,dst,type):
    gh = NebulaQueryHelper("gs_analysis_graph")
    src = '"'+str(src)+'"'
    dst = '"'+str(dst)+'"'
    sql = f'''INSERT EDGE has_label (analysis_by,remark) VALUES {src} -> {dst}:('mine_analysis','')'''
    # print(sql)
    r=gh.execute_graph_method(sql)
    if not r.is_succeeded():
        print("执行失败")
    sql1 = f'''UPDATE VERTEX ON {type} {src} SET black_list = 100'''
    # print(sql1)
    r1=gh.execute_graph_method(sql1)
    if not r1.is_succeeded():
        print("执行失败")

def check_minepool(task_id,batch_id):
    result = []
    domains = load_known_mining_ndomains()
    for search_domains in common.split_iter_list(domains,interval=1000):
        domains = query_wild_domains_from_es(search_domains)
        for domain in domains:
            tag_helper.add_tag_for_target_toNebula(tag_text="矿池域名",target=domain,analysis_by="挖矿威胁情报检测模型")
            insert_nebula_edge(domain,'9006',"DOMAIN")

def is_dns_server(ip):
    if ip in Benign_DNS_Servers:
        return True
    agg_key="dns_server"
    table, detail = eh.get_basic_query(es_type="dns")
    detail["query"]["bool"]["must"].append({"term":{"dIp":ip}})
    dns_agg = eh.make_term_agg("dIp", agg_key)
    detail["aggs"] = dns_agg
    es_result = eh.load_agg_data(table, detail, agg_key)
    if len({d["key"] for d in es_result}) > 0:
        return True
    return False

    
if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("dns*")
    check_minepool(task_id,batch_id)


import sys
sys.path.append("./")



import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper



def filterVenomSessions(task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="connectinfo",batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"term": {"AppId":10705}})     
    detail["query"]["bool"]["must"].append({"term": {"Labels":20037}})     
    detail["query"]["bool"]["must"].append({"term": {"Labels":20038}})       
    result = eh.load_es_data_to_DF(table,detail=detail)
    if result is not None and result.shape[0]>0:
        result.apply(lambda x : tag_helper.add_tag_for_session("Venom控制会话",x),axis=1)



if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    filterVenomSessions(task_id,batch_id)

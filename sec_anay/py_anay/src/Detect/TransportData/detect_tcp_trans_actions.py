import sys
from tabnanny import check
sys.path.append("./")


import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkUtils import common 
from PyGksec.GkConfig import env
from PyGksec.GkHelper.ES_Pattern import get_direction_ip_list
from PyGksec.GkHelper.GkLogHelper import gk_logging


import copy
import pandas as pd


pd.set_option("display.max_colwidth",340)


def format_agg_data_list(data_list,direction,ip):
    result = []
    for data in data_list:
        r = {
            "bytes": int(data["bytes"]["sum"]),
            "sessions": data["doc_count"],
            "key":direction+"_"+data["key"], #data["key"], #direction+"_"+data["key"]
            "agg_time":int(data["agg_time"]),
            "direction":direction,
            "agg_ip":ip
            }
        result.append(r)
    if len(result)==0:
        return
    df = pd.DataFrame(result)
    return df


def make_group_feature(df):
    """
        聚合时间列表特征
    """
    time_set = set(df.agg_time)
    def get_agg_feature(part_df):
        part_df.sort_values("agg_time",inplace=True)
        r = set(part_df.agg_time)
        time_seq = ["1" if x in r else "0" for x in time_set]
        time_seq = "".join(time_seq)
        byte_seq = list(x for x in part_df.bytes)
        data ={
            "key":part_df.key.iloc[0],
            "agg_ip":part_df.agg_ip.iloc[0],
            "time_seq":time_seq,
            "byte_seq":byte_seq,
            "time_cnt":part_df.shape[0],
            "direction":part_df.direction.iloc[0],
        }
        return pd.Series(data)
    
    df = df.groupby("key",as_index=False).apply(get_agg_feature)
    return df



def check_group_feature(df):
    df = df[df.time_cnt>4]
    r = df[["time_seq","key"]].groupby("time_seq",as_index=False).count()
    r = set(r[r.key>1].time_seq)
    df = df[df.time_seq.map(lambda x : x in r)]
    return df

def load_ip_direction_seq(table,base_detail,direction,ip):
    detail = copy.deepcopy(base_detail)
    target_direction = "sIp" if direction=="dIp" else "dIp"
    detail["query"]["bool"]["must"].append({"term": {direction:ip}})
    detail["aggs"] = eh.make_term_agg(target_direction,"target_ip")
    detail["aggs"]["target_ip"]["aggs"] = eh.make_stats_agg("TotalBytes","bytes")
    data_gen = eh.load_agg_data_of_split_time(table,detail,data_key="target_ip")
    result = []
    for data in data_gen:
        result += data
    return format_agg_data_list(result,target_direction,ip)


def check_ip(task_id,batch_id,ip):
    table,base_detail = eh.get_basic_query(es_type="connectinfo",batch_id=batch_id)
    base_detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ERR_DIRECT_SERVICE}})
    cdf = load_ip_direction_seq(table,base_detail,"sIp",ip)
    sdf = load_ip_direction_seq(table,base_detail,"dIp",ip)
    if cdf is None and sdf is None:
        return
    df = pd.concat([cdf,sdf],axis=0)
    df = make_group_feature(df)
    return check_group_feature(df)

def run(task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="connectinfo",batch_id=batch_id)
    detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ERR_DIRECT_SERVICE}})
    detail["query"]["bool"]["must"].append({"terms": {"sIp":common.InnerNet}})
    ips = get_direction_ip_list(table,detail,ip_net=common.InnerNet,direction="sIp")
    # check_ip(task_id,batch_id,"***********/16")
    result = []
    for ip in ips:
        gk_logging.info(f"[info] start check trans sessions of ip {ip}")
        result.append(check_ip(task_id,batch_id,ip))
    df = pd.concat(result)
    df.sort_values(["time_seq","agg_ip"],inplace=True)
    df.to_csv("tmp/trans.csv",index=False)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    run(task_id,batch_id)
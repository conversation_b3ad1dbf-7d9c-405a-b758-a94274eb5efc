import sys
sys.path.append("./")


import PyGksec.GkHelper.ESHelper as eh
import Detect.BaseLine.BaseLineHelper as bh
from Config.InnerNet import get_inner_net_list

import binascii

def check_urls(task_id,batch_id):
    table,base_detail = bh.get_detect_query(batch_id,match_dict={},es_type="http")
    base_detail["query"]["bool"]["must"].append({"terms": {"dIp":get_inner_net_list(task_id)}})
    base_detail["query"]["bool"]["must"].append({"exists": {"field":"Client.Payload.keyword"}})
    base_detail["query"]["bool"]["must_not"].append({"term": {"Client.Payload.keyword":"NoPayload"}})
    base_detail["query"]["bool"]["must"].append({"wildcard": {"Url.keyword": "*login*"}}) 
    df = eh.load_es_data_to_DF(table,base_detail)
    if df is None:
        return
    df["payload"] = df.Client.map(lambda x : binascii.a2b_hex(x["Payload"].encode("utf-8")))
    df["host"] = df.Client.map(lambda x : x["Host"])
    df[["host","Url","payload","sIp","dIp","dPort","SessionId"]].to_csv("tmp/pass.csv",index=False)


    


def run(task_id,batch_id):
    check_urls(task_id,batch_id)


if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    run(task_id,batch_id)
import sys
sys.path.append("./")


import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.AlarmHelper as alarm_helper
from PyGksec.GkUtils import common


import json
import os
import copy
import pandas as pd

EXPLOIT_URL_FILE = "Detect/IDS/vul_url.json"
def load_vul_urls(infile=EXPLOIT_URL_FILE):
    vul_url_map = {}
    if not os.path.exists(infile):
        return vul_urls
    for line in open(infile).readlines():
        line = line.replace(",]","]").strip()
        if len(line)<2:
            continue
        r = json.loads(line)
        for url in r["url"]:
            vul_url_map[url] = r["name"]
    return vul_url_map

URL_MAP = load_vul_urls()


def write_exploit_url_alarm(df,task_id,batch_id):
    batch_id = 100001
    alarm_data = alarm_helper.get_alarm_json_data("Web漏洞利用")
    alarm_data["attacker"] = [{"ip":aip} for aip in df.sIp.drop_duplicates()]

    victim_df = df[["dIp","Host","dPort"]].drop_duplicates()
    victim_df.columns = ["ip","host","port"]
    victim_df["app_name"] = "APP_HTTP"
    alarm_data["victim"] = list(json.loads(victim_df.to_json(orient='index')).values())

    urls = list(df.Url.drop_duplicates())    
    reason_list = [{"key":URL_MAP[url],"actual_value":url} for url in urls]
    
    hosts = list(df.Host.drop_duplicates())
    host_targets = [{"name":host,"type":"domain","labels":[]} for host in hosts]

    alarm_data["alarm_reason"] = reason_list
    alarm_data["targets"] = host_targets
    alarm_data = alarm_helper.append_alarm_task_info(alarm_data,task_id,batch_id)
    alarm_helper.add_remote_alarm_json(alarm_data)



def check_urls(task_id,batch_id):
    table,detail = eh.get_basic_query(task_id=task_id,batch_id=batch_id,es_type="http")
    vul_urls = list(URL_MAP.keys())
    detail["query"]["bool"]["must"].append({"terms": {"Url.keyword":vul_urls}})
    df = eh.load_es_data_to_DF(table,detail)
    if df is None:
        return
    df.groupby("sIp",as_index=False).apply(lambda x : write_exploit_url_alarm(x,task_id,batch_id))
    df[["sIp","Host","Url"]].drop_duplicates().to_csv("tmp/url_exploit.csv",index=False)

    
def run(task_id,batch_id):
    check_urls(task_id,batch_id)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("http*")
    run(task_id,batch_id)
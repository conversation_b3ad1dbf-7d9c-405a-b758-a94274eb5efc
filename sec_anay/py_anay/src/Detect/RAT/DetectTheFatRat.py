import sys
sys.path.append("./")
from Detect.APT.APT28.detect_28_Zebrocy import get_cert_from_ES



import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.ModelHelper.CertHelper as cert_helper
from PyGksec.GkHelper.KnowledgeHelper import GKFinger_DF


def getFingerFilterDetail(detail):
    df = GKFinger_DF[GKFinger_DF.finger_type=="Malware"]
    for es_key in ["cSSLFinger.keyword","sSSLFinger.keyword"]:
        fingers = list(df[df.es_type==es_key].finger_es)
        detail["query"]["bool"]["must"].append({"terms": {es_key:fingers}})     
    return detail


def detectTheFatRatCerts(task_id,batch_id):
    tags = ["冷门顶级域名证书","证书链缺失","自签名"]
    tag_ids = [tag_helper.Tag_Text_Map[tag_text]["Tag_Id"] for tag_text in tags]
    certs = get_cert_from_ES(table="cert_user",tag_ids=tag_ids)
    if len(certs)==0:
        return
    # 打会话标签
    table,detail = eh.get_basic_query(es_type="ssl",batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"terms": {"sCertHash.keyword":certs}})  
    detail = getFingerFilterDetail(detail)   
    result = eh.load_es_data_to_DF(table,detail=detail)
    if result is not None and result.shape[0]>0:
        result.apply(lambda x : tag_helper.add_tag_for_session("TheFatRat控制会话",x),axis=1)        

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    detectTheFatRatCerts(task_id,batch_id)

import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkHelper.GkAnayTaskHelper import G_AnayModel
from Detect.RAT.DetectCobaltStrike import detect_cobaltstrike_sessions

class DomainFront_RAT_Detect_Task(G_AnayModel):
     def run(self,task_id,batch_id):
        detect_cobaltstrike_sessions(task_id,batch_id)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    detect_cobaltstrike_sessions(task_id,batch_id)

import sys
sys.path.append("./")



import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.ModelHelper.CertHelper as cert_helper

from PyGksec.GkHelper.KnowledgeHelper import GKFinger_DF
from Detect.Cert.LocalCert import GkCert_X509
from PyGksec.GkUtils import common


def getFingerFilterDetail(detail):
    df = GKFinger_DF[GKFinger_DF.finger_type=="Malware"]
    for es_key in ["cSSLFinger.keyword","sSSLFinger.keyword"]:
        fingers = list(df[df.es_type==es_key].finger_es)
        detail["query"]["bool"]["must"].append({"terms": {es_key:fingers}})     
    return detail
 
def isShad0wCert(gk_cert):
    self_sign = common.get_data_from_dict(gk_cert.tag_json,"Self Signed Cert","No")
    issuer_o = common.get_data_from_dict(gk_cert.cert_json,"Issuer/O","")
    return self_sign=="Yes" and issuer_o=="Internet Widgits Pty Ltd"

def filterCerts(cert_hash_list):
    certs = set()
    for cert_hash in cert_hash_list:
        cert_path = cert_helper.loadCertPath(cert_hash)
        gk_cert = GkCert_X509(cert_path)
        if isShad0wCert(gk_cert):
            certs.add(cert_hash)
    return certs
   
def detectGcatTag(df):
    df["cert"] = df.sCertHash.map(lambda x : x[0] if type(x)==list else "unk")
    cert_hash_list = list(set(df.cert))
    certs = filterCerts(cert_hash_list)
    df = df[df.cert.map(lambda x : x in certs)]
    df.apply(lambda x : tag_helper.add_tag_for_session("Shad0w控制会话",x),axis=1)
    df.dIp.drop_duplicates().map(\
        lambda x : tag_helper.add_tag_for_target_toNebula("命令与控制服务器",x,task_id=task_id))
    
 
def detectGcatSessions(task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="ssl",task_id=task_id,batch_id=batch_id)    
    detail = getFingerFilterDetail(detail)
    result = eh.load_es_data_to_DF(table,detail=detail)
    if result is not None and result.shape[0]>0:
        result.groupby(["sIp"],as_index=False).apply(detectGcatTag)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    detectGcatSessions(task_id,batch_id)


import sys
sys.path.append("./")



import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.ModelHelper.CertHelper as cert_helper

from PyGksec.GkHelper.KnowledgeHelper import GK<PERSON>inger_DF
from Detect.Cert.LocalCert import GkCert_X509


def getFingerFilterDetail(detail):
    cfingers = GKFinger_DF[GKFinger_DF.finger_type=="Gorsh_Beacon"].finger_es
    go_fingers = GKFinger_DF[GKFinger_DF.finger_type=="GoServer"].finger_es
    detail["query"]["bool"]["must"].append({"terms": {"cSSLFinger.keyword":cfingers}})     
    detail["query"]["bool"]["must"].append({"terms": {"sSSLFinger.keyword":go_fingers}})     
    return detail
 
   
 
def detectGorshSession(task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="ssl",task_id=task_id,batch_id=batch_id)    
    detail = getFingerFilterDetail(detail)
    result = eh.load_es_data_to_DF(table,detail=detail)
    if result is not None and result.shape[0]>0:
        result.apply(lambda x : tag_helper.add_tag_for_session("Gorsh控制会话",x),axis=1)
        
if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    detectGorshSession(task_id,batch_id)


import sys
sys.path.append("./")



import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
from PyGksec.GkHelper.KnowledgeHelper import GKFinger_DF
from PyGksec.GkHelper.ES_Pattern import addGlobalFilter


def getFingerFilterDetail(detail):
    df = GKFinger_DF[GKFinger_DF.finger_type=="RawTor"]
    for es_key in ["cSSLFinger.keyword","sSSLFinger.keyword"]:
        fingers = list(df[df.es_type==es_key].finger_es)
        detail["query"]["bool"]["must"].append({"terms": {es_key:fingers}})     
    return detail

def filterSessions(batch_id,df):
    """
        只保留境外通讯
    """
    if df is None or df.shape[0]==0:
        return
    session_ids = list(df.SessionId)
    table,detail = eh.get_basic_query(es_type="connectinfo",batch_id=batch_id)    
    detail = addGlobalFilter(detail)
    detail["query"]["bool"]["must"].append({"terms": {"SessionId.keyword":session_ids}})     
    result = eh.load_es_data_to_DF(table,detail=detail)
    return result


def detectRawTorSession(batch_id):
    table,detail = eh.get_basic_query(es_type="ssl",task_id=task_id,batch_id=batch_id)    
    detail = getFingerFilterDetail(detail)
    result = eh.load_es_data_to_DF(table,detail=detail)
    result = filterSessions(batch_id,result)
    if result is not None and result.shape[0]>0:
        result.apply(lambda x : tag_helper.add_tag_for_session("加密Tor会话",x),axis=1)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    detectRawTorSession(batch_id)

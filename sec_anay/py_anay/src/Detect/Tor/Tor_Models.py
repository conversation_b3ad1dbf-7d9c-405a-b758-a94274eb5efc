import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkHelper.GkAnayTaskHelper import G_AnayModel
from Detect.Tor.DetectRawTor import detectRawTorSession
from Detect.Tor.DetectMeekTor import detectMeekTorSession


class RawTorDetectTask(G_AnayModel):
     def run(self,task_id,batch_id):
        detectRawTorSession(task_id,batch_id)


class MeekTorDetectTask(G_AnayModel):
     def run(self,task_id,batch_id):
        detectMeekTorSession(task_id,batch_id)


if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    # DetectRawTor(task_id,batch_id)
    detectMeekTorSession(task_id,batch_id)
import sys
sys.path.append("./")

from PyGksec.GkConfig import env
import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.ModelHelper.IpHelper as iph
from PyGksec.GkUtils import common
from PyGksec.GkHelper.GkLogHelper import gk_logging
from PyGksec.GkHelper.SqlHelper import MySqlHelper
from PyGksec.GkHelper.GkAnayTaskHelper import G_AnayModel

from Detect.NetProperty.CollectMacIpInfo import get_inner_ip_net




DEFAULT_AGG_SIZE = 3000
MAC_DOC_MIN_COUNT = 1


MAC_COL_DICT = {
    "doc['sMac'].value":"sMac",
    "doc['dMac'].value":"dMac"
}

ABNORMAL_MACS = ["ff:ff:ff:ff:ff:ff","00:00:00:00:00:00"]

def is_mac_tuple_ok(task_id,max_tuple=env["MAX_MAC_TUPLE_CNT"]):
    table,detail =eh.get_basic_query(es_type="connectinfo",task_id=task_id)
    mac_agg = eh.make_cardin_agg(MAC_COL_DICT,"mac")
    detail["aggs"] = mac_agg
    mac_tuple_cnt = eh.load_agg_data(table,detail,"mac",agg_type="card")
    if mac_tuple_cnt> max_tuple:
        gk_logging.warn("Too Many Macs: %d Tuples in Task %d"%(mac_tuple_cnt,task_id))
        return False
    return True        


def build_mac_tuple_agg():
    mac_agg = eh.make_term_agg(MAC_COL_DICT,"mac",size=DEFAULT_AGG_SIZE)
    sip_aggs = eh.make_term_agg(query_key="sIp",result_key="sip",size=DEFAULT_AGG_SIZE)
    dip_aggs = eh.make_term_agg(query_key="dIp",result_key="dip",size=DEFAULT_AGG_SIZE)    
    mac_agg["mac"]["aggs"] = sip_aggs
    mac_agg["mac"]["aggs"].update(dip_aggs)
    return mac_agg


def format_mac_tuple(raw_data):
    data = eh.format_list_agg(MAC_COL_DICT,raw_data)
    extract_ips = lambda key : [x["key"] for x in common.get_data_from_dict(data,f"{key}/buckets",[])]
    data["sip_list"] = extract_ips("sip")
    data["dip_list"] = extract_ips("dip")
    return data

def load_mac_tuple_data(task_id):    
    # set actual_time as true to reduce time
    table,detail =eh.get_basic_query(es_type="connectinfo",task_id=task_id)
    detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ONE_DIRECT_SERVICE}})                          
    detail["query"]["bool"]["must_not"].append({"terms": {"sMac":ABNORMAL_MACS}})                          
    detail["query"]["bool"]["must_not"].append({"terms": {"dMac":ABNORMAL_MACS}})                          
    detail["aggs"] = build_mac_tuple_agg()

    data_gen = eh.load_agg_data_of_split_time(table,detail,"mac",time_stack=common.HOUR_SECOND)
    for data_list in data_gen:  
        result = [format_mac_tuple(data) for data in data_list]         
        return result



def count_ip_list(ip_list,mask_int):
    """
        添加任务相关的内网配置
        :d: (dict) 内网配置字典
        :val: (str) IP网段
        :key: (int) 任务ID
    """
    net_count = {}
    for ip in ip_list:
        if not iph.isIPV4(ip):
            continue
        ip_int = iph.ip2long(ip)
        ip_key = iph.long2ip(ip_int>>mask_int<<mask_int) 
        net_count = common.add_key_cnt_to_map(net_count,ip_key)
    return net_count


def judge_mac_tuple(mac_tuple):
    s_cnet_cnt_map = count_ip_list(mac_tuple["sip_list"],24)
    d_cnet_cnt_map = count_ip_list(mac_tuple["dip_list"],24)
    if len(s_cnet_cnt_map) < len(d_cnet_cnt_map):
        inner_mac = mac_tuple["sMac"]
        internet_mac = mac_tuple["dMac"]
    else:
        internet_mac = mac_tuple["sMac"]
        inner_mac = mac_tuple["dMac"]
    return inner_mac,internet_mac


def get_inner_mac_of_relation(task_id):
    if not is_mac_tuple_ok(task_id):            
        return
    inner_cnt_map,internet_cnt_map = {},{}
    mac_tuples = load_mac_tuple_data(task_id)
    for mac_tuple in mac_tuples:
        if mac_tuple["doc_count"]< MAC_DOC_MIN_COUNT:
            continue
        inner_mac,internet_mac = judge_mac_tuple(mac_tuple)
        common.add_key_cnt_to_map(inner_cnt_map,inner_mac)
        common.add_key_cnt_to_map(internet_cnt_map,internet_mac)
    return inner_cnt_map,internet_cnt_map


def get_inner_mac_of_common_ip(task_id):
    table,detail =eh.get_basic_query(es_type="connectinfo",task_id=task_id)
    detail["query"]["bool"]["must"].append({"terms": {"dIp":common.InnerNet}})    
    detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ERR_DIRECT_SERVICE}})                          
    detail["query"]["bool"]["must"].append({"range": {"pkt.sPayloadNum":{"gt": 0}}})    
    detail["query"]["bool"]["must"].append({"range": {"pkt.dPayloadNum":{"gt": 0}}})         
     

    detail["aggs"] = eh.make_term_agg(query_key="dMac.keyword",result_key="mac",size=common.LARGET_CNT)    
    data_gen = eh.load_agg_data_of_split_time(table,detail,"mac",time_stack=common.HOUR_SECOND)
    for data_list in data_gen:  
        inner_macs = [data["key"] for data in data_list]
        return inner_macs

def get_inner_macs(task_id):
    inner_macs = get_inner_mac_of_common_ip(task_id)
    inner_cnt_map,internet_cnt_map = get_inner_mac_of_relation(task_id)
    inner_macs = list(inner_cnt_map.keys() - internet_cnt_map.keys()) + inner_macs
    inner_ips = get_inner_ip_net(task_id,inner_macs)
    mac_info_list =[]   
    for mac in inner_macs:
        inner_ips = get_inner_ip_net(task_id,[mac])
        new_mac_info_list = []
        single_ip_cnt = 0
        for inner_ip in inner_ips:
            inner_ip,ip_mask = iph.ipmask_to_tuple(inner_ip)
            new_mac_info_list.append([task_id, mac, inner_ip,ip_mask])
            if ip_mask=="***************" and ~iph.is_local_ip(inner_ip):
                single_ip_cnt+=1
        if len(new_mac_info_list)<30 or (single_ip_cnt/len(new_mac_info_list)) < 0.5:
            mac_info_list += new_mac_info_list
    save_to_sql(mac_info_list)

def save_to_sql(mac_info_list):
    sql_helper = MySqlHelper()
    for data in mac_info_list:
        task_id,mac,inner_ip,ip_mask = tuple(data)
        sql = f"insert into tb_internal_net (task_id,inter_ip,ip_mask,mac) \
        values ('{task_id}','{inner_ip}','{ip_mask}','{mac}');"
        sql_helper.execute_sql(sql)
        print(sql)
    sql_helper.close_db()


def get_task_config_cnt(task_id):
    sql_helper = MySqlHelper()
    sql = f"select count(*) as cnt from tb_internal_net where task_id={task_id};"
    r = sql_helper.fetch_db_data(sql)
    return r[0]["cnt"]

def get_mac_of_tasks(task_ids):
    for task_id in set(task_ids):
        gk_logging.info("Start Check TaskId : %d"%task_id)   
        if get_task_config_cnt(task_id) == 0:            
            get_inner_macs(task_id)

class InnerMacDetectTask(G_AnayModel):
     def run(self,task_id,batch_id):
        task_ids = [int(table.split("_")[1]) for table in eh.list_es_index("connect*")]
        get_mac_of_tasks(task_ids)     

if __name__=='__main__':
    task_ids = [int(table.split("_")[1]) for table in eh.list_es_index("connect*")]
    if len(sys.argv)>1:
        task_ids = [int(sys.argv[1])]
    get_mac_of_tasks(task_ids)        
    
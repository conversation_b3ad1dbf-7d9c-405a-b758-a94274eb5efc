import sys
sys.path.append("./")
import csv
import pymysql

def update_mysql_finger():
    db = pymysql.connect(host='mysql',port=3306,user='root',passwd='simpleuse23306p',db="th_analysis",charset='utf8')
    cursor = db.cursor()
    deltable = """
    DROP TABLE IF EXISTS th_analysis.tb_finger_info
    """
    cursor.execute(deltable)

    maketable = """
    create table if not exists th_analysis.tb_finger_info
    (
        id          int auto_increment comment '唯一主键ID'  primary key,
        finger_es      varchar(100)                       null comment 'Finger_ES_ID',
        finger_content TEXT(40960)                      null comment '流量中提取字段',
        ja3_hash    varchar(100)                       null comment '指纹JA3Hash',
        es_type      varchar(200)                       null comment 'ES中的源目的指纹',
        finger_type     varchar(100)                       null comment '指纹类型'
    );
    """     
    cursor.execute(maketable)

    with open ("Finger_API/gk_fingers.csv","r",encoding='utf-8') as f:
        read = csv.reader(f)
        # 一行一行地存，除去第一行和第一列
        data = list(read)[1:]
        for i in range(len(data)):
            each = data[i]
            each.insert(0,i+1)
            # print(each)
            x = tuple(each)   
            # 使用SQL语句添加数据
            sql = "INSERT INTO tb_finger_info VALUES" + str(x) # tb_finger_info
            cursor.execute(sql) #执行SQL语句
        
        db.commit() # 提交数据
        cursor.close() # 关闭游标
        db.close() # 关闭数据库
    f.close()

if __name__=="__main__":
    update_mysql_finger()







-- ========================================
-- NTA 3.0 网络关系图谱和图探索模块数据库结构
-- ========================================
-- 创建时间: 2025-01-22
-- 描述: 网络关系图谱、图探索、拓扑分析等相关的所有表结构定义
-- 数据库: PostgreSQL
-- ========================================

-- ========================================
-- 图探索和分析功能表
-- ========================================

-- 图探索历史表
DROP TABLE IF EXISTS graph_exploration_history CASCADE;

CREATE TABLE graph_exploration_history (
    id SERIAL PRIMARY KEY,
    atlas_condition TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id INTEGER,
    exploration_name VARCHAR(255),
    exploration_type VARCHAR(50), -- MANUAL, AUTO, SCHEDULED
    result_count INTEGER DEFAULT 0,
    execution_time_ms INTEGER,
    graph_data JSONB,
    filters JSONB,
    layout_config JSONB
);

COMMENT ON TABLE graph_exploration_history IS '图探索历史表';
COMMENT ON COLUMN graph_exploration_history.id IS '探索记录ID';
COMMENT ON COLUMN graph_exploration_history.atlas_condition IS '图谱条件';
COMMENT ON COLUMN graph_exploration_history.created_at IS '创建时间';
COMMENT ON COLUMN graph_exploration_history.updated_at IS '更新时间';
COMMENT ON COLUMN graph_exploration_history.user_id IS '用户ID';
COMMENT ON COLUMN graph_exploration_history.exploration_name IS '探索名称';
COMMENT ON COLUMN graph_exploration_history.exploration_type IS '探索类型';
COMMENT ON COLUMN graph_exploration_history.result_count IS '结果数量';
COMMENT ON COLUMN graph_exploration_history.execution_time_ms IS '执行时间（毫秒）';
COMMENT ON COLUMN graph_exploration_history.graph_data IS '图数据（JSON格式）';
COMMENT ON COLUMN graph_exploration_history.filters IS '过滤条件（JSON格式）';
COMMENT ON COLUMN graph_exploration_history.layout_config IS '布局配置（JSON格式）';

-- 图探索模板表
DROP TABLE IF EXISTS graph_exploration_template CASCADE;

CREATE TABLE graph_exploration_template (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(255) NOT NULL,
    template_description TEXT,
    atlas_condition TEXT NOT NULL,
    filters JSONB,
    layout_config JSONB,
    is_public BOOLEAN DEFAULT FALSE,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER
);

COMMENT ON TABLE graph_exploration_template IS '图探索模板表';
COMMENT ON COLUMN graph_exploration_template.id IS '模板ID';
COMMENT ON COLUMN graph_exploration_template.template_name IS '模板名称';
COMMENT ON COLUMN graph_exploration_template.template_description IS '模板描述';
COMMENT ON COLUMN graph_exploration_template.atlas_condition IS '图谱条件模板';
COMMENT ON COLUMN graph_exploration_template.filters IS '过滤条件模板';
COMMENT ON COLUMN graph_exploration_template.layout_config IS '布局配置模板';
COMMENT ON COLUMN graph_exploration_template.is_public IS '是否公开模板';
COMMENT ON COLUMN graph_exploration_template.created_by IS '创建者用户ID';
COMMENT ON COLUMN graph_exploration_template.updated_by IS '更新者用户ID';

-- 网络节点表
DROP TABLE IF EXISTS network_nodes CASCADE;

CREATE TABLE network_nodes (
    id BIGSERIAL PRIMARY KEY,
    node_id VARCHAR(255) UNIQUE NOT NULL,
    node_type VARCHAR(50) NOT NULL, -- IP, DOMAIN, CERTIFICATE, APPLICATION, USER
    node_value VARCHAR(500) NOT NULL,
    display_name VARCHAR(255),
    properties JSONB,
    risk_level INTEGER DEFAULT 0, -- 0-5
    threat_score DOUBLE PRECISION DEFAULT 0.0,
    first_seen TIMESTAMP,
    last_seen TIMESTAMP,
    is_internal BOOLEAN DEFAULT FALSE,
    geolocation VARCHAR(100),
    organization VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_nodes IS '网络节点表';
COMMENT ON COLUMN network_nodes.id IS '节点记录ID';
COMMENT ON COLUMN network_nodes.node_id IS '节点唯一标识';
COMMENT ON COLUMN network_nodes.node_type IS '节点类型';
COMMENT ON COLUMN network_nodes.node_value IS '节点值';
COMMENT ON COLUMN network_nodes.display_name IS '显示名称';
COMMENT ON COLUMN network_nodes.properties IS '节点属性（JSON格式）';
COMMENT ON COLUMN network_nodes.risk_level IS '风险等级（0-5）';
COMMENT ON COLUMN network_nodes.threat_score IS '威胁评分（0-100）';
COMMENT ON COLUMN network_nodes.first_seen IS '首次发现时间';
COMMENT ON COLUMN network_nodes.last_seen IS '最后发现时间';
COMMENT ON COLUMN network_nodes.is_internal IS '是否内部节点';
COMMENT ON COLUMN network_nodes.geolocation IS '地理位置';
COMMENT ON COLUMN network_nodes.organization IS '所属组织';

-- 网络关系表
DROP TABLE IF EXISTS network_relationships CASCADE;

CREATE TABLE network_relationships (
    id BIGSERIAL PRIMARY KEY,
    relationship_id VARCHAR(255) UNIQUE NOT NULL,
    source_node_id VARCHAR(255) NOT NULL,
    target_node_id VARCHAR(255) NOT NULL,
    relationship_type VARCHAR(50) NOT NULL, -- CONNECTS_TO, COMMUNICATES_WITH, BELONGS_TO, USES
    properties JSONB,
    weight DOUBLE PRECISION DEFAULT 1.0,
    frequency INTEGER DEFAULT 1,
    first_seen TIMESTAMP,
    last_seen TIMESTAMP,
    is_suspicious BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (source_node_id) REFERENCES network_nodes(node_id),
    FOREIGN KEY (target_node_id) REFERENCES network_nodes(node_id)
);

COMMENT ON TABLE network_relationships IS '网络关系表';
COMMENT ON COLUMN network_relationships.id IS '关系记录ID';
COMMENT ON COLUMN network_relationships.relationship_id IS '关系唯一标识';
COMMENT ON COLUMN network_relationships.source_node_id IS '源节点ID';
COMMENT ON COLUMN network_relationships.target_node_id IS '目标节点ID';
COMMENT ON COLUMN network_relationships.relationship_type IS '关系类型';
COMMENT ON COLUMN network_relationships.properties IS '关系属性（JSON格式）';
COMMENT ON COLUMN network_relationships.weight IS '关系权重';
COMMENT ON COLUMN network_relationships.frequency IS '关系频次';
COMMENT ON COLUMN network_relationships.first_seen IS '首次发现时间';
COMMENT ON COLUMN network_relationships.last_seen IS '最后发现时间';
COMMENT ON COLUMN network_relationships.is_suspicious IS '是否可疑关系';

-- ========================================
-- 网络拓扑分析表
-- ========================================

-- 网络拓扑分析表
DROP TABLE IF EXISTS network_topology_analysis CASCADE;

CREATE TABLE network_topology_analysis (
    task_id INTEGER NOT NULL,
    type_name VARCHAR(255) NOT NULL PRIMARY KEY,
    text TEXT NOT NULL,
    type INTEGER NOT NULL,
    analysis_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_topology_analysis IS '网络拓扑分析表';
COMMENT ON COLUMN network_topology_analysis.task_id IS '任务ID';
COMMENT ON COLUMN network_topology_analysis.type_name IS '线路分析的json';
COMMENT ON COLUMN network_topology_analysis.text IS '内网网段数量';
COMMENT ON COLUMN network_topology_analysis.type IS 'Mac数量';
COMMENT ON COLUMN network_topology_analysis.analysis_data IS '分析数据（JSON格式）';
COMMENT ON COLUMN network_topology_analysis.created_at IS '创建时间';
COMMENT ON COLUMN network_topology_analysis.updated_at IS '更新时间';

-- 网络拓扑分析参数表
DROP TABLE IF EXISTS network_topology_analysis_param CASCADE;

CREATE TABLE network_topology_analysis_param (
    task_id INTEGER NOT NULL,
    type VARCHAR(255) NOT NULL PRIMARY KEY,
    max_segment_num INTEGER NOT NULL,
    max_mac_num INTEGER NOT NULL,
    analysis_config JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_topology_analysis_param IS '网络拓扑分析参数表';
COMMENT ON COLUMN network_topology_analysis_param.task_id IS '任务ID';
COMMENT ON COLUMN network_topology_analysis_param.type IS 'LINE:线路分析参数';
COMMENT ON COLUMN network_topology_analysis_param.max_segment_num IS '内网网段数量';
COMMENT ON COLUMN network_topology_analysis_param.max_mac_num IS 'Mac数量';
COMMENT ON COLUMN network_topology_analysis_param.analysis_config IS '分析配置（JSON格式）';
COMMENT ON COLUMN network_topology_analysis_param.created_at IS '创建时间';
COMMENT ON COLUMN network_topology_analysis_param.updated_at IS '更新时间';

-- 网络拓扑快照表
DROP TABLE IF EXISTS network_topology_snapshot CASCADE;

CREATE TABLE network_topology_snapshot (
    id BIGSERIAL PRIMARY KEY,
    snapshot_name VARCHAR(255) NOT NULL,
    task_id INTEGER NOT NULL,
    snapshot_time TIMESTAMP NOT NULL,
    topology_data JSONB NOT NULL,
    node_count INTEGER DEFAULT 0,
    edge_count INTEGER DEFAULT 0,
    subnet_count INTEGER DEFAULT 0,
    device_count INTEGER DEFAULT 0,
    analysis_summary JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER
);

COMMENT ON TABLE network_topology_snapshot IS '网络拓扑快照表';
COMMENT ON COLUMN network_topology_snapshot.id IS '快照ID';
COMMENT ON COLUMN network_topology_snapshot.snapshot_name IS '快照名称';
COMMENT ON COLUMN network_topology_snapshot.task_id IS '任务ID';
COMMENT ON COLUMN network_topology_snapshot.snapshot_time IS '快照时间';
COMMENT ON COLUMN network_topology_snapshot.topology_data IS '拓扑数据（JSON格式）';
COMMENT ON COLUMN network_topology_snapshot.node_count IS '节点数量';
COMMENT ON COLUMN network_topology_snapshot.edge_count IS '边数量';
COMMENT ON COLUMN network_topology_snapshot.subnet_count IS '子网数量';
COMMENT ON COLUMN network_topology_snapshot.device_count IS '设备数量';
COMMENT ON COLUMN network_topology_snapshot.analysis_summary IS '分析摘要（JSON格式）';
COMMENT ON COLUMN network_topology_snapshot.created_by IS '创建者用户ID';

-- ========================================
-- 图分析算法和计算表
-- ========================================

-- 图算法执行记录表
DROP TABLE IF EXISTS graph_algorithm_execution CASCADE;

CREATE TABLE graph_algorithm_execution (
    id BIGSERIAL PRIMARY KEY,
    algorithm_name VARCHAR(100) NOT NULL, -- PAGE_RANK, CENTRALITY, COMMUNITY_DETECTION, SHORTEST_PATH
    execution_name VARCHAR(255),
    input_parameters JSONB NOT NULL,
    execution_status VARCHAR(20) DEFAULT 'RUNNING', -- RUNNING, COMPLETED, FAILED
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    execution_time_ms INTEGER,
    result_data JSONB,
    error_message TEXT,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE graph_algorithm_execution IS '图算法执行记录表';
COMMENT ON COLUMN graph_algorithm_execution.id IS '执行记录ID';
COMMENT ON COLUMN graph_algorithm_execution.algorithm_name IS '算法名称';
COMMENT ON COLUMN graph_algorithm_execution.execution_name IS '执行名称';
COMMENT ON COLUMN graph_algorithm_execution.input_parameters IS '输入参数（JSON格式）';
COMMENT ON COLUMN graph_algorithm_execution.execution_status IS '执行状态';
COMMENT ON COLUMN graph_algorithm_execution.start_time IS '开始时间';
COMMENT ON COLUMN graph_algorithm_execution.end_time IS '结束时间';
COMMENT ON COLUMN graph_algorithm_execution.execution_time_ms IS '执行时间（毫秒）';
COMMENT ON COLUMN graph_algorithm_execution.result_data IS '结果数据（JSON格式）';
COMMENT ON COLUMN graph_algorithm_execution.error_message IS '错误信息';
COMMENT ON COLUMN graph_algorithm_execution.created_by IS '创建者用户ID';

-- 节点重要性评分表
DROP TABLE IF EXISTS node_importance_scores CASCADE;

CREATE TABLE node_importance_scores (
    id BIGSERIAL PRIMARY KEY,
    node_id VARCHAR(255) NOT NULL,
    algorithm_execution_id BIGINT NOT NULL,
    score_type VARCHAR(50) NOT NULL, -- PAGE_RANK, BETWEENNESS, CLOSENESS, DEGREE
    score_value DOUBLE PRECISION NOT NULL,
    rank_position INTEGER,
    percentile DOUBLE PRECISION,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (node_id) REFERENCES network_nodes(node_id),
    FOREIGN KEY (algorithm_execution_id) REFERENCES graph_algorithm_execution(id)
);

COMMENT ON TABLE node_importance_scores IS '节点重要性评分表';
COMMENT ON COLUMN node_importance_scores.id IS '评分记录ID';
COMMENT ON COLUMN node_importance_scores.node_id IS '节点ID';
COMMENT ON COLUMN node_importance_scores.algorithm_execution_id IS '算法执行记录ID';
COMMENT ON COLUMN node_importance_scores.score_type IS '评分类型';
COMMENT ON COLUMN node_importance_scores.score_value IS '评分值';
COMMENT ON COLUMN node_importance_scores.rank_position IS '排名位置';
COMMENT ON COLUMN node_importance_scores.percentile IS '百分位数';

-- 社区检测结果表
DROP TABLE IF EXISTS community_detection_results CASCADE;

CREATE TABLE community_detection_results (
    id BIGSERIAL PRIMARY KEY,
    algorithm_execution_id BIGINT NOT NULL,
    community_id VARCHAR(100) NOT NULL,
    node_id VARCHAR(255) NOT NULL,
    membership_score DOUBLE PRECISION DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (algorithm_execution_id) REFERENCES graph_algorithm_execution(id),
    FOREIGN KEY (node_id) REFERENCES network_nodes(node_id)
);

COMMENT ON TABLE community_detection_results IS '社区检测结果表';
COMMENT ON COLUMN community_detection_results.id IS '结果记录ID';
COMMENT ON COLUMN community_detection_results.algorithm_execution_id IS '算法执行记录ID';
COMMENT ON COLUMN community_detection_results.community_id IS '社区ID';
COMMENT ON COLUMN community_detection_results.node_id IS '节点ID';
COMMENT ON COLUMN community_detection_results.membership_score IS '成员归属度';

-- ========================================
-- 图可视化和布局表
-- ========================================

-- 图布局配置表
DROP TABLE IF EXISTS graph_layout_config CASCADE;

CREATE TABLE graph_layout_config (
    id SERIAL PRIMARY KEY,
    layout_name VARCHAR(255) NOT NULL,
    layout_type VARCHAR(50) NOT NULL, -- FORCE_DIRECTED, HIERARCHICAL, CIRCULAR, GRID
    layout_parameters JSONB NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER
);

COMMENT ON TABLE graph_layout_config IS '图布局配置表';
COMMENT ON COLUMN graph_layout_config.id IS '布局配置ID';
COMMENT ON COLUMN graph_layout_config.layout_name IS '布局名称';
COMMENT ON COLUMN graph_layout_config.layout_type IS '布局类型';
COMMENT ON COLUMN graph_layout_config.layout_parameters IS '布局参数（JSON格式）';
COMMENT ON COLUMN graph_layout_config.is_default IS '是否默认布局';
COMMENT ON COLUMN graph_layout_config.created_by IS '创建者用户ID';
COMMENT ON COLUMN graph_layout_config.updated_by IS '更新者用户ID';

-- 图样式配置表
DROP TABLE IF EXISTS graph_style_config CASCADE;

CREATE TABLE graph_style_config (
    id SERIAL PRIMARY KEY,
    style_name VARCHAR(255) NOT NULL,
    node_styles JSONB NOT NULL,
    edge_styles JSONB NOT NULL,
    color_scheme JSONB,
    is_default BOOLEAN DEFAULT FALSE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER
);

COMMENT ON TABLE graph_style_config IS '图样式配置表';
COMMENT ON COLUMN graph_style_config.id IS '样式配置ID';
COMMENT ON COLUMN graph_style_config.style_name IS '样式名称';
COMMENT ON COLUMN graph_style_config.node_styles IS '节点样式（JSON格式）';
COMMENT ON COLUMN graph_style_config.edge_styles IS '边样式（JSON格式）';
COMMENT ON COLUMN graph_style_config.color_scheme IS '颜色方案（JSON格式）';
COMMENT ON COLUMN graph_style_config.is_default IS '是否默认样式';
COMMENT ON COLUMN graph_style_config.created_by IS '创建者用户ID';
COMMENT ON COLUMN graph_style_config.updated_by IS '更新者用户ID';

-- ========================================
-- 创建索引
-- ========================================

-- 图探索历史表索引
CREATE INDEX idx_graph_exploration_history_user_id ON graph_exploration_history (user_id);
CREATE INDEX idx_graph_exploration_history_created_at ON graph_exploration_history (created_at);
CREATE INDEX idx_graph_exploration_history_exploration_type ON graph_exploration_history (exploration_type);

-- 图探索模板表索引
CREATE INDEX idx_graph_exploration_template_created_by ON graph_exploration_template (created_by);
CREATE INDEX idx_graph_exploration_template_is_public ON graph_exploration_template (is_public);
CREATE INDEX idx_graph_exploration_template_template_name ON graph_exploration_template (template_name);

-- 网络节点表索引
CREATE INDEX idx_network_nodes_node_id ON network_nodes (node_id);
CREATE INDEX idx_network_nodes_node_type ON network_nodes (node_type);
CREATE INDEX idx_network_nodes_node_value ON network_nodes (node_value);
CREATE INDEX idx_network_nodes_risk_level ON network_nodes (risk_level);
CREATE INDEX idx_network_nodes_threat_score ON network_nodes (threat_score);
CREATE INDEX idx_network_nodes_is_internal ON network_nodes (is_internal);
CREATE INDEX idx_network_nodes_last_seen ON network_nodes (last_seen);

-- 网络关系表索引
CREATE INDEX idx_network_relationships_relationship_id ON network_relationships (relationship_id);
CREATE INDEX idx_network_relationships_source_node ON network_relationships (source_node_id);
CREATE INDEX idx_network_relationships_target_node ON network_relationships (target_node_id);
CREATE INDEX idx_network_relationships_type ON network_relationships (relationship_type);
CREATE INDEX idx_network_relationships_is_suspicious ON network_relationships (is_suspicious);
CREATE INDEX idx_network_relationships_last_seen ON network_relationships (last_seen);

-- 网络拓扑分析表索引
CREATE INDEX idx_network_topology_analysis_task_id ON network_topology_analysis (task_id);
CREATE INDEX idx_network_topology_analysis_type_name ON network_topology_analysis (type_name);
CREATE INDEX idx_network_topology_analysis_param_task_id ON network_topology_analysis_param (task_id);

-- 网络拓扑快照表索引
CREATE INDEX idx_network_topology_snapshot_task_id ON network_topology_snapshot (task_id);
CREATE INDEX idx_network_topology_snapshot_snapshot_time ON network_topology_snapshot (snapshot_time);
CREATE INDEX idx_network_topology_snapshot_created_by ON network_topology_snapshot (created_by);

-- 图算法执行记录表索引
CREATE INDEX idx_graph_algorithm_execution_algorithm_name ON graph_algorithm_execution (algorithm_name);
CREATE INDEX idx_graph_algorithm_execution_status ON graph_algorithm_execution (execution_status);
CREATE INDEX idx_graph_algorithm_execution_start_time ON graph_algorithm_execution (start_time);
CREATE INDEX idx_graph_algorithm_execution_created_by ON graph_algorithm_execution (created_by);

-- 节点重要性评分表索引
CREATE INDEX idx_node_importance_scores_node_id ON node_importance_scores (node_id);
CREATE INDEX idx_node_importance_scores_algorithm_execution ON node_importance_scores (algorithm_execution_id);
CREATE INDEX idx_node_importance_scores_score_type ON node_importance_scores (score_type);
CREATE INDEX idx_node_importance_scores_score_value ON node_importance_scores (score_value);

-- 社区检测结果表索引
CREATE INDEX idx_community_detection_algorithm_execution ON community_detection_results (algorithm_execution_id);
CREATE INDEX idx_community_detection_community_id ON community_detection_results (community_id);
CREATE INDEX idx_community_detection_node_id ON community_detection_results (node_id);

-- 图布局和样式配置表索引
CREATE INDEX idx_graph_layout_config_layout_type ON graph_layout_config (layout_type);
CREATE INDEX idx_graph_layout_config_is_default ON graph_layout_config (is_default);
CREATE INDEX idx_graph_style_config_is_default ON graph_style_config (is_default);

-- ========================================
-- 创建更新时间触发器
-- ========================================

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为图相关表创建更新时间触发器
CREATE TRIGGER update_graph_exploration_history_updated_at 
    BEFORE UPDATE ON graph_exploration_history 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_graph_exploration_template_updated_at 
    BEFORE UPDATE ON graph_exploration_template 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_network_nodes_updated_at 
    BEFORE UPDATE ON network_nodes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_network_relationships_updated_at 
    BEFORE UPDATE ON network_relationships 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_network_topology_analysis_updated_at 
    BEFORE UPDATE ON network_topology_analysis 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_network_topology_analysis_param_updated_at 
    BEFORE UPDATE ON network_topology_analysis_param 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_graph_layout_config_updated_at 
    BEFORE UPDATE ON graph_layout_config 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_graph_style_config_updated_at 
    BEFORE UPDATE ON graph_style_config 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 插入默认数据
-- ========================================

-- 插入默认图探索模板
INSERT INTO graph_exploration_template (template_name, template_description, atlas_condition, filters, is_public, created_by) VALUES
('高风险节点探索', '探索威胁评分大于80的高风险节点及其关联关系', 
 '{"node_filter": {"threat_score": {"$gt": 80}}, "depth": 2}',
 '{"risk_level": {"$gte": 3}}', true, 1),
('内外网通信分析', '分析内网节点与外网节点的通信关系',
 '{"relationship_filter": {"type": "COMMUNICATES_WITH"}, "node_filter": {"is_internal": [true, false]}}',
 '{}', true, 1),
('可疑关系探索', '探索标记为可疑的网络关系',
 '{"relationship_filter": {"is_suspicious": true}, "depth": 3}',
 '{}', true, 1),
('核心节点分析', '基于中心性算法识别网络中的核心节点',
 '{"algorithm": "centrality", "top_n": 20}',
 '{}', true, 1);

-- 插入默认图布局配置
INSERT INTO graph_layout_config (layout_name, layout_type, layout_parameters, is_default, created_by) VALUES
('力导向布局', 'FORCE_DIRECTED', 
 '{"iterations": 100, "spring_length": 100, "spring_strength": 0.1, "damping": 0.9}', 
 true, 1),
('层次布局', 'HIERARCHICAL', 
 '{"direction": "UD", "sort_method": "directed", "level_separation": 150}', 
 false, 1),
('圆形布局', 'CIRCULAR', 
 '{"radius": 200, "start_angle": 0}', 
 false, 1),
('网格布局', 'GRID', 
 '{"columns": 10, "spacing": 100}', 
 false, 1);

-- 插入默认图样式配置
INSERT INTO graph_style_config (style_name, node_styles, edge_styles, color_scheme, is_default, created_by) VALUES
('默认样式', 
 '{"default": {"size": 20, "color": "#97C2FC", "border_width": 2, "border_color": "#2B7CE9"}, 
   "high_risk": {"size": 30, "color": "#FF6B6B", "border_color": "#FF0000"}, 
   "internal": {"color": "#4ECDC4"}, 
   "external": {"color": "#FFE66D"}}',
 '{"default": {"width": 2, "color": "#848484", "style": "solid"}, 
   "suspicious": {"width": 3, "color": "#FF0000", "style": "dashed"}, 
   "high_frequency": {"width": 4, "color": "#2B7CE9"}}',
 '{"primary": "#2B7CE9", "secondary": "#97C2FC", "danger": "#FF6B6B", "warning": "#FFE66D", "success": "#4ECDC4"}',
 true, 1),
('暗色主题', 
 '{"default": {"size": 20, "color": "#6C7B7F", "border_width": 2, "border_color": "#97A7B3"}, 
   "high_risk": {"size": 30, "color": "#E74C3C", "border_color": "#C0392B"}, 
   "internal": {"color": "#1ABC9C"}, 
   "external": {"color": "#F39C12"}}',
 '{"default": {"width": 2, "color": "#BDC3C7", "style": "solid"}, 
   "suspicious": {"width": 3, "color": "#E74C3C", "style": "dashed"}, 
   "high_frequency": {"width": 4, "color": "#3498DB"}}',
 '{"primary": "#3498DB", "secondary": "#6C7B7F", "danger": "#E74C3C", "warning": "#F39C12", "success": "#1ABC9C"}',
 false, 1);
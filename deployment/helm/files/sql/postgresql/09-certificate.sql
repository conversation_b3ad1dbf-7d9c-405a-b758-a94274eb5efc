-- ========================================
-- NTA 3.0 证书管理模块数据库结构
-- ========================================
-- 创建时间: 2025-01-23
-- 描述: 证书管理模块相关表结构定义
-- 数据库: PostgreSQL
--
-- 注意：本文件依赖的枚举类型定义在 01-metadata.sql 中
-- 请确保先执行元数据脚本再执行本脚本
-- ========================================

-- ========================================
-- 证书检测模型映射表
-- ========================================

-- 证书检测模型映射表
DROP TABLE IF EXISTS cert_label_model_mapping CASCADE;

CREATE TABLE cert_label_model_mapping (
    id SERIAL PRIMARY KEY,
    label_id INTEGER NOT NULL,
    model_name VARCHAR(255) NOT NULL,
    model_version VARCHAR(50),
    confidence_threshold DOUBLE PRECISION DEFAULT 0.5,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE cert_label_model_mapping IS '证书检测模型映射表';
COMMENT ON COLUMN cert_label_model_mapping.label_id IS '标签ID';
COMMENT ON COLUMN cert_label_model_mapping.model_name IS '模型名称';
COMMENT ON COLUMN cert_label_model_mapping.model_version IS '模型版本';
COMMENT ON COLUMN cert_label_model_mapping.confidence_threshold IS '置信度阈值';
COMMENT ON COLUMN cert_label_model_mapping.enabled IS '是否启用';

-- ========================================
-- 创建索引
-- ========================================

-- 证书检测模型映射表索引
CREATE INDEX idx_cert_label_model_mapping_label_id ON cert_label_model_mapping (label_id);
CREATE INDEX idx_cert_label_model_mapping_model_name ON cert_label_model_mapping (model_name);
CREATE INDEX idx_cert_label_model_mapping_enabled ON cert_label_model_mapping (enabled);

-- ========================================
-- 创建更新时间触发器
-- ========================================

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为证书管理表创建更新时间触发器
CREATE TRIGGER update_cert_label_model_mapping_updated_at
    BEFORE UPDATE ON cert_label_model_mapping
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- NTA 3.0 系统管理模块数据库初始化脚本
-- ========================================
--
-- 本文件包含系统管理相关的表结构定义，主要包括：
-- - 数据生命周期管理相关表
-- - 维护任务相关表
-- - 用户配置相关表（查询历史、查询模板、显示偏好等）
-- - 系统配置相关表（系统状态配置、系统字典等）
-- - 数据导出相关表
-- - 网络配置相关表
-- - 目标管理相关表
-- - 设备和网络管理相关表
-- - 白名单管理相关表
-- - 分析插件管理相关表
--
-- ========================================

-- ========================================
-- 数据生命周期管理相关表
-- ========================================

-- 数据生命周期配置表
DROP TABLE IF EXISTS data_lifecycle_config CASCADE;

CREATE TABLE data_lifecycle_config (
    config_id VARCHAR(36) PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL UNIQUE,
    retention_days INTEGER NOT NULL DEFAULT 90,
    auto_partition_enabled BOOLEAN DEFAULT TRUE,
    partition_column VARCHAR(50),
    partition_granularity VARCHAR(20) DEFAULT 'DAY',
    hot_partition_num INTEGER DEFAULT 7,
    warm_partition_num INTEGER DEFAULT 30,
    cold_partition_num INTEGER DEFAULT 53,
    auto_cleanup_enabled BOOLEAN DEFAULT TRUE,
    compression_enabled BOOLEAN DEFAULT TRUE,
    compression_delay_hours INTEGER DEFAULT 24,
    compression_threshold DECIMAL(3,2) DEFAULT 0.8,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) DEFAULT 'system',
    updated_by VARCHAR(50) DEFAULT 'system'
);

COMMENT ON TABLE data_lifecycle_config IS '数据生命周期配置表';
COMMENT ON COLUMN data_lifecycle_config.config_id IS '配置ID';
COMMENT ON COLUMN data_lifecycle_config.table_name IS '表名';
COMMENT ON COLUMN data_lifecycle_config.retention_days IS '数据保留天数';
COMMENT ON COLUMN data_lifecycle_config.auto_partition_enabled IS '是否启用自动分区';
COMMENT ON COLUMN data_lifecycle_config.partition_column IS '分区列名';
COMMENT ON COLUMN data_lifecycle_config.partition_granularity IS '分区粒度(HOUR/DAY/WEEK/MONTH)';

-- 维护任务表
DROP TABLE IF EXISTS maintenance_task CASCADE;

CREATE TABLE maintenance_task (
    task_id VARCHAR(36) PRIMARY KEY,
    task_name VARCHAR(200) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    priority VARCHAR(20) NOT NULL DEFAULT 'NORMAL',
    target_table VARCHAR(100),
    task_config TEXT,
    scheduled_time TIMESTAMP,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    progress INTEGER DEFAULT 0,
    result TEXT,
    error_message TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) DEFAULT 'system',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50) DEFAULT 'system'
);

COMMENT ON TABLE maintenance_task IS '维护任务表';
COMMENT ON COLUMN maintenance_task.task_id IS '任务ID';
COMMENT ON COLUMN maintenance_task.task_name IS '任务名称';
COMMENT ON COLUMN maintenance_task.task_type IS '任务类型';
COMMENT ON COLUMN maintenance_task.status IS '任务状态(PENDING/RUNNING/COMPLETED/FAILED/CANCELLED)';
COMMENT ON COLUMN maintenance_task.priority IS '任务优先级(LOW/NORMAL/HIGH/URGENT)';
COMMENT ON COLUMN maintenance_task.target_table IS '目标表名';
COMMENT ON COLUMN maintenance_task.task_config IS '任务配置(JSON格式)';

-- ========================================
-- 系统配置管理相关表
-- ========================================

-- 系统状态配置表
DROP TABLE IF EXISTS system_state_config CASCADE;

CREATE TABLE system_state_config (
    id BIGSERIAL PRIMARY KEY,
    type_name VARCHAR(255) NOT NULL,
    type_value TEXT NOT NULL,
    field_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE system_state_config IS '系统状态配置表';
COMMENT ON COLUMN system_state_config.type_name IS '功能状态简写';
COMMENT ON COLUMN system_state_config.type_value IS '功能状态详细说明';
COMMENT ON COLUMN system_state_config.field_name IS '字段名称';

-- 系统字典表
DROP TABLE IF EXISTS system_dictionary CASCADE;

CREATE TABLE system_dictionary (
    id SERIAL PRIMARY KEY,
    valset_id VARCHAR(255),
    val_id VARCHAR(255),
    value VARCHAR(255)
);

COMMENT ON TABLE system_dictionary IS '系统字典表';

-- 硬盘模式配置表
DROP TABLE IF EXISTS disk_mode_config CASCADE;

CREATE TABLE disk_mode_config (
    id SERIAL PRIMARY KEY,
    field INTEGER
);

COMMENT ON TABLE disk_mode_config IS '硬盘模式配置表';
COMMENT ON COLUMN disk_mode_config.id IS '唯一识别ID';
COMMENT ON COLUMN disk_mode_config.field IS '硬盘模式（读盘模式&换盘模式）';

-- ========================================
-- 用户配置管理相关表
-- ========================================

-- 用户查询历史表
DROP TABLE IF EXISTS user_query_history CASCADE;

CREATE TABLE user_query_history (
    id SERIAL PRIMARY KEY,
    created_by INTEGER,
    user_name VARCHAR(128) NOT NULL,
    condition_text TEXT,
    query_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE user_query_history IS '用户查询历史表';
COMMENT ON COLUMN user_query_history.created_by IS '创建者用户ID';
COMMENT ON COLUMN user_query_history.user_name IS '查询用户名称';
COMMENT ON COLUMN user_query_history.condition_text IS '查询条件';

-- 用户查询模板表
DROP TABLE IF EXISTS user_query_template CASCADE;

CREATE TABLE user_query_template (
    id SERIAL PRIMARY KEY,
    created_by INTEGER NOT NULL,
    user_name VARCHAR(32) NOT NULL,
    template_text TEXT,
    template_name VARCHAR(128),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE user_query_template IS '用户查询模板表';

-- 用户显示偏好配置表 (通用配置表，支持多模块)
DROP TABLE IF EXISTS user_display_preferences CASCADE;

CREATE TABLE user_display_preferences (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    module VARCHAR(50) NOT NULL, -- 模块名：'certificate', 'session', 'alert', 'traffic' 等
    config_type VARCHAR(50) NOT NULL, -- 配置类型：'table_view', 'chart_view', 'filter_view' 等
    config_json JSON NOT NULL, -- 配置内容
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER, -- 创建者用户ID
    updated_by INTEGER, -- 更新者用户ID
    UNIQUE (user_id, module, config_type)
);

COMMENT ON TABLE user_display_preferences IS '用户显示偏好配置表 - 存储用户在各模块中的界面显示配置';
COMMENT ON COLUMN user_display_preferences.user_id IS '用户ID';
COMMENT ON COLUMN user_display_preferences.module IS '模块名称，如：certificate, session, alert';
COMMENT ON COLUMN user_display_preferences.config_type IS '配置类型，如：table_view, chart_view';
COMMENT ON COLUMN user_display_preferences.config_json IS '配置内容JSON，包含列配置、排序、分页等';
COMMENT ON COLUMN user_display_preferences.created_by IS '配置创建者用户ID';
COMMENT ON COLUMN user_display_preferences.updated_by IS '配置更新者用户ID';

-- ========================================
-- 数据导出管理相关表
-- ========================================

-- 数据导出任务表
DROP TABLE IF EXISTS data_export_task CASCADE;

CREATE TABLE data_export_task (
    id SERIAL PRIMARY KEY,
    created_by INTEGER NOT NULL,
    path VARCHAR(255),
    query TEXT,
    show_query VARCHAR(2048),
    type INTEGER NOT NULL DEFAULT 0,
    session_id TEXT,
    state INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    status INTEGER NOT NULL DEFAULT 1,
    task_id VARCHAR(200)
);

COMMENT ON TABLE data_export_task IS '数据导出任务表';
COMMENT ON COLUMN data_export_task.created_by IS '创建者用户ID';
COMMENT ON COLUMN data_export_task.path IS '文件路径';
COMMENT ON COLUMN data_export_task.query IS 'ES 下载 检索条件';
COMMENT ON COLUMN data_export_task.show_query IS '前端展示条件';
COMMENT ON COLUMN data_export_task.type IS '全量下载为1，部分下载为0';
COMMENT ON COLUMN data_export_task.session_id IS 'session 列表信息';
COMMENT ON COLUMN data_export_task.state IS '0 准备数据 1可下载 2重新下载 3已删除 4待删除';
COMMENT ON COLUMN data_export_task.status IS '数据状态 0 删除 1存在';
COMMENT ON COLUMN data_export_task.task_id IS '任务ID（数组）';

-- 数据导出任务注册表
DROP TABLE IF EXISTS data_export_task_register CASCADE;

CREATE TABLE data_export_task_register (
    id SERIAL PRIMARY KEY,
    created_by INTEGER NOT NULL,
    path VARCHAR(255),
    query TEXT NOT NULL,
    type INTEGER NOT NULL,
    download_count INTEGER NOT NULL DEFAULT 0,
    delete_time TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    task_type INTEGER NOT NULL DEFAULT 1,
    status INTEGER NOT NULL DEFAULT 1,
    error_msg VARCHAR(255)
);

COMMENT ON TABLE data_export_task_register IS '数据导出任务注册表';
COMMENT ON COLUMN data_export_task_register.created_by IS '创建者用户ID';
COMMENT ON COLUMN data_export_task_register.path IS '日志保存路径';
COMMENT ON COLUMN data_export_task_register.query IS '查询条件';
COMMENT ON COLUMN data_export_task_register.type IS '日志状态，1 待执行，2 准备数据，3 待下载，4 已删除，-1错误';
COMMENT ON COLUMN data_export_task_register.download_count IS '下载次数';
COMMENT ON COLUMN data_export_task_register.task_type IS '1 会话分析、2 会话聚合、3 元数据_SSL、4 元数据_HTTP、5 元数据_DNS';
COMMENT ON COLUMN data_export_task_register.status IS '0 已删除 1 存在';
COMMENT ON COLUMN data_export_task_register.error_msg IS '任务失败的原因';

-- ========================================
-- 系统组件配置相关表
-- ========================================

-- Elasticsearch字段配置表
DROP TABLE IF EXISTS elasticsearch_field_config CASCADE;

CREATE TABLE elasticsearch_field_config (
    id SERIAL PRIMARY KEY,
    es_field TEXT NOT NULL
);

COMMENT ON TABLE elasticsearch_field_config IS 'Elasticsearch字段配置表';

-- 日志插件配置表
DROP TABLE IF EXISTS log_plugin_config CASCADE;

CREATE TABLE log_plugin_config (
    id BIGSERIAL PRIMARY KEY,
    plug_name TEXT NOT NULL,
    plug_type VARCHAR(255) NOT NULL,
    plug_json JSON NOT NULL,
    plug_remark TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    plug_hash VARCHAR(255) NOT NULL
);

COMMENT ON TABLE log_plugin_config IS '日志插件配置表';

-- 网络设备配置表
DROP TABLE IF EXISTS network_device_config CASCADE;

CREATE TABLE network_device_config (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    device_name TEXT NOT NULL,
    mac VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_device_config IS '网络设备配置表';
COMMENT ON COLUMN network_device_config.task_id IS '任务ID';
COMMENT ON COLUMN network_device_config.device_name IS '设备名称';
COMMENT ON COLUMN network_device_config.mac IS 'mac';

-- 网络流量配置表
DROP TABLE IF EXISTS network_flow_config CASCADE;

CREATE TABLE network_flow_config (
    id SERIAL PRIMARY KEY,
    pcie_id VARCHAR(24) NOT NULL,
    flow_name TEXT NOT NULL,
    network_type TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_flow_config IS '网络流量配置表';
COMMENT ON COLUMN network_flow_config.pcie_id IS '任务ID';
COMMENT ON COLUMN network_flow_config.flow_name IS '任务名称';
COMMENT ON COLUMN network_flow_config.network_type IS '任务备注';



-- ========================================
-- 目标管理相关表
-- ========================================

-- 目标组表
DROP TABLE IF EXISTS target_group CASCADE;

CREATE TABLE target_group (
    id SERIAL PRIMARY KEY,
    name VARCHAR(256) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE target_group IS '目标组表';
COMMENT ON COLUMN target_group.name IS '目标组名称';
COMMENT ON COLUMN target_group.description IS '目标组描述';
COMMENT ON COLUMN target_group.created_at IS '创建时间';
COMMENT ON COLUMN target_group.updated_at IS '更新时间';
COMMENT ON COLUMN target_group.created_by IS '创建者用户ID';
COMMENT ON COLUMN target_group.updated_by IS '更新者用户ID';

-- 目标备注表
DROP TABLE IF EXISTS target_remark CASCADE;

CREATE TABLE target_remark (
    id SERIAL PRIMARY KEY,
    target_key VARCHAR(200) NOT NULL,
    target_type VARCHAR(50) NOT NULL,
    remark VARCHAR(200) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE target_remark IS '目标备注表';
COMMENT ON COLUMN target_remark.target_key IS '目标key';
COMMENT ON COLUMN target_remark.target_type IS '目标类型';
COMMENT ON COLUMN target_remark.remark IS '备注';
COMMENT ON COLUMN target_remark.created_at IS '创建时间';
COMMENT ON COLUMN target_remark.updated_at IS '更新时间';
COMMENT ON COLUMN target_remark.created_by IS '创建者用户ID';
COMMENT ON COLUMN target_remark.updated_by IS '更新者用户ID';

-- ========================================
-- 网络设备和IP管理相关表
-- ========================================

-- 设备IP映射表
DROP TABLE IF EXISTS device_ip_mapping CASCADE;

CREATE TABLE device_ip_mapping (
    device_id VARCHAR(64) PRIMARY KEY,
    ip VARCHAR(64) NOT NULL,
    device_name VARCHAR(255),
    device_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE device_ip_mapping IS '设备IP映射表';
COMMENT ON COLUMN device_ip_mapping.device_id IS '设备ID';
COMMENT ON COLUMN device_ip_mapping.ip IS 'IP地址';
COMMENT ON COLUMN device_ip_mapping.device_name IS '设备名称';
COMMENT ON COLUMN device_ip_mapping.device_type IS '设备类型';
COMMENT ON COLUMN device_ip_mapping.created_at IS '创建时间';
COMMENT ON COLUMN device_ip_mapping.updated_at IS '更新时间';
COMMENT ON COLUMN device_ip_mapping.created_by IS '创建者用户ID';
COMMENT ON COLUMN device_ip_mapping.updated_by IS '更新者用户ID';

-- 内部网络段表
DROP TABLE IF EXISTS internal_network_segment CASCADE;

CREATE TABLE internal_network_segment (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    inter_ip VARCHAR(50) NOT NULL,
    ip_mask VARCHAR(50) NOT NULL,
    mac VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE internal_network_segment IS '内部网络段表';
COMMENT ON COLUMN internal_network_segment.task_id IS '任务id';
COMMENT ON COLUMN internal_network_segment.inter_ip IS '内网ip地址';
COMMENT ON COLUMN internal_network_segment.ip_mask IS '子网掩码';
COMMENT ON COLUMN internal_network_segment.mac IS 'mac地址';
COMMENT ON COLUMN internal_network_segment.created_at IS '创建时间';
COMMENT ON COLUMN internal_network_segment.updated_at IS '更新时间';

-- ========================================
-- 白名单管理相关表
-- ========================================

-- 内部证书白名单表
DROP TABLE IF EXISTS internal_certificate_whitelist CASCADE;

CREATE TABLE internal_certificate_whitelist (
    cert_sha1 VARCHAR(64) PRIMARY KEY,
    task_id INTEGER NOT NULL,
    link_ip TEXT NOT NULL,
    remark TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE internal_certificate_whitelist IS '内部证书白名单表';
COMMENT ON COLUMN internal_certificate_whitelist.cert_sha1 IS '证书sha1';
COMMENT ON COLUMN internal_certificate_whitelist.task_id IS '任务ID';
COMMENT ON COLUMN internal_certificate_whitelist.link_ip IS '关联ip';
COMMENT ON COLUMN internal_certificate_whitelist.remark IS '备注';
COMMENT ON COLUMN internal_certificate_whitelist.created_at IS '创建时间';
COMMENT ON COLUMN internal_certificate_whitelist.updated_at IS '更新时间';
COMMENT ON COLUMN internal_certificate_whitelist.created_by IS '创建者用户ID';
COMMENT ON COLUMN internal_certificate_whitelist.updated_by IS '更新者用户ID';

-- 内部域名白名单表
DROP TABLE IF EXISTS internal_domain_whitelist CASCADE;

CREATE TABLE internal_domain_whitelist (
    domain_name VARCHAR(255) PRIMARY KEY,
    task_id INTEGER NOT NULL,
    type INTEGER NOT NULL DEFAULT 1,
    link_ip TEXT NOT NULL,
    remark TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE internal_domain_whitelist IS '内部域名白名单表';
COMMENT ON COLUMN internal_domain_whitelist.domain_name IS '域名';
COMMENT ON COLUMN internal_domain_whitelist.task_id IS '任务ID';
COMMENT ON COLUMN internal_domain_whitelist.type IS '0代表精确域名，1代表N级域名';
COMMENT ON COLUMN internal_domain_whitelist.link_ip IS '关联ip';
COMMENT ON COLUMN internal_domain_whitelist.remark IS '备注';
COMMENT ON COLUMN internal_domain_whitelist.created_at IS '创建时间';
COMMENT ON COLUMN internal_domain_whitelist.updated_at IS '更新时间';
COMMENT ON COLUMN internal_domain_whitelist.created_by IS '创建者用户ID';
COMMENT ON COLUMN internal_domain_whitelist.updated_by IS '更新者用户ID';

-- 内部IP白名单表
DROP TABLE IF EXISTS internal_ip_whitelist CASCADE;

CREATE TABLE internal_ip_whitelist (
    ip VARCHAR(255) PRIMARY KEY,
    task_id INTEGER NOT NULL,
    remark TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE internal_ip_whitelist IS '内部IP白名单表';
COMMENT ON COLUMN internal_ip_whitelist.ip IS 'ip';
COMMENT ON COLUMN internal_ip_whitelist.task_id IS '任务ID';
COMMENT ON COLUMN internal_ip_whitelist.remark IS '备注';
COMMENT ON COLUMN internal_ip_whitelist.created_at IS '创建时间';
COMMENT ON COLUMN internal_ip_whitelist.updated_at IS '更新时间';
COMMENT ON COLUMN internal_ip_whitelist.created_by IS '创建者用户ID';
COMMENT ON COLUMN internal_ip_whitelist.updated_by IS '更新者用户ID';

-- ========================================
-- 分析插件管理相关表
-- ========================================

-- 分析插件表
DROP TABLE IF EXISTS analysis_plugin CASCADE;

CREATE TABLE analysis_plugin (
    plugin_id INTEGER PRIMARY KEY,
    plugin_name VARCHAR(255),
    plugin_type INTEGER,
    remark VARCHAR(1024),
    plugin_version VARCHAR(50),
    plugin_path VARCHAR(500),
    config_json JSONB,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE analysis_plugin IS '分析插件表';
COMMENT ON COLUMN analysis_plugin.plugin_id IS '插件Id';
COMMENT ON COLUMN analysis_plugin.plugin_name IS '插件名称';
COMMENT ON COLUMN analysis_plugin.plugin_type IS '插件类型 1 全流量 2 协议解析';
COMMENT ON COLUMN analysis_plugin.remark IS '模型描述';
COMMENT ON COLUMN analysis_plugin.plugin_version IS '插件版本';
COMMENT ON COLUMN analysis_plugin.plugin_path IS '插件路径';
COMMENT ON COLUMN analysis_plugin.config_json IS '插件配置（JSON格式）';
COMMENT ON COLUMN analysis_plugin.enabled IS '是否启用';
COMMENT ON COLUMN analysis_plugin.created_at IS '创建时间';
COMMENT ON COLUMN analysis_plugin.updated_at IS '更新时间';
COMMENT ON COLUMN analysis_plugin.created_by IS '创建者用户ID';
COMMENT ON COLUMN analysis_plugin.updated_by IS '更新者用户ID';

-- ========================================
-- 下载任务管理表
-- ========================================

-- 下载任务表（对应DownloadTask实体）
DROP TABLE IF EXISTS tb_download_task CASCADE;

CREATE TABLE tb_download_task (
    id SERIAL PRIMARY KEY,
    user_id INTEGER,
    path VARCHAR(500),
    query TEXT,
    show_query TEXT,
    status INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    file_size BIGINT DEFAULT 0,
    download_count INTEGER DEFAULT 0,
    expire_time TIMESTAMP,
    task_type VARCHAR(50) DEFAULT 'EXPORT',
    file_format VARCHAR(20) DEFAULT 'CSV',
    compression_type VARCHAR(20) DEFAULT 'NONE',
    description TEXT
);

COMMENT ON TABLE tb_download_task IS '下载任务表';
COMMENT ON COLUMN tb_download_task.user_id IS '用户ID';
COMMENT ON COLUMN tb_download_task.path IS '文件路径';
COMMENT ON COLUMN tb_download_task.query IS '查询条件';
COMMENT ON COLUMN tb_download_task.show_query IS '显示查询条件';
COMMENT ON COLUMN tb_download_task.status IS '任务状态：0-待处理，1-处理中，2-已完成，3-失败';
COMMENT ON COLUMN tb_download_task.file_size IS '文件大小（字节）';
COMMENT ON COLUMN tb_download_task.download_count IS '下载次数';
COMMENT ON COLUMN tb_download_task.expire_time IS '过期时间';
COMMENT ON COLUMN tb_download_task.task_type IS '任务类型：EXPORT-导出，BACKUP-备份';
COMMENT ON COLUMN tb_download_task.file_format IS '文件格式：CSV, JSON, EXCEL';
COMMENT ON COLUMN tb_download_task.compression_type IS '压缩类型：NONE, ZIP, GZIP';

-- ========================================
-- 索引定义
-- ========================================

-- 数据生命周期配置表索引
CREATE INDEX idx_data_lifecycle_config_table_name ON data_lifecycle_config (table_name);
CREATE INDEX idx_data_lifecycle_config_retention_days ON data_lifecycle_config (retention_days);

-- 维护任务表索引
CREATE INDEX idx_maintenance_task_task_type ON maintenance_task (task_type);
CREATE INDEX idx_maintenance_task_status ON maintenance_task (status);
CREATE INDEX idx_maintenance_task_scheduled_time ON maintenance_task (scheduled_time);
CREATE INDEX idx_maintenance_task_target_table ON maintenance_task (target_table);

-- 系统配置相关表索引
CREATE INDEX idx_system_state_config_type_name ON system_state_config (type_name);
CREATE INDEX idx_system_dictionary_valset_id ON system_dictionary (valset_id);

-- 用户配置相关表索引
CREATE INDEX idx_user_query_history_created_by ON user_query_history (created_by);
CREATE INDEX idx_user_query_history_user_name ON user_query_history (user_name);
CREATE INDEX idx_user_query_history_created_at ON user_query_history (created_at);

CREATE INDEX idx_user_query_template_created_by ON user_query_template (created_by);
CREATE INDEX idx_user_query_template_user_name ON user_query_template (user_name);

CREATE INDEX idx_user_display_preferences_user_id ON user_display_preferences (user_id);
CREATE INDEX idx_user_display_preferences_module ON user_display_preferences (module);

-- 数据导出相关表索引
CREATE INDEX idx_data_export_task_created_by ON data_export_task (created_by);
CREATE INDEX idx_data_export_task_state ON data_export_task (state);
CREATE INDEX idx_data_export_task_created_at ON data_export_task (created_at);

CREATE INDEX idx_data_export_task_register_created_by ON data_export_task_register (created_by);
CREATE INDEX idx_data_export_task_register_type ON data_export_task_register (type);
CREATE INDEX idx_data_export_task_register_task_type ON data_export_task_register (task_type);

-- 网络配置相关表索引
CREATE INDEX idx_network_device_config_task_id ON network_device_config (task_id);
CREATE INDEX idx_network_device_config_mac ON network_device_config (mac);

CREATE INDEX idx_network_flow_config_pcie_id ON network_flow_config (pcie_id);



-- 目标管理相关表索引
CREATE INDEX idx_target_group_name ON target_group (name);
CREATE INDEX idx_target_remark_target_key ON target_remark (target_key);
CREATE INDEX idx_target_remark_target_type ON target_remark (target_type);

-- 设备和网络管理相关表索引
CREATE INDEX idx_device_ip_mapping_ip ON device_ip_mapping (ip);
CREATE INDEX idx_device_ip_mapping_device_type ON device_ip_mapping (device_type);

CREATE INDEX idx_internal_network_segment_task_id ON internal_network_segment (task_id);
CREATE INDEX idx_internal_network_segment_inter_ip ON internal_network_segment (inter_ip);

-- 白名单相关表索引
CREATE INDEX idx_internal_certificate_whitelist_task_id ON internal_certificate_whitelist (task_id);
CREATE INDEX idx_internal_domain_whitelist_task_id ON internal_domain_whitelist (task_id);
CREATE INDEX idx_internal_domain_whitelist_type ON internal_domain_whitelist (type);
CREATE INDEX idx_internal_ip_whitelist_task_id ON internal_ip_whitelist (task_id);

-- 分析插件相关表索引
CREATE INDEX idx_analysis_plugin_plugin_name ON analysis_plugin (plugin_name);
CREATE INDEX idx_analysis_plugin_plugin_type ON analysis_plugin (plugin_type);
CREATE INDEX idx_analysis_plugin_enabled ON analysis_plugin (enabled);

-- 下载任务表索引
CREATE INDEX idx_tb_download_task_user_id ON tb_download_task (user_id);
CREATE INDEX idx_tb_download_task_status ON tb_download_task (status);
CREATE INDEX idx_tb_download_task_create_time ON tb_download_task (create_time);
CREATE INDEX idx_tb_download_task_expire_time ON tb_download_task (expire_time);
CREATE INDEX idx_tb_download_task_task_type ON tb_download_task (task_type);

-- ========================================
-- 触发器定义
-- ========================================

-- 为有updated_at字段的表创建更新时间触发器
CREATE TRIGGER update_data_lifecycle_config_updated_at
    BEFORE UPDATE ON data_lifecycle_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_maintenance_task_updated_at
    BEFORE UPDATE ON maintenance_task
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_query_history_updated_at
    BEFORE UPDATE ON user_query_history
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_query_template_updated_at
    BEFORE UPDATE ON user_query_template
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_display_preferences_updated_at
    BEFORE UPDATE ON user_display_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_data_export_task_register_updated_at
    BEFORE UPDATE ON data_export_task_register
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_network_device_config_updated_at
    BEFORE UPDATE ON network_device_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();



CREATE TRIGGER update_target_group_updated_at
    BEFORE UPDATE ON target_group
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_target_remark_updated_at
    BEFORE UPDATE ON target_remark
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_device_ip_mapping_updated_at
    BEFORE UPDATE ON device_ip_mapping
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_internal_network_segment_updated_at
    BEFORE UPDATE ON internal_network_segment
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_internal_certificate_whitelist_updated_at
    BEFORE UPDATE ON internal_certificate_whitelist
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_internal_domain_whitelist_updated_at
    BEFORE UPDATE ON internal_domain_whitelist
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_internal_ip_whitelist_updated_at
    BEFORE UPDATE ON internal_ip_whitelist
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_analysis_plugin_updated_at
    BEFORE UPDATE ON analysis_plugin
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tb_download_task_updated_at
    BEFORE UPDATE ON tb_download_task
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

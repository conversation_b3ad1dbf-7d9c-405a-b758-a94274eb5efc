-- ========================================
-- NTA 3.0 会话查询和分析模块数据库结构
-- ========================================
-- 创建时间: 2025-01-22
-- 描述: 会话查询和分析模块相关的所有表结构定义
-- 数据库: PostgreSQL
-- ========================================

-- ========================================
-- 会话分析和管理表
-- ========================================

-- 会话分析表
DROP TABLE IF EXISTS session_analysis CASCADE;

CREATE TABLE session_analysis (
    session_id VARCHAR(255) PRIMARY KEY,
    black_list INTEGER NOT NULL,
    white_list INTEGER NOT NULL,
    remark VARCHAR(4096) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE session_analysis IS '会话分析表';
COMMENT ON COLUMN session_analysis.session_id IS '会话ID';
COMMENT ON COLUMN session_analysis.black_list IS '黑名单标识';
COMMENT ON COLUMN session_analysis.white_list IS '白名单标识';
COMMENT ON COLUMN session_analysis.remark IS '备注';
COMMENT ON COLUMN session_analysis.created_at IS '创建时间';
COMMENT ON COLUMN session_analysis.updated_at IS '更新时间';
COMMENT ON COLUMN session_analysis.created_by IS '创建者用户ID';
COMMENT ON COLUMN session_analysis.updated_by IS '更新者用户ID';

-- 会话统计表
DROP TABLE IF EXISTS session_statistics CASCADE;

CREATE TABLE session_statistics (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_seconds INTEGER,
    packet_count BIGINT DEFAULT 0,
    bytes_total BIGINT DEFAULT 0,
    bytes_up BIGINT DEFAULT 0,
    bytes_down BIGINT DEFAULT 0,
    src_ip VARCHAR(45),
    dst_ip VARCHAR(45),
    src_port INTEGER,
    dst_port INTEGER,
    protocol VARCHAR(20),
    application_protocol VARCHAR(50),
    risk_level INTEGER DEFAULT 0,
    threat_score DOUBLE PRECISION DEFAULT 0.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE session_statistics IS '会话统计表';
COMMENT ON COLUMN session_statistics.id IS '统计记录ID';
COMMENT ON COLUMN session_statistics.session_id IS '会话ID';
COMMENT ON COLUMN session_statistics.start_time IS '会话开始时间';
COMMENT ON COLUMN session_statistics.end_time IS '会话结束时间';
COMMENT ON COLUMN session_statistics.duration_seconds IS '会话持续时间（秒）';
COMMENT ON COLUMN session_statistics.packet_count IS '数据包数量';
COMMENT ON COLUMN session_statistics.bytes_total IS '总字节数';
COMMENT ON COLUMN session_statistics.bytes_up IS '上行字节数';
COMMENT ON COLUMN session_statistics.bytes_down IS '下行字节数';
COMMENT ON COLUMN session_statistics.src_ip IS '源IP地址';
COMMENT ON COLUMN session_statistics.dst_ip IS '目标IP地址';
COMMENT ON COLUMN session_statistics.src_port IS '源端口';
COMMENT ON COLUMN session_statistics.dst_port IS '目标端口';
COMMENT ON COLUMN session_statistics.protocol IS '协议类型';
COMMENT ON COLUMN session_statistics.application_protocol IS '应用层协议';
COMMENT ON COLUMN session_statistics.risk_level IS '风险等级（0-5）';
COMMENT ON COLUMN session_statistics.threat_score IS '威胁评分（0-100）';

-- 会话标签关联表
DROP TABLE IF EXISTS session_labels CASCADE;

CREATE TABLE session_labels (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(64) NOT NULL, -- 对应 Doris dwd_session_logs 表的 session_id 字段
    label_id INTEGER NOT NULL, -- 引用统一标签表 labels.id
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER, -- 创建者用户ID
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER, -- 更新者用户ID
    UNIQUE (session_id, label_id) -- 同一会话不能重复关联同一个标签
);

COMMENT ON TABLE session_labels IS '会话标签关联表 - 存储会话与标签的关联关系';
COMMENT ON COLUMN session_labels.session_id IS '会话ID，对应 Doris dwd_session_logs.session_id';
COMMENT ON COLUMN session_labels.label_id IS '标签ID，引用 labels 表';
COMMENT ON COLUMN session_labels.created_by IS '关联创建者用户ID';
COMMENT ON COLUMN session_labels.updated_by IS '关联更新者用户ID';

-- 会话查询历史表
DROP TABLE IF EXISTS session_query_history CASCADE;

CREATE TABLE session_query_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    query_name VARCHAR(255),
    query_conditions JSONB NOT NULL,
    query_sql TEXT,
    result_count INTEGER DEFAULT 0,
    execution_time_ms INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE session_query_history IS '会话查询历史表';
COMMENT ON COLUMN session_query_history.id IS '查询历史ID';
COMMENT ON COLUMN session_query_history.user_id IS '用户ID';
COMMENT ON COLUMN session_query_history.query_name IS '查询名称';
COMMENT ON COLUMN session_query_history.query_conditions IS '查询条件（JSON格式）';
COMMENT ON COLUMN session_query_history.query_sql IS '生成的SQL语句';
COMMENT ON COLUMN session_query_history.result_count IS '查询结果数量';
COMMENT ON COLUMN session_query_history.execution_time_ms IS '执行时间（毫秒）';

-- 会话查询模板表
DROP TABLE IF EXISTS session_query_template CASCADE;

CREATE TABLE session_query_template (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(255) NOT NULL,
    template_description TEXT,
    query_conditions JSONB NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER
);

COMMENT ON TABLE session_query_template IS '会话查询模板表';
COMMENT ON COLUMN session_query_template.id IS '模板ID';
COMMENT ON COLUMN session_query_template.template_name IS '模板名称';
COMMENT ON COLUMN session_query_template.template_description IS '模板描述';
COMMENT ON COLUMN session_query_template.query_conditions IS '查询条件模板（JSON格式）';
COMMENT ON COLUMN session_query_template.is_public IS '是否公开模板';
COMMENT ON COLUMN session_query_template.created_by IS '创建者用户ID';
COMMENT ON COLUMN session_query_template.updated_by IS '更新者用户ID';

-- 会话异常检测表
DROP TABLE IF EXISTS session_anomaly_detection CASCADE;

CREATE TABLE session_anomaly_detection (
    id BIGSERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    anomaly_type VARCHAR(50) NOT NULL, -- TRAFFIC_SPIKE, UNUSUAL_PROTOCOL, SUSPICIOUS_BEHAVIOR
    anomaly_score DOUBLE PRECISION NOT NULL,
    threshold_value DOUBLE PRECISION,
    actual_value DOUBLE PRECISION,
    description TEXT,
    detection_algorithm VARCHAR(100),
    detection_time TIMESTAMP NOT NULL,
    status VARCHAR(20) DEFAULT 'DETECTED', -- DETECTED, CONFIRMED, FALSE_POSITIVE, RESOLVED
    analyst_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE session_anomaly_detection IS '会话异常检测表';
COMMENT ON COLUMN session_anomaly_detection.id IS '异常检测ID';
COMMENT ON COLUMN session_anomaly_detection.session_id IS '会话ID';
COMMENT ON COLUMN session_anomaly_detection.anomaly_type IS '异常类型';
COMMENT ON COLUMN session_anomaly_detection.anomaly_score IS '异常评分（0-100）';
COMMENT ON COLUMN session_anomaly_detection.threshold_value IS '阈值';
COMMENT ON COLUMN session_anomaly_detection.actual_value IS '实际值';
COMMENT ON COLUMN session_anomaly_detection.description IS '异常描述';
COMMENT ON COLUMN session_anomaly_detection.detection_algorithm IS '检测算法';
COMMENT ON COLUMN session_anomaly_detection.detection_time IS '检测时间';
COMMENT ON COLUMN session_anomaly_detection.status IS '处理状态';
COMMENT ON COLUMN session_anomaly_detection.analyst_notes IS '分析师备注';

-- 会话聚合分析表
DROP TABLE IF EXISTS session_aggregation CASCADE;

CREATE TABLE session_aggregation (
    id BIGSERIAL PRIMARY KEY,
    aggregation_type VARCHAR(50) NOT NULL, -- HOURLY, DAILY, WEEKLY
    time_bucket TIMESTAMP NOT NULL,
    src_ip VARCHAR(45),
    dst_ip VARCHAR(45),
    protocol VARCHAR(20),
    application_protocol VARCHAR(50),
    session_count BIGINT DEFAULT 0,
    total_bytes BIGINT DEFAULT 0,
    total_packets BIGINT DEFAULT 0,
    avg_duration_seconds DOUBLE PRECISION,
    unique_src_count INTEGER DEFAULT 0,
    unique_dst_count INTEGER DEFAULT 0,
    threat_session_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE session_aggregation IS '会话聚合分析表';
COMMENT ON COLUMN session_aggregation.id IS '聚合记录ID';
COMMENT ON COLUMN session_aggregation.aggregation_type IS '聚合类型';
COMMENT ON COLUMN session_aggregation.time_bucket IS '时间桶';
COMMENT ON COLUMN session_aggregation.src_ip IS '源IP地址';
COMMENT ON COLUMN session_aggregation.dst_ip IS '目标IP地址';
COMMENT ON COLUMN session_aggregation.protocol IS '协议类型';
COMMENT ON COLUMN session_aggregation.application_protocol IS '应用层协议';
COMMENT ON COLUMN session_aggregation.session_count IS '会话数量';
COMMENT ON COLUMN session_aggregation.total_bytes IS '总字节数';
COMMENT ON COLUMN session_aggregation.total_packets IS '总数据包数';
COMMENT ON COLUMN session_aggregation.avg_duration_seconds IS '平均持续时间（秒）';
COMMENT ON COLUMN session_aggregation.unique_src_count IS '唯一源IP数量';
COMMENT ON COLUMN session_aggregation.unique_dst_count IS '唯一目标IP数量';
COMMENT ON COLUMN session_aggregation.threat_session_count IS '威胁会话数量';

-- 会话白名单表
DROP TABLE IF EXISTS session_whitelist CASCADE;

CREATE TABLE session_whitelist (
    id SERIAL PRIMARY KEY,
    whitelist_name VARCHAR(255) NOT NULL,
    whitelist_type VARCHAR(50) NOT NULL, -- IP_PAIR, PROTOCOL, APPLICATION, PATTERN
    src_ip_pattern VARCHAR(100),
    dst_ip_pattern VARCHAR(100),
    protocol VARCHAR(20),
    application_protocol VARCHAR(50),
    port_range VARCHAR(50),
    pattern_rules JSONB,
    reason TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    expiry_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE session_whitelist IS '会话白名单表';
COMMENT ON COLUMN session_whitelist.id IS '白名单ID';
COMMENT ON COLUMN session_whitelist.whitelist_name IS '白名单名称';
COMMENT ON COLUMN session_whitelist.whitelist_type IS '白名单类型';
COMMENT ON COLUMN session_whitelist.src_ip_pattern IS '源IP模式';
COMMENT ON COLUMN session_whitelist.dst_ip_pattern IS '目标IP模式';
COMMENT ON COLUMN session_whitelist.protocol IS '协议类型';
COMMENT ON COLUMN session_whitelist.application_protocol IS '应用层协议';
COMMENT ON COLUMN session_whitelist.port_range IS '端口范围';
COMMENT ON COLUMN session_whitelist.pattern_rules IS '模式规则（JSON格式）';
COMMENT ON COLUMN session_whitelist.reason IS '加入白名单的原因';
COMMENT ON COLUMN session_whitelist.enabled IS '是否启用';
COMMENT ON COLUMN session_whitelist.expiry_time IS '过期时间';

-- 会话黑名单表
DROP TABLE IF EXISTS session_blacklist CASCADE;

CREATE TABLE session_blacklist (
    id SERIAL PRIMARY KEY,
    blacklist_name VARCHAR(255) NOT NULL,
    blacklist_type VARCHAR(50) NOT NULL, -- IP_PAIR, PROTOCOL, APPLICATION, PATTERN
    src_ip_pattern VARCHAR(100),
    dst_ip_pattern VARCHAR(100),
    protocol VARCHAR(20),
    application_protocol VARCHAR(50),
    port_range VARCHAR(50),
    pattern_rules JSONB,
    threat_level INTEGER DEFAULT 1, -- 1-5
    reason TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    expiry_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE session_blacklist IS '会话黑名单表';
COMMENT ON COLUMN session_blacklist.id IS '黑名单ID';
COMMENT ON COLUMN session_blacklist.blacklist_name IS '黑名单名称';
COMMENT ON COLUMN session_blacklist.blacklist_type IS '黑名单类型';
COMMENT ON COLUMN session_blacklist.src_ip_pattern IS '源IP模式';
COMMENT ON COLUMN session_blacklist.dst_ip_pattern IS '目标IP模式';
COMMENT ON COLUMN session_blacklist.protocol IS '协议类型';
COMMENT ON COLUMN session_blacklist.application_protocol IS '应用层协议';
COMMENT ON COLUMN session_blacklist.port_range IS '端口范围';
COMMENT ON COLUMN session_blacklist.pattern_rules IS '模式规则（JSON格式）';
COMMENT ON COLUMN session_blacklist.threat_level IS '威胁等级（1-5）';
COMMENT ON COLUMN session_blacklist.reason IS '加入黑名单的原因';
COMMENT ON COLUMN session_blacklist.enabled IS '是否启用';
COMMENT ON COLUMN session_blacklist.expiry_time IS '过期时间';

-- ========================================
-- 创建索引
-- ========================================

-- 会话分析表索引
CREATE INDEX idx_session_analysis_session_id ON session_analysis (session_id);
CREATE INDEX idx_session_analysis_black_list ON session_analysis (black_list);
CREATE INDEX idx_session_analysis_white_list ON session_analysis (white_list);
CREATE INDEX idx_session_analysis_created_at ON session_analysis (created_at);

-- 会话统计表索引
CREATE INDEX idx_session_statistics_session_id ON session_statistics (session_id);
CREATE INDEX idx_session_statistics_start_time ON session_statistics (start_time);
CREATE INDEX idx_session_statistics_src_ip ON session_statistics (src_ip);
CREATE INDEX idx_session_statistics_dst_ip ON session_statistics (dst_ip);
CREATE INDEX idx_session_statistics_protocol ON session_statistics (protocol);
CREATE INDEX idx_session_statistics_risk_level ON session_statistics (risk_level);
CREATE INDEX idx_session_statistics_threat_score ON session_statistics (threat_score);

-- 会话标签关联表索引
CREATE INDEX idx_session_labels_session_id ON session_labels (session_id);
CREATE INDEX idx_session_labels_label_id ON session_labels (label_id);
CREATE INDEX idx_session_labels_created_at ON session_labels (created_at);
CREATE INDEX idx_session_labels_created_by ON session_labels (created_by);

-- 会话查询历史表索引
CREATE INDEX idx_session_query_history_user_id ON session_query_history (user_id);
CREATE INDEX idx_session_query_history_created_at ON session_query_history (created_at);
CREATE INDEX idx_session_query_history_execution_time ON session_query_history (execution_time_ms);

-- 会话查询模板表索引
CREATE INDEX idx_session_query_template_created_by ON session_query_template (created_by);
CREATE INDEX idx_session_query_template_is_public ON session_query_template (is_public);
CREATE INDEX idx_session_query_template_template_name ON session_query_template (template_name);

-- 会话异常检测表索引
CREATE INDEX idx_session_anomaly_session_id ON session_anomaly_detection (session_id);
CREATE INDEX idx_session_anomaly_type ON session_anomaly_detection (anomaly_type);
CREATE INDEX idx_session_anomaly_score ON session_anomaly_detection (anomaly_score);
CREATE INDEX idx_session_anomaly_detection_time ON session_anomaly_detection (detection_time);
CREATE INDEX idx_session_anomaly_status ON session_anomaly_detection (status);

-- 会话聚合分析表索引
CREATE INDEX idx_session_aggregation_type_bucket ON session_aggregation (aggregation_type, time_bucket);
CREATE INDEX idx_session_aggregation_src_ip ON session_aggregation (src_ip);
CREATE INDEX idx_session_aggregation_dst_ip ON session_aggregation (dst_ip);
CREATE INDEX idx_session_aggregation_protocol ON session_aggregation (protocol);
CREATE INDEX idx_session_aggregation_created_at ON session_aggregation (created_at);

-- 会话白名单表索引
CREATE INDEX idx_session_whitelist_type ON session_whitelist (whitelist_type);
CREATE INDEX idx_session_whitelist_enabled ON session_whitelist (enabled);
CREATE INDEX idx_session_whitelist_expiry_time ON session_whitelist (expiry_time);
CREATE INDEX idx_session_whitelist_src_ip ON session_whitelist (src_ip_pattern);
CREATE INDEX idx_session_whitelist_dst_ip ON session_whitelist (dst_ip_pattern);

-- 会话黑名单表索引
CREATE INDEX idx_session_blacklist_type ON session_blacklist (blacklist_type);
CREATE INDEX idx_session_blacklist_enabled ON session_blacklist (enabled);
CREATE INDEX idx_session_blacklist_threat_level ON session_blacklist (threat_level);
CREATE INDEX idx_session_blacklist_expiry_time ON session_blacklist (expiry_time);
CREATE INDEX idx_session_blacklist_src_ip ON session_blacklist (src_ip_pattern);
CREATE INDEX idx_session_blacklist_dst_ip ON session_blacklist (dst_ip_pattern);

-- ========================================
-- 创建更新时间触发器
-- ========================================

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为会话相关表创建更新时间触发器
CREATE TRIGGER update_session_analysis_updated_at 
    BEFORE UPDATE ON session_analysis 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_statistics_updated_at 
    BEFORE UPDATE ON session_statistics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_labels_updated_at 
    BEFORE UPDATE ON session_labels 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_query_history_updated_at 
    BEFORE UPDATE ON session_query_history 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_query_template_updated_at 
    BEFORE UPDATE ON session_query_template 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_anomaly_detection_updated_at 
    BEFORE UPDATE ON session_anomaly_detection 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_whitelist_updated_at 
    BEFORE UPDATE ON session_whitelist 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_session_blacklist_updated_at 
    BEFORE UPDATE ON session_blacklist 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 插入默认数据
-- ========================================

-- 插入默认会话查询模板
INSERT INTO session_query_template (template_name, template_description, query_conditions, is_public, created_by) VALUES
('高风险会话查询', '查询威胁评分大于80的会话', '{"threat_score": {"operator": ">", "value": 80}}', true, 1),
('长时间会话查询', '查询持续时间超过1小时的会话', '{"duration_seconds": {"operator": ">", "value": 3600}}', true, 1),
('大流量会话查询', '查询总字节数超过100MB的会话', '{"bytes_total": {"operator": ">", "value": 104857600}}', true, 1),
('异常端口会话查询', '查询使用非标准端口的会话', '{"dst_port": {"operator": "not_in", "value": [80, 443, 22, 21, 25, 53, 110, 143, 993, 995]}}', true, 1),
('外网通信会话查询', '查询与外网通信的会话', '{"dst_ip": {"operator": "not_like", "value": ["10.%", "172.16.%", "192.168.%"]}}', true, 1);

-- 插入默认会话白名单
INSERT INTO session_whitelist (whitelist_name, whitelist_type, src_ip_pattern, dst_ip_pattern, protocol, reason, created_by) VALUES
('内网DNS查询', 'PROTOCOL', '10.%', '10.%', 'UDP', 'DNS查询流量，正常业务通信', 1),
('内网HTTP通信', 'PROTOCOL', '192.168.%', '192.168.%', 'TCP', '内网HTTP服务通信，正常业务流量', 1),
('管理网段通信', 'IP_PAIR', '172.16.%', '172.16.%', NULL, '管理网段内部通信，运维管理流量', 1);

-- 插入默认会话黑名单
INSERT INTO session_blacklist (blacklist_name, blacklist_type, dst_ip_pattern, protocol, threat_level, reason, created_by) VALUES
('恶意IP通信', 'IP_PAIR', NULL, '*******', NULL, 5, '已知恶意IP地址，禁止通信', 1),
('可疑端口扫描', 'PATTERN', NULL, NULL, 'TCP', 3, '端口扫描行为，可疑活动', 1),
('异常协议通信', 'PROTOCOL', NULL, NULL, 'ICMP', 2, '异常ICMP流量，可能的隧道通信', 1);
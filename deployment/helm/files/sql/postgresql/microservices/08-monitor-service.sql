-- ========================================
-- Monitor Service Database Schema
-- 监控服务数据库结构
-- ========================================

-- 创建数据库（如果需要）
-- CREATE DATABASE monitor_service;
-- \c monitor_service;

-- ========================================
-- 系统监控表
-- ========================================

-- 系统性能监控表
DROP TABLE IF EXISTS system_performance CASCADE;
CREATE TABLE system_performance (
    id SERIAL PRIMARY KEY,
    metric_id VARCHAR(100) UNIQUE NOT NULL,
    node_id VARCHAR(100) NOT NULL,
    node_name VARCHAR(200),
    node_type VARCHAR(50) NOT NULL,
    metric_time TIMESTAMP NOT NULL,
    cpu_usage_percent DECIMAL(5,2),
    memory_usage_percent DECIMAL(5,2),
    memory_total_mb BIGINT,
    memory_used_mb BIGINT,
    memory_available_mb BIGINT,
    disk_usage_percent DECIMAL(5,2),
    disk_total_gb BIGINT,
    disk_used_gb BIGINT,
    disk_available_gb BIGINT,
    disk_io_read_mb DECIMAL(10,2),
    disk_io_write_mb DECIMAL(10,2),
    network_in_mb DECIMAL(10,2),
    network_out_mb DECIMAL(10,2),
    network_connections INTEGER,
    load_average_1m DECIMAL(5,2),
    load_average_5m DECIMAL(5,2),
    load_average_15m DECIMAL(5,2),
    process_count INTEGER,
    thread_count INTEGER,
    uptime_seconds BIGINT,
    temperature_celsius DECIMAL(5,2),
    additional_metrics JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE system_performance IS '系统性能监控表';
COMMENT ON COLUMN system_performance.id IS '性能记录ID';
COMMENT ON COLUMN system_performance.metric_id IS '指标唯一标识';
COMMENT ON COLUMN system_performance.node_id IS '节点ID';
COMMENT ON COLUMN system_performance.node_name IS '节点名称';
COMMENT ON COLUMN system_performance.node_type IS '节点类型：SERVER, CONTAINER, VM, PHYSICAL';
COMMENT ON COLUMN system_performance.metric_time IS '指标时间';
COMMENT ON COLUMN system_performance.cpu_usage_percent IS 'CPU使用率（%）';
COMMENT ON COLUMN system_performance.memory_usage_percent IS '内存使用率（%）';
COMMENT ON COLUMN system_performance.memory_total_mb IS '总内存（MB）';
COMMENT ON COLUMN system_performance.memory_used_mb IS '已用内存（MB）';
COMMENT ON COLUMN system_performance.memory_available_mb IS '可用内存（MB）';
COMMENT ON COLUMN system_performance.disk_usage_percent IS '磁盘使用率（%）';
COMMENT ON COLUMN system_performance.disk_total_gb IS '总磁盘空间（GB）';
COMMENT ON COLUMN system_performance.disk_used_gb IS '已用磁盘空间（GB）';
COMMENT ON COLUMN system_performance.disk_available_gb IS '可用磁盘空间（GB）';
COMMENT ON COLUMN system_performance.disk_io_read_mb IS '磁盘读取（MB）';
COMMENT ON COLUMN system_performance.disk_io_write_mb IS '磁盘写入（MB）';
COMMENT ON COLUMN system_performance.network_in_mb IS '网络入流量（MB）';
COMMENT ON COLUMN system_performance.network_out_mb IS '网络出流量（MB）';
COMMENT ON COLUMN system_performance.network_connections IS '网络连接数';
COMMENT ON COLUMN system_performance.load_average_1m IS '1分钟负载平均值';
COMMENT ON COLUMN system_performance.load_average_5m IS '5分钟负载平均值';
COMMENT ON COLUMN system_performance.load_average_15m IS '15分钟负载平均值';
COMMENT ON COLUMN system_performance.process_count IS '进程数';
COMMENT ON COLUMN system_performance.thread_count IS '线程数';
COMMENT ON COLUMN system_performance.uptime_seconds IS '运行时间（秒）';
COMMENT ON COLUMN system_performance.temperature_celsius IS '温度（摄氏度）';
COMMENT ON COLUMN system_performance.additional_metrics IS '附加指标';
COMMENT ON COLUMN system_performance.created_at IS '创建时间';

-- 应用性能监控表
DROP TABLE IF EXISTS application_performance CASCADE;
CREATE TABLE application_performance (
    id SERIAL PRIMARY KEY,
    metric_id VARCHAR(100) UNIQUE NOT NULL,
    application_id VARCHAR(100) NOT NULL,
    application_name VARCHAR(200),
    service_name VARCHAR(200),
    instance_id VARCHAR(100),
    metric_time TIMESTAMP NOT NULL,
    response_time_ms DECIMAL(10,2),
    throughput_rps DECIMAL(10,2),
    error_rate_percent DECIMAL(5,2),
    success_rate_percent DECIMAL(5,2),
    active_connections INTEGER,
    queue_length INTEGER,
    memory_usage_mb DECIMAL(10,2),
    cpu_usage_percent DECIMAL(5,2),
    gc_time_ms DECIMAL(10,2),
    gc_count INTEGER,
    thread_pool_active INTEGER,
    thread_pool_total INTEGER,
    database_connections INTEGER,
    cache_hit_rate_percent DECIMAL(5,2),
    custom_metrics JSONB,
    health_status VARCHAR(20) DEFAULT 'UNKNOWN',
    version VARCHAR(50),
    environment VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE application_performance IS '应用性能监控表';
COMMENT ON COLUMN application_performance.id IS '性能记录ID';
COMMENT ON COLUMN application_performance.metric_id IS '指标唯一标识';
COMMENT ON COLUMN application_performance.application_id IS '应用ID';
COMMENT ON COLUMN application_performance.application_name IS '应用名称';
COMMENT ON COLUMN application_performance.service_name IS '服务名称';
COMMENT ON COLUMN application_performance.instance_id IS '实例ID';
COMMENT ON COLUMN application_performance.metric_time IS '指标时间';
COMMENT ON COLUMN application_performance.response_time_ms IS '响应时间（毫秒）';
COMMENT ON COLUMN application_performance.throughput_rps IS '吞吐量（请求/秒）';
COMMENT ON COLUMN application_performance.error_rate_percent IS '错误率（%）';
COMMENT ON COLUMN application_performance.success_rate_percent IS '成功率（%）';
COMMENT ON COLUMN application_performance.active_connections IS '活跃连接数';
COMMENT ON COLUMN application_performance.queue_length IS '队列长度';
COMMENT ON COLUMN application_performance.memory_usage_mb IS '内存使用量（MB）';
COMMENT ON COLUMN application_performance.cpu_usage_percent IS 'CPU使用率（%）';
COMMENT ON COLUMN application_performance.gc_time_ms IS 'GC时间（毫秒）';
COMMENT ON COLUMN application_performance.gc_count IS 'GC次数';
COMMENT ON COLUMN application_performance.thread_pool_active IS '活跃线程数';
COMMENT ON COLUMN application_performance.thread_pool_total IS '总线程数';
COMMENT ON COLUMN application_performance.database_connections IS '数据库连接数';
COMMENT ON COLUMN application_performance.cache_hit_rate_percent IS '缓存命中率（%）';
COMMENT ON COLUMN application_performance.custom_metrics IS '自定义指标';
COMMENT ON COLUMN application_performance.health_status IS '健康状态：HEALTHY, WARNING, ERROR, UNKNOWN';
COMMENT ON COLUMN application_performance.version IS '应用版本';
COMMENT ON COLUMN application_performance.environment IS '环境';
COMMENT ON COLUMN application_performance.created_at IS '创建时间';

-- ========================================
-- 健康检查表
-- ========================================

-- 健康检查配置表
DROP TABLE IF EXISTS health_check_config CASCADE;
CREATE TABLE health_check_config (
    id SERIAL PRIMARY KEY,
    check_id VARCHAR(100) UNIQUE NOT NULL,
    check_name VARCHAR(200) NOT NULL,
    check_type VARCHAR(50) NOT NULL,
    target_type VARCHAR(50) NOT NULL,
    target_id VARCHAR(100) NOT NULL,
    check_interval_seconds INTEGER DEFAULT 60,
    timeout_seconds INTEGER DEFAULT 30,
    retry_count INTEGER DEFAULT 3,
    check_config JSONB NOT NULL,
    threshold_config JSONB,
    notification_config JSONB,
    is_enabled BOOLEAN DEFAULT TRUE,
    is_critical BOOLEAN DEFAULT FALSE,
    tags JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE health_check_config IS '健康检查配置表';
COMMENT ON COLUMN health_check_config.id IS '配置记录ID';
COMMENT ON COLUMN health_check_config.check_id IS '检查唯一标识';
COMMENT ON COLUMN health_check_config.check_name IS '检查名称';
COMMENT ON COLUMN health_check_config.check_type IS '检查类型：HTTP, TCP, PING, DATABASE, CUSTOM';
COMMENT ON COLUMN health_check_config.target_type IS '目标类型：SERVICE, DATABASE, API, ENDPOINT';
COMMENT ON COLUMN health_check_config.target_id IS '目标ID';
COMMENT ON COLUMN health_check_config.check_interval_seconds IS '检查间隔（秒）';
COMMENT ON COLUMN health_check_config.timeout_seconds IS '超时时间（秒）';
COMMENT ON COLUMN health_check_config.retry_count IS '重试次数';
COMMENT ON COLUMN health_check_config.check_config IS '检查配置';
COMMENT ON COLUMN health_check_config.threshold_config IS '阈值配置';
COMMENT ON COLUMN health_check_config.notification_config IS '通知配置';
COMMENT ON COLUMN health_check_config.is_enabled IS '是否启用';
COMMENT ON COLUMN health_check_config.is_critical IS '是否关键';
COMMENT ON COLUMN health_check_config.tags IS '标签';
COMMENT ON COLUMN health_check_config.created_at IS '创建时间';
COMMENT ON COLUMN health_check_config.updated_at IS '更新时间';
COMMENT ON COLUMN health_check_config.created_by IS '创建人';
COMMENT ON COLUMN health_check_config.updated_by IS '更新人';

-- 健康检查结果表
DROP TABLE IF EXISTS health_check_result CASCADE;
CREATE TABLE health_check_result (
    id SERIAL PRIMARY KEY,
    result_id VARCHAR(100) UNIQUE NOT NULL,
    check_id VARCHAR(100) NOT NULL,
    check_time TIMESTAMP NOT NULL,
    status VARCHAR(20) NOT NULL,
    response_time_ms INTEGER,
    status_code INTEGER,
    response_message TEXT,
    error_message TEXT,
    check_details JSONB,
    metrics JSONB,
    is_state_change BOOLEAN DEFAULT FALSE,
    previous_status VARCHAR(20),
    consecutive_failures INTEGER DEFAULT 0,
    consecutive_successes INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (check_id) REFERENCES health_check_config(check_id) ON DELETE CASCADE
);

COMMENT ON TABLE health_check_result IS '健康检查结果表';
COMMENT ON COLUMN health_check_result.id IS '结果记录ID';
COMMENT ON COLUMN health_check_result.result_id IS '结果唯一标识';
COMMENT ON COLUMN health_check_result.check_id IS '检查ID';
COMMENT ON COLUMN health_check_result.check_time IS '检查时间';
COMMENT ON COLUMN health_check_result.status IS '状态：HEALTHY, WARNING, ERROR, TIMEOUT, UNKNOWN';
COMMENT ON COLUMN health_check_result.response_time_ms IS '响应时间（毫秒）';
COMMENT ON COLUMN health_check_result.status_code IS '状态码';
COMMENT ON COLUMN health_check_result.response_message IS '响应消息';
COMMENT ON COLUMN health_check_result.error_message IS '错误消息';
COMMENT ON COLUMN health_check_result.check_details IS '检查详情';
COMMENT ON COLUMN health_check_result.metrics IS '指标数据';
COMMENT ON COLUMN health_check_result.is_state_change IS '是否状态变更';
COMMENT ON COLUMN health_check_result.previous_status IS '前一状态';
COMMENT ON COLUMN health_check_result.consecutive_failures IS '连续失败次数';
COMMENT ON COLUMN health_check_result.consecutive_successes IS '连续成功次数';
COMMENT ON COLUMN health_check_result.created_at IS '创建时间';

-- ========================================
-- 告警监控表
-- ========================================

-- 监控告警规则表
DROP TABLE IF EXISTS monitor_alert_rule CASCADE;
CREATE TABLE monitor_alert_rule (
    id SERIAL PRIMARY KEY,
    rule_id VARCHAR(100) UNIQUE NOT NULL,
    rule_name VARCHAR(200) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    metric_type VARCHAR(50) NOT NULL,
    target_type VARCHAR(50),
    target_filter JSONB,
    condition_expression TEXT NOT NULL,
    threshold_config JSONB NOT NULL,
    evaluation_window_seconds INTEGER DEFAULT 300,
    evaluation_frequency_seconds INTEGER DEFAULT 60,
    severity_level INTEGER DEFAULT 1,
    alert_message_template TEXT,
    notification_channels JSONB,
    suppression_config JSONB,
    recovery_config JSONB,
    rule_status VARCHAR(20) DEFAULT 'ACTIVE',
    last_evaluation TIMESTAMP,
    evaluation_count INTEGER DEFAULT 0,
    trigger_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE monitor_alert_rule IS '监控告警规则表';
COMMENT ON COLUMN monitor_alert_rule.id IS '规则记录ID';
COMMENT ON COLUMN monitor_alert_rule.rule_id IS '规则唯一标识';
COMMENT ON COLUMN monitor_alert_rule.rule_name IS '规则名称';
COMMENT ON COLUMN monitor_alert_rule.rule_type IS '规则类型：THRESHOLD, ANOMALY, COMPOSITE, RATE';
COMMENT ON COLUMN monitor_alert_rule.metric_type IS '指标类型：SYSTEM, APPLICATION, BUSINESS, CUSTOM';
COMMENT ON COLUMN monitor_alert_rule.target_type IS '目标类型';
COMMENT ON COLUMN monitor_alert_rule.target_filter IS '目标过滤器';
COMMENT ON COLUMN monitor_alert_rule.condition_expression IS '条件表达式';
COMMENT ON COLUMN monitor_alert_rule.threshold_config IS '阈值配置';
COMMENT ON COLUMN monitor_alert_rule.evaluation_window_seconds IS '评估窗口（秒）';
COMMENT ON COLUMN monitor_alert_rule.evaluation_frequency_seconds IS '评估频率（秒）';
COMMENT ON COLUMN monitor_alert_rule.severity_level IS '严重级别：1-低，2-中，3-高，4-严重';
COMMENT ON COLUMN monitor_alert_rule.alert_message_template IS '告警消息模板';
COMMENT ON COLUMN monitor_alert_rule.notification_channels IS '通知渠道';
COMMENT ON COLUMN monitor_alert_rule.suppression_config IS '抑制配置';
COMMENT ON COLUMN monitor_alert_rule.recovery_config IS '恢复配置';
COMMENT ON COLUMN monitor_alert_rule.rule_status IS '规则状态：ACTIVE, INACTIVE, TESTING, MAINTENANCE';
COMMENT ON COLUMN monitor_alert_rule.last_evaluation IS '最后评估时间';
COMMENT ON COLUMN monitor_alert_rule.evaluation_count IS '评估次数';
COMMENT ON COLUMN monitor_alert_rule.trigger_count IS '触发次数';
COMMENT ON COLUMN monitor_alert_rule.created_at IS '创建时间';
COMMENT ON COLUMN monitor_alert_rule.updated_at IS '更新时间';
COMMENT ON COLUMN monitor_alert_rule.created_by IS '创建人';
COMMENT ON COLUMN monitor_alert_rule.updated_by IS '更新人';

-- 监控告警记录表
DROP TABLE IF EXISTS monitor_alert_record CASCADE;
CREATE TABLE monitor_alert_record (
    id SERIAL PRIMARY KEY,
    alert_id VARCHAR(100) UNIQUE NOT NULL,
    rule_id VARCHAR(100) NOT NULL,
    alert_time TIMESTAMP NOT NULL,
    alert_type VARCHAR(50) NOT NULL,
    severity_level INTEGER NOT NULL,
    alert_status VARCHAR(20) DEFAULT 'FIRING',
    alert_message TEXT,
    metric_values JSONB,
    threshold_values JSONB,
    target_info JSONB,
    context_info JSONB,
    fingerprint VARCHAR(64),
    group_key VARCHAR(100),
    suppressed_until TIMESTAMP,
    acknowledged_at TIMESTAMP,
    acknowledged_by VARCHAR(100),
    resolved_at TIMESTAMP,
    resolved_by VARCHAR(100),
    resolution_notes TEXT,
    notification_sent BOOLEAN DEFAULT FALSE,
    notification_details JSONB,
    escalation_level INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (rule_id) REFERENCES monitor_alert_rule(rule_id) ON DELETE CASCADE
);

COMMENT ON TABLE monitor_alert_record IS '监控告警记录表';
COMMENT ON COLUMN monitor_alert_record.id IS '告警记录ID';
COMMENT ON COLUMN monitor_alert_record.alert_id IS '告警唯一标识';
COMMENT ON COLUMN monitor_alert_record.rule_id IS '规则ID';
COMMENT ON COLUMN monitor_alert_record.alert_time IS '告警时间';
COMMENT ON COLUMN monitor_alert_record.alert_type IS '告警类型：THRESHOLD, ANOMALY, HEALTH_CHECK, CUSTOM';
COMMENT ON COLUMN monitor_alert_record.severity_level IS '严重级别：1-低，2-中，3-高，4-严重';
COMMENT ON COLUMN monitor_alert_record.alert_status IS '告警状态：FIRING, ACKNOWLEDGED, RESOLVED, SUPPRESSED';
COMMENT ON COLUMN monitor_alert_record.alert_message IS '告警消息';
COMMENT ON COLUMN monitor_alert_record.metric_values IS '指标值';
COMMENT ON COLUMN monitor_alert_record.threshold_values IS '阈值';
COMMENT ON COLUMN monitor_alert_record.target_info IS '目标信息';
COMMENT ON COLUMN monitor_alert_record.context_info IS '上下文信息';
COMMENT ON COLUMN monitor_alert_record.fingerprint IS '指纹';
COMMENT ON COLUMN monitor_alert_record.group_key IS '分组键';
COMMENT ON COLUMN monitor_alert_record.suppressed_until IS '抑制到期时间';
COMMENT ON COLUMN monitor_alert_record.acknowledged_at IS '确认时间';
COMMENT ON COLUMN monitor_alert_record.acknowledged_by IS '确认人';
COMMENT ON COLUMN monitor_alert_record.resolved_at IS '解决时间';
COMMENT ON COLUMN monitor_alert_record.resolved_by IS '解决人';
COMMENT ON COLUMN monitor_alert_record.resolution_notes IS '解决备注';
COMMENT ON COLUMN monitor_alert_record.notification_sent IS '是否已发送通知';
COMMENT ON COLUMN monitor_alert_record.notification_details IS '通知详情';
COMMENT ON COLUMN monitor_alert_record.escalation_level IS '升级级别';
COMMENT ON COLUMN monitor_alert_record.created_at IS '创建时间';
COMMENT ON COLUMN monitor_alert_record.updated_at IS '更新时间';

-- ========================================
-- 日志监控表
-- ========================================

-- 日志监控配置表
DROP TABLE IF EXISTS log_monitor_config CASCADE;
CREATE TABLE log_monitor_config (
    id SERIAL PRIMARY KEY,
    config_id VARCHAR(100) UNIQUE NOT NULL,
    config_name VARCHAR(200) NOT NULL,
    log_source VARCHAR(200) NOT NULL,
    log_pattern TEXT,
    log_level VARCHAR(20),
    filter_conditions JSONB,
    aggregation_config JSONB,
    alert_conditions JSONB,
    retention_days INTEGER DEFAULT 30,
    sampling_rate DECIMAL(5,4) DEFAULT 1.0,
    is_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE log_monitor_config IS '日志监控配置表';
COMMENT ON COLUMN log_monitor_config.id IS '配置记录ID';
COMMENT ON COLUMN log_monitor_config.config_id IS '配置唯一标识';
COMMENT ON COLUMN log_monitor_config.config_name IS '配置名称';
COMMENT ON COLUMN log_monitor_config.log_source IS '日志源';
COMMENT ON COLUMN log_monitor_config.log_pattern IS '日志模式';
COMMENT ON COLUMN log_monitor_config.log_level IS '日志级别：DEBUG, INFO, WARN, ERROR, FATAL';
COMMENT ON COLUMN log_monitor_config.filter_conditions IS '过滤条件';
COMMENT ON COLUMN log_monitor_config.aggregation_config IS '聚合配置';
COMMENT ON COLUMN log_monitor_config.alert_conditions IS '告警条件';
COMMENT ON COLUMN log_monitor_config.retention_days IS '保留天数';
COMMENT ON COLUMN log_monitor_config.sampling_rate IS '采样率';
COMMENT ON COLUMN log_monitor_config.is_enabled IS '是否启用';
COMMENT ON COLUMN log_monitor_config.created_at IS '创建时间';
COMMENT ON COLUMN log_monitor_config.updated_at IS '更新时间';
COMMENT ON COLUMN log_monitor_config.created_by IS '创建人';
COMMENT ON COLUMN log_monitor_config.updated_by IS '更新人';

-- 日志统计表
DROP TABLE IF EXISTS log_statistics CASCADE;
CREATE TABLE log_statistics (
    id SERIAL PRIMARY KEY,
    stat_time TIMESTAMP NOT NULL,
    log_source VARCHAR(200),
    log_level VARCHAR(20),
    time_window_minutes INTEGER DEFAULT 5,
    total_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    warning_count INTEGER DEFAULT 0,
    info_count INTEGER DEFAULT 0,
    debug_count INTEGER DEFAULT 0,
    unique_messages INTEGER DEFAULT 0,
    avg_message_size DECIMAL(10,2),
    top_errors JSONB,
    top_sources JSONB,
    anomaly_score DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(stat_time, log_source, log_level, time_window_minutes)
);

COMMENT ON TABLE log_statistics IS '日志统计表';
COMMENT ON COLUMN log_statistics.id IS '统计ID';
COMMENT ON COLUMN log_statistics.stat_time IS '统计时间';
COMMENT ON COLUMN log_statistics.log_source IS '日志源';
COMMENT ON COLUMN log_statistics.log_level IS '日志级别';
COMMENT ON COLUMN log_statistics.time_window_minutes IS '时间窗口（分钟）';
COMMENT ON COLUMN log_statistics.total_count IS '总数量';
COMMENT ON COLUMN log_statistics.error_count IS '错误数量';
COMMENT ON COLUMN log_statistics.warning_count IS '警告数量';
COMMENT ON COLUMN log_statistics.info_count IS '信息数量';
COMMENT ON COLUMN log_statistics.debug_count IS '调试数量';
COMMENT ON COLUMN log_statistics.unique_messages IS '唯一消息数';
COMMENT ON COLUMN log_statistics.avg_message_size IS '平均消息大小';
COMMENT ON COLUMN log_statistics.top_errors IS '热门错误';
COMMENT ON COLUMN log_statistics.top_sources IS '热门来源';
COMMENT ON COLUMN log_statistics.anomaly_score IS '异常评分';
COMMENT ON COLUMN log_statistics.created_at IS '创建时间';

-- ========================================
-- 监控统计表
-- ========================================

-- 监控统计表
DROP TABLE IF EXISTS monitor_statistics CASCADE;
CREATE TABLE monitor_statistics (
    id SERIAL PRIMARY KEY,
    stat_date DATE NOT NULL,
    stat_hour INTEGER,
    metric_type VARCHAR(50),
    target_type VARCHAR(50),
    total_metrics INTEGER DEFAULT 0,
    healthy_targets INTEGER DEFAULT 0,
    warning_targets INTEGER DEFAULT 0,
    error_targets INTEGER DEFAULT 0,
    unknown_targets INTEGER DEFAULT 0,
    total_alerts INTEGER DEFAULT 0,
    critical_alerts INTEGER DEFAULT 0,
    warning_alerts INTEGER DEFAULT 0,
    info_alerts INTEGER DEFAULT 0,
    resolved_alerts INTEGER DEFAULT 0,
    avg_response_time_ms DECIMAL(10,2),
    max_response_time_ms DECIMAL(10,2),
    min_response_time_ms DECIMAL(10,2),
    avg_cpu_usage DECIMAL(5,2),
    avg_memory_usage DECIMAL(5,2),
    avg_disk_usage DECIMAL(5,2),
    total_downtime_minutes INTEGER DEFAULT 0,
    availability_percent DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(stat_date, stat_hour, metric_type, target_type)
);

COMMENT ON TABLE monitor_statistics IS '监控统计表';
COMMENT ON COLUMN monitor_statistics.id IS '统计ID';
COMMENT ON COLUMN monitor_statistics.stat_date IS '统计日期';
COMMENT ON COLUMN monitor_statistics.stat_hour IS '统计小时（0-23）';
COMMENT ON COLUMN monitor_statistics.metric_type IS '指标类型';
COMMENT ON COLUMN monitor_statistics.target_type IS '目标类型';
COMMENT ON COLUMN monitor_statistics.total_metrics IS '总指标数';
COMMENT ON COLUMN monitor_statistics.healthy_targets IS '健康目标数';
COMMENT ON COLUMN monitor_statistics.warning_targets IS '警告目标数';
COMMENT ON COLUMN monitor_statistics.error_targets IS '错误目标数';
COMMENT ON COLUMN monitor_statistics.unknown_targets IS '未知目标数';
COMMENT ON COLUMN monitor_statistics.total_alerts IS '总告警数';
COMMENT ON COLUMN monitor_statistics.critical_alerts IS '严重告警数';
COMMENT ON COLUMN monitor_statistics.warning_alerts IS '警告告警数';
COMMENT ON COLUMN monitor_statistics.info_alerts IS '信息告警数';
COMMENT ON COLUMN monitor_statistics.resolved_alerts IS '已解决告警数';
COMMENT ON COLUMN monitor_statistics.avg_response_time_ms IS '平均响应时间（毫秒）';
COMMENT ON COLUMN monitor_statistics.max_response_time_ms IS '最大响应时间（毫秒）';
COMMENT ON COLUMN monitor_statistics.min_response_time_ms IS '最小响应时间（毫秒）';
COMMENT ON COLUMN monitor_statistics.avg_cpu_usage IS '平均CPU使用率';
COMMENT ON COLUMN monitor_statistics.avg_memory_usage IS '平均内存使用率';
COMMENT ON COLUMN monitor_statistics.avg_disk_usage IS '平均磁盘使用率';
COMMENT ON COLUMN monitor_statistics.total_downtime_minutes IS '总停机时间（分钟）';
COMMENT ON COLUMN monitor_statistics.availability_percent IS '可用性（%）';
COMMENT ON COLUMN monitor_statistics.created_at IS '创建时间';
COMMENT ON COLUMN monitor_statistics.updated_at IS '更新时间';

-- ========================================
-- 索引创建
-- ========================================

-- 系统性能监控表索引
CREATE INDEX idx_system_performance_metric_id ON system_performance(metric_id);
CREATE INDEX idx_system_performance_node_id ON system_performance(node_id);
CREATE INDEX idx_system_performance_node_type ON system_performance(node_type);
CREATE INDEX idx_system_performance_metric_time ON system_performance(metric_time);
CREATE INDEX idx_system_performance_cpu_usage ON system_performance(cpu_usage_percent);
CREATE INDEX idx_system_performance_memory_usage ON system_performance(memory_usage_percent);
CREATE INDEX idx_system_performance_disk_usage ON system_performance(disk_usage_percent);
CREATE INDEX idx_system_performance_created_at ON system_performance(created_at);

-- 应用性能监控表索引
CREATE INDEX idx_application_performance_metric_id ON application_performance(metric_id);
CREATE INDEX idx_application_performance_app_id ON application_performance(application_id);
CREATE INDEX idx_application_performance_service ON application_performance(service_name);
CREATE INDEX idx_application_performance_instance ON application_performance(instance_id);
CREATE INDEX idx_application_performance_metric_time ON application_performance(metric_time);
CREATE INDEX idx_application_performance_health ON application_performance(health_status);
CREATE INDEX idx_application_performance_environment ON application_performance(environment);
CREATE INDEX idx_application_performance_response_time ON application_performance(response_time_ms);
CREATE INDEX idx_application_performance_error_rate ON application_performance(error_rate_percent);

-- 健康检查配置表索引
CREATE INDEX idx_health_check_config_check_id ON health_check_config(check_id);
CREATE INDEX idx_health_check_config_name ON health_check_config(check_name);
CREATE INDEX idx_health_check_config_type ON health_check_config(check_type);
CREATE INDEX idx_health_check_config_target ON health_check_config(target_type, target_id);
CREATE INDEX idx_health_check_config_enabled ON health_check_config(is_enabled);
CREATE INDEX idx_health_check_config_critical ON health_check_config(is_critical);

-- 健康检查结果表索引
CREATE INDEX idx_health_check_result_result_id ON health_check_result(result_id);
CREATE INDEX idx_health_check_result_check_id ON health_check_result(check_id);
CREATE INDEX idx_health_check_result_check_time ON health_check_result(check_time);
CREATE INDEX idx_health_check_result_status ON health_check_result(status);
CREATE INDEX idx_health_check_result_state_change ON health_check_result(is_state_change);
CREATE INDEX idx_health_check_result_response_time ON health_check_result(response_time_ms);

-- 监控告警规则表索引
CREATE INDEX idx_monitor_alert_rule_rule_id ON monitor_alert_rule(rule_id);
CREATE INDEX idx_monitor_alert_rule_name ON monitor_alert_rule(rule_name);
CREATE INDEX idx_monitor_alert_rule_type ON monitor_alert_rule(rule_type);
CREATE INDEX idx_monitor_alert_rule_metric_type ON monitor_alert_rule(metric_type);
CREATE INDEX idx_monitor_alert_rule_target_type ON monitor_alert_rule(target_type);
CREATE INDEX idx_monitor_alert_rule_severity ON monitor_alert_rule(severity_level);
CREATE INDEX idx_monitor_alert_rule_status ON monitor_alert_rule(rule_status);
CREATE INDEX idx_monitor_alert_rule_last_eval ON monitor_alert_rule(last_evaluation);

-- 监控告警记录表索引
CREATE INDEX idx_monitor_alert_record_alert_id ON monitor_alert_record(alert_id);
CREATE INDEX idx_monitor_alert_record_rule_id ON monitor_alert_record(rule_id);
CREATE INDEX idx_monitor_alert_record_alert_time ON monitor_alert_record(alert_time);
CREATE INDEX idx_monitor_alert_record_type ON monitor_alert_record(alert_type);
CREATE INDEX idx_monitor_alert_record_severity ON monitor_alert_record(severity_level);
CREATE INDEX idx_monitor_alert_record_status ON monitor_alert_record(alert_status);
CREATE INDEX idx_monitor_alert_record_fingerprint ON monitor_alert_record(fingerprint);
CREATE INDEX idx_monitor_alert_record_group_key ON monitor_alert_record(group_key);
CREATE INDEX idx_monitor_alert_record_acknowledged ON monitor_alert_record(acknowledged_at);
CREATE INDEX idx_monitor_alert_record_resolved ON monitor_alert_record(resolved_at);

-- 日志监控配置表索引
CREATE INDEX idx_log_monitor_config_config_id ON log_monitor_config(config_id);
CREATE INDEX idx_log_monitor_config_name ON log_monitor_config(config_name);
CREATE INDEX idx_log_monitor_config_source ON log_monitor_config(log_source);
CREATE INDEX idx_log_monitor_config_level ON log_monitor_config(log_level);
CREATE INDEX idx_log_monitor_config_enabled ON log_monitor_config(is_enabled);

-- 日志统计表索引
CREATE INDEX idx_log_statistics_stat_time ON log_statistics(stat_time);
CREATE INDEX idx_log_statistics_source ON log_statistics(log_source);
CREATE INDEX idx_log_statistics_level ON log_statistics(log_level);
CREATE INDEX idx_log_statistics_window ON log_statistics(time_window_minutes);
CREATE INDEX idx_log_statistics_error_count ON log_statistics(error_count);
CREATE INDEX idx_log_statistics_anomaly_score ON log_statistics(anomaly_score);

-- 监控统计表索引
CREATE INDEX idx_monitor_statistics_date ON monitor_statistics(stat_date);
CREATE INDEX idx_monitor_statistics_metric_type ON monitor_statistics(metric_type);
CREATE INDEX idx_monitor_statistics_target_type ON monitor_statistics(target_type);
CREATE INDEX idx_monitor_statistics_date_hour ON monitor_statistics(stat_date, stat_hour);

-- ========================================
-- 触发器创建
-- ========================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
CREATE TRIGGER update_health_check_config_updated_at BEFORE UPDATE ON health_check_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_monitor_alert_rule_updated_at BEFORE UPDATE ON monitor_alert_rule
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_monitor_alert_record_updated_at BEFORE UPDATE ON monitor_alert_record
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_log_monitor_config_updated_at BEFORE UPDATE ON log_monitor_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_monitor_statistics_updated_at BEFORE UPDATE ON monitor_statistics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 初始化数据
-- ========================================

-- 插入示例健康检查配置
INSERT INTO health_check_config (check_id, check_name, check_type, target_type, target_id, check_config, is_critical, created_by) VALUES
('HC_DATABASE_001', '数据库连接检查', 'DATABASE', 'DATABASE', 'main_db', 
'{"connection_string": "postgresql://localhost:5432/nta", "query": "SELECT 1"}', true, 'system'),
('HC_API_001', 'API健康检查', 'HTTP', 'API', 'auth_service', 
'{"url": "http://localhost:8080/health", "method": "GET", "expected_status": 200}', true, 'system'),
('HC_REDIS_001', 'Redis连接检查', 'TCP', 'CACHE', 'redis_cache', 
'{"host": "localhost", "port": 6379, "timeout": 5}', false, 'system');

-- 插入示例监控告警规则
INSERT INTO monitor_alert_rule (rule_id, rule_name, rule_type, metric_type, condition_expression, threshold_config, severity_level, created_by) VALUES
('ALERT_CPU_HIGH', 'CPU使用率过高', 'THRESHOLD', 'SYSTEM', 'cpu_usage_percent > threshold', 
'{"threshold": 80, "duration": 300}', 3, 'system'),
('ALERT_MEMORY_HIGH', '内存使用率过高', 'THRESHOLD', 'SYSTEM', 'memory_usage_percent > threshold', 
'{"threshold": 85, "duration": 300}', 3, 'system'),
('ALERT_DISK_FULL', '磁盘空间不足', 'THRESHOLD', 'SYSTEM', 'disk_usage_percent > threshold', 
'{"threshold": 90, "duration": 60}', 4, 'system'),
('ALERT_RESPONSE_SLOW', '响应时间过慢', 'THRESHOLD', 'APPLICATION', 'response_time_ms > threshold', 
'{"threshold": 5000, "duration": 180}', 2, 'system');

-- 插入示例日志监控配置
INSERT INTO log_monitor_config (config_id, config_name, log_source, log_level, filter_conditions, alert_conditions, created_by) VALUES
('LOG_ERROR_001', '错误日志监控', 'application.log', 'ERROR', 
'{"exclude_patterns": ["test", "debug"]}', 
'{"threshold": 10, "window_minutes": 5}', 'system'),
('LOG_AUTH_001', '认证失败监控', 'auth.log', 'WARN', 
'{"include_patterns": ["authentication failed", "login failed"]}', 
'{"threshold": 5, "window_minutes": 1}', 'system');

-- ========================================
-- CDC配置（如果需要）
-- ========================================

-- 为需要CDC的表设置复制标识
ALTER TABLE system_performance REPLICA IDENTITY FULL;
ALTER TABLE application_performance REPLICA IDENTITY FULL;
ALTER TABLE health_check_config REPLICA IDENTITY FULL;
ALTER TABLE health_check_result REPLICA IDENTITY FULL;
ALTER TABLE monitor_alert_rule REPLICA IDENTITY FULL;
ALTER TABLE monitor_alert_record REPLICA IDENTITY FULL;
ALTER TABLE log_monitor_config REPLICA IDENTITY FULL;
ALTER TABLE log_statistics REPLICA IDENTITY FULL;
ALTER TABLE monitor_statistics REPLICA IDENTITY FULL;
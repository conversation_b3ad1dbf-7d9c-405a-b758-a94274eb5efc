-- ========================================
-- Admin Service Database Schema
-- 系统管理服务数据库结构
-- ========================================

-- 创建数据库（如果需要）
-- CREATE DATABASE admin_service;
-- \c admin_service;

-- ========================================
-- 系统配置管理表
-- ========================================

-- 系统字典表
DROP TABLE IF EXISTS system_dictionary CASCADE;
CREATE TABLE system_dictionary (
    id SERIAL PRIMARY KEY,
    dict_type VARCHAR(100) NOT NULL,
    dict_key VARCHAR(100) NOT NULL,
    dict_value VARCHAR(500) NOT NULL,
    dict_label VARCHAR(200),
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    UNIQUE(dict_type, dict_key)
);

COMMENT ON TABLE system_dictionary IS '系统字典表';
COMMENT ON COLUMN system_dictionary.id IS '字典ID';
COMMENT ON COLUMN system_dictionary.dict_type IS '字典类型';
COMMENT ON COLUMN system_dictionary.dict_key IS '字典键';
COMMENT ON COLUMN system_dictionary.dict_value IS '字典值';
COMMENT ON COLUMN system_dictionary.dict_label IS '字典标签';
COMMENT ON COLUMN system_dictionary.description IS '描述';
COMMENT ON COLUMN system_dictionary.sort_order IS '排序序号';
COMMENT ON COLUMN system_dictionary.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN system_dictionary.created_at IS '创建时间';
COMMENT ON COLUMN system_dictionary.updated_at IS '更新时间';
COMMENT ON COLUMN system_dictionary.created_by IS '创建人';
COMMENT ON COLUMN system_dictionary.updated_by IS '更新人';

-- 系统配置表
DROP TABLE IF EXISTS system_config CASCADE;
CREATE TABLE system_config (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    config_name VARCHAR(200),
    config_type VARCHAR(50) DEFAULT 'STRING',
    description TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    is_readonly BOOLEAN DEFAULT FALSE,
    validation_rule VARCHAR(500),
    default_value TEXT,
    category VARCHAR(100),
    sort_order INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE system_config IS '系统配置表';
COMMENT ON COLUMN system_config.id IS '配置ID';
COMMENT ON COLUMN system_config.config_key IS '配置键';
COMMENT ON COLUMN system_config.config_value IS '配置值';
COMMENT ON COLUMN system_config.config_name IS '配置名称';
COMMENT ON COLUMN system_config.config_type IS '配置类型：STRING, INTEGER, BOOLEAN, JSON';
COMMENT ON COLUMN system_config.description IS '配置描述';
COMMENT ON COLUMN system_config.is_encrypted IS '是否加密存储';
COMMENT ON COLUMN system_config.is_readonly IS '是否只读';
COMMENT ON COLUMN system_config.validation_rule IS '验证规则';
COMMENT ON COLUMN system_config.default_value IS '默认值';
COMMENT ON COLUMN system_config.category IS '配置分类';
COMMENT ON COLUMN system_config.sort_order IS '排序序号';
COMMENT ON COLUMN system_config.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN system_config.created_at IS '创建时间';
COMMENT ON COLUMN system_config.updated_at IS '更新时间';
COMMENT ON COLUMN system_config.created_by IS '创建人';
COMMENT ON COLUMN system_config.updated_by IS '更新人';

-- ========================================
-- 设备管理表
-- ========================================

-- 网络设备表
DROP TABLE IF EXISTS network_device CASCADE;
CREATE TABLE network_device (
    id SERIAL PRIMARY KEY,
    device_name VARCHAR(200) NOT NULL,
    device_type VARCHAR(50),
    device_ip VARCHAR(45),
    device_mac VARCHAR(17),
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    firmware_version VARCHAR(50),
    location VARCHAR(200),
    department VARCHAR(100),
    responsible_person VARCHAR(100),
    contact_info VARCHAR(200),
    status INTEGER DEFAULT 1,
    last_online_time TIMESTAMP,
    monitoring_enabled BOOLEAN DEFAULT TRUE,
    snmp_community VARCHAR(100),
    snmp_version VARCHAR(10),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE network_device IS '网络设备表';
COMMENT ON COLUMN network_device.id IS '设备ID';
COMMENT ON COLUMN network_device.device_name IS '设备名称';
COMMENT ON COLUMN network_device.device_type IS '设备类型：ROUTER, SWITCH, FIREWALL, SERVER';
COMMENT ON COLUMN network_device.device_ip IS '设备IP地址';
COMMENT ON COLUMN network_device.device_mac IS '设备MAC地址';
COMMENT ON COLUMN network_device.manufacturer IS '制造商';
COMMENT ON COLUMN network_device.model IS '设备型号';
COMMENT ON COLUMN network_device.firmware_version IS '固件版本';
COMMENT ON COLUMN network_device.location IS '物理位置';
COMMENT ON COLUMN network_device.department IS '所属部门';
COMMENT ON COLUMN network_device.responsible_person IS '负责人';
COMMENT ON COLUMN network_device.contact_info IS '联系方式';
COMMENT ON COLUMN network_device.status IS '设备状态：0-离线，1-在线，2-故障';
COMMENT ON COLUMN network_device.last_online_time IS '最后在线时间';
COMMENT ON COLUMN network_device.monitoring_enabled IS '是否启用监控';
COMMENT ON COLUMN network_device.snmp_community IS 'SNMP团体名';
COMMENT ON COLUMN network_device.snmp_version IS 'SNMP版本';
COMMENT ON COLUMN network_device.description IS '设备描述';
COMMENT ON COLUMN network_device.created_at IS '创建时间';
COMMENT ON COLUMN network_device.updated_at IS '更新时间';
COMMENT ON COLUMN network_device.created_by IS '创建人';
COMMENT ON COLUMN network_device.updated_by IS '更新人';

-- 设备IP映射表
DROP TABLE IF EXISTS device_ip_mapping CASCADE;
CREATE TABLE device_ip_mapping (
    id SERIAL PRIMARY KEY,
    device_id INTEGER,
    ip_address VARCHAR(45) NOT NULL,
    ip_type VARCHAR(20) DEFAULT 'IPv4',
    is_primary BOOLEAN DEFAULT FALSE,
    subnet_mask VARCHAR(45),
    gateway VARCHAR(45),
    vlan_id INTEGER,
    interface_name VARCHAR(50),
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    FOREIGN KEY (device_id) REFERENCES network_device(id) ON DELETE CASCADE
);

COMMENT ON TABLE device_ip_mapping IS '设备IP映射表';
COMMENT ON COLUMN device_ip_mapping.id IS '映射ID';
COMMENT ON COLUMN device_ip_mapping.device_id IS '设备ID';
COMMENT ON COLUMN device_ip_mapping.ip_address IS 'IP地址';
COMMENT ON COLUMN device_ip_mapping.ip_type IS 'IP类型：IPv4, IPv6';
COMMENT ON COLUMN device_ip_mapping.is_primary IS '是否主IP';
COMMENT ON COLUMN device_ip_mapping.subnet_mask IS '子网掩码';
COMMENT ON COLUMN device_ip_mapping.gateway IS '网关地址';
COMMENT ON COLUMN device_ip_mapping.vlan_id IS 'VLAN ID';
COMMENT ON COLUMN device_ip_mapping.interface_name IS '接口名称';
COMMENT ON COLUMN device_ip_mapping.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN device_ip_mapping.created_at IS '创建时间';
COMMENT ON COLUMN device_ip_mapping.updated_at IS '更新时间';
COMMENT ON COLUMN device_ip_mapping.created_by IS '创建人';
COMMENT ON COLUMN device_ip_mapping.updated_by IS '更新人';

-- 内网网段表
DROP TABLE IF EXISTS internal_network_segment CASCADE;
CREATE TABLE internal_network_segment (
    id SERIAL PRIMARY KEY,
    segment_name VARCHAR(100) NOT NULL,
    network_address VARCHAR(45) NOT NULL,
    subnet_mask VARCHAR(45) NOT NULL,
    cidr_notation VARCHAR(20),
    gateway VARCHAR(45),
    dns_servers TEXT,
    vlan_id INTEGER,
    segment_type VARCHAR(50),
    department VARCHAR(100),
    description TEXT,
    is_monitored BOOLEAN DEFAULT TRUE,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE internal_network_segment IS '内网网段表';
COMMENT ON COLUMN internal_network_segment.id IS '网段ID';
COMMENT ON COLUMN internal_network_segment.segment_name IS '网段名称';
COMMENT ON COLUMN internal_network_segment.network_address IS '网络地址';
COMMENT ON COLUMN internal_network_segment.subnet_mask IS '子网掩码';
COMMENT ON COLUMN internal_network_segment.cidr_notation IS 'CIDR表示法';
COMMENT ON COLUMN internal_network_segment.gateway IS '网关地址';
COMMENT ON COLUMN internal_network_segment.dns_servers IS 'DNS服务器列表';
COMMENT ON COLUMN internal_network_segment.vlan_id IS 'VLAN ID';
COMMENT ON COLUMN internal_network_segment.segment_type IS '网段类型：OFFICE, SERVER, DMZ, GUEST';
COMMENT ON COLUMN internal_network_segment.department IS '所属部门';
COMMENT ON COLUMN internal_network_segment.description IS '网段描述';
COMMENT ON COLUMN internal_network_segment.is_monitored IS '是否监控';
COMMENT ON COLUMN internal_network_segment.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN internal_network_segment.created_at IS '创建时间';
COMMENT ON COLUMN internal_network_segment.updated_at IS '更新时间';
COMMENT ON COLUMN internal_network_segment.created_by IS '创建人';
COMMENT ON COLUMN internal_network_segment.updated_by IS '更新人';

-- ========================================
-- 插件管理表
-- ========================================

-- 日志插件表
DROP TABLE IF EXISTS log_plugin CASCADE;
CREATE TABLE log_plugin (
    id SERIAL PRIMARY KEY,
    plugin_name VARCHAR(100) NOT NULL,
    plugin_type VARCHAR(50) NOT NULL,
    plugin_version VARCHAR(20),
    plugin_path VARCHAR(500),
    config_template JSONB,
    default_config JSONB,
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE log_plugin IS '日志插件表';
COMMENT ON COLUMN log_plugin.id IS '插件ID';
COMMENT ON COLUMN log_plugin.plugin_name IS '插件名称';
COMMENT ON COLUMN log_plugin.plugin_type IS '插件类型：INPUT, FILTER, OUTPUT';
COMMENT ON COLUMN log_plugin.plugin_version IS '插件版本';
COMMENT ON COLUMN log_plugin.plugin_path IS '插件路径';
COMMENT ON COLUMN log_plugin.config_template IS '配置模板';
COMMENT ON COLUMN log_plugin.default_config IS '默认配置';
COMMENT ON COLUMN log_plugin.description IS '插件描述';
COMMENT ON COLUMN log_plugin.enabled IS '是否启用';
COMMENT ON COLUMN log_plugin.sort_order IS '排序序号';
COMMENT ON COLUMN log_plugin.created_at IS '创建时间';
COMMENT ON COLUMN log_plugin.updated_at IS '更新时间';
COMMENT ON COLUMN log_plugin.created_by IS '创建人';
COMMENT ON COLUMN log_plugin.updated_by IS '更新人';

-- 分析插件表
DROP TABLE IF EXISTS analysis_plugin CASCADE;
CREATE TABLE analysis_plugin (
    id SERIAL PRIMARY KEY,
    plugin_name VARCHAR(100) NOT NULL,
    plugin_type VARCHAR(50) NOT NULL,
    plugin_version VARCHAR(20),
    plugin_path VARCHAR(500),
    config_schema JSONB,
    default_config JSONB,
    supported_protocols TEXT,
    performance_metrics JSONB,
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE analysis_plugin IS '分析插件表';
COMMENT ON COLUMN analysis_plugin.id IS '插件ID';
COMMENT ON COLUMN analysis_plugin.plugin_name IS '插件名称';
COMMENT ON COLUMN analysis_plugin.plugin_type IS '插件类型：PROTOCOL, BEHAVIOR, THREAT';
COMMENT ON COLUMN analysis_plugin.plugin_version IS '插件版本';
COMMENT ON COLUMN analysis_plugin.plugin_path IS '插件路径';
COMMENT ON COLUMN analysis_plugin.config_schema IS '配置模式';
COMMENT ON COLUMN analysis_plugin.default_config IS '默认配置';
COMMENT ON COLUMN analysis_plugin.supported_protocols IS '支持的协议';
COMMENT ON COLUMN analysis_plugin.performance_metrics IS '性能指标';
COMMENT ON COLUMN analysis_plugin.description IS '插件描述';
COMMENT ON COLUMN analysis_plugin.enabled IS '是否启用';
COMMENT ON COLUMN analysis_plugin.sort_order IS '排序序号';
COMMENT ON COLUMN analysis_plugin.created_at IS '创建时间';
COMMENT ON COLUMN analysis_plugin.updated_at IS '更新时间';
COMMENT ON COLUMN analysis_plugin.created_by IS '创建人';
COMMENT ON COLUMN analysis_plugin.updated_by IS '更新人';

-- ========================================
-- Elasticsearch配置表
-- ========================================

-- Elasticsearch字段配置表
DROP TABLE IF EXISTS elasticsearch_fields CASCADE;
CREATE TABLE elasticsearch_fields (
    id SERIAL PRIMARY KEY,
    index_name VARCHAR(100) NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    field_type VARCHAR(50) NOT NULL,
    field_mapping JSONB,
    is_searchable BOOLEAN DEFAULT TRUE,
    is_aggregatable BOOLEAN DEFAULT TRUE,
    is_stored BOOLEAN DEFAULT TRUE,
    analyzer VARCHAR(50),
    format_pattern VARCHAR(100),
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    UNIQUE(index_name, field_name)
);

COMMENT ON TABLE elasticsearch_fields IS 'Elasticsearch字段配置表';
COMMENT ON COLUMN elasticsearch_fields.id IS '字段ID';
COMMENT ON COLUMN elasticsearch_fields.index_name IS '索引名称';
COMMENT ON COLUMN elasticsearch_fields.field_name IS '字段名称';
COMMENT ON COLUMN elasticsearch_fields.field_type IS '字段类型：text, keyword, long, double, date, boolean';
COMMENT ON COLUMN elasticsearch_fields.field_mapping IS '字段映射配置';
COMMENT ON COLUMN elasticsearch_fields.is_searchable IS '是否可搜索';
COMMENT ON COLUMN elasticsearch_fields.is_aggregatable IS '是否可聚合';
COMMENT ON COLUMN elasticsearch_fields.is_stored IS '是否存储';
COMMENT ON COLUMN elasticsearch_fields.analyzer IS '分析器';
COMMENT ON COLUMN elasticsearch_fields.format_pattern IS '格式模式';
COMMENT ON COLUMN elasticsearch_fields.description IS '字段描述';
COMMENT ON COLUMN elasticsearch_fields.sort_order IS '排序序号';
COMMENT ON COLUMN elasticsearch_fields.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN elasticsearch_fields.created_at IS '创建时间';
COMMENT ON COLUMN elasticsearch_fields.updated_at IS '更新时间';
COMMENT ON COLUMN elasticsearch_fields.created_by IS '创建人';
COMMENT ON COLUMN elasticsearch_fields.updated_by IS '更新人';

-- ========================================
-- 白名单管理表
-- ========================================

-- 证书白名单表
DROP TABLE IF EXISTS certificate_whitelist CASCADE;
CREATE TABLE certificate_whitelist (
    id SERIAL PRIMARY KEY,
    certificate_hash VARCHAR(128) NOT NULL,
    certificate_subject VARCHAR(500),
    certificate_issuer VARCHAR(500),
    certificate_serial VARCHAR(100),
    valid_from TIMESTAMP,
    valid_to TIMESTAMP,
    whitelist_reason TEXT,
    added_by VARCHAR(100),
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE certificate_whitelist IS '证书白名单表';
COMMENT ON COLUMN certificate_whitelist.id IS '白名单ID';
COMMENT ON COLUMN certificate_whitelist.certificate_hash IS '证书哈希值';
COMMENT ON COLUMN certificate_whitelist.certificate_subject IS '证书主题';
COMMENT ON COLUMN certificate_whitelist.certificate_issuer IS '证书颁发者';
COMMENT ON COLUMN certificate_whitelist.certificate_serial IS '证书序列号';
COMMENT ON COLUMN certificate_whitelist.valid_from IS '证书有效期开始';
COMMENT ON COLUMN certificate_whitelist.valid_to IS '证书有效期结束';
COMMENT ON COLUMN certificate_whitelist.whitelist_reason IS '加入白名单原因';
COMMENT ON COLUMN certificate_whitelist.added_by IS '添加人';
COMMENT ON COLUMN certificate_whitelist.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN certificate_whitelist.created_at IS '创建时间';
COMMENT ON COLUMN certificate_whitelist.updated_at IS '更新时间';
COMMENT ON COLUMN certificate_whitelist.created_by IS '创建人';
COMMENT ON COLUMN certificate_whitelist.updated_by IS '更新人';

-- 域名白名单表
DROP TABLE IF EXISTS domain_whitelist CASCADE;
CREATE TABLE domain_whitelist (
    id SERIAL PRIMARY KEY,
    domain_name VARCHAR(255) NOT NULL,
    domain_type VARCHAR(50) DEFAULT 'EXACT',
    whitelist_category VARCHAR(50),
    whitelist_reason TEXT,
    added_by VARCHAR(100),
    expire_time TIMESTAMP,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE domain_whitelist IS '域名白名单表';
COMMENT ON COLUMN domain_whitelist.id IS '白名单ID';
COMMENT ON COLUMN domain_whitelist.domain_name IS '域名';
COMMENT ON COLUMN domain_whitelist.domain_type IS '域名类型：EXACT-精确匹配，WILDCARD-通配符匹配';
COMMENT ON COLUMN domain_whitelist.whitelist_category IS '白名单分类';
COMMENT ON COLUMN domain_whitelist.whitelist_reason IS '加入白名单原因';
COMMENT ON COLUMN domain_whitelist.added_by IS '添加人';
COMMENT ON COLUMN domain_whitelist.expire_time IS '过期时间';
COMMENT ON COLUMN domain_whitelist.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN domain_whitelist.created_at IS '创建时间';
COMMENT ON COLUMN domain_whitelist.updated_at IS '更新时间';
COMMENT ON COLUMN domain_whitelist.created_by IS '创建人';
COMMENT ON COLUMN domain_whitelist.updated_by IS '更新人';

-- IP白名单表
DROP TABLE IF EXISTS ip_whitelist CASCADE;
CREATE TABLE ip_whitelist (
    id SERIAL PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    ip_type VARCHAR(20) DEFAULT 'IPv4',
    subnet_mask VARCHAR(45),
    whitelist_category VARCHAR(50),
    whitelist_reason TEXT,
    added_by VARCHAR(100),
    expire_time TIMESTAMP,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE ip_whitelist IS 'IP白名单表';
COMMENT ON COLUMN ip_whitelist.id IS '白名单ID';
COMMENT ON COLUMN ip_whitelist.ip_address IS 'IP地址';
COMMENT ON COLUMN ip_whitelist.ip_type IS 'IP类型：IPv4, IPv6';
COMMENT ON COLUMN ip_whitelist.subnet_mask IS '子网掩码';
COMMENT ON COLUMN ip_whitelist.whitelist_category IS '白名单分类';
COMMENT ON COLUMN ip_whitelist.whitelist_reason IS '加入白名单原因';
COMMENT ON COLUMN ip_whitelist.added_by IS '添加人';
COMMENT ON COLUMN ip_whitelist.expire_time IS '过期时间';
COMMENT ON COLUMN ip_whitelist.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN ip_whitelist.created_at IS '创建时间';
COMMENT ON COLUMN ip_whitelist.updated_at IS '更新时间';
COMMENT ON COLUMN ip_whitelist.created_by IS '创建人';
COMMENT ON COLUMN ip_whitelist.updated_by IS '更新人';

-- ========================================
-- 索引创建
-- ========================================

-- 系统字典表索引
CREATE INDEX idx_system_dictionary_type ON system_dictionary(dict_type);
CREATE INDEX idx_system_dictionary_key ON system_dictionary(dict_key);
CREATE INDEX idx_system_dictionary_status ON system_dictionary(status);

-- 系统配置表索引
CREATE INDEX idx_system_config_key ON system_config(config_key);
CREATE INDEX idx_system_config_category ON system_config(category);
CREATE INDEX idx_system_config_status ON system_config(status);

-- 网络设备表索引
CREATE INDEX idx_network_device_name ON network_device(device_name);
CREATE INDEX idx_network_device_type ON network_device(device_type);
CREATE INDEX idx_network_device_ip ON network_device(device_ip);
CREATE INDEX idx_network_device_status ON network_device(status);
CREATE INDEX idx_network_device_created_at ON network_device(created_at);

-- 设备IP映射表索引
CREATE INDEX idx_device_ip_mapping_device_id ON device_ip_mapping(device_id);
CREATE INDEX idx_device_ip_mapping_ip ON device_ip_mapping(ip_address);
CREATE INDEX idx_device_ip_mapping_status ON device_ip_mapping(status);

-- 内网网段表索引
CREATE INDEX idx_internal_network_segment_name ON internal_network_segment(segment_name);
CREATE INDEX idx_internal_network_segment_type ON internal_network_segment(segment_type);
CREATE INDEX idx_internal_network_segment_status ON internal_network_segment(status);

-- 日志插件表索引
CREATE INDEX idx_log_plugin_name ON log_plugin(plugin_name);
CREATE INDEX idx_log_plugin_type ON log_plugin(plugin_type);
CREATE INDEX idx_log_plugin_enabled ON log_plugin(enabled);

-- 分析插件表索引
CREATE INDEX idx_analysis_plugin_name ON analysis_plugin(plugin_name);
CREATE INDEX idx_analysis_plugin_type ON analysis_plugin(plugin_type);
CREATE INDEX idx_analysis_plugin_enabled ON analysis_plugin(enabled);

-- Elasticsearch字段配置表索引
CREATE INDEX idx_elasticsearch_fields_index ON elasticsearch_fields(index_name);
CREATE INDEX idx_elasticsearch_fields_name ON elasticsearch_fields(field_name);
CREATE INDEX idx_elasticsearch_fields_type ON elasticsearch_fields(field_type);
CREATE INDEX idx_elasticsearch_fields_status ON elasticsearch_fields(status);

-- 白名单表索引
CREATE INDEX idx_certificate_whitelist_hash ON certificate_whitelist(certificate_hash);
CREATE INDEX idx_certificate_whitelist_status ON certificate_whitelist(status);
CREATE INDEX idx_domain_whitelist_domain ON domain_whitelist(domain_name);
CREATE INDEX idx_domain_whitelist_status ON domain_whitelist(status);
CREATE INDEX idx_ip_whitelist_ip ON ip_whitelist(ip_address);
CREATE INDEX idx_ip_whitelist_status ON ip_whitelist(status);

-- ========================================
-- 触发器创建
-- ========================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
CREATE TRIGGER update_system_dictionary_updated_at BEFORE UPDATE ON system_dictionary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_config_updated_at BEFORE UPDATE ON system_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_network_device_updated_at BEFORE UPDATE ON network_device
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_device_ip_mapping_updated_at BEFORE UPDATE ON device_ip_mapping
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_internal_network_segment_updated_at BEFORE UPDATE ON internal_network_segment
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_log_plugin_updated_at BEFORE UPDATE ON log_plugin
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_analysis_plugin_updated_at BEFORE UPDATE ON analysis_plugin
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_elasticsearch_fields_updated_at BEFORE UPDATE ON elasticsearch_fields
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_certificate_whitelist_updated_at BEFORE UPDATE ON certificate_whitelist
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_domain_whitelist_updated_at BEFORE UPDATE ON domain_whitelist
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ip_whitelist_updated_at BEFORE UPDATE ON ip_whitelist
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 初始化数据
-- ========================================

-- 插入系统字典初始数据
INSERT INTO system_dictionary (dict_type, dict_key, dict_value, dict_label, description, created_by) VALUES
('device_type', 'ROUTER', 'ROUTER', '路由器', '网络路由设备', 'system'),
('device_type', 'SWITCH', 'SWITCH', '交换机', '网络交换设备', 'system'),
('device_type', 'FIREWALL', 'FIREWALL', '防火墙', '网络安全设备', 'system'),
('device_type', 'SERVER', 'SERVER', '服务器', '计算服务设备', 'system'),
('segment_type', 'OFFICE', 'OFFICE', '办公网段', '办公区域网络', 'system'),
('segment_type', 'SERVER', 'SERVER', '服务器网段', '服务器区域网络', 'system'),
('segment_type', 'DMZ', 'DMZ', 'DMZ网段', '非军事化区域网络', 'system'),
('segment_type', 'GUEST', 'GUEST', '访客网段', '访客区域网络', 'system');

-- 插入系统配置初始数据
INSERT INTO system_config (config_key, config_value, config_name, config_type, description, category, created_by) VALUES
('system.name', 'NTA 3.0', '系统名称', 'STRING', '网络流量分析系统名称', 'SYSTEM', 'system'),
('system.version', '3.0.0', '系统版本', 'STRING', '当前系统版本号', 'SYSTEM', 'system'),
('elasticsearch.host', 'localhost', 'Elasticsearch主机', 'STRING', 'Elasticsearch服务器地址', 'ELASTICSEARCH', 'system'),
('elasticsearch.port', '9200', 'Elasticsearch端口', 'INTEGER', 'Elasticsearch服务端口', 'ELASTICSEARCH', 'system'),
('session.timeout', '3600', '会话超时时间', 'INTEGER', '用户会话超时时间（秒）', 'SECURITY', 'system'),
('password.min_length', '8', '密码最小长度', 'INTEGER', '用户密码最小长度要求', 'SECURITY', 'system'),
('login.max_attempts', '5', '最大登录尝试次数', 'INTEGER', '账户锁定前的最大登录失败次数', 'SECURITY', 'system');

-- ========================================
-- CDC配置（如果需要）
-- ========================================

-- 为需要CDC的表设置复制标识
ALTER TABLE system_dictionary REPLICA IDENTITY FULL;
ALTER TABLE system_config REPLICA IDENTITY FULL;
ALTER TABLE network_device REPLICA IDENTITY FULL;
ALTER TABLE device_ip_mapping REPLICA IDENTITY FULL;
ALTER TABLE internal_network_segment REPLICA IDENTITY FULL;
ALTER TABLE log_plugin REPLICA IDENTITY FULL;
ALTER TABLE analysis_plugin REPLICA IDENTITY FULL;
ALTER TABLE elasticsearch_fields REPLICA IDENTITY FULL;
ALTER TABLE certificate_whitelist REPLICA IDENTITY FULL;
ALTER TABLE domain_whitelist REPLICA IDENTITY FULL;
ALTER TABLE ip_whitelist REPLICA IDENTITY FULL;
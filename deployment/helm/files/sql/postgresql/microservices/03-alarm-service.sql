-- ========================================
-- Alarm Service Database Schema
-- 告警服务数据库结构
-- ========================================

-- 创建数据库（如果需要）
-- CREATE DATABASE alarm_service;
-- \c alarm_service;

-- ========================================
-- 告警规则管理表
-- ========================================

-- 告警规则表
DROP TABLE IF EXISTS alarm_rule CASCADE;
CREATE TABLE alarm_rule (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(200) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    rule_category VARCHAR(50),
    rule_description TEXT,
    rule_condition JSONB NOT NULL,
    severity_level INTEGER DEFAULT 3,
    threshold_config JSONB,
    time_window INTEGER DEFAULT 300,
    suppression_time INTEGER DEFAULT 0,
    notification_config JSONB,
    auto_resolve BOOLEAN DEFAULT FALSE,
    resolve_condition JSONB,
    tags JSONB,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE alarm_rule IS '告警规则表';
COMMENT ON COLUMN alarm_rule.id IS '规则ID';
COMMENT ON COLUMN alarm_rule.rule_name IS '规则名称';
COMMENT ON COLUMN alarm_rule.rule_type IS '规则类型：THRESHOLD, ANOMALY, PATTERN, CORRELATION';
COMMENT ON COLUMN alarm_rule.rule_category IS '规则分类：SECURITY, PERFORMANCE, AVAILABILITY';
COMMENT ON COLUMN alarm_rule.rule_description IS '规则描述';
COMMENT ON COLUMN alarm_rule.rule_condition IS '规则条件配置';
COMMENT ON COLUMN alarm_rule.severity_level IS '严重级别：1-严重，2-高，3-中，4-低，5-信息';
COMMENT ON COLUMN alarm_rule.threshold_config IS '阈值配置';
COMMENT ON COLUMN alarm_rule.time_window IS '时间窗口（秒）';
COMMENT ON COLUMN alarm_rule.suppression_time IS '抑制时间（秒）';
COMMENT ON COLUMN alarm_rule.notification_config IS '通知配置';
COMMENT ON COLUMN alarm_rule.auto_resolve IS '是否自动解决';
COMMENT ON COLUMN alarm_rule.resolve_condition IS '解决条件';
COMMENT ON COLUMN alarm_rule.tags IS '标签';
COMMENT ON COLUMN alarm_rule.enabled IS '是否启用';
COMMENT ON COLUMN alarm_rule.created_at IS '创建时间';
COMMENT ON COLUMN alarm_rule.updated_at IS '更新时间';
COMMENT ON COLUMN alarm_rule.created_by IS '创建人';
COMMENT ON COLUMN alarm_rule.updated_by IS '更新人';

-- 告警规则组表
DROP TABLE IF EXISTS alarm_rule_group CASCADE;
CREATE TABLE alarm_rule_group (
    id SERIAL PRIMARY KEY,
    group_name VARCHAR(100) NOT NULL,
    group_description TEXT,
    group_type VARCHAR(50) DEFAULT 'CUSTOM',
    evaluation_interval INTEGER DEFAULT 60,
    notification_config JSONB,
    enabled BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE alarm_rule_group IS '告警规则组表';
COMMENT ON COLUMN alarm_rule_group.id IS '规则组ID';
COMMENT ON COLUMN alarm_rule_group.group_name IS '规则组名称';
COMMENT ON COLUMN alarm_rule_group.group_description IS '规则组描述';
COMMENT ON COLUMN alarm_rule_group.group_type IS '规则组类型：SYSTEM, CUSTOM';
COMMENT ON COLUMN alarm_rule_group.evaluation_interval IS '评估间隔（秒）';
COMMENT ON COLUMN alarm_rule_group.notification_config IS '通知配置';
COMMENT ON COLUMN alarm_rule_group.enabled IS '是否启用';
COMMENT ON COLUMN alarm_rule_group.sort_order IS '排序序号';
COMMENT ON COLUMN alarm_rule_group.created_at IS '创建时间';
COMMENT ON COLUMN alarm_rule_group.updated_at IS '更新时间';
COMMENT ON COLUMN alarm_rule_group.created_by IS '创建人';
COMMENT ON COLUMN alarm_rule_group.updated_by IS '更新人';

-- 告警规则与规则组关联表
DROP TABLE IF EXISTS alarm_rule_group_mapping CASCADE;
CREATE TABLE alarm_rule_group_mapping (
    id SERIAL PRIMARY KEY,
    rule_id INTEGER NOT NULL,
    group_id INTEGER NOT NULL,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    FOREIGN KEY (rule_id) REFERENCES alarm_rule(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES alarm_rule_group(id) ON DELETE CASCADE,
    UNIQUE(rule_id, group_id)
);

COMMENT ON TABLE alarm_rule_group_mapping IS '告警规则与规则组关联表';
COMMENT ON COLUMN alarm_rule_group_mapping.id IS '关联ID';
COMMENT ON COLUMN alarm_rule_group_mapping.rule_id IS '规则ID';
COMMENT ON COLUMN alarm_rule_group_mapping.group_id IS '规则组ID';
COMMENT ON COLUMN alarm_rule_group_mapping.sort_order IS '排序序号';
COMMENT ON COLUMN alarm_rule_group_mapping.created_at IS '创建时间';
COMMENT ON COLUMN alarm_rule_group_mapping.created_by IS '创建人';

-- ========================================
-- 告警记录管理表
-- ========================================

-- 告警记录表
DROP TABLE IF EXISTS alarm_record CASCADE;
CREATE TABLE alarm_record (
    id SERIAL PRIMARY KEY,
    alarm_id VARCHAR(100) UNIQUE NOT NULL,
    rule_id INTEGER,
    rule_name VARCHAR(200),
    alarm_title VARCHAR(500) NOT NULL,
    alarm_content TEXT,
    severity_level INTEGER DEFAULT 3,
    alarm_status VARCHAR(20) DEFAULT 'OPEN',
    source_system VARCHAR(100),
    source_data JSONB,
    trigger_time TIMESTAMP NOT NULL,
    first_occurrence TIMESTAMP,
    last_occurrence TIMESTAMP,
    occurrence_count INTEGER DEFAULT 1,
    acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_by VARCHAR(100),
    acknowledged_at TIMESTAMP,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by VARCHAR(100),
    resolved_at TIMESTAMP,
    resolution_note TEXT,
    tags JSONB,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (rule_id) REFERENCES alarm_rule(id) ON DELETE SET NULL
);

COMMENT ON TABLE alarm_record IS '告警记录表';
COMMENT ON COLUMN alarm_record.id IS '记录ID';
COMMENT ON COLUMN alarm_record.alarm_id IS '告警唯一标识';
COMMENT ON COLUMN alarm_record.rule_id IS '触发规则ID';
COMMENT ON COLUMN alarm_record.rule_name IS '触发规则名称';
COMMENT ON COLUMN alarm_record.alarm_title IS '告警标题';
COMMENT ON COLUMN alarm_record.alarm_content IS '告警内容';
COMMENT ON COLUMN alarm_record.severity_level IS '严重级别：1-严重，2-高，3-中，4-低，5-信息';
COMMENT ON COLUMN alarm_record.alarm_status IS '告警状态：OPEN, ACKNOWLEDGED, RESOLVED, CLOSED';
COMMENT ON COLUMN alarm_record.source_system IS '来源系统';
COMMENT ON COLUMN alarm_record.source_data IS '来源数据';
COMMENT ON COLUMN alarm_record.trigger_time IS '触发时间';
COMMENT ON COLUMN alarm_record.first_occurrence IS '首次发生时间';
COMMENT ON COLUMN alarm_record.last_occurrence IS '最后发生时间';
COMMENT ON COLUMN alarm_record.occurrence_count IS '发生次数';
COMMENT ON COLUMN alarm_record.acknowledged IS '是否已确认';
COMMENT ON COLUMN alarm_record.acknowledged_by IS '确认人';
COMMENT ON COLUMN alarm_record.acknowledged_at IS '确认时间';
COMMENT ON COLUMN alarm_record.resolved IS '是否已解决';
COMMENT ON COLUMN alarm_record.resolved_by IS '解决人';
COMMENT ON COLUMN alarm_record.resolved_at IS '解决时间';
COMMENT ON COLUMN alarm_record.resolution_note IS '解决备注';
COMMENT ON COLUMN alarm_record.tags IS '标签';
COMMENT ON COLUMN alarm_record.metadata IS '元数据';
COMMENT ON COLUMN alarm_record.created_at IS '创建时间';
COMMENT ON COLUMN alarm_record.updated_at IS '更新时间';

-- 告警处理历史表
DROP TABLE IF EXISTS alarm_action_history CASCADE;
CREATE TABLE alarm_action_history (
    id SERIAL PRIMARY KEY,
    alarm_id VARCHAR(100) NOT NULL,
    action_type VARCHAR(50) NOT NULL,
    action_description TEXT,
    action_data JSONB,
    action_result VARCHAR(20),
    action_by VARCHAR(100),
    action_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT
);

COMMENT ON TABLE alarm_action_history IS '告警处理历史表';
COMMENT ON COLUMN alarm_action_history.id IS '历史ID';
COMMENT ON COLUMN alarm_action_history.alarm_id IS '告警ID';
COMMENT ON COLUMN alarm_action_history.action_type IS '操作类型：CREATE, ACKNOWLEDGE, RESOLVE, ESCALATE, COMMENT';
COMMENT ON COLUMN alarm_action_history.action_description IS '操作描述';
COMMENT ON COLUMN alarm_action_history.action_data IS '操作数据';
COMMENT ON COLUMN alarm_action_history.action_result IS '操作结果：SUCCESS, FAILED';
COMMENT ON COLUMN alarm_action_history.action_by IS '操作人';
COMMENT ON COLUMN alarm_action_history.action_at IS '操作时间';
COMMENT ON COLUMN alarm_action_history.notes IS '备注';

-- ========================================
-- 告警知识库表
-- ========================================

-- 告警知识库表
DROP TABLE IF EXISTS alarm_knowledge_base CASCADE;
CREATE TABLE alarm_knowledge_base (
    id SERIAL PRIMARY KEY,
    kb_title VARCHAR(500) NOT NULL,
    kb_category VARCHAR(100),
    kb_tags JSONB,
    problem_description TEXT,
    solution_steps TEXT,
    related_rules JSONB,
    reference_links JSONB,
    severity_mapping JSONB,
    auto_resolution BOOLEAN DEFAULT FALSE,
    resolution_script TEXT,
    effectiveness_score DECIMAL(3,2) DEFAULT 0.0,
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE alarm_knowledge_base IS '告警知识库表';
COMMENT ON COLUMN alarm_knowledge_base.id IS '知识库ID';
COMMENT ON COLUMN alarm_knowledge_base.kb_title IS '知识库标题';
COMMENT ON COLUMN alarm_knowledge_base.kb_category IS '知识库分类';
COMMENT ON COLUMN alarm_knowledge_base.kb_tags IS '标签';
COMMENT ON COLUMN alarm_knowledge_base.problem_description IS '问题描述';
COMMENT ON COLUMN alarm_knowledge_base.solution_steps IS '解决步骤';
COMMENT ON COLUMN alarm_knowledge_base.related_rules IS '相关规则';
COMMENT ON COLUMN alarm_knowledge_base.reference_links IS '参考链接';
COMMENT ON COLUMN alarm_knowledge_base.severity_mapping IS '严重级别映射';
COMMENT ON COLUMN alarm_knowledge_base.auto_resolution IS '是否支持自动解决';
COMMENT ON COLUMN alarm_knowledge_base.resolution_script IS '解决脚本';
COMMENT ON COLUMN alarm_knowledge_base.effectiveness_score IS '有效性评分';
COMMENT ON COLUMN alarm_knowledge_base.usage_count IS '使用次数';
COMMENT ON COLUMN alarm_knowledge_base.last_used_at IS '最后使用时间';
COMMENT ON COLUMN alarm_knowledge_base.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN alarm_knowledge_base.created_at IS '创建时间';
COMMENT ON COLUMN alarm_knowledge_base.updated_at IS '更新时间';
COMMENT ON COLUMN alarm_knowledge_base.created_by IS '创建人';
COMMENT ON COLUMN alarm_knowledge_base.updated_by IS '更新人';

-- ========================================
-- 通知管理表
-- ========================================

-- 通知渠道表
DROP TABLE IF EXISTS notification_channel CASCADE;
CREATE TABLE notification_channel (
    id SERIAL PRIMARY KEY,
    channel_name VARCHAR(100) NOT NULL,
    channel_type VARCHAR(50) NOT NULL,
    channel_config JSONB NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    test_config JSONB,
    last_test_time TIMESTAMP,
    last_test_result VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE notification_channel IS '通知渠道表';
COMMENT ON COLUMN notification_channel.id IS '渠道ID';
COMMENT ON COLUMN notification_channel.channel_name IS '渠道名称';
COMMENT ON COLUMN notification_channel.channel_type IS '渠道类型：EMAIL, SMS, WEBHOOK, DINGTALK, WECHAT';
COMMENT ON COLUMN notification_channel.channel_config IS '渠道配置';
COMMENT ON COLUMN notification_channel.description IS '渠道描述';
COMMENT ON COLUMN notification_channel.enabled IS '是否启用';
COMMENT ON COLUMN notification_channel.test_config IS '测试配置';
COMMENT ON COLUMN notification_channel.last_test_time IS '最后测试时间';
COMMENT ON COLUMN notification_channel.last_test_result IS '最后测试结果：SUCCESS, FAILED';
COMMENT ON COLUMN notification_channel.created_at IS '创建时间';
COMMENT ON COLUMN notification_channel.updated_at IS '更新时间';
COMMENT ON COLUMN notification_channel.created_by IS '创建人';
COMMENT ON COLUMN notification_channel.updated_by IS '更新人';

-- 通知模板表
DROP TABLE IF EXISTS notification_template CASCADE;
CREATE TABLE notification_template (
    id SERIAL PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL,
    template_type VARCHAR(50) NOT NULL,
    channel_type VARCHAR(50) NOT NULL,
    subject_template TEXT,
    content_template TEXT NOT NULL,
    variables JSONB,
    format_type VARCHAR(20) DEFAULT 'TEXT',
    description TEXT,
    is_default BOOLEAN DEFAULT FALSE,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE notification_template IS '通知模板表';
COMMENT ON COLUMN notification_template.id IS '模板ID';
COMMENT ON COLUMN notification_template.template_name IS '模板名称';
COMMENT ON COLUMN notification_template.template_type IS '模板类型：ALARM_CREATE, ALARM_RESOLVE, ALARM_ESCALATE';
COMMENT ON COLUMN notification_template.channel_type IS '适用渠道类型';
COMMENT ON COLUMN notification_template.subject_template IS '主题模板';
COMMENT ON COLUMN notification_template.content_template IS '内容模板';
COMMENT ON COLUMN notification_template.variables IS '模板变量';
COMMENT ON COLUMN notification_template.format_type IS '格式类型：TEXT, HTML, MARKDOWN';
COMMENT ON COLUMN notification_template.description IS '模板描述';
COMMENT ON COLUMN notification_template.is_default IS '是否默认模板';
COMMENT ON COLUMN notification_template.enabled IS '是否启用';
COMMENT ON COLUMN notification_template.created_at IS '创建时间';
COMMENT ON COLUMN notification_template.updated_at IS '更新时间';
COMMENT ON COLUMN notification_template.created_by IS '创建人';
COMMENT ON COLUMN notification_template.updated_by IS '更新人';

-- 通知记录表
DROP TABLE IF EXISTS notification_record CASCADE;
CREATE TABLE notification_record (
    id SERIAL PRIMARY KEY,
    alarm_id VARCHAR(100),
    channel_id INTEGER,
    template_id INTEGER,
    recipient VARCHAR(500) NOT NULL,
    subject VARCHAR(500),
    content TEXT,
    send_status VARCHAR(20) DEFAULT 'PENDING',
    send_time TIMESTAMP,
    retry_count INTEGER DEFAULT 0,
    max_retry INTEGER DEFAULT 3,
    error_message TEXT,
    response_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (channel_id) REFERENCES notification_channel(id) ON DELETE SET NULL,
    FOREIGN KEY (template_id) REFERENCES notification_template(id) ON DELETE SET NULL
);

COMMENT ON TABLE notification_record IS '通知记录表';
COMMENT ON COLUMN notification_record.id IS '记录ID';
COMMENT ON COLUMN notification_record.alarm_id IS '告警ID';
COMMENT ON COLUMN notification_record.channel_id IS '通知渠道ID';
COMMENT ON COLUMN notification_record.template_id IS '通知模板ID';
COMMENT ON COLUMN notification_record.recipient IS '接收人';
COMMENT ON COLUMN notification_record.subject IS '通知主题';
COMMENT ON COLUMN notification_record.content IS '通知内容';
COMMENT ON COLUMN notification_record.send_status IS '发送状态：PENDING, SENDING, SUCCESS, FAILED';
COMMENT ON COLUMN notification_record.send_time IS '发送时间';
COMMENT ON COLUMN notification_record.retry_count IS '重试次数';
COMMENT ON COLUMN notification_record.max_retry IS '最大重试次数';
COMMENT ON COLUMN notification_record.error_message IS '错误信息';
COMMENT ON COLUMN notification_record.response_data IS '响应数据';
COMMENT ON COLUMN notification_record.created_at IS '创建时间';

-- ========================================
-- 告警统计表
-- ========================================

-- 告警统计表
DROP TABLE IF EXISTS alarm_statistics CASCADE;
CREATE TABLE alarm_statistics (
    id SERIAL PRIMARY KEY,
    stat_date DATE NOT NULL,
    stat_hour INTEGER,
    rule_id INTEGER,
    rule_name VARCHAR(200),
    severity_level INTEGER,
    alarm_count INTEGER DEFAULT 0,
    resolved_count INTEGER DEFAULT 0,
    avg_resolution_time INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(stat_date, stat_hour, rule_id, severity_level)
);

COMMENT ON TABLE alarm_statistics IS '告警统计表';
COMMENT ON COLUMN alarm_statistics.id IS '统计ID';
COMMENT ON COLUMN alarm_statistics.stat_date IS '统计日期';
COMMENT ON COLUMN alarm_statistics.stat_hour IS '统计小时（0-23）';
COMMENT ON COLUMN alarm_statistics.rule_id IS '规则ID';
COMMENT ON COLUMN alarm_statistics.rule_name IS '规则名称';
COMMENT ON COLUMN alarm_statistics.severity_level IS '严重级别';
COMMENT ON COLUMN alarm_statistics.alarm_count IS '告警数量';
COMMENT ON COLUMN alarm_statistics.resolved_count IS '解决数量';
COMMENT ON COLUMN alarm_statistics.avg_resolution_time IS '平均解决时间（秒）';
COMMENT ON COLUMN alarm_statistics.created_at IS '创建时间';
COMMENT ON COLUMN alarm_statistics.updated_at IS '更新时间';

-- ========================================
-- 索引创建
-- ========================================

-- 告警规则表索引
CREATE INDEX idx_alarm_rule_name ON alarm_rule(rule_name);
CREATE INDEX idx_alarm_rule_type ON alarm_rule(rule_type);
CREATE INDEX idx_alarm_rule_category ON alarm_rule(rule_category);
CREATE INDEX idx_alarm_rule_enabled ON alarm_rule(enabled);
CREATE INDEX idx_alarm_rule_severity ON alarm_rule(severity_level);
CREATE INDEX idx_alarm_rule_created_at ON alarm_rule(created_at);

-- 告警规则组表索引
CREATE INDEX idx_alarm_rule_group_name ON alarm_rule_group(group_name);
CREATE INDEX idx_alarm_rule_group_type ON alarm_rule_group(group_type);
CREATE INDEX idx_alarm_rule_group_enabled ON alarm_rule_group(enabled);

-- 告警规则组映射表索引
CREATE INDEX idx_alarm_rule_group_mapping_rule_id ON alarm_rule_group_mapping(rule_id);
CREATE INDEX idx_alarm_rule_group_mapping_group_id ON alarm_rule_group_mapping(group_id);

-- 告警记录表索引
CREATE INDEX idx_alarm_record_alarm_id ON alarm_record(alarm_id);
CREATE INDEX idx_alarm_record_rule_id ON alarm_record(rule_id);
CREATE INDEX idx_alarm_record_status ON alarm_record(alarm_status);
CREATE INDEX idx_alarm_record_severity ON alarm_record(severity_level);
CREATE INDEX idx_alarm_record_trigger_time ON alarm_record(trigger_time);
CREATE INDEX idx_alarm_record_acknowledged ON alarm_record(acknowledged);
CREATE INDEX idx_alarm_record_resolved ON alarm_record(resolved);
CREATE INDEX idx_alarm_record_created_at ON alarm_record(created_at);

-- 告警处理历史表索引
CREATE INDEX idx_alarm_action_history_alarm_id ON alarm_action_history(alarm_id);
CREATE INDEX idx_alarm_action_history_type ON alarm_action_history(action_type);
CREATE INDEX idx_alarm_action_history_action_at ON alarm_action_history(action_at);

-- 告警知识库表索引
CREATE INDEX idx_alarm_knowledge_base_title ON alarm_knowledge_base(kb_title);
CREATE INDEX idx_alarm_knowledge_base_category ON alarm_knowledge_base(kb_category);
CREATE INDEX idx_alarm_knowledge_base_status ON alarm_knowledge_base(status);
CREATE INDEX idx_alarm_knowledge_base_usage_count ON alarm_knowledge_base(usage_count);

-- 通知渠道表索引
CREATE INDEX idx_notification_channel_name ON notification_channel(channel_name);
CREATE INDEX idx_notification_channel_type ON notification_channel(channel_type);
CREATE INDEX idx_notification_channel_enabled ON notification_channel(enabled);

-- 通知模板表索引
CREATE INDEX idx_notification_template_name ON notification_template(template_name);
CREATE INDEX idx_notification_template_type ON notification_template(template_type);
CREATE INDEX idx_notification_template_channel_type ON notification_template(channel_type);
CREATE INDEX idx_notification_template_enabled ON notification_template(enabled);

-- 通知记录表索引
CREATE INDEX idx_notification_record_alarm_id ON notification_record(alarm_id);
CREATE INDEX idx_notification_record_channel_id ON notification_record(channel_id);
CREATE INDEX idx_notification_record_status ON notification_record(send_status);
CREATE INDEX idx_notification_record_send_time ON notification_record(send_time);
CREATE INDEX idx_notification_record_created_at ON notification_record(created_at);

-- 告警统计表索引
CREATE INDEX idx_alarm_statistics_date ON alarm_statistics(stat_date);
CREATE INDEX idx_alarm_statistics_rule_id ON alarm_statistics(rule_id);
CREATE INDEX idx_alarm_statistics_severity ON alarm_statistics(severity_level);
CREATE INDEX idx_alarm_statistics_date_hour ON alarm_statistics(stat_date, stat_hour);

-- ========================================
-- 触发器创建
-- ========================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
CREATE TRIGGER update_alarm_rule_updated_at BEFORE UPDATE ON alarm_rule
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_rule_group_updated_at BEFORE UPDATE ON alarm_rule_group
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_record_updated_at BEFORE UPDATE ON alarm_record
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_knowledge_base_updated_at BEFORE UPDATE ON alarm_knowledge_base
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_channel_updated_at BEFORE UPDATE ON notification_channel
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_template_updated_at BEFORE UPDATE ON notification_template
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_statistics_updated_at BEFORE UPDATE ON alarm_statistics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 初始化数据
-- ========================================

-- 插入默认告警规则组
INSERT INTO alarm_rule_group (group_name, group_description, group_type, created_by) VALUES
('系统默认规则组', '系统内置的默认告警规则组', 'SYSTEM', 'system'),
('安全告警规则组', '安全相关的告警规则', 'SYSTEM', 'system'),
('性能告警规则组', '性能相关的告警规则', 'SYSTEM', 'system'),
('可用性告警规则组', '可用性相关的告警规则', 'SYSTEM', 'system');

-- 插入默认通知渠道
INSERT INTO notification_channel (channel_name, channel_type, channel_config, description, created_by) VALUES
('默认邮件通知', 'EMAIL', '{"smtp_host": "localhost", "smtp_port": 25, "from_email": "<EMAIL>"}', '默认邮件通知渠道', 'system'),
('系统管理员邮件', 'EMAIL', '{"smtp_host": "localhost", "smtp_port": 25, "from_email": "<EMAIL>"}', '系统管理员邮件通知', 'system');

-- 插入默认通知模板
INSERT INTO notification_template (template_name, template_type, channel_type, subject_template, content_template, description, is_default, created_by) VALUES
('告警创建邮件模板', 'ALARM_CREATE', 'EMAIL', '[NTA告警] {{alarm_title}}', 
'告警详情：\n标题：{{alarm_title}}\n级别：{{severity_level}}\n时间：{{trigger_time}}\n内容：{{alarm_content}}\n\n请及时处理。', 
'告警创建时的邮件通知模板', TRUE, 'system'),
('告警解决邮件模板', 'ALARM_RESOLVE', 'EMAIL', '[NTA告警已解决] {{alarm_title}}', 
'告警已解决：\n标题：{{alarm_title}}\n解决时间：{{resolved_at}}\n解决人：{{resolved_by}}\n解决备注：{{resolution_note}}', 
'告警解决时的邮件通知模板', TRUE, 'system');

-- 插入示例告警规则
INSERT INTO alarm_rule (rule_name, rule_type, rule_category, rule_description, rule_condition, severity_level, threshold_config, created_by) VALUES
('CPU使用率过高', 'THRESHOLD', 'PERFORMANCE', '监控系统CPU使用率，超过阈值时触发告警', 
'{"metric": "cpu_usage", "operator": ">", "threshold": 80}', 2, 
'{"warning_threshold": 70, "critical_threshold": 90}', 'system'),
('内存使用率过高', 'THRESHOLD', 'PERFORMANCE', '监控系统内存使用率，超过阈值时触发告警', 
'{"metric": "memory_usage", "operator": ">", "threshold": 85}', 2, 
'{"warning_threshold": 75, "critical_threshold": 95}', 'system'),
('异常流量检测', 'ANOMALY', 'SECURITY', '检测网络流量异常模式', 
'{"metric": "network_traffic", "algorithm": "isolation_forest", "sensitivity": 0.1}', 1, 
'{"anomaly_score_threshold": 0.8}', 'system');

-- ========================================
-- CDC配置（如果需要）
-- ========================================

-- 为需要CDC的表设置复制标识
ALTER TABLE alarm_rule REPLICA IDENTITY FULL;
ALTER TABLE alarm_rule_group REPLICA IDENTITY FULL;
ALTER TABLE alarm_rule_group_mapping REPLICA IDENTITY FULL;
ALTER TABLE alarm_record REPLICA IDENTITY FULL;
ALTER TABLE alarm_action_history REPLICA IDENTITY FULL;
ALTER TABLE alarm_knowledge_base REPLICA IDENTITY FULL;
ALTER TABLE notification_channel REPLICA IDENTITY FULL;
ALTER TABLE notification_template REPLICA IDENTITY FULL;
ALTER TABLE notification_record REPLICA IDENTITY FULL;
ALTER TABLE alarm_statistics REPLICA IDENTITY FULL;
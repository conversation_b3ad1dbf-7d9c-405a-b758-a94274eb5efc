-- ========================================
-- Auth Service Database Schema
-- 认证服务数据库结构
-- ========================================

-- 创建数据库（如果需要）
-- CREATE DATABASE auth_service;
-- \c auth_service;

-- ========================================
-- 用户管理表
-- ========================================

-- 用户表
DROP TABLE IF EXISTS sys_user CASCADE;
CREATE TABLE sys_user (
    user_id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    display_name VARCHAR(100),
    group_id INTEGER,
    status INTEGER DEFAULT 1,
    created_by VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER DEFAULT 0,
    version BIGINT DEFAULT 1
);

COMMENT ON TABLE sys_user IS '用户表';
COMMENT ON COLUMN sys_user.user_id IS '用户ID';
COMMENT ON COLUMN sys_user.username IS '用户名';
COMMENT ON COLUMN sys_user.password IS '密码（加密存储）';
COMMENT ON COLUMN sys_user.display_name IS '显示名称';
COMMENT ON COLUMN sys_user.group_id IS '用户组ID';
COMMENT ON COLUMN sys_user.status IS '用户状态：0-禁用，1-启用';
COMMENT ON COLUMN sys_user.created_by IS '创建人';
COMMENT ON COLUMN sys_user.created_at IS '创建时间';
COMMENT ON COLUMN sys_user.updated_by IS '更新人';
COMMENT ON COLUMN sys_user.updated_at IS '更新时间';
COMMENT ON COLUMN sys_user.deleted IS '逻辑删除标记：0-未删除，1-已删除';
COMMENT ON COLUMN sys_user.version IS '版本号（乐观锁）';

-- 角色表
DROP TABLE IF EXISTS sys_role CASCADE;
CREATE TABLE sys_role (
    role_id BIGSERIAL PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    created_by VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER DEFAULT 0
);

COMMENT ON TABLE sys_role IS '角色表';
COMMENT ON COLUMN sys_role.role_id IS '角色ID';
COMMENT ON COLUMN sys_role.role_name IS '角色名称';
COMMENT ON COLUMN sys_role.description IS '角色描述';
COMMENT ON COLUMN sys_role.created_by IS '创建人';
COMMENT ON COLUMN sys_role.created_at IS '创建时间';
COMMENT ON COLUMN sys_role.updated_by IS '更新人';
COMMENT ON COLUMN sys_role.updated_at IS '更新时间';
COMMENT ON COLUMN sys_role.deleted IS '逻辑删除标记：0-未删除，1-已删除';

-- 权限表
DROP TABLE IF EXISTS sys_permission CASCADE;
CREATE TABLE sys_permission (
    permission_id BIGSERIAL PRIMARY KEY,
    permission_code VARCHAR(100) UNIQUE NOT NULL,
    permission_name VARCHAR(100) NOT NULL,
    description TEXT,
    created_by VARCHAR(100) DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER DEFAULT 0
);

COMMENT ON TABLE sys_permission IS '权限表';
COMMENT ON COLUMN sys_permission.permission_id IS '权限ID';
COMMENT ON COLUMN sys_permission.permission_code IS '权限编码';
COMMENT ON COLUMN sys_permission.permission_name IS '权限名称';
COMMENT ON COLUMN sys_permission.description IS '权限描述';
COMMENT ON COLUMN sys_permission.created_by IS '创建人';
COMMENT ON COLUMN sys_permission.created_at IS '创建时间';
COMMENT ON COLUMN sys_permission.updated_by IS '更新人';
COMMENT ON COLUMN sys_permission.updated_at IS '更新时间';
COMMENT ON COLUMN sys_permission.deleted IS '逻辑删除标记：0-未删除，1-已删除';

-- 用户角色关联表
DROP TABLE IF EXISTS user_role CASCADE;
CREATE TABLE user_role (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    UNIQUE(user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES sys_role(role_id) ON DELETE CASCADE
);

COMMENT ON TABLE user_role IS '用户角色关联表';
COMMENT ON COLUMN user_role.id IS '关联ID';
COMMENT ON COLUMN user_role.user_id IS '用户ID';
COMMENT ON COLUMN user_role.role_id IS '角色ID';

-- 角色权限关联表
DROP TABLE IF EXISTS role_permission CASCADE;
CREATE TABLE role_permission (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    UNIQUE(role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES sys_role(role_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES sys_permission(permission_id) ON DELETE CASCADE
);

COMMENT ON TABLE role_permission IS '角色权限关联表';
COMMENT ON COLUMN role_permission.id IS '关联ID';
COMMENT ON COLUMN role_permission.role_id IS '角色ID';
COMMENT ON COLUMN role_permission.permission_id IS '权限ID';

-- ========================================
-- 索引创建
-- ========================================

-- 用户表索引
CREATE INDEX idx_sys_user_username ON sys_user(username);
CREATE INDEX idx_sys_user_status ON sys_user(status);
CREATE INDEX idx_sys_user_created_at ON sys_user(created_at);

-- 角色表索引
CREATE INDEX idx_sys_role_role_name ON sys_role(role_name);
CREATE INDEX idx_sys_role_created_at ON sys_role(created_at);

-- 权限表索引
CREATE INDEX idx_sys_permission_permission_code ON sys_permission(permission_code);
CREATE INDEX idx_sys_permission_permission_name ON sys_permission(permission_name);
CREATE INDEX idx_sys_permission_created_at ON sys_permission(created_at);

-- 用户角色关联表索引
CREATE INDEX idx_user_role_user_id ON user_role(user_id);
CREATE INDEX idx_user_role_role_id ON user_role(role_id);

-- 角色权限关联表索引
CREATE INDEX idx_role_permission_role_id ON role_permission(role_id);
CREATE INDEX idx_role_permission_permission_id ON role_permission(permission_id);



-- ========================================
-- 触发器创建
-- ========================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为用户表创建触发器
CREATE TRIGGER trigger_sys_user_updated_at
    BEFORE UPDATE ON sys_user
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 为角色表创建触发器
CREATE TRIGGER trigger_sys_role_updated_at
    BEFORE UPDATE ON sys_role
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 为权限表创建触发器
CREATE TRIGGER trigger_sys_permission_updated_at
    BEFORE UPDATE ON sys_permission
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();



-- ========================================
-- 初始化数据
-- ========================================

-- 插入默认管理员用户
INSERT INTO sys_user (username, password, display_name, status, created_by) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKVjzieMwkOBSaEwHJnlQNEFOJSa', '系统管理员', 1, 'system');

-- 插入默认角色
INSERT INTO sys_role (role_name, description, created_by) VALUES
('超级管理员', '系统超级管理员，拥有所有权限', 'system'),
('系统管理员', '系统管理员，拥有大部分管理权限', 'system'),
('普通用户', '普通用户，拥有基本查看权限', 'system');

-- 插入默认权限
INSERT INTO sys_permission (permission_code, permission_name, description, created_by) VALUES
('USER_MANAGE', '用户管理', '用户管理菜单权限', 'system'),
('USER_VIEW', '用户查看', '查看用户信息权限', 'system'),
('USER_CREATE', '用户创建', '创建用户权限', 'system'),
('USER_UPDATE', '用户编辑', '编辑用户权限', 'system'),
('USER_DELETE', '用户删除', '删除用户权限', 'system'),
('ROLE_MANAGE', '角色管理', '角色管理菜单权限', 'system'),
('ROLE_VIEW', '角色查看', '查看角色信息权限', 'system'),
('ROLE_CREATE', '角色创建', '创建角色权限', 'system'),
('ROLE_UPDATE', '角色编辑', '编辑角色权限', 'system'),
('ROLE_DELETE', '角色删除', '删除角色权限', 'system');

-- 为超级管理员分配角色
INSERT INTO user_role (user_id, role_id) VALUES
(1, 1);

-- 为超级管理员角色分配所有权限
INSERT INTO role_permission (role_id, permission_id)
SELECT 1, permission_id FROM sys_permission;

-- ========================================
-- CDC配置（如果需要）
-- ========================================

-- 为需要CDC的表设置复制标识
ALTER TABLE sys_user REPLICA IDENTITY FULL;
ALTER TABLE sys_role REPLICA IDENTITY FULL;
ALTER TABLE sys_permission REPLICA IDENTITY FULL;
ALTER TABLE user_role REPLICA IDENTITY FULL;
ALTER TABLE role_permission REPLICA IDENTITY FULL;
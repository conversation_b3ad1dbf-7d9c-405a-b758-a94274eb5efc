# NTA 3.0 元数据服务SQL文件修正说明

## 修正概述

由于 `01-metadata.sql` 文件将被删除，对 `07-metadata-service.sql` 进行了全面重构，将所有系统元数据管理的核心功能迁移到元数据服务中，确保元数据服务成为系统元数据管理的唯一入口。

## 修正原则

1. **标签库管理和系统字典功能属于系统元数据管理** - 这些核心功能必须在元数据服务中管理
2. **完整迁移核心元数据表** - 将 `01-metadata.sql` 中的所有核心表迁移到元数据服务
3. **保持功能完整性** - 确保所有元数据管理功能在迁移后正常工作
4. **统一管理** - 元数据服务成为系统元数据的统一管理入口

## 主要修正内容

### 1. 完整迁移的核心元数据表

从 `01-metadata.sql` 迁移到元数据服务的核心表：

- **枚举类型定义**：所有元数据相关的枚举类型
  - `threat_type_enum` - 威胁类型枚举
  - `threat_level_enum` - 威胁等级枚举
  - `detector_type_enum` - 检测器类型枚举
  - `label_source_enum` - 标签来源枚举
  - `label_target_type_enum` - 标签目标类型枚举
  - `label_category_enum` - 标签分类枚举
  - `cyber_kill_chain_enum` - Cyber Kill Chain枚举

- **核心元数据表**：
  - `labels` - 统一标签表（标签库管理核心表）
  - `entity_labels` - 统一标签关联表
  - `cyber_kill_chain_details` - Cyber Kill Chain详情表
  - `detector_config` - 检测器配置表
  - `network_protocols` - 网络协议表
  - `internet_protocols` - 互联网协议表
  - `application_protocol_info` - 应用协议信息表
  - `system_dictionary` - 系统字典表
  - `metadata_definitions` - 元数据定义表
  - `metadata_values` - 元数据值表
  - `cert_label_model_mapping` - 证书检测模型映射表

### 2. 新增的元数据服务专用表

#### 2.1 ES查询字段配置表 (`es_query_field_config`)

- 元数据服务专用表
- 管理Elasticsearch查询字段的元数据配置
- 支持字段类型、搜索性、聚合性等属性配置

#### 2.2 下载搜索字段配置表 (`download_search_field_config`)

- 元数据服务专用表
- 管理下载搜索功能的字段配置
- 支持字段显示名称和描述配置

#### 2.3 Nebula图数据库元数据配置表

- `nebula_type_config` - Nebula类型配置表
- `nebula_property_config` - Nebula属性配置表
- 支持图数据库的元数据管理

#### 2.4 缓存配置表 (`cache_config`)

- 元数据服务专用表
- 管理各种元数据缓存的配置
- 支持TTL、刷新间隔等配置

### 3. 完整的初始化数据

#### 3.1 Cyber Kill Chain详情数据

- 包含完整的攻击链阶段定义
- 每个阶段的中英文名称、描述、典型技术和防护建议

#### 3.2 基础协议数据

- 网络协议表初始化数据（TCP、UDP、HTTP等）
- 互联网协议表初始化数据（IPv4、IPv6等）
- 应用协议信息初始化数据（端口映射）

## 元数据服务的核心职责

修正后的元数据服务承担以下核心职责：

1. **标签库管理** - 通过 `labels` 和 `entity_labels` 表管理统一标签系统
2. **系统字典管理** - 通过 `system_dictionary` 表管理各种系统配置字典
3. **协议管理** - 通过 `network_protocols`、`internet_protocols` 和 `application_protocol_info` 管理协议信息
4. **Cyber Kill Chain管理** - 通过 `cyber_kill_chain_details` 管理攻击链模型
5. **检测器配置管理** - 通过 `detector_config` 管理检测器配置
6. **元数据定义和值管理** - 通过 `metadata_definitions` 和 `metadata_values` 管理元数据
7. **查询字段元数据管理** - 通过新增的配置表管理查询相关的元数据
8. **图数据库元数据管理** - 通过新增的Nebula配置表管理图数据库元数据
9. **缓存管理** - 通过缓存配置表管理各种元数据缓存

## 依赖关系变更

- **无前置依赖**: 由于 `01-metadata.sql` 被删除，元数据服务现在是独立的
- **自包含**: 包含所有必要的枚举类型定义和核心元数据表
- **完整初始化**: 包含了所有基础数据的初始化

## 文件结构

```text
07-metadata-service.sql
├── 枚举类型定义
├── 系统元数据核心表
├── 元数据查询字段配置表
├── Nebula图数据库元数据配置表
├── 统一标签管理表
├── 系统字典和元数据管理表
├── 元数据缓存管理表
├── 索引创建
├── 触发器创建
├── 初始化数据
└── CDC配置
```

## 验证建议

1. 验证所有枚举类型定义正确
2. 确认元数据服务代码中使用的表都已正确定义
3. 检查标签管理、协议管理等核心功能是否正常工作
4. 验证缓存配置是否与代码中的缓存策略一致
5. 测试Cyber Kill Chain数据初始化是否完整

## 总结

修正后的 `07-metadata-service.sql` 文件：

- 成为系统元数据管理的唯一入口
- 包含完整的元数据管理功能
- 自包含所有必要的定义和初始化数据
- 支持标签库管理、系统字典等核心功能
- 符合微服务架构的设计原则

# NTA 3.0 元数据服务SQL文件修正说明

## 修正概述

根据对 `01-metadata.sql`、`02-th_analysis.sql` 和 `services/metadata` 代码的分析，对 `07-metadata-service.sql` 进行了全面修正，确保元数据服务只包含真正属于系统元数据管理的功能。

## 修正原则

1. **标签库管理和系统字典功能属于系统元数据管理** - 这些核心功能应该在元数据服务中管理
2. **避免重复定义** - 已在 `01-metadata.sql` 中定义的表不在元数据服务中重复创建
3. **专注元数据服务职责** - 只包含元数据服务特有的扩展功能表

## 主要修正内容

### 1. 删除的表（不属于元数据服务）
- `data_source` - 数据源管理应该在数据管理服务中
- `data_schema` - 数据模式管理应该在数据管理服务中  
- `data_dictionary` - 与系统字典重复，应使用 `01-metadata.sql` 中的 `system_dictionary`
- `system_configuration` - 系统配置应该在配置管理服务中
- `configuration_history` - 配置历史应该在配置管理服务中
- `tag_definition` - 标签定义功能已在 `labels` 表中实现
- `resource_tag` - 资源标签关联已在 `entity_labels` 表中实现
- `data_lineage` - 数据血缘应该在数据管理服务中
- `metadata_statistics` - 统计功能应该在分析服务中

### 2. 保留的核心元数据管理功能（引用说明）
从 `01-metadata.sql` 中引用的核心表：
- `labels` - 统一标签表（标签库管理核心表）
- `entity_labels` - 统一标签关联表
- `cyber_kill_chain_details` - Cyber Kill Chain详情表
- `detector_config` - 检测器配置表
- `network_protocols` - 网络协议表
- `internet_protocols` - 互联网协议表
- `system_dictionary` - 系统字典表
- `metadata_definitions` - 元数据定义表
- `metadata_values` - 元数据值表

### 3. 新增的元数据服务专用表

#### 3.1 应用协议信息表 (`application_protocol_info`)
- 从 `01-metadata.sql` 迁移到元数据服务
- 管理应用层协议信息
- 支持端口、应用ID、协议类型映射

#### 3.2 证书检测模型映射表 (`cert_label_model_mapping`)
- 从 `01-metadata.sql` 迁移到元数据服务
- 管理证书标签与检测模型的映射关系
- 支持证书安全检测功能

#### 3.3 ES查询字段配置表 (`es_query_field_config`)
- 元数据服务专用表
- 管理Elasticsearch查询字段的元数据配置
- 支持字段类型、搜索性、聚合性等属性配置

#### 3.4 下载搜索字段配置表 (`download_search_field_config`)
- 元数据服务专用表
- 管理下载搜索功能的字段配置
- 支持字段显示名称和描述配置

#### 3.5 Nebula图数据库元数据配置表
- `nebula_type_config` - Nebula类型配置表
- `nebula_property_config` - Nebula属性配置表
- 支持图数据库的元数据管理

#### 3.6 缓存配置表 (`cache_config`)
- 元数据服务专用表
- 管理各种元数据缓存的配置
- 支持TTL、刷新间隔等配置

## 元数据服务的核心职责

根据代码分析，元数据服务主要负责：

1. **标签库管理** - 通过 `labels` 和 `entity_labels` 表管理统一标签系统
2. **系统字典管理** - 通过 `system_dictionary` 表管理各种系统配置字典
3. **协议管理** - 通过 `network_protocols`、`internet_protocols` 和 `application_protocol_info` 管理协议信息
4. **Cyber Kill Chain管理** - 通过 `cyber_kill_chain_details` 管理攻击链模型
5. **检测器配置管理** - 通过 `detector_config` 管理检测器配置
6. **元数据定义和值管理** - 通过 `metadata_definitions` 和 `metadata_values` 管理元数据
7. **查询字段元数据管理** - 通过新增的配置表管理查询相关的元数据
8. **图数据库元数据管理** - 通过新增的Nebula配置表管理图数据库元数据
9. **缓存管理** - 通过缓存配置表管理各种元数据缓存

## 依赖关系

- **前置依赖**: 必须先执行 `01-metadata.sql` 创建枚举类型和核心元数据表
- **服务依赖**: 元数据服务依赖核心元数据表，但不重复创建这些表
- **数据初始化**: 包含了基础的配置数据初始化

## 文件结构

```
07-metadata-service.sql
├── 系统元数据管理表（引用说明）
├── 元数据服务专用表
├── 索引创建
├── 触发器创建
├── 初始化数据
└── CDC配置
```

## 验证建议

1. 确保 `01-metadata.sql` 已正确执行
2. 验证元数据服务代码中使用的表都已正确定义或引用
3. 检查标签管理、协议管理等核心功能是否正常工作
4. 验证缓存配置是否与代码中的缓存策略一致

## 总结

修正后的 `07-metadata-service.sql` 文件：
- 专注于元数据服务的核心职责
- 避免了与其他服务的功能重叠
- 正确引用了核心元数据表
- 包含了元数据服务特有的扩展功能
- 符合微服务架构的设计原则

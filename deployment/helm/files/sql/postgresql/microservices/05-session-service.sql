-- ========================================
-- Session Service Database Schema
-- 会话服务数据库结构
-- ========================================

-- 创建数据库（如果需要）
-- CREATE DATABASE session_service;
-- \c session_service;

-- ========================================
-- 会话管理表
-- ========================================

-- 会话表
DROP TABLE IF EXISTS session CASCADE;
CREATE TABLE session (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    src_ip INET NOT NULL,
    dst_ip INET NOT NULL,
    src_port INTEGER NOT NULL,
    dst_port INTEGER NOT NULL,
    protocol VARCHAR(10) NOT NULL,
    session_type VARCHAR(50),
    session_status VARCHAR(20) DEFAULT 'ACTIVE',
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    duration_seconds INTEGER,
    bytes_sent BIGINT DEFAULT 0,
    bytes_received BIGINT DEFAULT 0,
    packets_sent INTEGER DEFAULT 0,
    packets_received INTEGER DEFAULT 0,
    flags VARCHAR(50),
    tcp_state VARCHAR(20),
    application_protocol VARCHAR(50),
    service_name VARCHAR(100),
    country_code VARCHAR(10),
    region VARCHAR(100),
    city VARCHAR(100),
    organization VARCHAR(200),
    threat_level INTEGER DEFAULT 0,
    risk_score DECIMAL(5,2) DEFAULT 0.0,
    tags JSONB,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE session IS '会话表';
COMMENT ON COLUMN session.id IS '会话记录ID';
COMMENT ON COLUMN session.session_id IS '会话唯一标识';
COMMENT ON COLUMN session.src_ip IS '源IP地址';
COMMENT ON COLUMN session.dst_ip IS '目标IP地址';
COMMENT ON COLUMN session.src_port IS '源端口';
COMMENT ON COLUMN session.dst_port IS '目标端口';
COMMENT ON COLUMN session.protocol IS '协议类型：TCP, UDP, ICMP';
COMMENT ON COLUMN session.session_type IS '会话类型：INBOUND, OUTBOUND, INTERNAL';
COMMENT ON COLUMN session.session_status IS '会话状态：ACTIVE, CLOSED, TIMEOUT, RESET';
COMMENT ON COLUMN session.start_time IS '会话开始时间';
COMMENT ON COLUMN session.end_time IS '会话结束时间';
COMMENT ON COLUMN session.duration_seconds IS '会话持续时间（秒）';
COMMENT ON COLUMN session.bytes_sent IS '发送字节数';
COMMENT ON COLUMN session.bytes_received IS '接收字节数';
COMMENT ON COLUMN session.packets_sent IS '发送包数';
COMMENT ON COLUMN session.packets_received IS '接收包数';
COMMENT ON COLUMN session.flags IS 'TCP标志位';
COMMENT ON COLUMN session.tcp_state IS 'TCP连接状态';
COMMENT ON COLUMN session.application_protocol IS '应用层协议';
COMMENT ON COLUMN session.service_name IS '服务名称';
COMMENT ON COLUMN session.country_code IS '国家代码';
COMMENT ON COLUMN session.region IS '地区';
COMMENT ON COLUMN session.city IS '城市';
COMMENT ON COLUMN session.organization IS '组织机构';
COMMENT ON COLUMN session.threat_level IS '威胁等级：0-无威胁，1-低，2-中，3-高，4-严重';
COMMENT ON COLUMN session.risk_score IS '风险评分';
COMMENT ON COLUMN session.tags IS '标签';
COMMENT ON COLUMN session.metadata IS '元数据';
COMMENT ON COLUMN session.created_at IS '创建时间';
COMMENT ON COLUMN session.updated_at IS '更新时间';

-- 流量统计表
DROP TABLE IF EXISTS traffic_statistics CASCADE;
CREATE TABLE traffic_statistics (
    id SERIAL PRIMARY KEY,
    stat_time TIMESTAMP NOT NULL,
    stat_interval INTEGER NOT NULL,
    src_ip INET,
    dst_ip INET,
    protocol VARCHAR(10),
    application_protocol VARCHAR(50),
    session_count INTEGER DEFAULT 0,
    total_bytes BIGINT DEFAULT 0,
    total_packets INTEGER DEFAULT 0,
    avg_session_duration DECIMAL(10,2),
    max_session_duration INTEGER,
    min_session_duration INTEGER,
    unique_src_ips INTEGER DEFAULT 0,
    unique_dst_ips INTEGER DEFAULT 0,
    threat_sessions INTEGER DEFAULT 0,
    high_risk_sessions INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE traffic_statistics IS '流量统计表';
COMMENT ON COLUMN traffic_statistics.id IS '统计记录ID';
COMMENT ON COLUMN traffic_statistics.stat_time IS '统计时间';
COMMENT ON COLUMN traffic_statistics.stat_interval IS '统计间隔（秒）';
COMMENT ON COLUMN traffic_statistics.src_ip IS '源IP地址（可为空表示全部）';
COMMENT ON COLUMN traffic_statistics.dst_ip IS '目标IP地址（可为空表示全部）';
COMMENT ON COLUMN traffic_statistics.protocol IS '协议类型';
COMMENT ON COLUMN traffic_statistics.application_protocol IS '应用层协议';
COMMENT ON COLUMN traffic_statistics.session_count IS '会话数量';
COMMENT ON COLUMN traffic_statistics.total_bytes IS '总字节数';
COMMENT ON COLUMN traffic_statistics.total_packets IS '总包数';
COMMENT ON COLUMN traffic_statistics.avg_session_duration IS '平均会话持续时间';
COMMENT ON COLUMN traffic_statistics.max_session_duration IS '最大会话持续时间';
COMMENT ON COLUMN traffic_statistics.min_session_duration IS '最小会话持续时间';
COMMENT ON COLUMN traffic_statistics.unique_src_ips IS '唯一源IP数量';
COMMENT ON COLUMN traffic_statistics.unique_dst_ips IS '唯一目标IP数量';
COMMENT ON COLUMN traffic_statistics.threat_sessions IS '威胁会话数量';
COMMENT ON COLUMN traffic_statistics.high_risk_sessions IS '高风险会话数量';
COMMENT ON COLUMN traffic_statistics.created_at IS '创建时间';
COMMENT ON COLUMN traffic_statistics.updated_at IS '更新时间';

-- ========================================
-- 网络拓扑表
-- ========================================

-- 网络拓扑节点表
DROP TABLE IF EXISTS network_topology_node CASCADE;
CREATE TABLE network_topology_node (
    id SERIAL PRIMARY KEY,
    node_id VARCHAR(100) UNIQUE NOT NULL,
    node_type VARCHAR(50) NOT NULL,
    ip_address INET,
    mac_address VARCHAR(17),
    hostname VARCHAR(200),
    device_vendor VARCHAR(100),
    device_model VARCHAR(100),
    device_version VARCHAR(100),
    operating_system VARCHAR(100),
    node_status VARCHAR(20) DEFAULT 'ACTIVE',
    first_seen TIMESTAMP,
    last_seen TIMESTAMP,
    location JSONB,
    properties JSONB,
    risk_level INTEGER DEFAULT 0,
    tags JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_topology_node IS '网络拓扑节点表';
COMMENT ON COLUMN network_topology_node.id IS '节点记录ID';
COMMENT ON COLUMN network_topology_node.node_id IS '节点唯一标识';
COMMENT ON COLUMN network_topology_node.node_type IS '节点类型：HOST, ROUTER, SWITCH, FIREWALL, SERVER, WORKSTATION';
COMMENT ON COLUMN network_topology_node.ip_address IS 'IP地址';
COMMENT ON COLUMN network_topology_node.mac_address IS 'MAC地址';
COMMENT ON COLUMN network_topology_node.hostname IS '主机名';
COMMENT ON COLUMN network_topology_node.device_vendor IS '设备厂商';
COMMENT ON COLUMN network_topology_node.device_model IS '设备型号';
COMMENT ON COLUMN network_topology_node.device_version IS '设备版本';
COMMENT ON COLUMN network_topology_node.operating_system IS '操作系统';
COMMENT ON COLUMN network_topology_node.node_status IS '节点状态：ACTIVE, INACTIVE, UNKNOWN';
COMMENT ON COLUMN network_topology_node.first_seen IS '首次发现时间';
COMMENT ON COLUMN network_topology_node.last_seen IS '最后发现时间';
COMMENT ON COLUMN network_topology_node.location IS '位置信息';
COMMENT ON COLUMN network_topology_node.properties IS '节点属性';
COMMENT ON COLUMN network_topology_node.risk_level IS '风险等级';
COMMENT ON COLUMN network_topology_node.tags IS '标签';
COMMENT ON COLUMN network_topology_node.created_at IS '创建时间';
COMMENT ON COLUMN network_topology_node.updated_at IS '更新时间';

-- 网络拓扑连接表
DROP TABLE IF EXISTS network_topology_edge CASCADE;
CREATE TABLE network_topology_edge (
    id SERIAL PRIMARY KEY,
    edge_id VARCHAR(100) UNIQUE NOT NULL,
    source_node_id VARCHAR(100) NOT NULL,
    target_node_id VARCHAR(100) NOT NULL,
    connection_type VARCHAR(50),
    protocol VARCHAR(20),
    port_info JSONB,
    bandwidth BIGINT,
    latency_ms DECIMAL(10,2),
    packet_loss_rate DECIMAL(5,4),
    connection_status VARCHAR(20) DEFAULT 'ACTIVE',
    first_seen TIMESTAMP,
    last_seen TIMESTAMP,
    traffic_volume BIGINT DEFAULT 0,
    session_count INTEGER DEFAULT 0,
    properties JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_node_id) REFERENCES network_topology_node(node_id) ON DELETE CASCADE,
    FOREIGN KEY (target_node_id) REFERENCES network_topology_node(node_id) ON DELETE CASCADE
);

COMMENT ON TABLE network_topology_edge IS '网络拓扑连接表';
COMMENT ON COLUMN network_topology_edge.id IS '连接记录ID';
COMMENT ON COLUMN network_topology_edge.edge_id IS '连接唯一标识';
COMMENT ON COLUMN network_topology_edge.source_node_id IS '源节点ID';
COMMENT ON COLUMN network_topology_edge.target_node_id IS '目标节点ID';
COMMENT ON COLUMN network_topology_edge.connection_type IS '连接类型：PHYSICAL, LOGICAL, VIRTUAL';
COMMENT ON COLUMN network_topology_edge.protocol IS '协议类型';
COMMENT ON COLUMN network_topology_edge.port_info IS '端口信息';
COMMENT ON COLUMN network_topology_edge.bandwidth IS '带宽（bps）';
COMMENT ON COLUMN network_topology_edge.latency_ms IS '延迟（毫秒）';
COMMENT ON COLUMN network_topology_edge.packet_loss_rate IS '丢包率';
COMMENT ON COLUMN network_topology_edge.connection_status IS '连接状态：ACTIVE, INACTIVE, UNSTABLE';
COMMENT ON COLUMN network_topology_edge.first_seen IS '首次发现时间';
COMMENT ON COLUMN network_topology_edge.last_seen IS '最后发现时间';
COMMENT ON COLUMN network_topology_edge.traffic_volume IS '流量总量';
COMMENT ON COLUMN network_topology_edge.session_count IS '会话数量';
COMMENT ON COLUMN network_topology_edge.properties IS '连接属性';
COMMENT ON COLUMN network_topology_edge.created_at IS '创建时间';
COMMENT ON COLUMN network_topology_edge.updated_at IS '更新时间';

-- ========================================
-- 协议分析表
-- ========================================

-- 协议统计表
DROP TABLE IF EXISTS protocol_statistics CASCADE;
CREATE TABLE protocol_statistics (
    id SERIAL PRIMARY KEY,
    stat_time TIMESTAMP NOT NULL,
    stat_interval INTEGER NOT NULL,
    protocol VARCHAR(20) NOT NULL,
    application_protocol VARCHAR(50),
    port INTEGER,
    session_count INTEGER DEFAULT 0,
    total_bytes BIGINT DEFAULT 0,
    total_packets INTEGER DEFAULT 0,
    avg_session_duration DECIMAL(10,2),
    unique_src_ips INTEGER DEFAULT 0,
    unique_dst_ips INTEGER DEFAULT 0,
    top_src_ips JSONB,
    top_dst_ips JSONB,
    anomaly_score DECIMAL(5,2) DEFAULT 0.0,
    threat_indicators JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE protocol_statistics IS '协议统计表';
COMMENT ON COLUMN protocol_statistics.id IS '统计记录ID';
COMMENT ON COLUMN protocol_statistics.stat_time IS '统计时间';
COMMENT ON COLUMN protocol_statistics.stat_interval IS '统计间隔（秒）';
COMMENT ON COLUMN protocol_statistics.protocol IS '网络协议';
COMMENT ON COLUMN protocol_statistics.application_protocol IS '应用层协议';
COMMENT ON COLUMN protocol_statistics.port IS '端口号';
COMMENT ON COLUMN protocol_statistics.session_count IS '会话数量';
COMMENT ON COLUMN protocol_statistics.total_bytes IS '总字节数';
COMMENT ON COLUMN protocol_statistics.total_packets IS '总包数';
COMMENT ON COLUMN protocol_statistics.avg_session_duration IS '平均会话持续时间';
COMMENT ON COLUMN protocol_statistics.unique_src_ips IS '唯一源IP数量';
COMMENT ON COLUMN protocol_statistics.unique_dst_ips IS '唯一目标IP数量';
COMMENT ON COLUMN protocol_statistics.top_src_ips IS 'Top源IP列表';
COMMENT ON COLUMN protocol_statistics.top_dst_ips IS 'Top目标IP列表';
COMMENT ON COLUMN protocol_statistics.anomaly_score IS '异常评分';
COMMENT ON COLUMN protocol_statistics.threat_indicators IS '威胁指标';
COMMENT ON COLUMN protocol_statistics.created_at IS '创建时间';
COMMENT ON COLUMN protocol_statistics.updated_at IS '更新时间';

-- 应用识别表
DROP TABLE IF EXISTS application_identification CASCADE;
CREATE TABLE application_identification (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(100) NOT NULL,
    application_name VARCHAR(100),
    application_category VARCHAR(50),
    application_vendor VARCHAR(100),
    application_version VARCHAR(50),
    confidence_score DECIMAL(5,2) DEFAULT 0.0,
    identification_method VARCHAR(50),
    signature_id VARCHAR(100),
    payload_analysis JSONB,
    behavioral_analysis JSONB,
    risk_assessment JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES session(session_id) ON DELETE CASCADE
);

COMMENT ON TABLE application_identification IS '应用识别表';
COMMENT ON COLUMN application_identification.id IS '识别记录ID';
COMMENT ON COLUMN application_identification.session_id IS '会话ID';
COMMENT ON COLUMN application_identification.application_name IS '应用名称';
COMMENT ON COLUMN application_identification.application_category IS '应用分类';
COMMENT ON COLUMN application_identification.application_vendor IS '应用厂商';
COMMENT ON COLUMN application_identification.application_version IS '应用版本';
COMMENT ON COLUMN application_identification.confidence_score IS '置信度评分';
COMMENT ON COLUMN application_identification.identification_method IS '识别方法：DPI, BEHAVIOR, SIGNATURE, ML';
COMMENT ON COLUMN application_identification.signature_id IS '特征签名ID';
COMMENT ON COLUMN application_identification.payload_analysis IS '载荷分析结果';
COMMENT ON COLUMN application_identification.behavioral_analysis IS '行为分析结果';
COMMENT ON COLUMN application_identification.risk_assessment IS '风险评估结果';
COMMENT ON COLUMN application_identification.created_at IS '创建时间';
COMMENT ON COLUMN application_identification.updated_at IS '更新时间';

-- ========================================
-- 性能监控表
-- ========================================

-- 网络性能监控表
DROP TABLE IF EXISTS network_performance CASCADE;
CREATE TABLE network_performance (
    id SERIAL PRIMARY KEY,
    monitor_time TIMESTAMP NOT NULL,
    monitor_interval INTEGER NOT NULL,
    interface_name VARCHAR(50),
    bandwidth_utilization DECIMAL(5,2),
    throughput_bps BIGINT,
    packet_rate_pps INTEGER,
    error_rate DECIMAL(5,4),
    drop_rate DECIMAL(5,4),
    latency_avg_ms DECIMAL(10,2),
    latency_max_ms DECIMAL(10,2),
    latency_min_ms DECIMAL(10,2),
    jitter_ms DECIMAL(10,2),
    connection_count INTEGER,
    active_sessions INTEGER,
    tcp_retransmissions INTEGER,
    udp_errors INTEGER,
    performance_score DECIMAL(5,2),
    alert_level INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_performance IS '网络性能监控表';
COMMENT ON COLUMN network_performance.id IS '监控记录ID';
COMMENT ON COLUMN network_performance.monitor_time IS '监控时间';
COMMENT ON COLUMN network_performance.monitor_interval IS '监控间隔（秒）';
COMMENT ON COLUMN network_performance.interface_name IS '网络接口名称';
COMMENT ON COLUMN network_performance.bandwidth_utilization IS '带宽利用率（%）';
COMMENT ON COLUMN network_performance.throughput_bps IS '吞吐量（bps）';
COMMENT ON COLUMN network_performance.packet_rate_pps IS '包速率（pps）';
COMMENT ON COLUMN network_performance.error_rate IS '错误率';
COMMENT ON COLUMN network_performance.drop_rate IS '丢包率';
COMMENT ON COLUMN network_performance.latency_avg_ms IS '平均延迟（毫秒）';
COMMENT ON COLUMN network_performance.latency_max_ms IS '最大延迟（毫秒）';
COMMENT ON COLUMN network_performance.latency_min_ms IS '最小延迟（毫秒）';
COMMENT ON COLUMN network_performance.jitter_ms IS '抖动（毫秒）';
COMMENT ON COLUMN network_performance.connection_count IS '连接数量';
COMMENT ON COLUMN network_performance.active_sessions IS '活跃会话数';
COMMENT ON COLUMN network_performance.tcp_retransmissions IS 'TCP重传次数';
COMMENT ON COLUMN network_performance.udp_errors IS 'UDP错误次数';
COMMENT ON COLUMN network_performance.performance_score IS '性能评分';
COMMENT ON COLUMN network_performance.alert_level IS '告警级别';
COMMENT ON COLUMN network_performance.created_at IS '创建时间';

-- ========================================
-- 索引创建
-- ========================================

-- 会话表索引
CREATE INDEX idx_session_session_id ON session(session_id);
CREATE INDEX idx_session_src_ip ON session(src_ip);
CREATE INDEX idx_session_dst_ip ON session(dst_ip);
CREATE INDEX idx_session_protocol ON session(protocol);
CREATE INDEX idx_session_status ON session(session_status);
CREATE INDEX idx_session_start_time ON session(start_time);
CREATE INDEX idx_session_end_time ON session(end_time);
CREATE INDEX idx_session_threat_level ON session(threat_level);
CREATE INDEX idx_session_risk_score ON session(risk_score);
CREATE INDEX idx_session_application_protocol ON session(application_protocol);
CREATE INDEX idx_session_src_dst_ip ON session(src_ip, dst_ip);
CREATE INDEX idx_session_time_range ON session(start_time, end_time);

-- 流量统计表索引
CREATE INDEX idx_traffic_statistics_stat_time ON traffic_statistics(stat_time);
CREATE INDEX idx_traffic_statistics_src_ip ON traffic_statistics(src_ip);
CREATE INDEX idx_traffic_statistics_dst_ip ON traffic_statistics(dst_ip);
CREATE INDEX idx_traffic_statistics_protocol ON traffic_statistics(protocol);
CREATE INDEX idx_traffic_statistics_app_protocol ON traffic_statistics(application_protocol);
CREATE INDEX idx_traffic_statistics_interval ON traffic_statistics(stat_interval);

-- 网络拓扑节点表索引
CREATE INDEX idx_topology_node_node_id ON network_topology_node(node_id);
CREATE INDEX idx_topology_node_ip_address ON network_topology_node(ip_address);
CREATE INDEX idx_topology_node_mac_address ON network_topology_node(mac_address);
CREATE INDEX idx_topology_node_type ON network_topology_node(node_type);
CREATE INDEX idx_topology_node_status ON network_topology_node(node_status);
CREATE INDEX idx_topology_node_hostname ON network_topology_node(hostname);
CREATE INDEX idx_topology_node_last_seen ON network_topology_node(last_seen);

-- 网络拓扑连接表索引
CREATE INDEX idx_topology_edge_edge_id ON network_topology_edge(edge_id);
CREATE INDEX idx_topology_edge_source_node ON network_topology_edge(source_node_id);
CREATE INDEX idx_topology_edge_target_node ON network_topology_edge(target_node_id);
CREATE INDEX idx_topology_edge_connection_type ON network_topology_edge(connection_type);
CREATE INDEX idx_topology_edge_status ON network_topology_edge(connection_status);
CREATE INDEX idx_topology_edge_last_seen ON network_topology_edge(last_seen);

-- 协议统计表索引
CREATE INDEX idx_protocol_statistics_stat_time ON protocol_statistics(stat_time);
CREATE INDEX idx_protocol_statistics_protocol ON protocol_statistics(protocol);
CREATE INDEX idx_protocol_statistics_app_protocol ON protocol_statistics(application_protocol);
CREATE INDEX idx_protocol_statistics_port ON protocol_statistics(port);
CREATE INDEX idx_protocol_statistics_anomaly_score ON protocol_statistics(anomaly_score);

-- 应用识别表索引
CREATE INDEX idx_application_identification_session_id ON application_identification(session_id);
CREATE INDEX idx_application_identification_app_name ON application_identification(application_name);
CREATE INDEX idx_application_identification_category ON application_identification(application_category);
CREATE INDEX idx_application_identification_confidence ON application_identification(confidence_score);
CREATE INDEX idx_application_identification_method ON application_identification(identification_method);

-- 网络性能监控表索引
CREATE INDEX idx_network_performance_monitor_time ON network_performance(monitor_time);
CREATE INDEX idx_network_performance_interface ON network_performance(interface_name);
CREATE INDEX idx_network_performance_utilization ON network_performance(bandwidth_utilization);
CREATE INDEX idx_network_performance_alert_level ON network_performance(alert_level);
CREATE INDEX idx_network_performance_score ON network_performance(performance_score);

-- ========================================
-- 触发器创建
-- ========================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
CREATE TRIGGER update_session_updated_at BEFORE UPDATE ON session
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_traffic_statistics_updated_at BEFORE UPDATE ON traffic_statistics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_topology_node_updated_at BEFORE UPDATE ON network_topology_node
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_topology_edge_updated_at BEFORE UPDATE ON network_topology_edge
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_protocol_statistics_updated_at BEFORE UPDATE ON protocol_statistics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_application_identification_updated_at BEFORE UPDATE ON application_identification
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 分区表创建（针对大数据量表）
-- ========================================

-- 为会话表创建按时间分区（示例）
-- CREATE TABLE session_y2024m01 PARTITION OF session
--     FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 为流量统计表创建按时间分区（示例）
-- CREATE TABLE traffic_statistics_y2024m01 PARTITION OF traffic_statistics
--     FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- ========================================
-- 初始化数据
-- ========================================

-- 插入示例网络拓扑节点
INSERT INTO network_topology_node (node_id, node_type, ip_address, hostname, node_status, first_seen, last_seen) VALUES
('node_gateway_001', 'ROUTER', '***********', 'gateway.local', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('node_server_001', 'SERVER', '***********00', 'server01.local', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('node_workstation_001', 'WORKSTATION', '*************', 'ws01.local', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 插入示例网络拓扑连接
INSERT INTO network_topology_edge (edge_id, source_node_id, target_node_id, connection_type, protocol, connection_status, first_seen, last_seen) VALUES
('edge_001', 'node_gateway_001', 'node_server_001', 'LOGICAL', 'TCP', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('edge_002', 'node_gateway_001', 'node_workstation_001', 'LOGICAL', 'TCP', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ========================================
-- CDC配置（如果需要）
-- ========================================

-- 为需要CDC的表设置复制标识
ALTER TABLE session REPLICA IDENTITY FULL;
ALTER TABLE traffic_statistics REPLICA IDENTITY FULL;
ALTER TABLE network_topology_node REPLICA IDENTITY FULL;
ALTER TABLE network_topology_edge REPLICA IDENTITY FULL;
ALTER TABLE protocol_statistics REPLICA IDENTITY FULL;
ALTER TABLE application_identification REPLICA IDENTITY FULL;
ALTER TABLE network_performance REPLICA IDENTITY FULL;
-- ========================================
-- NTA 3.0 元数据服务数据库结构
-- ========================================
-- 创建时间: 2025-01-25
-- 描述: 元数据服务相关的所有表结构定义
-- 数据库: PostgreSQL
--
-- 注意：本文件依赖的枚举类型定义在 01-metadata.sql 中
-- 请确保先执行元数据脚本再执行本脚本
-- ========================================

-- ========================================
-- 系统元数据管理表（从01-metadata.sql引用）
-- ========================================

-- 注意：以下表已在01-metadata.sql中定义，这里仅作引用说明
-- 1. labels - 统一标签表（标签库管理核心表）
-- 2. entity_labels - 统一标签关联表
-- 3. cyber_kill_chain_details - Cyber Kill Chain详情表
-- 4. detector_config - 检测器配置表
-- 5. network_protocols - 网络协议表
-- 6. internet_protocols - 互联网协议表
-- 7. system_dictionary - 系统字典表
-- 8. metadata_definitions - 元数据定义表
-- 9. metadata_values - 元数据值表

-- ========================================
-- 元数据服务专用表（扩展系统元数据功能）
-- ========================================

-- 应用协议信息表（从01-metadata.sql迁移到元数据服务）
DROP TABLE IF EXISTS application_protocol_info CASCADE;

CREATE TABLE application_protocol_info (
    id SERIAL PRIMARY KEY,
    port INTEGER,
    appid INTEGER,
    ippro INTEGER,
    remark TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE application_protocol_info IS '应用协议信息表';
COMMENT ON COLUMN application_protocol_info.port IS '端口';
COMMENT ON COLUMN application_protocol_info.appid IS 'app ID';
COMMENT ON COLUMN application_protocol_info.ippro IS '17 udp 6 tcp';
COMMENT ON COLUMN application_protocol_info.remark IS '备注信息';

-- 证书检测模型映射表（从01-metadata.sql迁移到元数据服务）
DROP TABLE IF EXISTS cert_label_model_mapping CASCADE;

CREATE TABLE cert_label_model_mapping (
    id BIGSERIAL PRIMARY KEY,
    label_id INTEGER NOT NULL,
    model_id BIGINT NOT NULL,
    model_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE cert_label_model_mapping IS '证书检测模型映射表';
COMMENT ON COLUMN cert_label_model_mapping.label_id IS '标签ID（引用labels表）';
COMMENT ON COLUMN cert_label_model_mapping.model_id IS '模型ID';
COMMENT ON COLUMN cert_label_model_mapping.model_name IS '模型名称';

-- ========================================
-- 元数据查询字段配置表
-- ========================================

-- ES查询字段配置表（支持元数据查询功能）
DROP TABLE IF EXISTS es_query_field_config CASCADE;

CREATE TABLE es_query_field_config (
    id SERIAL PRIMARY KEY,
    field_name VARCHAR(255) NOT NULL UNIQUE,
    field_display_name VARCHAR(255) NOT NULL,
    field_type VARCHAR(50) NOT NULL,
    field_category VARCHAR(100),
    is_searchable BOOLEAN DEFAULT TRUE,
    is_aggregatable BOOLEAN DEFAULT FALSE,
    is_sortable BOOLEAN DEFAULT FALSE,
    field_description TEXT,
    example_value VARCHAR(500),
    validation_pattern VARCHAR(500),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE es_query_field_config IS 'ES查询字段配置表';
COMMENT ON COLUMN es_query_field_config.field_name IS '字段名称';
COMMENT ON COLUMN es_query_field_config.field_display_name IS '字段显示名称';
COMMENT ON COLUMN es_query_field_config.field_type IS '字段类型：STRING, INTEGER, BOOLEAN, DATE, IP';
COMMENT ON COLUMN es_query_field_config.field_category IS '字段分类';
COMMENT ON COLUMN es_query_field_config.is_searchable IS '是否可搜索';
COMMENT ON COLUMN es_query_field_config.is_aggregatable IS '是否可聚合';
COMMENT ON COLUMN es_query_field_config.is_sortable IS '是否可排序';

-- 下载搜索字段配置表
DROP TABLE IF EXISTS download_search_field_config CASCADE;

CREATE TABLE download_search_field_config (
    id SERIAL PRIMARY KEY,
    field_name VARCHAR(255) NOT NULL UNIQUE,
    field_display_name VARCHAR(255) NOT NULL,
    field_description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE download_search_field_config IS '下载搜索字段配置表';
COMMENT ON COLUMN download_search_field_config.field_name IS '字段名称';
COMMENT ON COLUMN download_search_field_config.field_display_name IS '字段显示名称';

-- ========================================
-- Nebula图数据库元数据配置表
-- ========================================

-- Nebula类型配置表
DROP TABLE IF EXISTS nebula_type_config CASCADE;

CREATE TABLE nebula_type_config (
    id SERIAL PRIMARY KEY,
    type_name VARCHAR(100) NOT NULL UNIQUE,
    type_display_name VARCHAR(200) NOT NULL,
    type_category VARCHAR(50),
    type_description TEXT,
    is_vertex BOOLEAN DEFAULT TRUE,
    is_edge BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE nebula_type_config IS 'Nebula类型配置表';
COMMENT ON COLUMN nebula_type_config.type_name IS '类型名称';
COMMENT ON COLUMN nebula_type_config.type_display_name IS '类型显示名称';
COMMENT ON COLUMN nebula_type_config.is_vertex IS '是否为顶点类型';
COMMENT ON COLUMN nebula_type_config.is_edge IS '是否为边类型';

-- Nebula属性配置表
DROP TABLE IF EXISTS nebula_property_config CASCADE;

CREATE TABLE nebula_property_config (
    id SERIAL PRIMARY KEY,
    property_name VARCHAR(100) NOT NULL,
    property_display_name VARCHAR(200) NOT NULL,
    property_type VARCHAR(50) NOT NULL,
    property_description TEXT,
    is_indexed BOOLEAN DEFAULT FALSE,
    is_required BOOLEAN DEFAULT FALSE,
    default_value VARCHAR(500),
    validation_pattern VARCHAR(500),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(property_name)
);

COMMENT ON TABLE nebula_property_config IS 'Nebula属性配置表';
COMMENT ON COLUMN nebula_property_config.property_name IS '属性名称';
COMMENT ON COLUMN nebula_property_config.property_display_name IS '属性显示名称';
COMMENT ON COLUMN nebula_property_config.property_type IS '属性类型：STRING, INT, DOUBLE, BOOL, TIMESTAMP';

-- ========================================
-- 元数据缓存管理表
-- ========================================

-- 缓存配置表
DROP TABLE IF EXISTS cache_config CASCADE;

CREATE TABLE cache_config (
    id SERIAL PRIMARY KEY,
    cache_name VARCHAR(100) NOT NULL UNIQUE,
    cache_type VARCHAR(50) NOT NULL,
    cache_description TEXT,
    max_size INTEGER DEFAULT 10000,
    ttl_seconds INTEGER DEFAULT 3600,
    refresh_interval_seconds INTEGER DEFAULT 1800,
    is_auto_refresh BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE cache_config IS '缓存配置表';
COMMENT ON COLUMN cache_config.cache_name IS '缓存名称';
COMMENT ON COLUMN cache_config.cache_type IS '缓存类型：LABEL, PROTOCOL, DICT, QUERY_FIELD';
COMMENT ON COLUMN cache_config.max_size IS '最大缓存大小';
COMMENT ON COLUMN cache_config.ttl_seconds IS 'TTL时间（秒）';
COMMENT ON COLUMN cache_config.refresh_interval_seconds IS '刷新间隔（秒）';

-- ========================================
-- 索引创建
-- ========================================

-- 应用协议信息表索引
CREATE INDEX idx_application_protocol_info_port ON application_protocol_info(port);
CREATE INDEX idx_application_protocol_info_appid ON application_protocol_info(appid);
CREATE INDEX idx_application_protocol_info_ippro ON application_protocol_info(ippro);

-- 证书检测模型映射表索引
CREATE INDEX idx_cert_label_model_mapping_label_id ON cert_label_model_mapping(label_id);
CREATE INDEX idx_cert_label_model_mapping_model_id ON cert_label_model_mapping(model_id);

-- ES查询字段配置表索引
CREATE INDEX idx_es_query_field_config_name ON es_query_field_config(field_name);
CREATE INDEX idx_es_query_field_config_category ON es_query_field_config(field_category);
CREATE INDEX idx_es_query_field_config_type ON es_query_field_config(field_type);
CREATE INDEX idx_es_query_field_config_active ON es_query_field_config(is_active);

-- 下载搜索字段配置表索引
CREATE INDEX idx_download_search_field_config_name ON download_search_field_config(field_name);
CREATE INDEX idx_download_search_field_config_active ON download_search_field_config(is_active);

-- Nebula类型配置表索引
CREATE INDEX idx_nebula_type_config_name ON nebula_type_config(type_name);
CREATE INDEX idx_nebula_type_config_category ON nebula_type_config(type_category);
CREATE INDEX idx_nebula_type_config_vertex ON nebula_type_config(is_vertex);
CREATE INDEX idx_nebula_type_config_edge ON nebula_type_config(is_edge);

-- Nebula属性配置表索引
CREATE INDEX idx_nebula_property_config_name ON nebula_property_config(property_name);
CREATE INDEX idx_nebula_property_config_type ON nebula_property_config(property_type);
CREATE INDEX idx_nebula_property_config_indexed ON nebula_property_config(is_indexed);

-- 缓存配置表索引
CREATE INDEX idx_cache_config_name ON cache_config(cache_name);
CREATE INDEX idx_cache_config_type ON cache_config(cache_type);
CREATE INDEX idx_cache_config_active ON cache_config(is_active);

-- ========================================
-- 触发器创建
-- ========================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
CREATE TRIGGER update_application_protocol_info_updated_at BEFORE UPDATE ON application_protocol_info
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cert_label_model_mapping_updated_at BEFORE UPDATE ON cert_label_model_mapping
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_es_query_field_config_updated_at BEFORE UPDATE ON es_query_field_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_download_search_field_config_updated_at BEFORE UPDATE ON download_search_field_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_nebula_type_config_updated_at BEFORE UPDATE ON nebula_type_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_nebula_property_config_updated_at BEFORE UPDATE ON nebula_property_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cache_config_updated_at BEFORE UPDATE ON cache_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 初始化数据
-- ========================================

-- 插入基础ES查询字段配置
INSERT INTO es_query_field_config (field_name, field_display_name, field_type, field_category, is_searchable, is_aggregatable, is_sortable, field_description) VALUES
('src_ip', '源IP地址', 'IP', 'NETWORK', true, true, true, '网络流量源IP地址'),
('dst_ip', '目标IP地址', 'IP', 'NETWORK', true, true, true, '网络流量目标IP地址'),
('src_port', '源端口', 'INTEGER', 'NETWORK', true, true, true, '网络流量源端口'),
('dst_port', '目标端口', 'INTEGER', 'NETWORK', true, true, true, '网络流量目标端口'),
('protocol', '协议', 'STRING', 'NETWORK', true, true, true, '网络协议类型'),
('timestamp', '时间戳', 'DATE', 'TIME', true, false, true, '事件发生时间'),
('domain', '域名', 'STRING', 'DNS', true, true, true, 'DNS域名'),
('cert_hash', '证书哈希', 'STRING', 'CERTIFICATE', true, false, false, 'SSL证书哈希值'),
('session_id', '会话ID', 'STRING', 'SESSION', true, false, false, '网络会话标识'),
('fingerprint', '指纹', 'STRING', 'FINGERPRINT', true, false, false, '设备或应用指纹');

-- 插入基础下载搜索字段配置
INSERT INTO download_search_field_config (field_name, field_display_name, field_description) VALUES
('src_ip', '源IP地址', '网络流量源IP地址'),
('dst_ip', '目标IP地址', '网络流量目标IP地址'),
('src_port', '源端口', '网络流量源端口'),
('dst_port', '目标端口', '网络流量目标端口'),
('protocol', '协议', '网络协议类型'),
('timestamp', '时间戳', '事件发生时间'),
('domain', '域名', 'DNS域名'),
('cert_hash', '证书哈希', 'SSL证书哈希值'),
('session_id', '会话ID', '网络会话标识'),
('fingerprint', '指纹', '设备或应用指纹');

-- 插入基础Nebula类型配置
INSERT INTO nebula_type_config (type_name, type_display_name, type_category, type_description, is_vertex, is_edge) VALUES
('IP', 'IP地址', 'NETWORK', 'IP地址顶点', true, false),
('DOMAIN', '域名', 'DNS', '域名顶点', true, false),
('CERTIFICATE', '证书', 'SECURITY', 'SSL证书顶点', true, false),
('APPLICATION', '应用', 'APPLICATION', '应用程序顶点', true, false),
('SESSION', '会话', 'SESSION', '网络会话顶点', true, false),
('CONNECTS_TO', '连接到', 'NETWORK', 'IP连接关系', false, true),
('RESOLVES_TO', '解析到', 'DNS', 'DNS解析关系', false, true),
('USES_CERT', '使用证书', 'SECURITY', '证书使用关系', false, true);

-- 插入基础Nebula属性配置
INSERT INTO nebula_property_config (property_name, property_display_name, property_type, property_description, is_indexed, is_required) VALUES
('ip_address', 'IP地址', 'STRING', 'IP地址值', true, true),
('domain_name', '域名', 'STRING', '域名值', true, true),
('cert_hash', '证书哈希', 'STRING', '证书哈希值', true, true),
('app_name', '应用名称', 'STRING', '应用程序名称', true, true),
('session_id', '会话ID', 'STRING', '会话标识符', true, true),
('timestamp', '时间戳', 'TIMESTAMP', '时间戳', true, false),
('port', '端口', 'INT', '端口号', true, false),
('protocol', '协议', 'STRING', '协议类型', true, false);

-- 插入基础缓存配置
INSERT INTO cache_config (cache_name, cache_type, cache_description, max_size, ttl_seconds, refresh_interval_seconds) VALUES
('labelMetadata', 'LABEL', '标签元数据缓存', 50000, 3600, 1800),
('protocolMetadata', 'PROTOCOL', '协议元数据缓存', 10000, 7200, 3600),
('queryFieldMetadata', 'QUERY_FIELD', '查询字段元数据缓存', 5000, 3600, 1800),
('systemDictMetadata', 'DICT', '系统字典元数据缓存', 20000, 7200, 3600),
('cyberKillChainMetadata', 'DICT', 'Cyber Kill Chain元数据缓存', 1000, 86400, 43200);

-- ========================================
-- CDC配置（如果需要）
-- ========================================

-- 为需要CDC的表设置复制标识
ALTER TABLE application_protocol_info REPLICA IDENTITY FULL;
ALTER TABLE cert_label_model_mapping REPLICA IDENTITY FULL;
ALTER TABLE es_query_field_config REPLICA IDENTITY FULL;
ALTER TABLE download_search_field_config REPLICA IDENTITY FULL;
ALTER TABLE nebula_type_config REPLICA IDENTITY FULL;
ALTER TABLE nebula_property_config REPLICA IDENTITY FULL;
ALTER TABLE cache_config REPLICA IDENTITY FULL;
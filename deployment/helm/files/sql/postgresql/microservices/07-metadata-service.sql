-- ========================================
-- NTA 3.0 元数据服务数据库结构
-- ========================================
-- 创建时间: 2025-01-25
-- 描述: 元数据服务相关的所有表结构定义，包含系统元数据管理的核心功能
-- 数据库: PostgreSQL
--
-- 包括标签库管理、系统字典、协议管理、Cyber Kill Chain等核心功能
-- ========================================

-- ========================================
-- 枚举类型定义（系统元数据）
-- ========================================

-- 威胁类型枚举
DROP TYPE IF EXISTS threat_type_enum CASCADE;
CREATE TYPE threat_type_enum AS ENUM (
    'MALWARE',
    'APT',
    'BOTNET',
    'PHISHING',
    'C2',
    'MINING',
    'RANSOMWARE',
    'TROJAN',
    'BACKDOOR',
    'EXPLOIT',
    'OTHER'
);

-- 威胁等级枚举 (0-4级)
DROP TYPE IF EXISTS threat_level_enum CASCADE;
CREATE TYPE threat_level_enum AS ENUM ('NONE', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- 检测器类型枚举
DROP TYPE IF EXISTS detector_type_enum CASCADE;
CREATE TYPE detector_type_enum AS ENUM (
    'CERTIFICATE',
    'NETWORK',
    'DNS',
    'HTTP',
    'SSL',
    'FINGERPRINT',
    'C2',
    'APT',
    'MALWARE',
    'BRUTEFORCE',
    'ANOMALY'
);

-- 域名类型枚举
DROP TYPE IF EXISTS domain_type_enum CASCADE;
CREATE TYPE domain_type_enum AS ENUM (
    'MALICIOUS',
    'BENIGN',
    'SUSPICIOUS',
    'WHITELIST',
    'BLACKLIST',
    'CDN'
);

-- 指纹类型枚举
DROP TYPE IF EXISTS fingerprint_type_enum CASCADE;
CREATE TYPE fingerprint_type_enum AS ENUM (
    'HTTP',
    'SSL',
    'SSH',
    'FTP',
    'SMTP',
    'DNS',
    'TLS_JA3',
    'TLS_JA3S'
);

-- 标签来源枚举
DROP TYPE IF EXISTS label_source_enum CASCADE;
CREATE TYPE label_source_enum AS ENUM (
    'SYSTEM',    -- 系统内置标签
    'RULE',      -- 规则标签（来自检测规则）
    'USER'       -- 用户自定义标签
);

-- 标签目标类型枚举
DROP TYPE IF EXISTS label_target_type_enum CASCADE;
CREATE TYPE label_target_type_enum AS ENUM (
    'IP',           -- IP地址标签
    'APPLICATION',  -- 应用标签
    'DOMAIN',       -- 域名标签
    'CERTIFICATE',  -- 证书标签
    'SESSION',      -- 会话标签
    'FINGERPRINT',  -- 指纹标签
    'MAC',          -- MAC地址标签
    'PORT'          -- 端口标签
);

-- 标签分类枚举
DROP TYPE IF EXISTS label_category_enum CASCADE;
CREATE TYPE label_category_enum AS ENUM (
    -- 实际使用的标签类别
    'THREAT',                        -- 威胁（533个标签）
    'KNOWLEDGE_BASE',                -- 知识库（390个标签）
    'HIGH_DIMENSION_LABEL',          -- 高维度标签（87个标签）
    'PROXY',                         -- 代理（56个标签）
    'ENCRYPTED_TRAFFIC_DETECTION',   -- 加密流量检测（22个标签）
    'LEGITIMACY',                    -- 合法性（22个标签）
    'BASIC_ATTRIBUTES',              -- 基础属性（11个标签）
    'APT',                           -- APT（5个标签）
    'BEHAVIOR_DESCRIPTION',          -- 行为描述（3个标签）
    'BEHAVIOR_DETECTION_MODULE',     -- 行为检测模块（2个标签）
    'COMMAND_CONTROL',               -- 命令与控制（1个标签）
    'FINGERPRINT_DESCRIPTION',       -- 指纹描述（1个标签）
    -- 证书标签类别
    'SECURITY',                      -- 安全
    'TRUST',                         -- 信任
    'USAGE',                         -- 使用
    'VALIDATION',                    -- 验证
    'MALICIOUS',                     -- 恶意
    -- 预留类别
    'REMOTE_CONTROL',                -- 远程控制
    'FUNCTION_DESCRIPTION',          -- 功能描述
    'ATTACK_INTRUSION',              -- 攻击入侵
    'IDENTITY_SPOOFING',             -- 身份欺骗
    'CIRCUMVENTION',                 -- 翻墙上网
    'MAN_IN_MIDDLE',                 -- 中间人
    'PRIVATE_DETECTION'              -- 私有检测
);

-- Cyber Kill Chain枚举（基于Lockheed Martin Cyber Kill Chain模型）
DROP TYPE IF EXISTS cyber_kill_chain_enum CASCADE;
CREATE TYPE cyber_kill_chain_enum AS ENUM (
    'RECONNAISSANCE',        -- 侦察探测
    'WEAPONIZATION',         -- 武器化
    'DELIVERY',              -- 投递
    'EXPLOITATION',          -- 漏洞利用
    'INSTALLATION',          -- 安装植入
    'COMMAND_AND_CONTROL',   -- 命令控制
    'ACTIONS_ON_OBJECTIVES', -- 目标行动
    'OTHER',                 -- 其他
    'UNKNOWN'                -- 未知
);

COMMENT ON TYPE cyber_kill_chain_enum IS 'Cyber Kill Chain枚举，基于Lockheed Martin标准杀伤链模型';

-- 规则来源枚举
DROP TYPE IF EXISTS rule_source_enum CASCADE;
CREATE TYPE rule_source_enum AS ENUM (
    'SYSTEM',  -- 系统内置 (0)
    'USER'     -- 用户自定义 (1)
);

-- 采集模式枚举
DROP TYPE IF EXISTS capture_mode_enum CASCADE;
CREATE TYPE capture_mode_enum AS ENUM (
    'SINGLE_PACKET',        -- 单包 (1)
    'CONNECTION',           -- 连接 (2)
    'SOURCE_IP',           -- 获取源IP (3)
    'DEST_IP',             -- 获取目的IP (4)
    'SOURCE_IP_PORT',      -- 获取源IP+Port (5)
    'DEST_IP_PORT',        -- 获取目的IP+Port (6)
    'SOURCE_DEST_IP',      -- 获取源IP+目的IP (7)
    'DNS_CLIENT_IP',       -- 获取DNS 客户端IP (8)
    'DNS_A_RECORD_IP',     -- 获取DNS A记录端IP (9)
    'DNS_CLIENT_A_RECORD'  -- 获取DNS 客户端+A记录 (10)
);

-- 过滤条件枚举
DROP TYPE IF EXISTS filter_criteria_enum CASCADE;
CREATE TYPE filter_criteria_enum AS ENUM (
    'PORT',                -- 按端口过滤 (0)
    'INTERNET_PROTOCOL',   -- 按互联网协议过滤 (1)
    'SUBNET'               -- 按子网过滤 (2)
);

-- ========================================
-- 系统元数据核心表
-- ========================================

-- Cyber Kill Chain详情表（提供枚举值的详细信息）
DROP TABLE IF EXISTS cyber_kill_chain_details CASCADE;

CREATE TABLE cyber_kill_chain_details (
    cyber_kill_chain cyber_kill_chain_enum PRIMARY KEY,
    chinese_name VARCHAR(64) NOT NULL,
    english_name VARCHAR(64) NOT NULL,
    description TEXT DEFAULT '',
    typical_techniques TEXT[] DEFAULT '{}',
    defense_recommendations TEXT[] DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE cyber_kill_chain_details IS 'Cyber Kill Chain详情表，提供枚举值的详细信息';
COMMENT ON COLUMN cyber_kill_chain_details.cyber_kill_chain IS 'Cyber Kill Chain阶段枚举值（主键）';
COMMENT ON COLUMN cyber_kill_chain_details.chinese_name IS '阶段中文名称';
COMMENT ON COLUMN cyber_kill_chain_details.english_name IS '阶段英文名称';
COMMENT ON COLUMN cyber_kill_chain_details.description IS '阶段详细描述';
COMMENT ON COLUMN cyber_kill_chain_details.typical_techniques IS '典型攻击技术列表';
COMMENT ON COLUMN cyber_kill_chain_details.defense_recommendations IS '防护建议列表';

-- 检测器配置表
DROP TABLE IF EXISTS detector_config CASCADE;

CREATE TABLE detector_config (
    id SERIAL PRIMARY KEY,
    detector_name VARCHAR(255) NOT NULL UNIQUE,
    detector_type detector_type_enum NOT NULL,
    description TEXT,
    config_json JSONB,
    enabled BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE detector_config IS '检测器配置表';

-- 创建索引
CREATE INDEX idx_detector_config_name ON detector_config (detector_name);
CREATE INDEX idx_detector_config_type ON detector_config (detector_type);
CREATE INDEX idx_detector_config_enabled ON detector_config (enabled);

-- 网络协议表
DROP TABLE IF EXISTS network_protocols CASCADE;

CREATE TABLE network_protocols (
    id INTEGER PRIMARY KEY,
    protocol_name TEXT NOT NULL,
    display_name TEXT,
    category TEXT,
    description TEXT,
    protocol_type INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_protocols IS '网络协议表';

-- 互联网协议表
DROP TABLE IF EXISTS internet_protocols CASCADE;

CREATE TABLE internet_protocols (
    id INTEGER PRIMARY KEY,
    protocol_name TEXT NOT NULL,
    display_name TEXT,
    category TEXT,
    description TEXT,
    protocol_type INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE internet_protocols IS '互联网协议表';

-- 应用协议信息表
DROP TABLE IF EXISTS application_protocol_info CASCADE;

CREATE TABLE application_protocol_info (
    id SERIAL PRIMARY KEY,
    port INTEGER,
    appid INTEGER,
    ippro INTEGER,
    remark TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE application_protocol_info IS '应用协议信息表';
COMMENT ON COLUMN application_protocol_info.port IS '端口';
COMMENT ON COLUMN application_protocol_info.appid IS 'app ID';
COMMENT ON COLUMN application_protocol_info.ippro IS '17 udp 6 tcp';
COMMENT ON COLUMN application_protocol_info.remark IS '备注信息';

-- ========================================
-- 元数据查询字段配置表
-- ========================================

-- ES查询字段配置表（支持元数据查询功能）
DROP TABLE IF EXISTS es_query_field_config CASCADE;

CREATE TABLE es_query_field_config (
    id SERIAL PRIMARY KEY,
    field_name VARCHAR(255) NOT NULL UNIQUE,
    field_display_name VARCHAR(255) NOT NULL,
    field_type VARCHAR(50) NOT NULL,
    field_category VARCHAR(100),
    is_searchable BOOLEAN DEFAULT TRUE,
    is_aggregatable BOOLEAN DEFAULT FALSE,
    is_sortable BOOLEAN DEFAULT FALSE,
    field_description TEXT,
    example_value VARCHAR(500),
    validation_pattern VARCHAR(500),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE es_query_field_config IS 'ES查询字段配置表';
COMMENT ON COLUMN es_query_field_config.field_name IS '字段名称';
COMMENT ON COLUMN es_query_field_config.field_display_name IS '字段显示名称';
COMMENT ON COLUMN es_query_field_config.field_type IS '字段类型：STRING, INTEGER, BOOLEAN, DATE, IP';
COMMENT ON COLUMN es_query_field_config.field_category IS '字段分类';
COMMENT ON COLUMN es_query_field_config.is_searchable IS '是否可搜索';
COMMENT ON COLUMN es_query_field_config.is_aggregatable IS '是否可聚合';
COMMENT ON COLUMN es_query_field_config.is_sortable IS '是否可排序';

-- 下载搜索字段配置表
DROP TABLE IF EXISTS download_search_field_config CASCADE;

CREATE TABLE download_search_field_config (
    id SERIAL PRIMARY KEY,
    field_name VARCHAR(255) NOT NULL UNIQUE,
    field_display_name VARCHAR(255) NOT NULL,
    field_description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE download_search_field_config IS '下载搜索字段配置表';
COMMENT ON COLUMN download_search_field_config.field_name IS '字段名称';
COMMENT ON COLUMN download_search_field_config.field_display_name IS '字段显示名称';

-- ========================================
-- Nebula图数据库元数据配置表
-- ========================================

-- Nebula类型配置表
DROP TABLE IF EXISTS nebula_type_config CASCADE;

CREATE TABLE nebula_type_config (
    id SERIAL PRIMARY KEY,
    type_name VARCHAR(100) NOT NULL UNIQUE,
    type_display_name VARCHAR(200) NOT NULL,
    type_category VARCHAR(50),
    type_description TEXT,
    is_vertex BOOLEAN DEFAULT TRUE,
    is_edge BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE nebula_type_config IS 'Nebula类型配置表';
COMMENT ON COLUMN nebula_type_config.type_name IS '类型名称';
COMMENT ON COLUMN nebula_type_config.type_display_name IS '类型显示名称';
COMMENT ON COLUMN nebula_type_config.is_vertex IS '是否为顶点类型';
COMMENT ON COLUMN nebula_type_config.is_edge IS '是否为边类型';

-- Nebula属性配置表
DROP TABLE IF EXISTS nebula_property_config CASCADE;

CREATE TABLE nebula_property_config (
    id SERIAL PRIMARY KEY,
    property_name VARCHAR(100) NOT NULL,
    property_display_name VARCHAR(200) NOT NULL,
    property_type VARCHAR(50) NOT NULL,
    property_description TEXT,
    is_indexed BOOLEAN DEFAULT FALSE,
    is_required BOOLEAN DEFAULT FALSE,
    default_value VARCHAR(500),
    validation_pattern VARCHAR(500),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(property_name)
);

COMMENT ON TABLE nebula_property_config IS 'Nebula属性配置表';
COMMENT ON COLUMN nebula_property_config.property_name IS '属性名称';
COMMENT ON COLUMN nebula_property_config.property_display_name IS '属性显示名称';
COMMENT ON COLUMN nebula_property_config.property_type IS '属性类型：STRING, INT, DOUBLE, BOOL, TIMESTAMP';

-- ========================================
-- 统一标签管理表（标签库管理核心）
-- ========================================

-- 统一标签表
DROP TABLE IF EXISTS labels CASCADE;

CREATE TABLE labels (
    -- 基础标识字段
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,                    -- 标签名称（英文标识，如 "malicious_ip"）
    display_name VARCHAR(255) NOT NULL,            -- 显示名称（用户友好名称，如 "恶意IP"）
    description TEXT,                              -- 详细描述
    remark TEXT,                                   -- 备注信息

    -- 分类字段
    target_type label_target_type_enum NOT NULL,  -- 标签目标类型
    category label_category_enum,                  -- 标签类别（威胁、合法性等）
    source label_source_enum NOT NULL DEFAULT 'SYSTEM', -- 标签来源

    -- 评分字段（0-100）
    threat_level INTEGER DEFAULT 0 CHECK (
        threat_level >= 0 AND threat_level <= 100
    ),
    trust_level INTEGER DEFAULT 0 CHECK (
        trust_level >= 0 AND trust_level <= 100
    ),
    default_threat_level INTEGER DEFAULT 0 CHECK (
        default_threat_level >= 0 AND default_threat_level <= 100
    ),
    default_trust_level INTEGER DEFAULT 0 CHECK (
        default_trust_level >= 0 AND default_trust_level <= 100
    ),

    -- 业务字段
    cyber_kill_chain cyber_kill_chain_enum, -- Cyber Kill Chain阶段
    color VARCHAR(7) DEFAULT '#666666',     -- 标签颜色（十六进制，用于UI显示）
    sort_order INTEGER DEFAULT 0,          -- 排序顺序

    -- 状态字段
    is_active BOOLEAN NOT NULL DEFAULT true, -- 是否激活
    version INTEGER NOT NULL DEFAULT 1,      -- 版本号（乐观锁）

    -- 审计字段
    created_by INTEGER,                       -- 创建者用户ID
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    CONSTRAINT uk_labels_name_target_type UNIQUE (name, target_type), -- 同类型下标签名称唯一
    CONSTRAINT ck_labels_threat_trust CHECK (threat_level + trust_level <= 100) -- 威胁+信任不超过100
);

-- 创建索引
CREATE INDEX idx_labels_target_type ON labels (target_type);
CREATE INDEX idx_labels_category ON labels (category);
CREATE INDEX idx_labels_is_active ON labels (is_active);
CREATE INDEX idx_labels_created_at ON labels (created_at);
CREATE INDEX idx_labels_target_category ON labels (target_type, category);
CREATE INDEX idx_labels_source ON labels (source);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_labels_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    NEW.version = OLD.version + 1;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_labels_updated_at
    BEFORE UPDATE ON labels
    FOR EACH ROW
    EXECUTE FUNCTION update_labels_updated_at();

-- 表注释
COMMENT ON TABLE labels IS '统一标签表，支持多种目标类型的标签管理';
COMMENT ON COLUMN labels.id IS '标签唯一标识';
COMMENT ON COLUMN labels.name IS '标签名称（英文标识）';
COMMENT ON COLUMN labels.display_name IS '显示名称（用户友好名称）';
COMMENT ON COLUMN labels.description IS '标签详细描述';
COMMENT ON COLUMN labels.remark IS '备注信息';
COMMENT ON COLUMN labels.target_type IS '标签目标类型';
COMMENT ON COLUMN labels.category IS '标签类别';
COMMENT ON COLUMN labels.source IS '标签来源：SYSTEM-系统内置, USER-用户自定义';
COMMENT ON COLUMN labels.threat_level IS '威胁等级（0-100）';
COMMENT ON COLUMN labels.trust_level IS '信任等级（0-100）';
COMMENT ON COLUMN labels.default_threat_level IS '默认威胁等级（0-100）';
COMMENT ON COLUMN labels.default_trust_level IS '默认信任等级（0-100）';
COMMENT ON COLUMN labels.cyber_kill_chain IS '攻击阶段';

-- 统一标签关联表 - 支持所有类型的实体与标签的关联
DROP TABLE IF EXISTS entity_labels CASCADE;

CREATE TABLE entity_labels (
    id SERIAL PRIMARY KEY,
    entity_type label_target_type_enum NOT NULL, -- 实体类型（IP、CERTIFICATE、SESSION等）
    entity_id VARCHAR(255) NOT NULL,             -- 实体标识符（如IP地址、证书哈希、会话ID等）
    label_id INTEGER NOT NULL REFERENCES labels (id) ON DELETE CASCADE, -- 引用统一标签表
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,                          -- 创建者用户ID
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER,                          -- 更新者用户ID
    UNIQUE (entity_type, entity_id, label_id)   -- 同一实体不能重复关联同一个标签
);

COMMENT ON TABLE entity_labels IS '统一标签关联表 - 存储所有类型实体与标签的关联关系';
COMMENT ON COLUMN entity_labels.entity_type IS '实体类型，使用label_target_type_enum枚举';
COMMENT ON COLUMN entity_labels.entity_id IS '实体标识符，如IP地址、证书哈希、会话ID等';
COMMENT ON COLUMN entity_labels.label_id IS '标签ID，引用labels表';
COMMENT ON COLUMN entity_labels.created_by IS '关联创建者用户ID';
COMMENT ON COLUMN entity_labels.updated_by IS '关联更新者用户ID';

-- 创建索引以支持高效查询
CREATE INDEX idx_entity_labels_entity ON entity_labels (entity_type, entity_id);
CREATE INDEX idx_entity_labels_label_id ON entity_labels (label_id);
CREATE INDEX idx_entity_labels_created_at ON entity_labels (created_at);
CREATE INDEX idx_entity_labels_created_by ON entity_labels (created_by);
CREATE INDEX idx_entity_labels_type_label ON entity_labels (entity_type, label_id);

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建更新时间触发器
CREATE TRIGGER update_entity_labels_updated_at
    BEFORE UPDATE ON entity_labels
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 系统字典和元数据管理表
-- ========================================

-- 系统字典表
DROP TABLE IF EXISTS system_dictionary CASCADE;

CREATE TABLE system_dictionary (
    id SERIAL PRIMARY KEY,
    valset_id VARCHAR(255),
    val_id VARCHAR(255),
    value VARCHAR(255)
);

COMMENT ON TABLE system_dictionary IS '系统字典表';

-- 元数据定义表
DROP TABLE IF EXISTS metadata_definitions CASCADE;

CREATE TABLE metadata_definitions (
    id SERIAL PRIMARY KEY,
    metadata_key VARCHAR(255) UNIQUE NOT NULL,
    metadata_name VARCHAR(255) NOT NULL,
    data_type VARCHAR(50) NOT NULL,
    description TEXT,
    default_value TEXT,
    validation_rules JSONB,
    category VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE metadata_definitions IS '元数据定义表';
COMMENT ON COLUMN metadata_definitions.data_type IS '数据类型: STRING, INTEGER, BOOLEAN, JSON, ARRAY';

-- 元数据值表
DROP TABLE IF EXISTS metadata_values CASCADE;

CREATE TABLE metadata_values (
    id SERIAL PRIMARY KEY,
    metadata_key VARCHAR(255) NOT NULL,
    entity_type VARCHAR(100) NOT NULL,
    entity_id VARCHAR(255) NOT NULL,
    metadata_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (
        metadata_key,
        entity_type,
        entity_id
    )
);

COMMENT ON TABLE metadata_values IS '元数据值表';
COMMENT ON COLUMN metadata_values.entity_type IS '实体类型';
COMMENT ON COLUMN metadata_values.entity_id IS '实体ID';

-- 证书检测模型映射表
DROP TABLE IF EXISTS cert_label_model_mapping CASCADE;

CREATE TABLE cert_label_model_mapping (
    id BIGSERIAL PRIMARY KEY,
    label_id INTEGER REFERENCES labels (id),
    model_id BIGINT NOT NULL,
    model_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE cert_label_model_mapping IS '证书检测模型映射表';

-- ========================================
-- 元数据缓存管理表
-- ========================================

-- 缓存配置表
DROP TABLE IF EXISTS cache_config CASCADE;

CREATE TABLE cache_config (
    id SERIAL PRIMARY KEY,
    cache_name VARCHAR(100) NOT NULL UNIQUE,
    cache_type VARCHAR(50) NOT NULL,
    cache_description TEXT,
    max_size INTEGER DEFAULT 10000,
    ttl_seconds INTEGER DEFAULT 3600,
    refresh_interval_seconds INTEGER DEFAULT 1800,
    is_auto_refresh BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE cache_config IS '缓存配置表';
COMMENT ON COLUMN cache_config.cache_name IS '缓存名称';
COMMENT ON COLUMN cache_config.cache_type IS '缓存类型：LABEL, PROTOCOL, DICT, QUERY_FIELD';
COMMENT ON COLUMN cache_config.max_size IS '最大缓存大小';
COMMENT ON COLUMN cache_config.ttl_seconds IS 'TTL时间（秒）';
COMMENT ON COLUMN cache_config.refresh_interval_seconds IS '刷新间隔（秒）';

-- ========================================
-- 索引创建
-- ========================================

-- 应用协议信息表索引
CREATE INDEX idx_application_protocol_info_port ON application_protocol_info(port);
CREATE INDEX idx_application_protocol_info_appid ON application_protocol_info(appid);
CREATE INDEX idx_application_protocol_info_ippro ON application_protocol_info(ippro);

-- 证书检测模型映射表索引
CREATE INDEX idx_cert_label_model_mapping_label_id ON cert_label_model_mapping(label_id);
CREATE INDEX idx_cert_label_model_mapping_model_id ON cert_label_model_mapping(model_id);

-- ES查询字段配置表索引
CREATE INDEX idx_es_query_field_config_name ON es_query_field_config(field_name);
CREATE INDEX idx_es_query_field_config_category ON es_query_field_config(field_category);
CREATE INDEX idx_es_query_field_config_type ON es_query_field_config(field_type);
CREATE INDEX idx_es_query_field_config_active ON es_query_field_config(is_active);

-- 下载搜索字段配置表索引
CREATE INDEX idx_download_search_field_config_name ON download_search_field_config(field_name);
CREATE INDEX idx_download_search_field_config_active ON download_search_field_config(is_active);

-- Nebula类型配置表索引
CREATE INDEX idx_nebula_type_config_name ON nebula_type_config(type_name);
CREATE INDEX idx_nebula_type_config_category ON nebula_type_config(type_category);
CREATE INDEX idx_nebula_type_config_vertex ON nebula_type_config(is_vertex);
CREATE INDEX idx_nebula_type_config_edge ON nebula_type_config(is_edge);

-- Nebula属性配置表索引
CREATE INDEX idx_nebula_property_config_name ON nebula_property_config(property_name);
CREATE INDEX idx_nebula_property_config_type ON nebula_property_config(property_type);
CREATE INDEX idx_nebula_property_config_indexed ON nebula_property_config(is_indexed);

-- 缓存配置表索引
CREATE INDEX idx_cache_config_name ON cache_config(cache_name);
CREATE INDEX idx_cache_config_type ON cache_config(cache_type);
CREATE INDEX idx_cache_config_active ON cache_config(is_active);

-- ========================================
-- 触发器创建
-- ========================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
CREATE TRIGGER update_application_protocol_info_updated_at BEFORE UPDATE ON application_protocol_info
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cert_label_model_mapping_updated_at BEFORE UPDATE ON cert_label_model_mapping
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_es_query_field_config_updated_at BEFORE UPDATE ON es_query_field_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_download_search_field_config_updated_at BEFORE UPDATE ON download_search_field_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_nebula_type_config_updated_at BEFORE UPDATE ON nebula_type_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_nebula_property_config_updated_at BEFORE UPDATE ON nebula_property_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cache_config_updated_at BEFORE UPDATE ON cache_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 初始化数据
-- ========================================

-- 插入Cyber Kill Chain详情数据
INSERT INTO cyber_kill_chain_details (cyber_kill_chain, chinese_name, english_name, description, typical_techniques, defense_recommendations) VALUES
('RECONNAISSANCE', '侦察探测', 'Reconnaissance', '攻击者收集目标信息的阶段',
 ARRAY['网络扫描', '社会工程学', 'OSINT收集', '域名枚举'],
 ARRAY['网络监控', '访问控制', '信息泄露防护']),
('WEAPONIZATION', '武器化', 'Weaponization', '攻击者准备攻击载荷的阶段',
 ARRAY['恶意软件制作', '漏洞利用工具', '后门植入'],
 ARRAY['威胁情报', '沙箱检测', '代码审计']),
('DELIVERY', '投递', 'Delivery', '攻击者传递攻击载荷的阶段',
 ARRAY['钓鱼邮件', '水坑攻击', 'USB投递', '供应链攻击'],
 ARRAY['邮件安全', '端点防护', '网络隔离']),
('EXPLOITATION', '漏洞利用', 'Exploitation', '攻击者利用漏洞获得初始访问权限的阶段',
 ARRAY['缓冲区溢出', 'SQL注入', 'XSS攻击', '零日漏洞'],
 ARRAY['补丁管理', '漏洞扫描', '应用防火墙']),
('INSTALLATION', '安装植入', 'Installation', '攻击者在目标系统中安装恶意软件的阶段',
 ARRAY['后门安装', '持久化机制', '权限维持'],
 ARRAY['主机监控', '文件完整性检查', '行为分析']),
('COMMAND_AND_CONTROL', '命令控制', 'Command and Control', '攻击者建立与被感染系统通信渠道的阶段',
 ARRAY['C2通信', '隧道技术', '域名生成算法'],
 ARRAY['网络监控', 'DNS监控', '流量分析']),
('ACTIONS_ON_OBJECTIVES', '目标行动', 'Actions on Objectives', '攻击者执行最终攻击目标的阶段',
 ARRAY['数据窃取', '系统破坏', '横向移动', '权限提升'],
 ARRAY['数据防泄漏', '访问控制', '审计日志']),
('OTHER', '其他', 'Other', '其他未分类的攻击阶段',
 ARRAY['混合攻击', '新型攻击'],
 ARRAY['综合防护', '威胁狩猎']),
('UNKNOWN', '未知', 'Unknown', '未知或无法确定的攻击阶段',
 ARRAY['未知攻击'],
 ARRAY['持续监控', '异常检测']);

-- 插入基础网络协议数据
INSERT INTO network_protocols (id, protocol_name, display_name, category, description, protocol_type) VALUES
(1, 'TCP', 'TCP协议', 'TRANSPORT', '传输控制协议', 1),
(2, 'UDP', 'UDP协议', 'TRANSPORT', '用户数据报协议', 1),
(3, 'ICMP', 'ICMP协议', 'NETWORK', '互联网控制消息协议', 1),
(4, 'HTTP', 'HTTP协议', 'APPLICATION', '超文本传输协议', 1),
(5, 'HTTPS', 'HTTPS协议', 'APPLICATION', '安全超文本传输协议', 1),
(6, 'DNS', 'DNS协议', 'APPLICATION', '域名系统协议', 1),
(7, 'FTP', 'FTP协议', 'APPLICATION', '文件传输协议', 1),
(8, 'SSH', 'SSH协议', 'APPLICATION', '安全外壳协议', 1),
(9, 'SMTP', 'SMTP协议', 'APPLICATION', '简单邮件传输协议', 1),
(10, 'TLS', 'TLS协议', 'SECURITY', '传输层安全协议', 1);

-- 插入基础互联网协议数据
INSERT INTO internet_protocols (id, protocol_name, display_name, category, description, protocol_type) VALUES
(1, 'IPv4', 'IPv4协议', 'NETWORK', '互联网协议版本4', 1),
(2, 'IPv6', 'IPv6协议', 'NETWORK', '互联网协议版本6', 1),
(3, 'ARP', 'ARP协议', 'LINK', '地址解析协议', 1),
(4, 'DHCP', 'DHCP协议', 'APPLICATION', '动态主机配置协议', 1),
(5, 'NTP', 'NTP协议', 'APPLICATION', '网络时间协议', 1);

-- 插入基础应用协议信息
INSERT INTO application_protocol_info (port, appid, ippro, remark) VALUES
(80, 1, 6, 'HTTP协议默认端口'),
(443, 2, 6, 'HTTPS协议默认端口'),
(53, 3, 17, 'DNS协议默认端口（UDP）'),
(53, 3, 6, 'DNS协议默认端口（TCP）'),
(21, 4, 6, 'FTP协议默认端口'),
(22, 5, 6, 'SSH协议默认端口'),
(25, 6, 6, 'SMTP协议默认端口'),
(110, 7, 6, 'POP3协议默认端口'),
(143, 8, 6, 'IMAP协议默认端口'),
(993, 8, 6, 'IMAPS协议默认端口');

-- 插入基础ES查询字段配置
INSERT INTO es_query_field_config (field_name, field_display_name, field_type, field_category, is_searchable, is_aggregatable, is_sortable, field_description) VALUES
('src_ip', '源IP地址', 'IP', 'NETWORK', true, true, true, '网络流量源IP地址'),
('dst_ip', '目标IP地址', 'IP', 'NETWORK', true, true, true, '网络流量目标IP地址'),
('src_port', '源端口', 'INTEGER', 'NETWORK', true, true, true, '网络流量源端口'),
('dst_port', '目标端口', 'INTEGER', 'NETWORK', true, true, true, '网络流量目标端口'),
('protocol', '协议', 'STRING', 'NETWORK', true, true, true, '网络协议类型'),
('timestamp', '时间戳', 'DATE', 'TIME', true, false, true, '事件发生时间'),
('domain', '域名', 'STRING', 'DNS', true, true, true, 'DNS域名'),
('cert_hash', '证书哈希', 'STRING', 'CERTIFICATE', true, false, false, 'SSL证书哈希值'),
('session_id', '会话ID', 'STRING', 'SESSION', true, false, false, '网络会话标识'),
('fingerprint', '指纹', 'STRING', 'FINGERPRINT', true, false, false, '设备或应用指纹');

-- 插入基础下载搜索字段配置
INSERT INTO download_search_field_config (field_name, field_display_name, field_description) VALUES
('src_ip', '源IP地址', '网络流量源IP地址'),
('dst_ip', '目标IP地址', '网络流量目标IP地址'),
('src_port', '源端口', '网络流量源端口'),
('dst_port', '目标端口', '网络流量目标端口'),
('protocol', '协议', '网络协议类型'),
('timestamp', '时间戳', '事件发生时间'),
('domain', '域名', 'DNS域名'),
('cert_hash', '证书哈希', 'SSL证书哈希值'),
('session_id', '会话ID', '网络会话标识'),
('fingerprint', '指纹', '设备或应用指纹');

-- 插入基础Nebula类型配置
INSERT INTO nebula_type_config (type_name, type_display_name, type_category, type_description, is_vertex, is_edge) VALUES
('IP', 'IP地址', 'NETWORK', 'IP地址顶点', true, false),
('DOMAIN', '域名', 'DNS', '域名顶点', true, false),
('CERTIFICATE', '证书', 'SECURITY', 'SSL证书顶点', true, false),
('APPLICATION', '应用', 'APPLICATION', '应用程序顶点', true, false),
('SESSION', '会话', 'SESSION', '网络会话顶点', true, false),
('CONNECTS_TO', '连接到', 'NETWORK', 'IP连接关系', false, true),
('RESOLVES_TO', '解析到', 'DNS', 'DNS解析关系', false, true),
('USES_CERT', '使用证书', 'SECURITY', '证书使用关系', false, true);

-- 插入基础Nebula属性配置
INSERT INTO nebula_property_config (property_name, property_display_name, property_type, property_description, is_indexed, is_required) VALUES
('ip_address', 'IP地址', 'STRING', 'IP地址值', true, true),
('domain_name', '域名', 'STRING', '域名值', true, true),
('cert_hash', '证书哈希', 'STRING', '证书哈希值', true, true),
('app_name', '应用名称', 'STRING', '应用程序名称', true, true),
('session_id', '会话ID', 'STRING', '会话标识符', true, true),
('timestamp', '时间戳', 'TIMESTAMP', '时间戳', true, false),
('port', '端口', 'INT', '端口号', true, false),
('protocol', '协议', 'STRING', '协议类型', true, false);

-- 插入基础缓存配置
INSERT INTO cache_config (cache_name, cache_type, cache_description, max_size, ttl_seconds, refresh_interval_seconds) VALUES
('labelMetadata', 'LABEL', '标签元数据缓存', 50000, 3600, 1800),
('protocolMetadata', 'PROTOCOL', '协议元数据缓存', 10000, 7200, 3600),
('queryFieldMetadata', 'QUERY_FIELD', '查询字段元数据缓存', 5000, 3600, 1800),
('systemDictMetadata', 'DICT', '系统字典元数据缓存', 20000, 7200, 3600),
('cyberKillChainMetadata', 'DICT', 'Cyber Kill Chain元数据缓存', 1000, 86400, 43200);

-- ========================================
-- CDC配置（如果需要）
-- ========================================

-- 为需要CDC的表设置复制标识
-- 核心元数据表
ALTER TABLE cyber_kill_chain_details REPLICA IDENTITY FULL;
ALTER TABLE detector_config REPLICA IDENTITY FULL;
ALTER TABLE network_protocols REPLICA IDENTITY FULL;
ALTER TABLE internet_protocols REPLICA IDENTITY FULL;
ALTER TABLE application_protocol_info REPLICA IDENTITY FULL;
ALTER TABLE labels REPLICA IDENTITY FULL;
ALTER TABLE entity_labels REPLICA IDENTITY FULL;
ALTER TABLE system_dictionary REPLICA IDENTITY FULL;
ALTER TABLE metadata_definitions REPLICA IDENTITY FULL;
ALTER TABLE metadata_values REPLICA IDENTITY FULL;
ALTER TABLE cert_label_model_mapping REPLICA IDENTITY FULL;

-- 扩展功能表
ALTER TABLE es_query_field_config REPLICA IDENTITY FULL;
ALTER TABLE download_search_field_config REPLICA IDENTITY FULL;
ALTER TABLE nebula_type_config REPLICA IDENTITY FULL;
ALTER TABLE nebula_property_config REPLICA IDENTITY FULL;
ALTER TABLE cache_config REPLICA IDENTITY FULL;
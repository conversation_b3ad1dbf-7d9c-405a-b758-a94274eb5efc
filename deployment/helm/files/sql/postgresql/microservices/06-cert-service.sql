-- ========================================
-- Cert Service Database Schema
-- 证书服务数据库结构
-- ========================================

-- 创建数据库（如果需要）
-- CREATE DATABASE cert_service;
-- \c cert_service;

-- ========================================
-- 证书管理表
-- ========================================

-- 证书表
DROP TABLE IF EXISTS certificate CASCADE;
CREATE TABLE certificate (
    id SERIAL PRIMARY KEY,
    cert_id VARCHAR(100) UNIQUE NOT NULL,
    cert_name VARCHAR(200),
    cert_type VARCHAR(50) NOT NULL,
    cert_format VARCHAR(20) DEFAULT 'X509',
    subject_dn TEXT,
    issuer_dn TEXT,
    serial_number VARCHAR(100),
    fingerprint_sha1 VARCHAR(40),
    fingerprint_sha256 VARCHAR(64),
    fingerprint_md5 VARCHAR(32),
    public_key_algorithm VARCHAR(50),
    public_key_size INTEGER,
    signature_algorithm VARCHAR(100),
    version INTEGER,
    not_before TIMESTAMP,
    not_after TIMESTAMP,
    is_ca BOOLEAN DEFAULT FALSE,
    is_self_signed BOOLEAN DEFAULT FALSE,
    key_usage TEXT[],
    extended_key_usage TEXT[],
    subject_alt_names JSONB,
    cert_status VARCHAR(20) DEFAULT 'VALID',
    revocation_reason VARCHAR(50),
    revocation_date TIMESTAMP,
    cert_chain_length INTEGER DEFAULT 1,
    trust_level INTEGER DEFAULT 0,
    risk_score DECIMAL(5,2) DEFAULT 0.0,
    cert_data TEXT,
    cert_pem TEXT,
    source_info JSONB,
    validation_results JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE certificate IS '证书表';
COMMENT ON COLUMN certificate.id IS '证书记录ID';
COMMENT ON COLUMN certificate.cert_id IS '证书唯一标识';
COMMENT ON COLUMN certificate.cert_name IS '证书名称';
COMMENT ON COLUMN certificate.cert_type IS '证书类型：SSL, TLS, CODE_SIGNING, EMAIL, ROOT_CA, INTERMEDIATE_CA';
COMMENT ON COLUMN certificate.cert_format IS '证书格式：X509, PKCS7, PKCS12';
COMMENT ON COLUMN certificate.subject_dn IS '主题DN';
COMMENT ON COLUMN certificate.issuer_dn IS '颁发者DN';
COMMENT ON COLUMN certificate.serial_number IS '序列号';
COMMENT ON COLUMN certificate.fingerprint_sha1 IS 'SHA1指纹';
COMMENT ON COLUMN certificate.fingerprint_sha256 IS 'SHA256指纹';
COMMENT ON COLUMN certificate.fingerprint_md5 IS 'MD5指纹';
COMMENT ON COLUMN certificate.public_key_algorithm IS '公钥算法';
COMMENT ON COLUMN certificate.public_key_size IS '公钥长度';
COMMENT ON COLUMN certificate.signature_algorithm IS '签名算法';
COMMENT ON COLUMN certificate.version IS '证书版本';
COMMENT ON COLUMN certificate.not_before IS '有效期开始时间';
COMMENT ON COLUMN certificate.not_after IS '有效期结束时间';
COMMENT ON COLUMN certificate.is_ca IS '是否为CA证书';
COMMENT ON COLUMN certificate.is_self_signed IS '是否为自签名证书';
COMMENT ON COLUMN certificate.key_usage IS '密钥用途';
COMMENT ON COLUMN certificate.extended_key_usage IS '扩展密钥用途';
COMMENT ON COLUMN certificate.subject_alt_names IS '主题备用名称';
COMMENT ON COLUMN certificate.cert_status IS '证书状态：VALID, EXPIRED, REVOKED, INVALID, UNKNOWN';
COMMENT ON COLUMN certificate.revocation_reason IS '吊销原因';
COMMENT ON COLUMN certificate.revocation_date IS '吊销日期';
COMMENT ON COLUMN certificate.cert_chain_length IS '证书链长度';
COMMENT ON COLUMN certificate.trust_level IS '信任级别：0-不信任，1-低，2-中，3-高，4-完全信任';
COMMENT ON COLUMN certificate.risk_score IS '风险评分';
COMMENT ON COLUMN certificate.cert_data IS '证书原始数据';
COMMENT ON COLUMN certificate.cert_pem IS '证书PEM格式';
COMMENT ON COLUMN certificate.source_info IS '来源信息';
COMMENT ON COLUMN certificate.validation_results IS '验证结果';
COMMENT ON COLUMN certificate.created_at IS '创建时间';
COMMENT ON COLUMN certificate.updated_at IS '更新时间';
COMMENT ON COLUMN certificate.created_by IS '创建人';
COMMENT ON COLUMN certificate.updated_by IS '更新人';

-- 证书链表
DROP TABLE IF EXISTS certificate_chain CASCADE;
CREATE TABLE certificate_chain (
    id SERIAL PRIMARY KEY,
    chain_id VARCHAR(100) UNIQUE NOT NULL,
    leaf_cert_id VARCHAR(100) NOT NULL,
    intermediate_cert_ids TEXT[],
    root_cert_id VARCHAR(100),
    chain_length INTEGER NOT NULL,
    chain_status VARCHAR(20) DEFAULT 'VALID',
    validation_path JSONB,
    trust_anchor VARCHAR(100),
    validation_errors JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (leaf_cert_id) REFERENCES certificate(cert_id) ON DELETE CASCADE
);

COMMENT ON TABLE certificate_chain IS '证书链表';
COMMENT ON COLUMN certificate_chain.id IS '证书链记录ID';
COMMENT ON COLUMN certificate_chain.chain_id IS '证书链唯一标识';
COMMENT ON COLUMN certificate_chain.leaf_cert_id IS '叶子证书ID';
COMMENT ON COLUMN certificate_chain.intermediate_cert_ids IS '中间证书ID列表';
COMMENT ON COLUMN certificate_chain.root_cert_id IS '根证书ID';
COMMENT ON COLUMN certificate_chain.chain_length IS '证书链长度';
COMMENT ON COLUMN certificate_chain.chain_status IS '证书链状态：VALID, INVALID, INCOMPLETE, EXPIRED';
COMMENT ON COLUMN certificate_chain.validation_path IS '验证路径';
COMMENT ON COLUMN certificate_chain.trust_anchor IS '信任锚点';
COMMENT ON COLUMN certificate_chain.validation_errors IS '验证错误';
COMMENT ON COLUMN certificate_chain.created_at IS '创建时间';
COMMENT ON COLUMN certificate_chain.updated_at IS '更新时间';

-- ========================================
-- 证书检测表
-- ========================================

-- 证书检测记录表
DROP TABLE IF EXISTS certificate_detection CASCADE;
CREATE TABLE certificate_detection (
    id SERIAL PRIMARY KEY,
    detection_id VARCHAR(100) UNIQUE NOT NULL,
    cert_id VARCHAR(100) NOT NULL,
    detection_time TIMESTAMP NOT NULL,
    detection_source VARCHAR(100),
    detection_method VARCHAR(50),
    src_ip INET,
    dst_ip INET,
    src_port INTEGER,
    dst_port INTEGER,
    protocol VARCHAR(20),
    service_name VARCHAR(100),
    connection_info JSONB,
    tls_version VARCHAR(20),
    cipher_suite VARCHAR(100),
    sni_hostname VARCHAR(200),
    cert_position VARCHAR(20),
    cert_usage_context VARCHAR(100),
    anomaly_indicators JSONB,
    threat_indicators JSONB,
    risk_assessment JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (cert_id) REFERENCES certificate(cert_id) ON DELETE CASCADE
);

COMMENT ON TABLE certificate_detection IS '证书检测记录表';
COMMENT ON COLUMN certificate_detection.id IS '检测记录ID';
COMMENT ON COLUMN certificate_detection.detection_id IS '检测唯一标识';
COMMENT ON COLUMN certificate_detection.cert_id IS '证书ID';
COMMENT ON COLUMN certificate_detection.detection_time IS '检测时间';
COMMENT ON COLUMN certificate_detection.detection_source IS '检测来源';
COMMENT ON COLUMN certificate_detection.detection_method IS '检测方法：PASSIVE, ACTIVE, SCAN';
COMMENT ON COLUMN certificate_detection.src_ip IS '源IP地址';
COMMENT ON COLUMN certificate_detection.dst_ip IS '目标IP地址';
COMMENT ON COLUMN certificate_detection.src_port IS '源端口';
COMMENT ON COLUMN certificate_detection.dst_port IS '目标端口';
COMMENT ON COLUMN certificate_detection.protocol IS '协议类型';
COMMENT ON COLUMN certificate_detection.service_name IS '服务名称';
COMMENT ON COLUMN certificate_detection.connection_info IS '连接信息';
COMMENT ON COLUMN certificate_detection.tls_version IS 'TLS版本';
COMMENT ON COLUMN certificate_detection.cipher_suite IS '加密套件';
COMMENT ON COLUMN certificate_detection.sni_hostname IS 'SNI主机名';
COMMENT ON COLUMN certificate_detection.cert_position IS '证书位置：LEAF, INTERMEDIATE, ROOT';
COMMENT ON COLUMN certificate_detection.cert_usage_context IS '证书使用上下文';
COMMENT ON COLUMN certificate_detection.anomaly_indicators IS '异常指标';
COMMENT ON COLUMN certificate_detection.threat_indicators IS '威胁指标';
COMMENT ON COLUMN certificate_detection.risk_assessment IS '风险评估';
COMMENT ON COLUMN certificate_detection.created_at IS '创建时间';

-- 证书异常检测表
DROP TABLE IF EXISTS certificate_anomaly CASCADE;
CREATE TABLE certificate_anomaly (
    id SERIAL PRIMARY KEY,
    anomaly_id VARCHAR(100) UNIQUE NOT NULL,
    cert_id VARCHAR(100) NOT NULL,
    detection_id VARCHAR(100),
    anomaly_type VARCHAR(50) NOT NULL,
    anomaly_category VARCHAR(50),
    severity_level INTEGER DEFAULT 1,
    anomaly_description TEXT,
    anomaly_details JSONB,
    confidence_score DECIMAL(5,2) DEFAULT 0.0,
    baseline_comparison JSONB,
    statistical_analysis JSONB,
    ml_analysis JSONB,
    remediation_suggestions JSONB,
    status VARCHAR(20) DEFAULT 'OPEN',
    analyst_notes TEXT,
    resolution_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    FOREIGN KEY (cert_id) REFERENCES certificate(cert_id) ON DELETE CASCADE,
    FOREIGN KEY (detection_id) REFERENCES certificate_detection(detection_id) ON DELETE SET NULL
);

COMMENT ON TABLE certificate_anomaly IS '证书异常检测表';
COMMENT ON COLUMN certificate_anomaly.id IS '异常记录ID';
COMMENT ON COLUMN certificate_anomaly.anomaly_id IS '异常唯一标识';
COMMENT ON COLUMN certificate_anomaly.cert_id IS '证书ID';
COMMENT ON COLUMN certificate_anomaly.detection_id IS '检测记录ID';
COMMENT ON COLUMN certificate_anomaly.anomaly_type IS '异常类型：EXPIRED, WEAK_KEY, SUSPICIOUS_ISSUER, INVALID_CHAIN, REVOKED';
COMMENT ON COLUMN certificate_anomaly.anomaly_category IS '异常分类：SECURITY, COMPLIANCE, OPERATIONAL';
COMMENT ON COLUMN certificate_anomaly.severity_level IS '严重级别：1-低，2-中，3-高，4-严重';
COMMENT ON COLUMN certificate_anomaly.anomaly_description IS '异常描述';
COMMENT ON COLUMN certificate_anomaly.anomaly_details IS '异常详情';
COMMENT ON COLUMN certificate_anomaly.confidence_score IS '置信度评分';
COMMENT ON COLUMN certificate_anomaly.baseline_comparison IS '基线对比';
COMMENT ON COLUMN certificate_anomaly.statistical_analysis IS '统计分析';
COMMENT ON COLUMN certificate_anomaly.ml_analysis IS '机器学习分析';
COMMENT ON COLUMN certificate_anomaly.remediation_suggestions IS '修复建议';
COMMENT ON COLUMN certificate_anomaly.status IS '状态：OPEN, IN_PROGRESS, RESOLVED, CLOSED, FALSE_POSITIVE';
COMMENT ON COLUMN certificate_anomaly.analyst_notes IS '分析师备注';
COMMENT ON COLUMN certificate_anomaly.resolution_time IS '解决时间';
COMMENT ON COLUMN certificate_anomaly.created_at IS '创建时间';
COMMENT ON COLUMN certificate_anomaly.updated_at IS '更新时间';
COMMENT ON COLUMN certificate_anomaly.created_by IS '创建人';
COMMENT ON COLUMN certificate_anomaly.updated_by IS '更新人';

-- ========================================
-- 威胁检测表
-- ========================================

-- 威胁检测规则表
DROP TABLE IF EXISTS threat_detection_rule CASCADE;
CREATE TABLE threat_detection_rule (
    id SERIAL PRIMARY KEY,
    rule_id VARCHAR(100) UNIQUE NOT NULL,
    rule_name VARCHAR(200) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    rule_category VARCHAR(50),
    rule_description TEXT,
    rule_logic JSONB NOT NULL,
    rule_conditions JSONB,
    severity_level INTEGER DEFAULT 1,
    confidence_threshold DECIMAL(5,2) DEFAULT 0.5,
    rule_status VARCHAR(20) DEFAULT 'ACTIVE',
    rule_version VARCHAR(20) DEFAULT '1.0',
    last_updated_version VARCHAR(20),
    rule_tags JSONB,
    false_positive_rate DECIMAL(5,4),
    detection_rate DECIMAL(5,4),
    performance_metrics JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE threat_detection_rule IS '威胁检测规则表';
COMMENT ON COLUMN threat_detection_rule.id IS '规则记录ID';
COMMENT ON COLUMN threat_detection_rule.rule_id IS '规则唯一标识';
COMMENT ON COLUMN threat_detection_rule.rule_name IS '规则名称';
COMMENT ON COLUMN threat_detection_rule.rule_type IS '规则类型：SIGNATURE, BEHAVIORAL, STATISTICAL, ML';
COMMENT ON COLUMN threat_detection_rule.rule_category IS '规则分类：MALWARE, PHISHING, C2, EXFILTRATION';
COMMENT ON COLUMN threat_detection_rule.rule_description IS '规则描述';
COMMENT ON COLUMN threat_detection_rule.rule_logic IS '规则逻辑';
COMMENT ON COLUMN threat_detection_rule.rule_conditions IS '规则条件';
COMMENT ON COLUMN threat_detection_rule.severity_level IS '严重级别：1-低，2-中，3-高，4-严重';
COMMENT ON COLUMN threat_detection_rule.confidence_threshold IS '置信度阈值';
COMMENT ON COLUMN threat_detection_rule.rule_status IS '规则状态：ACTIVE, INACTIVE, TESTING, DEPRECATED';
COMMENT ON COLUMN threat_detection_rule.rule_version IS '规则版本';
COMMENT ON COLUMN threat_detection_rule.last_updated_version IS '最后更新版本';
COMMENT ON COLUMN threat_detection_rule.rule_tags IS '规则标签';
COMMENT ON COLUMN threat_detection_rule.false_positive_rate IS '误报率';
COMMENT ON COLUMN threat_detection_rule.detection_rate IS '检出率';
COMMENT ON COLUMN threat_detection_rule.performance_metrics IS '性能指标';
COMMENT ON COLUMN threat_detection_rule.created_at IS '创建时间';
COMMENT ON COLUMN threat_detection_rule.updated_at IS '更新时间';
COMMENT ON COLUMN threat_detection_rule.created_by IS '创建人';
COMMENT ON COLUMN threat_detection_rule.updated_by IS '更新人';

-- 威胁检测结果表
DROP TABLE IF EXISTS threat_detection_result CASCADE;
CREATE TABLE threat_detection_result (
    id SERIAL PRIMARY KEY,
    result_id VARCHAR(100) UNIQUE NOT NULL,
    rule_id VARCHAR(100) NOT NULL,
    cert_id VARCHAR(100),
    detection_id VARCHAR(100),
    detection_time TIMESTAMP NOT NULL,
    threat_type VARCHAR(50),
    threat_category VARCHAR(50),
    threat_name VARCHAR(200),
    threat_description TEXT,
    severity_level INTEGER DEFAULT 1,
    confidence_score DECIMAL(5,2) DEFAULT 0.0,
    risk_score DECIMAL(5,2) DEFAULT 0.0,
    evidence JSONB,
    indicators JSONB,
    context_info JSONB,
    mitigation_actions JSONB,
    status VARCHAR(20) DEFAULT 'NEW',
    analyst_verdict VARCHAR(50),
    analyst_notes TEXT,
    resolution_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    FOREIGN KEY (rule_id) REFERENCES threat_detection_rule(rule_id) ON DELETE CASCADE,
    FOREIGN KEY (cert_id) REFERENCES certificate(cert_id) ON DELETE SET NULL,
    FOREIGN KEY (detection_id) REFERENCES certificate_detection(detection_id) ON DELETE SET NULL
);

COMMENT ON TABLE threat_detection_result IS '威胁检测结果表';
COMMENT ON COLUMN threat_detection_result.id IS '检测结果ID';
COMMENT ON COLUMN threat_detection_result.result_id IS '结果唯一标识';
COMMENT ON COLUMN threat_detection_result.rule_id IS '规则ID';
COMMENT ON COLUMN threat_detection_result.cert_id IS '证书ID';
COMMENT ON COLUMN threat_detection_result.detection_id IS '检测记录ID';
COMMENT ON COLUMN threat_detection_result.detection_time IS '检测时间';
COMMENT ON COLUMN threat_detection_result.threat_type IS '威胁类型';
COMMENT ON COLUMN threat_detection_result.threat_category IS '威胁分类';
COMMENT ON COLUMN threat_detection_result.threat_name IS '威胁名称';
COMMENT ON COLUMN threat_detection_result.threat_description IS '威胁描述';
COMMENT ON COLUMN threat_detection_result.severity_level IS '严重级别：1-低，2-中，3-高，4-严重';
COMMENT ON COLUMN threat_detection_result.confidence_score IS '置信度评分';
COMMENT ON COLUMN threat_detection_result.risk_score IS '风险评分';
COMMENT ON COLUMN threat_detection_result.evidence IS '证据信息';
COMMENT ON COLUMN threat_detection_result.indicators IS '威胁指标';
COMMENT ON COLUMN threat_detection_result.context_info IS '上下文信息';
COMMENT ON COLUMN threat_detection_result.mitigation_actions IS '缓解措施';
COMMENT ON COLUMN threat_detection_result.status IS '状态：NEW, INVESTIGATING, CONFIRMED, FALSE_POSITIVE, RESOLVED';
COMMENT ON COLUMN threat_detection_result.analyst_verdict IS '分析师判定';
COMMENT ON COLUMN threat_detection_result.analyst_notes IS '分析师备注';
COMMENT ON COLUMN threat_detection_result.resolution_time IS '解决时间';
COMMENT ON COLUMN threat_detection_result.created_at IS '创建时间';
COMMENT ON COLUMN threat_detection_result.updated_at IS '更新时间';
COMMENT ON COLUMN threat_detection_result.created_by IS '创建人';
COMMENT ON COLUMN threat_detection_result.updated_by IS '更新人';

-- ========================================
-- 证书统计表
-- ========================================

-- 证书统计表
DROP TABLE IF EXISTS certificate_statistics CASCADE;
CREATE TABLE certificate_statistics (
    id SERIAL PRIMARY KEY,
    stat_date DATE NOT NULL,
    stat_hour INTEGER,
    cert_type VARCHAR(50),
    issuer_organization VARCHAR(200),
    total_certificates INTEGER DEFAULT 0,
    valid_certificates INTEGER DEFAULT 0,
    expired_certificates INTEGER DEFAULT 0,
    revoked_certificates INTEGER DEFAULT 0,
    self_signed_certificates INTEGER DEFAULT 0,
    weak_key_certificates INTEGER DEFAULT 0,
    expiring_soon_certificates INTEGER DEFAULT 0,
    new_certificates INTEGER DEFAULT 0,
    anomaly_count INTEGER DEFAULT 0,
    threat_count INTEGER DEFAULT 0,
    avg_key_size DECIMAL(10,2),
    avg_validity_days DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(stat_date, stat_hour, cert_type, issuer_organization)
);

COMMENT ON TABLE certificate_statistics IS '证书统计表';
COMMENT ON COLUMN certificate_statistics.id IS '统计ID';
COMMENT ON COLUMN certificate_statistics.stat_date IS '统计日期';
COMMENT ON COLUMN certificate_statistics.stat_hour IS '统计小时（0-23）';
COMMENT ON COLUMN certificate_statistics.cert_type IS '证书类型';
COMMENT ON COLUMN certificate_statistics.issuer_organization IS '颁发机构';
COMMENT ON COLUMN certificate_statistics.total_certificates IS '总证书数';
COMMENT ON COLUMN certificate_statistics.valid_certificates IS '有效证书数';
COMMENT ON COLUMN certificate_statistics.expired_certificates IS '过期证书数';
COMMENT ON COLUMN certificate_statistics.revoked_certificates IS '吊销证书数';
COMMENT ON COLUMN certificate_statistics.self_signed_certificates IS '自签名证书数';
COMMENT ON COLUMN certificate_statistics.weak_key_certificates IS '弱密钥证书数';
COMMENT ON COLUMN certificate_statistics.expiring_soon_certificates IS '即将过期证书数';
COMMENT ON COLUMN certificate_statistics.new_certificates IS '新增证书数';
COMMENT ON COLUMN certificate_statistics.anomaly_count IS '异常数量';
COMMENT ON COLUMN certificate_statistics.threat_count IS '威胁数量';
COMMENT ON COLUMN certificate_statistics.avg_key_size IS '平均密钥长度';
COMMENT ON COLUMN certificate_statistics.avg_validity_days IS '平均有效期天数';
COMMENT ON COLUMN certificate_statistics.created_at IS '创建时间';
COMMENT ON COLUMN certificate_statistics.updated_at IS '更新时间';

-- ========================================
-- 索引创建
-- ========================================

-- 证书表索引
CREATE INDEX idx_certificate_cert_id ON certificate(cert_id);
CREATE INDEX idx_certificate_fingerprint_sha256 ON certificate(fingerprint_sha256);
CREATE INDEX idx_certificate_fingerprint_sha1 ON certificate(fingerprint_sha1);
CREATE INDEX idx_certificate_subject_dn ON certificate(subject_dn);
CREATE INDEX idx_certificate_issuer_dn ON certificate(issuer_dn);
CREATE INDEX idx_certificate_serial_number ON certificate(serial_number);
CREATE INDEX idx_certificate_cert_type ON certificate(cert_type);
CREATE INDEX idx_certificate_cert_status ON certificate(cert_status);
CREATE INDEX idx_certificate_not_before ON certificate(not_before);
CREATE INDEX idx_certificate_not_after ON certificate(not_after);
CREATE INDEX idx_certificate_is_ca ON certificate(is_ca);
CREATE INDEX idx_certificate_is_self_signed ON certificate(is_self_signed);
CREATE INDEX idx_certificate_trust_level ON certificate(trust_level);
CREATE INDEX idx_certificate_risk_score ON certificate(risk_score);
CREATE INDEX idx_certificate_created_at ON certificate(created_at);

-- 证书链表索引
CREATE INDEX idx_certificate_chain_chain_id ON certificate_chain(chain_id);
CREATE INDEX idx_certificate_chain_leaf_cert_id ON certificate_chain(leaf_cert_id);
CREATE INDEX idx_certificate_chain_root_cert_id ON certificate_chain(root_cert_id);
CREATE INDEX idx_certificate_chain_status ON certificate_chain(chain_status);
CREATE INDEX idx_certificate_chain_length ON certificate_chain(chain_length);

-- 证书检测记录表索引
CREATE INDEX idx_certificate_detection_detection_id ON certificate_detection(detection_id);
CREATE INDEX idx_certificate_detection_cert_id ON certificate_detection(cert_id);
CREATE INDEX idx_certificate_detection_time ON certificate_detection(detection_time);
CREATE INDEX idx_certificate_detection_src_ip ON certificate_detection(src_ip);
CREATE INDEX idx_certificate_detection_dst_ip ON certificate_detection(dst_ip);
CREATE INDEX idx_certificate_detection_method ON certificate_detection(detection_method);
CREATE INDEX idx_certificate_detection_service ON certificate_detection(service_name);
CREATE INDEX idx_certificate_detection_sni ON certificate_detection(sni_hostname);

-- 证书异常检测表索引
CREATE INDEX idx_certificate_anomaly_anomaly_id ON certificate_anomaly(anomaly_id);
CREATE INDEX idx_certificate_anomaly_cert_id ON certificate_anomaly(cert_id);
CREATE INDEX idx_certificate_anomaly_detection_id ON certificate_anomaly(detection_id);
CREATE INDEX idx_certificate_anomaly_type ON certificate_anomaly(anomaly_type);
CREATE INDEX idx_certificate_anomaly_category ON certificate_anomaly(anomaly_category);
CREATE INDEX idx_certificate_anomaly_severity ON certificate_anomaly(severity_level);
CREATE INDEX idx_certificate_anomaly_status ON certificate_anomaly(status);
CREATE INDEX idx_certificate_anomaly_confidence ON certificate_anomaly(confidence_score);
CREATE INDEX idx_certificate_anomaly_created_at ON certificate_anomaly(created_at);

-- 威胁检测规则表索引
CREATE INDEX idx_threat_detection_rule_rule_id ON threat_detection_rule(rule_id);
CREATE INDEX idx_threat_detection_rule_name ON threat_detection_rule(rule_name);
CREATE INDEX idx_threat_detection_rule_type ON threat_detection_rule(rule_type);
CREATE INDEX idx_threat_detection_rule_category ON threat_detection_rule(rule_category);
CREATE INDEX idx_threat_detection_rule_status ON threat_detection_rule(rule_status);
CREATE INDEX idx_threat_detection_rule_severity ON threat_detection_rule(severity_level);
CREATE INDEX idx_threat_detection_rule_version ON threat_detection_rule(rule_version);

-- 威胁检测结果表索引
CREATE INDEX idx_threat_detection_result_result_id ON threat_detection_result(result_id);
CREATE INDEX idx_threat_detection_result_rule_id ON threat_detection_result(rule_id);
CREATE INDEX idx_threat_detection_result_cert_id ON threat_detection_result(cert_id);
CREATE INDEX idx_threat_detection_result_detection_id ON threat_detection_result(detection_id);
CREATE INDEX idx_threat_detection_result_time ON threat_detection_result(detection_time);
CREATE INDEX idx_threat_detection_result_type ON threat_detection_result(threat_type);
CREATE INDEX idx_threat_detection_result_category ON threat_detection_result(threat_category);
CREATE INDEX idx_threat_detection_result_severity ON threat_detection_result(severity_level);
CREATE INDEX idx_threat_detection_result_status ON threat_detection_result(status);
CREATE INDEX idx_threat_detection_result_confidence ON threat_detection_result(confidence_score);
CREATE INDEX idx_threat_detection_result_risk ON threat_detection_result(risk_score);

-- 证书统计表索引
CREATE INDEX idx_certificate_statistics_date ON certificate_statistics(stat_date);
CREATE INDEX idx_certificate_statistics_type ON certificate_statistics(cert_type);
CREATE INDEX idx_certificate_statistics_issuer ON certificate_statistics(issuer_organization);
CREATE INDEX idx_certificate_statistics_date_hour ON certificate_statistics(stat_date, stat_hour);

-- ========================================
-- 触发器创建
-- ========================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
CREATE TRIGGER update_certificate_updated_at BEFORE UPDATE ON certificate
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_certificate_chain_updated_at BEFORE UPDATE ON certificate_chain
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_certificate_anomaly_updated_at BEFORE UPDATE ON certificate_anomaly
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_threat_detection_rule_updated_at BEFORE UPDATE ON threat_detection_rule
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_threat_detection_result_updated_at BEFORE UPDATE ON threat_detection_result
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_certificate_statistics_updated_at BEFORE UPDATE ON certificate_statistics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 初始化数据
-- ========================================

-- 插入示例威胁检测规则
INSERT INTO threat_detection_rule (rule_id, rule_name, rule_type, rule_category, rule_description, rule_logic, severity_level, created_by) VALUES
('RULE_EXPIRED_CERT', '过期证书检测', 'SIGNATURE', 'COMPLIANCE', '检测使用过期证书的连接', 
'{"condition": "cert_status = EXPIRED", "action": "alert"}', 2, 'system'),
('RULE_WEAK_KEY', '弱密钥检测', 'SIGNATURE', 'SECURITY', '检测使用弱密钥的证书', 
'{"condition": "public_key_size < 2048", "action": "alert"}', 3, 'system'),
('RULE_SELF_SIGNED', '自签名证书检测', 'SIGNATURE', 'SECURITY', '检测自签名证书的使用', 
'{"condition": "is_self_signed = true", "action": "alert"}', 2, 'system');

-- ========================================
-- CDC配置（如果需要）
-- ========================================

-- 为需要CDC的表设置复制标识
ALTER TABLE certificate REPLICA IDENTITY FULL;
ALTER TABLE certificate_chain REPLICA IDENTITY FULL;
ALTER TABLE certificate_detection REPLICA IDENTITY FULL;
ALTER TABLE certificate_anomaly REPLICA IDENTITY FULL;
ALTER TABLE threat_detection_rule REPLICA IDENTITY FULL;
ALTER TABLE threat_detection_result REPLICA IDENTITY FULL;
ALTER TABLE certificate_statistics REPLICA IDENTITY FULL;
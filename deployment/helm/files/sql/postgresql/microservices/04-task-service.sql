-- ========================================
-- Task Service Database Schema
-- 任务服务数据库结构
-- ========================================

-- 创建数据库（如果需要）
-- CREATE DATABASE task_service;
-- \c task_service;

-- ========================================
-- 任务管理表
-- ========================================

-- 任务表
DROP TABLE IF EXISTS task CASCADE;
CREATE TABLE task (
    id SERIAL PRIMARY KEY,
    task_name VARCHAR(200) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    task_category VARCHAR(50),
    task_description TEXT,
    task_config JSONB,
    schedule_type VARCHAR(20) DEFAULT 'MANUAL',
    cron_expression VARCHAR(100),
    schedule_config JSONB,
    priority INTEGER DEFAULT 5,
    timeout_seconds INTEGER DEFAULT 3600,
    retry_count INTEGER DEFAULT 0,
    max_retry INTEGER DEFAULT 3,
    retry_interval INTEGER DEFAULT 60,
    status VARCHAR(20) DEFAULT 'CREATED',
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE task IS '任务表';
COMMENT ON COLUMN task.id IS '任务ID';
COMMENT ON COLUMN task.task_name IS '任务名称';
COMMENT ON COLUMN task.task_type IS '任务类型：DATA_EXPORT, REPORT_GENERATE, ANALYSIS, CLEANUP, BACKUP';
COMMENT ON COLUMN task.task_category IS '任务分类：SYSTEM, USER, SCHEDULED';
COMMENT ON COLUMN task.task_description IS '任务描述';
COMMENT ON COLUMN task.task_config IS '任务配置';
COMMENT ON COLUMN task.schedule_type IS '调度类型：MANUAL, CRON, INTERVAL, ONCE';
COMMENT ON COLUMN task.cron_expression IS 'Cron表达式';
COMMENT ON COLUMN task.schedule_config IS '调度配置';
COMMENT ON COLUMN task.priority IS '优先级：1-最高，10-最低';
COMMENT ON COLUMN task.timeout_seconds IS '超时时间（秒）';
COMMENT ON COLUMN task.retry_count IS '重试次数';
COMMENT ON COLUMN task.max_retry IS '最大重试次数';
COMMENT ON COLUMN task.retry_interval IS '重试间隔（秒）';
COMMENT ON COLUMN task.status IS '任务状态：CREATED, SCHEDULED, RUNNING, COMPLETED, FAILED, CANCELLED';
COMMENT ON COLUMN task.enabled IS '是否启用';
COMMENT ON COLUMN task.created_at IS '创建时间';
COMMENT ON COLUMN task.updated_at IS '更新时间';
COMMENT ON COLUMN task.created_by IS '创建人';
COMMENT ON COLUMN task.updated_by IS '更新人';

-- 任务执行记录表
DROP TABLE IF EXISTS task_execution CASCADE;
CREATE TABLE task_execution (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    execution_id VARCHAR(100) UNIQUE NOT NULL,
    execution_status VARCHAR(20) DEFAULT 'PENDING',
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_seconds INTEGER,
    progress_percentage INTEGER DEFAULT 0,
    current_step VARCHAR(200),
    total_steps INTEGER,
    completed_steps INTEGER DEFAULT 0,
    input_parameters JSONB,
    output_result JSONB,
    error_message TEXT,
    error_stack TEXT,
    execution_log TEXT,
    resource_usage JSONB,
    retry_attempt INTEGER DEFAULT 0,
    triggered_by VARCHAR(100),
    trigger_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES task(id) ON DELETE CASCADE
);

COMMENT ON TABLE task_execution IS '任务执行记录表';
COMMENT ON COLUMN task_execution.id IS '执行记录ID';
COMMENT ON COLUMN task_execution.task_id IS '任务ID';
COMMENT ON COLUMN task_execution.execution_id IS '执行唯一标识';
COMMENT ON COLUMN task_execution.execution_status IS '执行状态：PENDING, RUNNING, COMPLETED, FAILED, CANCELLED, TIMEOUT';
COMMENT ON COLUMN task_execution.start_time IS '开始时间';
COMMENT ON COLUMN task_execution.end_time IS '结束时间';
COMMENT ON COLUMN task_execution.duration_seconds IS '执行时长（秒）';
COMMENT ON COLUMN task_execution.progress_percentage IS '进度百分比';
COMMENT ON COLUMN task_execution.current_step IS '当前步骤';
COMMENT ON COLUMN task_execution.total_steps IS '总步骤数';
COMMENT ON COLUMN task_execution.completed_steps IS '已完成步骤数';
COMMENT ON COLUMN task_execution.input_parameters IS '输入参数';
COMMENT ON COLUMN task_execution.output_result IS '输出结果';
COMMENT ON COLUMN task_execution.error_message IS '错误信息';
COMMENT ON COLUMN task_execution.error_stack IS '错误堆栈';
COMMENT ON COLUMN task_execution.execution_log IS '执行日志';
COMMENT ON COLUMN task_execution.resource_usage IS '资源使用情况';
COMMENT ON COLUMN task_execution.retry_attempt IS '重试次数';
COMMENT ON COLUMN task_execution.triggered_by IS '触发人';
COMMENT ON COLUMN task_execution.trigger_type IS '触发类型：MANUAL, SCHEDULED, API, EVENT';
COMMENT ON COLUMN task_execution.created_at IS '创建时间';
COMMENT ON COLUMN task_execution.updated_at IS '更新时间';

-- 任务依赖关系表
DROP TABLE IF EXISTS task_dependency CASCADE;
CREATE TABLE task_dependency (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    dependent_task_id INTEGER NOT NULL,
    dependency_type VARCHAR(50) DEFAULT 'SUCCESS',
    condition_config JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    FOREIGN KEY (task_id) REFERENCES task(id) ON DELETE CASCADE,
    FOREIGN KEY (dependent_task_id) REFERENCES task(id) ON DELETE CASCADE,
    UNIQUE(task_id, dependent_task_id)
);

COMMENT ON TABLE task_dependency IS '任务依赖关系表';
COMMENT ON COLUMN task_dependency.id IS '依赖关系ID';
COMMENT ON COLUMN task_dependency.task_id IS '任务ID';
COMMENT ON COLUMN task_dependency.dependent_task_id IS '依赖的任务ID';
COMMENT ON COLUMN task_dependency.dependency_type IS '依赖类型：SUCCESS, FAILED, COMPLETED, ALWAYS';
COMMENT ON COLUMN task_dependency.condition_config IS '条件配置';
COMMENT ON COLUMN task_dependency.created_at IS '创建时间';
COMMENT ON COLUMN task_dependency.created_by IS '创建人';

-- ========================================
-- 批量处理表
-- ========================================

-- 批量处理任务表
DROP TABLE IF EXISTS batch_task CASCADE;
CREATE TABLE batch_task (
    id SERIAL PRIMARY KEY,
    batch_name VARCHAR(200) NOT NULL,
    batch_type VARCHAR(50) NOT NULL,
    batch_description TEXT,
    total_items INTEGER DEFAULT 0,
    processed_items INTEGER DEFAULT 0,
    success_items INTEGER DEFAULT 0,
    failed_items INTEGER DEFAULT 0,
    batch_status VARCHAR(20) DEFAULT 'CREATED',
    batch_config JSONB,
    input_source VARCHAR(500),
    output_destination VARCHAR(500),
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_seconds INTEGER,
    error_summary TEXT,
    progress_percentage INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE batch_task IS '批量处理任务表';
COMMENT ON COLUMN batch_task.id IS '批量任务ID';
COMMENT ON COLUMN batch_task.batch_name IS '批量任务名称';
COMMENT ON COLUMN batch_task.batch_type IS '批量任务类型：DATA_IMPORT, DATA_EXPORT, DATA_MIGRATION, BULK_UPDATE';
COMMENT ON COLUMN batch_task.batch_description IS '批量任务描述';
COMMENT ON COLUMN batch_task.total_items IS '总项目数';
COMMENT ON COLUMN batch_task.processed_items IS '已处理项目数';
COMMENT ON COLUMN batch_task.success_items IS '成功项目数';
COMMENT ON COLUMN batch_task.failed_items IS '失败项目数';
COMMENT ON COLUMN batch_task.batch_status IS '批量任务状态：CREATED, RUNNING, COMPLETED, FAILED, CANCELLED';
COMMENT ON COLUMN batch_task.batch_config IS '批量任务配置';
COMMENT ON COLUMN batch_task.input_source IS '输入源';
COMMENT ON COLUMN batch_task.output_destination IS '输出目标';
COMMENT ON COLUMN batch_task.start_time IS '开始时间';
COMMENT ON COLUMN batch_task.end_time IS '结束时间';
COMMENT ON COLUMN batch_task.duration_seconds IS '执行时长（秒）';
COMMENT ON COLUMN batch_task.error_summary IS '错误摘要';
COMMENT ON COLUMN batch_task.progress_percentage IS '进度百分比';
COMMENT ON COLUMN batch_task.created_at IS '创建时间';
COMMENT ON COLUMN batch_task.updated_at IS '更新时间';
COMMENT ON COLUMN batch_task.created_by IS '创建人';
COMMENT ON COLUMN batch_task.updated_by IS '更新人';

-- 批量处理项目表
DROP TABLE IF EXISTS batch_item CASCADE;
CREATE TABLE batch_item (
    id SERIAL PRIMARY KEY,
    batch_id INTEGER NOT NULL,
    item_index INTEGER NOT NULL,
    item_data JSONB NOT NULL,
    item_status VARCHAR(20) DEFAULT 'PENDING',
    process_start_time TIMESTAMP,
    process_end_time TIMESTAMP,
    process_duration_ms INTEGER,
    result_data JSONB,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (batch_id) REFERENCES batch_task(id) ON DELETE CASCADE
);

COMMENT ON TABLE batch_item IS '批量处理项目表';
COMMENT ON COLUMN batch_item.id IS '项目ID';
COMMENT ON COLUMN batch_item.batch_id IS '批量任务ID';
COMMENT ON COLUMN batch_item.item_index IS '项目索引';
COMMENT ON COLUMN batch_item.item_data IS '项目数据';
COMMENT ON COLUMN batch_item.item_status IS '项目状态：PENDING, PROCESSING, SUCCESS, FAILED, SKIPPED';
COMMENT ON COLUMN batch_item.process_start_time IS '处理开始时间';
COMMENT ON COLUMN batch_item.process_end_time IS '处理结束时间';
COMMENT ON COLUMN batch_item.process_duration_ms IS '处理时长（毫秒）';
COMMENT ON COLUMN batch_item.result_data IS '结果数据';
COMMENT ON COLUMN batch_item.error_message IS '错误信息';
COMMENT ON COLUMN batch_item.retry_count IS '重试次数';
COMMENT ON COLUMN batch_item.created_at IS '创建时间';
COMMENT ON COLUMN batch_item.updated_at IS '更新时间';

-- ========================================
-- 下载管理表
-- ========================================

-- 下载任务表
DROP TABLE IF EXISTS download_task CASCADE;
CREATE TABLE download_task (
    id SERIAL PRIMARY KEY,
    download_name VARCHAR(200) NOT NULL,
    download_type VARCHAR(50) NOT NULL,
    file_name VARCHAR(500),
    file_path VARCHAR(1000),
    file_size BIGINT,
    file_format VARCHAR(20),
    download_status VARCHAR(20) DEFAULT 'PREPARING',
    download_url VARCHAR(1000),
    expire_time TIMESTAMP,
    download_count INTEGER DEFAULT 0,
    max_download_count INTEGER DEFAULT 1,
    query_conditions JSONB,
    generation_config JSONB,
    progress_percentage INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE download_task IS '下载任务表';
COMMENT ON COLUMN download_task.id IS '下载任务ID';
COMMENT ON COLUMN download_task.download_name IS '下载任务名称';
COMMENT ON COLUMN download_task.download_type IS '下载类型：REPORT, DATA_EXPORT, LOG_EXPORT, ANALYSIS_RESULT';
COMMENT ON COLUMN download_task.file_name IS '文件名';
COMMENT ON COLUMN download_task.file_path IS '文件路径';
COMMENT ON COLUMN download_task.file_size IS '文件大小（字节）';
COMMENT ON COLUMN download_task.file_format IS '文件格式：PDF, EXCEL, CSV, JSON, XML';
COMMENT ON COLUMN download_task.download_status IS '下载状态：PREPARING, READY, DOWNLOADING, COMPLETED, EXPIRED, FAILED';
COMMENT ON COLUMN download_task.download_url IS '下载链接';
COMMENT ON COLUMN download_task.expire_time IS '过期时间';
COMMENT ON COLUMN download_task.download_count IS '下载次数';
COMMENT ON COLUMN download_task.max_download_count IS '最大下载次数';
COMMENT ON COLUMN download_task.query_conditions IS '查询条件';
COMMENT ON COLUMN download_task.generation_config IS '生成配置';
COMMENT ON COLUMN download_task.progress_percentage IS '进度百分比';
COMMENT ON COLUMN download_task.error_message IS '错误信息';
COMMENT ON COLUMN download_task.created_at IS '创建时间';
COMMENT ON COLUMN download_task.updated_at IS '更新时间';
COMMENT ON COLUMN download_task.created_by IS '创建人';
COMMENT ON COLUMN download_task.updated_by IS '更新人';

-- 下载记录表
DROP TABLE IF EXISTS download_record CASCADE;
CREATE TABLE download_record (
    id SERIAL PRIMARY KEY,
    download_task_id INTEGER NOT NULL,
    download_ip VARCHAR(45),
    download_user_agent TEXT,
    download_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    download_status VARCHAR(20) DEFAULT 'SUCCESS',
    download_size BIGINT,
    download_duration_ms INTEGER,
    error_message TEXT,
    FOREIGN KEY (download_task_id) REFERENCES download_task(id) ON DELETE CASCADE
);

COMMENT ON TABLE download_record IS '下载记录表';
COMMENT ON COLUMN download_record.id IS '下载记录ID';
COMMENT ON COLUMN download_record.download_task_id IS '下载任务ID';
COMMENT ON COLUMN download_record.download_ip IS '下载IP地址';
COMMENT ON COLUMN download_record.download_user_agent IS '用户代理';
COMMENT ON COLUMN download_record.download_time IS '下载时间';
COMMENT ON COLUMN download_record.download_status IS '下载状态：SUCCESS, FAILED, CANCELLED';
COMMENT ON COLUMN download_record.download_size IS '下载大小（字节）';
COMMENT ON COLUMN download_record.download_duration_ms IS '下载时长（毫秒）';
COMMENT ON COLUMN download_record.error_message IS '错误信息';

-- ========================================
-- 任务队列表
-- ========================================

-- 任务队列表
DROP TABLE IF EXISTS task_queue CASCADE;
CREATE TABLE task_queue (
    id SERIAL PRIMARY KEY,
    queue_name VARCHAR(100) NOT NULL,
    task_id INTEGER,
    execution_id VARCHAR(100),
    queue_priority INTEGER DEFAULT 5,
    queue_status VARCHAR(20) DEFAULT 'PENDING',
    scheduled_time TIMESTAMP,
    pickup_time TIMESTAMP,
    worker_id VARCHAR(100),
    retry_count INTEGER DEFAULT 0,
    max_retry INTEGER DEFAULT 3,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES task(id) ON DELETE CASCADE
);

COMMENT ON TABLE task_queue IS '任务队列表';
COMMENT ON COLUMN task_queue.id IS '队列ID';
COMMENT ON COLUMN task_queue.queue_name IS '队列名称';
COMMENT ON COLUMN task_queue.task_id IS '任务ID';
COMMENT ON COLUMN task_queue.execution_id IS '执行ID';
COMMENT ON COLUMN task_queue.queue_priority IS '队列优先级：1-最高，10-最低';
COMMENT ON COLUMN task_queue.queue_status IS '队列状态：PENDING, PICKED, PROCESSING, COMPLETED, FAILED';
COMMENT ON COLUMN task_queue.scheduled_time IS '计划执行时间';
COMMENT ON COLUMN task_queue.pickup_time IS '拾取时间';
COMMENT ON COLUMN task_queue.worker_id IS '工作节点ID';
COMMENT ON COLUMN task_queue.retry_count IS '重试次数';
COMMENT ON COLUMN task_queue.max_retry IS '最大重试次数';
COMMENT ON COLUMN task_queue.error_message IS '错误信息';
COMMENT ON COLUMN task_queue.created_at IS '创建时间';
COMMENT ON COLUMN task_queue.updated_at IS '更新时间';

-- ========================================
-- 任务统计表
-- ========================================

-- 任务统计表
DROP TABLE IF EXISTS task_statistics CASCADE;
CREATE TABLE task_statistics (
    id SERIAL PRIMARY KEY,
    stat_date DATE NOT NULL,
    stat_hour INTEGER,
    task_type VARCHAR(50),
    task_category VARCHAR(50),
    total_tasks INTEGER DEFAULT 0,
    completed_tasks INTEGER DEFAULT 0,
    failed_tasks INTEGER DEFAULT 0,
    cancelled_tasks INTEGER DEFAULT 0,
    avg_duration_seconds INTEGER,
    max_duration_seconds INTEGER,
    min_duration_seconds INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(stat_date, stat_hour, task_type, task_category)
);

COMMENT ON TABLE task_statistics IS '任务统计表';
COMMENT ON COLUMN task_statistics.id IS '统计ID';
COMMENT ON COLUMN task_statistics.stat_date IS '统计日期';
COMMENT ON COLUMN task_statistics.stat_hour IS '统计小时（0-23）';
COMMENT ON COLUMN task_statistics.task_type IS '任务类型';
COMMENT ON COLUMN task_statistics.task_category IS '任务分类';
COMMENT ON COLUMN task_statistics.total_tasks IS '总任务数';
COMMENT ON COLUMN task_statistics.completed_tasks IS '完成任务数';
COMMENT ON COLUMN task_statistics.failed_tasks IS '失败任务数';
COMMENT ON COLUMN task_statistics.cancelled_tasks IS '取消任务数';
COMMENT ON COLUMN task_statistics.avg_duration_seconds IS '平均执行时长（秒）';
COMMENT ON COLUMN task_statistics.max_duration_seconds IS '最大执行时长（秒）';
COMMENT ON COLUMN task_statistics.min_duration_seconds IS '最小执行时长（秒）';
COMMENT ON COLUMN task_statistics.created_at IS '创建时间';
COMMENT ON COLUMN task_statistics.updated_at IS '更新时间';

-- ========================================
-- 索引创建
-- ========================================

-- 任务表索引
CREATE INDEX idx_task_name ON task(task_name);
CREATE INDEX idx_task_type ON task(task_type);
CREATE INDEX idx_task_category ON task(task_category);
CREATE INDEX idx_task_status ON task(status);
CREATE INDEX idx_task_enabled ON task(enabled);
CREATE INDEX idx_task_schedule_type ON task(schedule_type);
CREATE INDEX idx_task_priority ON task(priority);
CREATE INDEX idx_task_created_at ON task(created_at);

-- 任务执行记录表索引
CREATE INDEX idx_task_execution_task_id ON task_execution(task_id);
CREATE INDEX idx_task_execution_execution_id ON task_execution(execution_id);
CREATE INDEX idx_task_execution_status ON task_execution(execution_status);
CREATE INDEX idx_task_execution_start_time ON task_execution(start_time);
CREATE INDEX idx_task_execution_end_time ON task_execution(end_time);
CREATE INDEX idx_task_execution_triggered_by ON task_execution(triggered_by);
CREATE INDEX idx_task_execution_created_at ON task_execution(created_at);

-- 任务依赖关系表索引
CREATE INDEX idx_task_dependency_task_id ON task_dependency(task_id);
CREATE INDEX idx_task_dependency_dependent_task_id ON task_dependency(dependent_task_id);

-- 批量处理任务表索引
CREATE INDEX idx_batch_task_name ON batch_task(batch_name);
CREATE INDEX idx_batch_task_type ON batch_task(batch_type);
CREATE INDEX idx_batch_task_status ON batch_task(batch_status);
CREATE INDEX idx_batch_task_created_at ON batch_task(created_at);

-- 批量处理项目表索引
CREATE INDEX idx_batch_item_batch_id ON batch_item(batch_id);
CREATE INDEX idx_batch_item_status ON batch_item(item_status);
CREATE INDEX idx_batch_item_index ON batch_item(batch_id, item_index);

-- 下载任务表索引
CREATE INDEX idx_download_task_name ON download_task(download_name);
CREATE INDEX idx_download_task_type ON download_task(download_type);
CREATE INDEX idx_download_task_status ON download_task(download_status);
CREATE INDEX idx_download_task_created_by ON download_task(created_by);
CREATE INDEX idx_download_task_expire_time ON download_task(expire_time);
CREATE INDEX idx_download_task_created_at ON download_task(created_at);

-- 下载记录表索引
CREATE INDEX idx_download_record_task_id ON download_record(download_task_id);
CREATE INDEX idx_download_record_download_time ON download_record(download_time);
CREATE INDEX idx_download_record_status ON download_record(download_status);

-- 任务队列表索引
CREATE INDEX idx_task_queue_name ON task_queue(queue_name);
CREATE INDEX idx_task_queue_status ON task_queue(queue_status);
CREATE INDEX idx_task_queue_priority ON task_queue(queue_priority);
CREATE INDEX idx_task_queue_scheduled_time ON task_queue(scheduled_time);
CREATE INDEX idx_task_queue_worker_id ON task_queue(worker_id);
CREATE INDEX idx_task_queue_created_at ON task_queue(created_at);

-- 任务统计表索引
CREATE INDEX idx_task_statistics_date ON task_statistics(stat_date);
CREATE INDEX idx_task_statistics_type ON task_statistics(task_type);
CREATE INDEX idx_task_statistics_category ON task_statistics(task_category);
CREATE INDEX idx_task_statistics_date_hour ON task_statistics(stat_date, stat_hour);

-- ========================================
-- 触发器创建
-- ========================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间触发器
CREATE TRIGGER update_task_updated_at BEFORE UPDATE ON task
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_execution_updated_at BEFORE UPDATE ON task_execution
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_batch_task_updated_at BEFORE UPDATE ON batch_task
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_batch_item_updated_at BEFORE UPDATE ON batch_item
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_download_task_updated_at BEFORE UPDATE ON download_task
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_queue_updated_at BEFORE UPDATE ON task_queue
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_statistics_updated_at BEFORE UPDATE ON task_statistics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 初始化数据
-- ========================================

-- 插入示例任务
INSERT INTO task (task_name, task_type, task_category, task_description, task_config, schedule_type, created_by) VALUES
('系统日志清理', 'CLEANUP', 'SYSTEM', '定期清理系统日志文件', 
'{"log_retention_days": 30, "log_path": "/var/log/nta"}', 'CRON', 'system'),
('数据库备份', 'BACKUP', 'SYSTEM', '定期备份数据库', 
'{"backup_path": "/backup/db", "compression": true}', 'CRON', 'system'),
('性能报告生成', 'REPORT_GENERATE', 'SCHEDULED', '生成系统性能报告', 
'{"report_type": "performance", "format": "PDF"}', 'CRON', 'system');

-- 插入示例下载任务
INSERT INTO download_task (download_name, download_type, file_format, download_status, expire_time, created_by) VALUES
('流量分析报告', 'REPORT', 'PDF', 'READY', CURRENT_TIMESTAMP + INTERVAL '7 days', 'admin'),
('告警数据导出', 'DATA_EXPORT', 'CSV', 'READY', CURRENT_TIMESTAMP + INTERVAL '3 days', 'admin');

-- ========================================
-- CDC配置（如果需要）
-- ========================================

-- 为需要CDC的表设置复制标识
ALTER TABLE task REPLICA IDENTITY FULL;
ALTER TABLE task_execution REPLICA IDENTITY FULL;
ALTER TABLE task_dependency REPLICA IDENTITY FULL;
ALTER TABLE batch_task REPLICA IDENTITY FULL;
ALTER TABLE batch_item REPLICA IDENTITY FULL;
ALTER TABLE download_task REPLICA IDENTITY FULL;
ALTER TABLE download_record REPLICA IDENTITY FULL;
ALTER TABLE task_queue REPLICA IDENTITY FULL;
ALTER TABLE task_statistics REPLICA IDENTITY FULL;
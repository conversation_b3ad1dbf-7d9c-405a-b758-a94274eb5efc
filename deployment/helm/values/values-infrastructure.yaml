# values-infrastructure.yaml - 基础设施配置文件
# 包含所有基础设施组件的配置信息

# Infrastructure configurations
# 基础设施配置 - 仅包含连接信息和应用程序需要的配置
infrastructure:
  postgresql:
    enabled: true
    # 连接配置 - 应用程序使用
    host: "postgresql"
    port: "5432"
    credentials:
      secretName: "postgresql-credentials"
      usernameKey: "username"
      passwordKey: "password"
      # 敏感信息处理：使用环境变量或外部密钥管理系统
      # 在部署时，请确保这些环境变量已经设置或使用 --set 参数覆盖
      postgresPassword:
        value: "${POSTGRESQL_POSTGRES_PASSWORD}"
      password:
        value: "${POSTGRESQL_PASSWORD}"

    # 依赖配置 - 用于 initContainer 等待和健康检查
    dependency:
      # 连接配置
      connection:
        host: "{{ .Release.Name }}-postgresql-rw"
        port: 5432
        readOnlyHost: "{{ .Release.Name }}-postgresql-ro"  # 只读副本
      # 认证配置
      credentials:
        username:
          secretName: "{{ include \"nta.credentialsSecretName\" (dict \"type\" \"postgresql\" \"root\" $) }}"
          secretKey: "{{ include \"nta.credentialsUsernameSecretKey\" (dict \"type\" \"postgresql\" \"root\" $) }}"
        password:
          secretName: "{{ include \"nta.credentialsSecretName\" (dict \"type\" \"postgresql\" \"root\" $) }}"
          secretKey: "{{ include \"nta.credentialsSecretKey\" (dict \"type\" \"postgresql\" \"root\" $) }}"
      # 健康检查配置
      healthCheck:
        command: ['pg_isready', '-h', '{{ .connection.host }}', '-p', '{{ .connection.port }}']
        initialDelaySeconds: 10
        periodSeconds: 5
        timeoutSeconds: 3
        failureThreshold: 3

    # PostgreSQL 认证配置 - 符合 Bitnami Chart 规范
    auth:
      enablePostgresUser: true
      postgresPassword: "${POSTGRESQL_POSTGRES_PASSWORD}"
      username: "nta_user"
      password: "${POSTGRESQL_PASSWORD}"
      database: "knowledgebase"

    # 主节点配置 - 符合 Bitnami Chart 规范
    primary:
      # 初始化数据库配置
      initdb:
        # 初始化脚本 - 符合 Bitnami Chart 规范
        scripts:
          # 创建数据库和表结构
          01-create-databases.sql: |
            -- 创建应用数据库
            CREATE DATABASE push_database;
            CREATE DATABASE auth_db;
            CREATE DATABASE nta;
            CREATE DATABASE knowledgebase;

            -- 创建用户
            CREATE USER nta_user WITH PASSWORD '${POSTGRESQL_PASSWORD}';

            -- 授权
            GRANT ALL PRIVILEGES ON DATABASE push_database TO nta_user;
            GRANT ALL PRIVILEGES ON DATABASE auth_db TO nta_user;
            GRANT ALL PRIVILEGES ON DATABASE nta TO nta_user;
            GRANT ALL PRIVILEGES ON DATABASE knowledgebase TO nta_user;

          # 初始化知识库数据库结构
          02-init-knowledge-base.sql: |
            \c knowledgebase;

            -- 执行知识库DDL脚本
            \i /docker-entrypoint-initdb.d/sql/postgresql/03-knowledge-base-schema.sql;

            -- 执行知识库数据插入脚本
            \i /docker-entrypoint-initdb.d/sql/postgresql/04-domain-knowledge-data.sql;
            \i /docker-entrypoint-initdb.d/sql/postgresql/05-tld-registry-data.sql;
            \i /docker-entrypoint-initdb.d/sql/postgresql/06-public-suffix-data.sql;

          # 初始化其他数据库结构
          03-init-other-databases.sql: |
            \c push_database;
            \i /docker-entrypoint-initdb.d/sql/postgresql/01-push_database.sql;

            \c auth_db;
            \i /docker-entrypoint-initdb.d/sql/postgresql/00-auth_db.sql;

            \c nta;
            \i /docker-entrypoint-initdb.d/sql/postgresql/02-th_analysis.sql;
            \i /docker-entrypoint-initdb.d/sql/postgresql/07-alarm.sql;
            \i /docker-entrypoint-initdb.d/sql/postgresql/08-graph.sql;
            \i /docker-entrypoint-initdb.d/sql/postgresql/09-import_attack_stage_data.sql;
            \i /docker-entrypoint-initdb.d/sql/postgresql/10-metadata.sql;
            \i /docker-entrypoint-initdb.d/sql/postgresql/11-monitor.sql;
            \i /docker-entrypoint-initdb.d/sql/postgresql/12-rule.sql;
            \i /docker-entrypoint-initdb.d/sql/postgresql/13-session.sql;
            \i /docker-entrypoint-initdb.d/sql/postgresql/14-task.sql;

      # 持久化存储配置
      persistence:
        enabled: true
        size: "20Gi"
        storageClass: ""
        accessModes:
          - ReadWriteOnce

      # 资源配置
      resources:
        requests:
          memory: "2Gi"
          cpu: "1000m"
        limits:
          memory: "4Gi"
          cpu: "2000m"

      # 初始化容器，用于复制 SQL 文件
      initContainers:
        - name: copy-init-files
          image: "{{ .Values.global.registry }}/busybox:1.36"
          command:
            - /bin/sh
            - -c
            - |
              echo "复制 PostgreSQL SQL 文件..."
              mkdir -p /postgresql-init/sql/postgresql

              # 复制 PostgreSQL SQL 文件
              cp /helm-files/sql/postgresql/*.sql /postgresql-init/sql/postgresql/

              echo "文件复制完成"
          volumeMounts:
            - name: helm-files
              mountPath: /helm-files
            - name: postgresql-init-files
              mountPath: /postgresql-init

      # 额外的卷挂载
      extraVolumeMounts:
        - name: postgresql-init-files
          mountPath: /docker-entrypoint-initdb.d/sql
          subPath: sql

      # 额外的卷
      extraVolumes:
        - name: helm-files
          hostPath:
            path: {{ .Values.global.helmFilesPath | default "files" }}
        - name: postgresql-init-files
          emptyDir: {}

    # 指标监控配置
    metrics:
      enabled: true
      serviceMonitor:
        enabled: true

    # 服务配置
    service:
      type: ClusterIP
      ports:
        postgresql: 5432

    # 架构配置
    architecture: standalone

  redis:
    enabled: true
    # 连接配置
    host: "redis"
    port: "6379"
    credentials:
      # 敏感信息处理：使用环境变量或外部密钥管理系统
      password:
        secretName: "redis-credentials"
        secretKey: "password"
        value: "${REDIS_PASSWORD}"
    # 数据库配置
    databases:
      - name: default
        db: 0
      - name: alarm
        db: 1
      - name: cert_import
        db: 2
      - name: nebula
        db: 2

    # 依赖配置 - 用于 initContainer 等待和健康检查
    dependency:
      # 连接配置
      connection:
        host: "{{ .Release.Name }}-redis-master"
        port: 6379
        slaveHost: "{{ .Release.Name }}-redis-slave"  # 从节点
      # 认证配置
      credentials:
        password:
          secretName: "{{ include \"nta.credentialsSecretName\" (dict \"type\" \"redis\" \"root\" $) }}"
          secretKey: "{{ include \"nta.credentialsSecretKey\" (dict \"type\" \"redis\" \"root\" $) }}"
      # 健康检查配置
      healthCheck:
        command: ['redis-cli', '-h', '{{ .connection.host }}', '-p', '{{ .connection.port }}', 'ping']
        initialDelaySeconds: 5
        periodSeconds: 3
        timeoutSeconds: 2
        failureThreshold: 3

  elasticsearch:
    enabled: true
    # 连接配置 - 应用程序使用
    host: "elasticsearch-es-http"  # ECK创建的服务名称
    port: "9200"
    credentials:
      username: "elastic"  # ECK创建的默认用户
      # 敏感信息处理：使用环境变量或外部密钥管理系统
      password:
        secretName: "elasticsearch-es-elastic-user"  # ECK创建的Secret
        secretKey: "elastic"
        value: "${ELASTICSEARCH_PASSWORD}"
    # 索引模板列表 - 用于创建IndexTemplate资源
    templates:
      - name: cert_template
      - name: connect_template
      - name: ssl_template
      - name: http_template
      - name: dns_template
      - name: alarm_template
      - name: es_index_template
      - name: ssh_template
    # 集群基本信息
    cluster:
      name: "elasticsearch"
      version: "7.17.14"

    # 依赖配置 - 用于 initContainer 等待和健康检查
    dependency:
      # 连接配置
      connection:
        host: "{{ .Release.Name }}-elasticsearch-master"
        port: 9200
        protocol: "http"
      # 认证配置
      credentials:
        username:
          secretName: "elasticsearch-credentials"
          secretKey: "username"
        password:
          secretName: "elasticsearch-credentials"
          secretKey: "password"
      # 健康检查配置
      healthCheck:
        command: ['curl', '-f', '{{ .connection.protocol }}://{{ .connection.host }}:{{ .connection.port }}/_cluster/health']
        initialDelaySeconds: 30
        periodSeconds: 15
        timeoutSeconds: 10
        failureThreshold: 5

  nebula:
    # Nebula Graph 数据库
    # 通用配置
    credentials:
      username: "root"
      # 敏感信息处理：使用环境变量或外部密钥管理系统
      password:
        secretName: "nebula-credentials"
        secretKey: "password"
        value: "${NEBULA_PASSWORD}"

    # 图空间配置
    space:
      name: "nta_analysis_graph"
      partitionNum: 3
      replicaFactor: 3
      vidType: "FIXED_STRING(64)"

    # 服务端口
    graphd:
      host: "nebula-graphd"
      port: 9669
      replicas: 1
    metad:
      host: "nebula-metad"
      port: 9559
      replicas: 1
    storaged:
      host: "nebula-storaged"
      port: 9779
      replicas: 3

    # 连接池配置
    pool:
      maxConnSize: 1000
      minConnSize: 50
      idleTime: 180000
      timeout: 300000
    sessionSize: 100

    # 依赖配置 - 用于 initContainer 等待和健康检查
    dependency:
      # 连接配置
      connection:
        graphdHost: "{{ .Release.Name }}-nebula-graphd-0.{{ .Release.Name }}-nebula-graphd.{{ .Release.Namespace }}.svc"
        graphdPort: "{{ .Values.infrastructure.nebula.graphd.port }}"
        metadHost: "{{ .Release.Name }}-nebula-metad-0.{{ .Release.Name }}-nebula-metad.{{ .Release.Namespace }}.svc"
        metadPort: "{{ .Values.infrastructure.nebula.metad.port }}"
      # 认证配置
      credentials:
        username:
          secretName: "{{ include \"nta.credentialsSecretName\" (dict \"type\" \"nebula\" \"root\" $) }}"
          secretKey: "{{ include \"nta.credentialsUsernameSecretKey\" (dict \"type\" \"nebula\" \"root\" $) }}"
        password:
          secretName: "{{ include \"nta.credentialsSecretName\" (dict \"type\" \"nebula\" \"root\" $) }}"
          secretKey: "{{ include \"nta.credentialsSecretKey\" (dict \"type\" \"nebula\" \"root\" $) }}"
      # 健康检查配置
      healthCheck:
        command: ['nc', '-z', '{{ .connection.graphdHost }}', '{{ .connection.graphdPort }}']
        initialDelaySeconds: 20
        periodSeconds: 10
        timeoutSeconds: 5
        failureThreshold: 6

    # 批处理配置
    batch:
      size: 100
      interval: 1000

  doris:
    enabled: true
    # 通用配置
    credentials:
      username: "root"
      # 敏感信息处理：使用环境变量或外部密钥管理系统
      password:
        secretName: "doris-credentials"
        secretKey: "password"
        value: "${DORIS_PASSWORD}"

    # DorisCluster配置
    cluster:
      name: "doris-cluster"
      # 启用CN组件
      enabledCn: false
      # 启用Broker组件
      enabledBroker: false

    # Doris初始化作业配置
    # 注意：使用mysql容器是合理的，因为doris支持的是mysql的客户端
    initJob:
      image:
        repository: mysql
        tag: 8.0
        pullPolicy: IfNotPresent
      resources:
        requests:
          memory: "256Mi"
          cpu: "100m"
        limits:
          memory: "512Mi"
          cpu: "200m"

    # FE配置
    fe:
      httpPort: 8030
      queryPort: 9030
      rpcPort: 9020
      editLogPort: 9010
      replicas: 3
      image:
        repository: apache/doris
        tag: fe-2.1.7
      resources:
        requests:
          memory: "4Gi"
          cpu: "2000m"
        limits:
          memory: "8Gi"
          cpu: "4000m"
      persistentVolumeClaim:
        metaPersistentVolume:
          storage: "20Gi"
          storageClassName: "standard"
        logsPersistentVolume:
          storage: "10Gi"
          storageClassName: "standard"
      configMap:
        "fe.conf": |
          http_port = 8030
          rpc_port = 9020
          query_port = 9030
          edit_log_port = 9010
          enable_fqdn_mode = true

    # BE配置
    be:
      httpPort: 8040
      bePort: 9060
      heartbeatPort: 9050
      brpcPort: 8060
      replicas: 3
      image:
        repository: apache/doris
        tag: be-2.1.7
      resources:
        requests:
          memory: "8Gi"
          cpu: "4000m"
        limits:
          memory: "16Gi"
          cpu: "8000m"
      persistentVolumeClaim:
        dataPersistentVolume:
          storage: "100Gi"
          storageClassName: "standard"
        logsPersistentVolume:
          storage: "10Gi"
          storageClassName: "standard"
      configMap:
        "be.conf": |
          be_port = 9060
          webserver_port = 8040
          heartbeat_service_port = 9050
          brpc_port = 8060
          storage_root_path = /opt/apache-doris/be/storage

    # CN配置
    cn:
      httpPort: 8040
      bePort: 9060
      heartbeatPort: 9050
      brpcPort: 8060
      replicas: 3
      image:
        repository: apache/doris
        tag: be-2.1.7
      resources:
        requests:
          memory: "8Gi"
          cpu: "4000m"
        limits:
          memory: "16Gi"
          cpu: "8000m"
      persistentVolumeClaim:
        dataPersistentVolume:
          storage: "100Gi"
          storageClassName: "standard"
        logsPersistentVolume:
          storage: "10Gi"
          storageClassName: "standard"
      configMap:
        "be.conf": |
          be_port = 9060
          webserver_port = 8040
          heartbeat_service_port = 9050
          brpc_port = 8060
          storage_root_path = /opt/apache-doris/be/storage
          be_node_role = computation

    # Broker配置
    broker:
      replicas: 1
      image:
        repository: apache/doris
        tag: broker-2.1.7
      resources:
        requests:
          memory: "1Gi"
          cpu: "500m"
        limits:
          memory: "2Gi"
          cpu: "1000m"
      persistentVolumeClaim:
        logsPersistentVolume:
          storage: "10Gi"
          storageClassName: "standard"
      configMap:
        "apache_hdfs_broker.conf": |
          broker_ipc_port = 8000
          client_expire_seconds = 3600

    # 监控配置
    metrics:
      enabled: true
      serviceMonitor:
        enabled: true

    # 依赖配置 - 用于 initContainer 等待和健康检查
    dependency:
      # 连接配置
      connection:
        feHost: "{{ .Values.infrastructure.doris.cluster.name }}-fe.{{ .Release.Namespace }}.svc"
        fePort: "{{ .Values.infrastructure.doris.fe.queryPort }}"
        beHost: "{{ .Values.infrastructure.doris.cluster.name }}-be.{{ .Release.Namespace }}.svc"
        bePort: "{{ .Values.infrastructure.doris.be.heartbeatPort }}"
      # 认证配置
      credentials:
        username: "{{ .Values.infrastructure.doris.credentials.username }}"
        password:
          secretName: "{{ include \"nta.credentialsSecretName\" (dict \"type\" \"doris\" \"root\" $) }}"
          secretKey: "{{ include \"nta.credentialsSecretKey\" (dict \"type\" \"doris\" \"root\" $) }}"
      # 健康检查配置
      healthCheck:
        command: ['mysql', '-h', '{{ .connection.feHost }}', '-P', '{{ .connection.fePort }}', '-u', '{{ .credentials.username }}', '-p{{ .credentials.password.value }}', '-e', 'SELECT 1']
        initialDelaySeconds: 30
        periodSeconds: 15
        timeoutSeconds: 10
        failureThreshold: 5

  kafka:
    enabled: true
    # 连接配置 - 应用程序使用
    host: "kafka"
    port: "9092"
    # Kafka主题配置 - 包含主题名称和初始化配置
    topics:
      # 主题名称映射 - 用于应用程序引用
      cert: "certfile"
      systemBuiltInCertificates: "certfile_system"
      meta: "meta"
      modelStateChanged: "model_state_changed"
      modelConfig: "model_config"
      # 主题初始化配置 - 用于创建Kafka主题
      definitions:
        - name: "certfile"
          partitions: 1
          replicas: 2
          retention_ms: 604800000  # 7天
          segment_bytes: **********  # 1GB
        - name: "certfile_system"
          partitions: 1
          replicas: 2
        - name: "meta"
          partitions: 1
          replicas: 2
        - name: "model_state_changed"
          partitions: 1
          replicas: 2
        - name: "model_config"
          partitions: 1
          replicas: 2
          config:
            cleanup.policy: "delete"
            min.insync.replicas: 2
        # 协议元数据主题 - traffic-etl-processor 输出，供下游分析模块消费
        - name: "connect-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "http-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "dns-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "ssl-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "ssh-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "rlogin-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "telnet-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "rdp-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "vnc-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "xdmcp-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "ntp-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
        - name: "icmp-info"
          partitions: 4
          replicas: 2
          retention_ms: 604800000  # 7天
    # 安全配置 - 应用程序使用
    security:
      protocol: "SASL_PLAINTEXT"
      mechanism: "SCRAM-SHA-512"
    # 认证配置
    credentials:
      username: "user"
      # 敏感信息处理：使用环境变量或外部密钥管理系统
      password:
        secretName: "kafka-credentials"
        secretKey: "password"
        value: "${KAFKA_PASSWORD}"
    # 集群基本信息
    cluster:
      name: "kafka"
      version: "3.9.0"

    # 依赖配置 - 用于 initContainer 等待和健康检查
    dependency:
      # 连接配置
      connection:
        bootstrapServers: "{{ .Release.Name }}-kafka-bootstrap:9092"
        host: "{{ .Release.Name }}-kafka-bootstrap"
        port: 9092
      # 认证配置
      credentials:
        username:
          secretName: "kafka-credentials"
          secretKey: "username"
        password:
          secretName: "kafka-credentials"
          secretKey: "password"
      # 健康检查配置
      healthCheck:
        command: ['nc', '-z', '{{ .connection.host }}', '{{ .connection.port }}']
        initialDelaySeconds: 15
        periodSeconds: 10
        timeoutSeconds: 5
        failureThreshold: 5

  # MinIO 对象存储配置 - 基于 Bitnami MinIO Chart
  minio:
    enabled: true
    # 连接配置 - 应用程序使用
    host: "minio"
    port: "9000"

    # Bitnami MinIO Chart 配置
    # 运行模式：standalone 或 distributed
    mode: standalone

    # 认证配置
    auth:
      rootUser: "minioadmin"
      rootPassword: "${MINIO_SECRET_KEY}"
      # 使用现有 Secret
      existingSecret: ""
      forcePassword: false
      usePasswordFiles: true

    # 默认存储桶配置
    defaultBuckets: "flink-checkpoints,flink-savepoints,nta-data,nta-application-data,certificates"

    # 资源配置
    resources:
      requests:
        memory: "2Gi"
        cpu: "1000m"
      limits:
        memory: "4Gi"
        cpu: "2000m"

    # 持久化存储配置
    persistence:
      enabled: true
      size: "100Gi"
      storageClass: "standard"
      accessModes:
        - ReadWriteOnce

    # 服务配置
    service:
      type: ClusterIP
      ports:
        api: 9000

    # MinIO Console 配置
    console:
      enabled: true
      service:
        type: ClusterIP
        ports:
          http: 9090

    # 网络策略
    networkPolicy:
      enabled: false
      allowExternal: true

    # 监控配置
    metrics:
      enabled: true
      serviceMonitor:
        enabled: true

    # 高级配置
    provisioning:
      enabled: true
      buckets:
        # Flink 相关存储桶
        - name: flink-checkpoints
          versioning: false
        - name: flink-savepoints
          versioning: false
        # 数据存储桶
        - name: nta-data
          versioning: true
        - name: nta-application-data
          versioning: false
        # 证书存储桶
        - name: certificates
          versioning: true

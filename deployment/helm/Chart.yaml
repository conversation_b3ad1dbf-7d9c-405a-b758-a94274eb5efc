apiVersion: v2
name: nta
description: A Helm chart for Network Traffic Analysis Platform
type: application
version: 1.0.0
appVersion: "3.0.0"
keywords:
  - microservices
  - spring-boot
  - kubernetes
  - istio
maintainers:
  - name: GeekSec Team
    email: <EMAIL>

dependencies:
  # 基础设施组件
  - name: postgresql
    version: 16.7.21
    repository: https://charts.bitnami.com/bitnami
    condition: infrastructure.postgresql.enabled
    alias: postgresql
  - name: redis
    version: 20.12.1
    repository: https://charts.bitnami.com/bitnami
    condition: infrastructure.redis.enabled
    alias: redis
  - name: kube-prometheus-stack
    version: 70.7.0
    repository: https://prometheus-community.github.io/helm-charts
    condition: monitoring.enabled
    alias: prometheus-stack
  - name: grafana
    version: 8.12.1
    repository: https://grafana.github.io/helm-charts
    condition: monitoring.grafana.enabled

  # Operator
  - name: flink-kubernetes-operator
    version: 1.11.0
    repository: https://downloads.apache.org/flink/flink-kubernetes-operator-1.11.0/
    condition: flink-kubernetes-operator.enabled
  - name: strimzi-kafka-operator
    version: 0.45.0
    repository: https://strimzi.io/charts/
    condition: strimzi-kafka-operator.enabled
  - name: eck-operator
    version: 2.16.1
    repository: https://helm.elastic.co
    condition: eck-operator.enabled
  - name: nebula-operator
    version: 1.8.4
    repository: https://vesoft-inc.github.io/nebula-operator/charts
    condition: nebula-operator.enabled
    alias: nebula
  - name: doris-operator
    version: 25.5.3
    repository: https://charts.selectdb.com
    condition: doris-operator.enabled
    alias: doris-operator
  - name: minio-operator
    version: 0.2.1
    repository: https://charts.bitnami.com/bitnami
    condition: minio-operator.enabled
    alias: minio-operator
  - name: minio
    version: 17.0.6
    repository: https://charts.bitnami.com/bitnami
    condition: infrastructure.minio.enabled
    alias: minio

  # Istio 组件
  - name: base
    version: 1.24.0
    repository: https://istio-release.storage.googleapis.com/charts
    condition: istio.enabled
    alias: istio-base
  - name: istiod
    version: 1.24.0
    repository: https://istio-release.storage.googleapis.com/charts
    condition: istio.enabled
  - name: gateway
    version: 1.24.0
    repository: https://istio-release.storage.googleapis.com/charts
    condition: istio.enabled
    alias: istio-ingress

package com.geeksec.alarm.notification.model;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * NotificationResult模型测试
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
class NotificationResultTest {

    @Test
    void testCreateSuccess() {
        // Given
        String alarmId = "alarm123";
        String subscriptionId = "sub123";
        String channel = "EMAIL";
        String recipient = "<EMAIL>";
        String subject = "测试告警";
        String content = "这是一个测试告警";

        // When
        NotificationResult result = NotificationResult.success(
                alarmId, subscriptionId, channel, recipient, subject, content);

        // Then
        assertThat(result.getId()).isNotNull();
        assertThat(result.getAlarmId()).isEqualTo(alarmId);
        assertThat(result.getSubscriptionId()).isEqualTo(subscriptionId);
        assertThat(result.getChannel()).isEqualTo(channel);
        assertThat(result.getRecipient()).isEqualTo(recipient);
        assertThat(result.getSubject()).isEqualTo(subject);
        assertThat(result.getContent()).isEqualTo(content);
        assertThat(result.getStatus()).isEqualTo(NotificationResult.Status.SUCCESS);
        assertThat(result.getSentAt()).isNotNull();
        assertThat(result.getCreatedAt()).isNotNull();
    }

    @Test
    void testCreateFailure() {
        // Given
        String alarmId = "alarm123";
        String subscriptionId = "sub123";
        String channel = "EMAIL";
        String recipient = "<EMAIL>";
        String subject = "测试告警";
        String content = "这是一个测试告警";
        String errorMessage = "发送失败：网络错误";

        // When
        NotificationResult result = NotificationResult.failure(
                alarmId, subscriptionId, channel, recipient, subject, content, errorMessage);

        // Then
        assertThat(result.getId()).isNotNull();
        assertThat(result.getAlarmId()).isEqualTo(alarmId);
        assertThat(result.getSubscriptionId()).isEqualTo(subscriptionId);
        assertThat(result.getChannel()).isEqualTo(channel);
        assertThat(result.getRecipient()).isEqualTo(recipient);
        assertThat(result.getSubject()).isEqualTo(subject);
        assertThat(result.getContent()).isEqualTo(content);
        assertThat(result.getStatus()).isEqualTo(NotificationResult.Status.FAILED);
        assertThat(result.getErrorMessage()).isEqualTo(errorMessage);
        assertThat(result.getSentAt()).isNull(); // 失败时没有发送时间
        assertThat(result.getCreatedAt()).isNotNull();
    }

    @Test
    void testStatusConstants() {
        // Test status constants
        assertThat(NotificationResult.Status.SUCCESS).isEqualTo("SUCCESS");
        assertThat(NotificationResult.Status.FAILED).isEqualTo("FAILED");
    }

    @Test
    void testBuilderPattern() {
        // Given
        LocalDateTime now = LocalDateTime.now();

        // When
        NotificationResult result = NotificationResult.builder()
                .id("test-id")
                .alarmId("alarm123")
                .subscriptionId("sub123")
                .channel("EMAIL")
                .recipient("<EMAIL>")
                .subject("测试")
                .content("内容")
                .status(NotificationResult.Status.SUCCESS)
                .sentAt(now)
                .createdAt(now)
                .build();

        // Then
        assertThat(result.getId()).isEqualTo("test-id");
        assertThat(result.getAlarmId()).isEqualTo("alarm123");
        assertThat(result.getSubscriptionId()).isEqualTo("sub123");
        assertThat(result.getChannel()).isEqualTo("EMAIL");
        assertThat(result.getRecipient()).isEqualTo("<EMAIL>");
        assertThat(result.getSubject()).isEqualTo("测试");
        assertThat(result.getContent()).isEqualTo("内容");
        assertThat(result.getStatus()).isEqualTo(NotificationResult.Status.SUCCESS);
        assertThat(result.getSentAt()).isEqualTo(now);
        assertThat(result.getCreatedAt()).isEqualTo(now);
    }
}

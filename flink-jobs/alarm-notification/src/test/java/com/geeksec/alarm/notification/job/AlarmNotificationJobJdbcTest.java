package com.geeksec.alarm.notification.job;

import com.geeksec.alarm.notification.model.NotificationResult;
import org.apache.flink.connector.jdbc.JdbcConnectionOptions;
import org.apache.flink.connector.jdbc.JdbcExecutionOptions;
import org.apache.flink.connector.jdbc.core.datastream.Jdbc;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 告警通知作业JDBC集成测试
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
class AlarmNotificationJobJdbcTest {
    
    @Test
    void testJdbcConnectionOptionsCreation() {
        // Given
        String jdbcUrl = "*************************************";
        String username = "test";
        String password = "test";
        
        // When
        JdbcConnectionOptions connectionOptions = new JdbcConnectionOptions.JdbcConnectionOptionsBuilder()
                .withUrl(jdbcUrl)
                .withDriverName("org.postgresql.Driver")
                .withUsername(username)
                .withPassword(password)
                .build();
        
        // Then
        assertThat(connectionOptions).isNotNull();
        assertThat(connectionOptions.getDbURL()).isEqualTo(jdbcUrl);
        assertThat(connectionOptions.getDriverName()).isEqualTo("org.postgresql.Driver");
        assertThat(connectionOptions.getUsername()).isEqualTo(username);
        assertThat(connectionOptions.getPassword()).isEqualTo(password);
    }
    
    @Test
    void testJdbcSinkBuilderCreation() {
        // When
        var sinkBuilder = Jdbc.sinkBuilder();

        // Then
        assertThat(sinkBuilder).isNotNull();
    }

    @Test
    void testJdbcExecutionOptionsCreation() {
        // When
        JdbcExecutionOptions executionOptions = JdbcExecutionOptions.builder()
                .withBatchSize(100)
                .withBatchIntervalMs(5000)
                .withMaxRetries(3)
                .build();

        // Then
        assertThat(executionOptions).isNotNull();
        assertThat(executionOptions.getBatchSize()).isEqualTo(100);
        assertThat(executionOptions.getBatchIntervalMs()).isEqualTo(5000);
        assertThat(executionOptions.getMaxRetries()).isEqualTo(3);
    }
    
    @Test
    void testNotificationResultForJdbc() {
        // Given
        String alarmId = "alarm123";
        String subscriptionId = "sub123";
        String notificationType = "EMAIL";
        String recipient = "<EMAIL>";
        String subject = "测试告警";
        String content = "这是一个测试告警";

        // When
        NotificationResult record = NotificationResult.success(
                alarmId, subscriptionId, notificationType, recipient, subject, content);

        // Then - 验证所有JDBC需要的字段都不为null
        assertThat(record.getId()).isNotNull();
        assertThat(record.getAlarmId()).isEqualTo(alarmId);
        assertThat(record.getSubscriptionId()).isEqualTo(subscriptionId);
        assertThat(record.getNotificationType()).isEqualTo(notificationType);
        assertThat(record.getRecipient()).isEqualTo(recipient);
        assertThat(record.getSubject()).isEqualTo(subject);
        assertThat(record.getContent()).isEqualTo(content);
        assertThat(record.getStatus()).isEqualTo(NotificationResult.Status.SUCCESS);
        assertThat(record.getSentAt()).isNotNull();
        assertThat(record.getCreatedAt()).isNotNull();
    }
    
    @Test
    void testNotificationResultWithFailure() {
        // Given
        NotificationResult record = NotificationResult.failure(
                "alarm123", "sub123", "EMAIL", "<EMAIL>",
                "测试", "内容", "发送失败");

        // When & Then - 验证失败记录的字段
        assertThat(record.getId()).isNotNull();
        assertThat(record.getStatus()).isEqualTo(NotificationResult.Status.FAILED);
        assertThat(record.getErrorMessage()).isEqualTo("发送失败");
        assertThat(record.getSentAt()).isNull(); // 失败记录没有发送时间
        assertThat(record.getCreatedAt()).isNotNull();
    }
    
    @Test
    void testSqlInsertStatement() {
        // Given
        String expectedSql = "INSERT INTO notification_log (" +
                "id, alarm_id, subscription_id, config_id, notification_type, recipient, " +
                "subject, content, status, error_message, retry_count, max_retries, " +
                "sent_at, created_at, updated_at" +
                ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        // When & Then - 验证SQL语句格式正确
        assertThat(expectedSql).contains("INSERT INTO notification_log");
        assertThat(expectedSql).contains("VALUES");
        
        // 验证字段数量匹配
        long fieldCount = expectedSql.chars().filter(ch -> ch == '?').count();
        assertThat(fieldCount).isEqualTo(15); // 15个字段
    }
    
    @Test
    void testDefaultBatchConfiguration() {
        // Given - 默认的批处理配置
        int expectedBatchSize = 100;
        long expectedBatchInterval = 5000L;
        int expectedMaxRetries = 3;

        // When & Then - 验证配置值
        assertThat(expectedBatchSize).isEqualTo(100);
        assertThat(expectedBatchInterval).isEqualTo(5000L);
        assertThat(expectedMaxRetries).isEqualTo(3);
    }
}

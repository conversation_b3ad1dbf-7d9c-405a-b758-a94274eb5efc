package com.geeksec.alarm.notification.model;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通知发送结果模型
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 通知记录ID（用于数据库主键）
     */
    private String id;

    /**
     * 告警ID
     */
    private String alarmId;

    /**
     * 订阅ID
     */
    private String subscriptionId;

    /**
     * 通知渠道 (EMAIL, KAFKA等)
     */
    private String channel;

    /**
     * 接收者
     */
    private String recipient;

    /**
     * 发送状态 (SUCCESS, FAILED)
     */
    private String status;

    /**
     * 发送时间
     */
    private LocalDateTime sentAt;

    /**
     * 错误信息（失败时记录）
     */
    private String errorMessage;

    /**
     * 通知主题
     */
    private String subject;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 状态常量
     */
    public static class Status {
        public static final String SUCCESS = "SUCCESS";
        public static final String FAILED = "FAILED";
    }

    /**
     * 创建成功的通知结果
     */
    public static NotificationResult success(String alarmId, String subscriptionId,
            String channel, String recipient,
            String subject, String content) {
        LocalDateTime now = LocalDateTime.now();
        return NotificationResult.builder()
                .id(java.util.UUID.randomUUID().toString().replace("-", ""))
                .alarmId(alarmId)
                .subscriptionId(subscriptionId)
                .channel(channel)
                .recipient(recipient)
                .subject(subject)
                .content(content)
                .status(Status.SUCCESS)
                .sentAt(now)
                .createdAt(now)
                .build();
    }

    /**
     * 创建失败的通知结果
     */
    public static NotificationResult failure(String alarmId, String subscriptionId,
            String channel, String recipient,
            String subject, String content, String errorMessage) {
        LocalDateTime now = LocalDateTime.now();
        return NotificationResult.builder()
                .id(java.util.UUID.randomUUID().toString().replace("-", ""))
                .alarmId(alarmId)
                .subscriptionId(subscriptionId)
                .channel(channel)
                .recipient(recipient)
                .subject(subject)
                .content(content)
                .status(Status.FAILED)
                .errorMessage(errorMessage)
                .createdAt(now)
                .build();
    }
}
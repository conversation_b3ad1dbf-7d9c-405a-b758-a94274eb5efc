package com.geeksec.alarm.notification.job;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.jdbc.JdbcConnectionOptions;
import org.apache.flink.connector.jdbc.JdbcExecutionOptions;
import org.apache.flink.connector.jdbc.core.datastream.Jdbc;
import org.apache.flink.runtime.state.hashmap.HashMapStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.sql.Timestamp;
import java.time.LocalDateTime;

import com.geeksec.alarm.notification.config.AlarmNotificationConfig;
import com.geeksec.alarm.notification.model.NotificationResult;
import com.geeksec.alarm.notification.pipeline.NotificationPipeline;
import com.geeksec.alarm.notification.sender.NotificationSender;

import lombok.extern.slf4j.Slf4j;

/**
 * 告警通知作业主类
 * 
 * 业务流程：
 * 1. 从 Kafka 接收来自 alarm-processor 的处理后告警数据
 * 2. 从告警服务获取订阅配置，并监听配置变更
 * 3. 根据订阅规则匹配告警并发送通知
 * 4. 支持邮件和 Kafka 两种通知方式
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class AlarmNotificationJob {

    public static void main(String[] args) throws Exception {
        log.info("启动告警通知作业");

        try {
            // 1. 获取配置（命令行参数）
            final ParameterTool parameterTool = ParameterTool.fromArgs(args);

            // 2. 创建配置对象
            final AlarmNotificationConfig config = AlarmNotificationConfig.fromParameterTool(parameterTool);

            // 打印配置信息
            config.printConfig();

            // 3. 创建执行环境
            final StreamExecutionEnvironment env = createExecutionEnvironment(config);

            // 4. 设置全局参数
            env.getConfig().setGlobalJobParameters(parameterTool);

            // 5. 配置检查点
            configureCheckpointing(env, config);

            // 6. 根据配置选择处理流水线
            // 创建通知发送器和流水线
            NotificationSender notificationSender = new NotificationSender(config);
            NotificationPipeline pipeline = NotificationPipeline.createDefault(config, notificationSender);
            DataStream<NotificationResult> notificationStream = pipeline.buildPipeline(env);

            // 7. 添加通知日志JDBC Sink
            String jdbcUrl = parameterTool.get("database.url",
                    "************************************");
            String username = parameterTool.get("database.username", "nta");
            String password = parameterTool.get("database.password", "nta123");

            // 使用Flink官方JDBC连接器
            notificationStream.sinkTo(
                    Jdbc.<NotificationResult>sinkBuilder()
                            .withQueryStatement(
                                    "INSERT INTO notification_log (" +
                                            "id, alarm_id, subscription_id, notification_type, recipient, " +
                                            "subject, content, status, error_message, sent_at, created_at" +
                                            ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                                    (statement, record) -> {
                                        try {
                                            // 直接使用record，无需类型转换
                                            statement.setString(1, record.getId());
                                            statement.setString(2, record.getAlarmId());
                                            statement.setString(3, record.getSubscriptionId());
                                            statement.setString(4, record.getNotificationType());
                                            statement.setString(5, record.getRecipient());
                                            statement.setString(6, record.getSubject());
                                            statement.setString(7, record.getContent());
                                            statement.setString(8, record.getStatus());
                                            statement.setString(9, record.getErrorMessage());

                                            // 优化时间字段处理
                                            LocalDateTime now = LocalDateTime.now();
                                            statement.setTimestamp(10, record.getSentAt() != null ?
                                                    Timestamp.valueOf(record.getSentAt()) : null);
                                            statement.setTimestamp(11, Timestamp.valueOf(
                                                    record.getCreatedAt() != null ? record.getCreatedAt() : now));
                                        } catch (Exception e) {
                                            log.error("设置JDBC参数失败: recordId={}", record.getId(), e);
                                            throw new RuntimeException("JDBC参数设置失败", e);
                                        }
                                    })
                            .withExecutionOptions(
                                    JdbcExecutionOptions.builder()
                                            .withBatchSize(100)
                                            .withBatchIntervalMs(5000)
                                            .withMaxRetries(3)
                                            .build())
                            .buildAtLeastOnce(
                                    new JdbcConnectionOptions.JdbcConnectionOptionsBuilder()
                                            .withUrl(jdbcUrl)
                                            .withDriverName("org.postgresql.Driver")
                                            .withUsername(username)
                                            .withPassword(password)
                                            .withConnectionCheckTimeoutSeconds(60)
                                            .build()))
                    .name("notification-log-jdbc-sink")
                    .setParallelism(1);

            log.info("使用通知日志保存模式，记录通知结果到数据库");

            // 8. 执行作业
            log.info("开始执行告警通知作业...");
            env.execute(config.getJobName());

            log.info("告警通知作业执行完成");

        } catch (Exception e) {
            log.error("告警通知作业执行失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建执行环境
     */
    private static StreamExecutionEnvironment createExecutionEnvironment(AlarmNotificationConfig config) {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 设置并行度
        env.setParallelism(config.getJobParallelism());

        // 设置状态后端（保持使用旧方式以确保兼容性）
        env.setStateBackend(new HashMapStateBackend());

        log.info("执行环境创建完成，并行度: {}", config.getJobParallelism());
        return env;
    }

    /**
     * 配置检查点
     */
    private static void configureCheckpointing(StreamExecutionEnvironment env, AlarmNotificationConfig config) {
        // 启用检查点（保持使用旧方式以确保兼容性）
        env.enableCheckpointing(config.getCheckpointInterval());

        // 设置检查点模式
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);

        // 设置检查点超时时间
        env.getCheckpointConfig().setCheckpointTimeout(config.getCheckpointTimeout());

        // 设置最小暂停间隔
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(config.getMinPauseBetweenCheckpoints());

        // 设置最大并发检查点数
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(config.getMaxConcurrentCheckpoints());

        // 设置检查点清理策略
        env.getCheckpointConfig().setExternalizedCheckpointCleanup(
                CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);

        log.info("检查点配置完成，间隔: {}ms, 超时: {}ms",
                config.getCheckpointInterval(), config.getCheckpointTimeout());
    }
}
